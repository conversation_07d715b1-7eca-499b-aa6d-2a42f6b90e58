import { EggPlugin } from 'egg';
const path = require('path');

const plugin: EggPlugin  = {
  static: true,
  
  mysql: {
      enable: true,
      package: 'egg-mysql'
  },
  
  jwt: {
      enable: true,
      package: 'egg-jwt',
  },

  //启动规则校验
  validate: {
    enable: true,
    package: 'egg-validate',
  },

  //redis
  redis:{
    enable: true,
    package: 'egg-redis',
  },

  oss: {
    enable: true,
    package: 'egg-oss',
  },

  mongo: {
    enable: true,
    package: 'egg-mongo-native'
  },

  clickhouse: {
    enable: true,
    path: path.join(__dirname,"../plugin/egg-clickhouse")
  }

};

export default plugin;
