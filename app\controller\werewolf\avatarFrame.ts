/*
 * @Description: 玩家头像管理
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: 张宇
 * @Date: 2018-11-26 16:25:52
 * @LastEditors: jiawen.wang
 * @LastEditTime: 2022-01-07 09:32:14
 */

import {
    IadListRequest,
    IadListResponse,
    IadOperationRequest,
    IadOperationResponse,
    IavFrameUserListResponse,
    IavFrameUserListRequest,
    IavFrameListRequest,
    IgiveAvFrameRequest,
    IavFrameIsShowRequest,
    IuploadAvatarFrameRequest,
    IuploadAvatarFrameResponse,
    IuserAvatarFrame,
    IopeAvaRecordListRequest,
    IopeAvaRecordListResponse,
    IframeCompleteRequest,
} from '../../model/werewolf'
import { Controller } from 'egg'
import { IerrorMsg, HttpErr } from '../../model/common'

export default class AvatarFrameController extends Controller {
    public async userName() {
        const { ctx, logger, app } = this
        // 校验规则
        const rule = {
            userIds: { type: 'string' },
        }
        try {
            // 校验
            ctx.validate(rule)
            const requestBody: IavFrameUserListRequest = ctx.request.body
            let noList = requestBody.userIds.split(';')
            const list = await ctx.service.werewolf.avatarFrame.userName(noList)
            const responseBody: IavFrameUserListResponse = {
                err_msg: '',
                dataArray: list,
            }
            if (responseBody.dataArray.length <= 0) {
                responseBody.err_msg = '无用户信息'
            }
            ctx.body = responseBody
            ctx.status = HttpErr.Success
        } catch (e) {
            logger.error(e)
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' }
            ctx.body = err
            ctx.status = HttpErr.BadRequest //请求参数错误 401Unauthorized未鉴权
        }
    }
    public async getAllFrames() {
        const { ctx, logger } = this
        // 校验规则
        const rule = {
            channel: { type: 'number' },
            isDynamic: { type: 'number' },
            isComplete: { type: 'number' },
            start: { type: 'number' },
            offset: { type: 'number' },
            frameName: { type: 'string?' },
        }
        try {
            // 校验
            ctx.validate(rule)
            const requestBody: IavFrameListRequest = ctx.request.body
            const FrameList = await ctx.service.werewolf.avatarFrame.getFrames(requestBody)
            ctx.body = FrameList
            ctx.status = HttpErr.Success
        } catch (e) {
            logger.error(e)
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' }
            ctx.body = err
            ctx.status = HttpErr.BadRequest //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async getStaticAvatarFrameList() {
        const { ctx, logger } = this
        try {
            const FrameList = await ctx.service.werewolf.avatarFrame.getStaticAvatarFrameList()
            ctx.body = FrameList
            ctx.status = HttpErr.Success
        } catch (e) {
            logger.error(e)
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' }
            ctx.body = err
            ctx.status = HttpErr.BadRequest //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async giveFrameToUser() {
        const { ctx, logger } = this
        // 校验规则
        const rule = {
            userIds: { type: 'string' },
            id: { type: 'number' },
            uid: { type: 'number' },
            frameName: { type: 'string' },
        }
        try {
            // 校验
            ctx.validate(rule)
            const requestBody: IgiveAvFrameRequest = ctx.request.body
            let result = await ctx.service.werewolf.avatarFrame.giveFrame(requestBody)
            if (result) {
                ctx.body = { code: 200 }
                ctx.status = HttpErr.Success
            } else {
                ctx.body = { code: 500 }
                ctx.status = HttpErr.Success
            }
        } catch (e) {
            logger.error(e)
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' }
            ctx.body = err
            ctx.status = HttpErr.BadRequest //请求参数错误 401Unauthorized未鉴权
        }
    }
    public async frameIsShow() {
        const { ctx, logger } = this
        // 校验规则
        const rule = {
            id: { type: 'number' },
            delsign: { type: 'number' },
            uid: { type: 'number' },
        }
        try {
            // 校验
            ctx.validate(rule)
            const requestBody: IavFrameIsShowRequest = ctx.request.body
            const success = await ctx.service.werewolf.avatarFrame.frameShow(requestBody)
            if (success) {
                ctx.body = { code: 200 }
                ctx.status = HttpErr.Success
            } else {
                const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' }
                ctx.body = err
                ctx.status = HttpErr.BadRequest //请求参数错误 401Unauthorized未鉴权
            }
        } catch (e) {
            logger.error(e)
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' }
            ctx.body = err
            ctx.status = HttpErr.BadRequest //请求参数错误 401Unauthorized未鉴权
        }
    }

    /**
     * 上传头像框基本信息
     */
    public async uploadAvatarFrame() {
        const { ctx, logger } = this
        // 校验规则
        const rule = {
            uid: { type: 'number' },
            name: { type: 'string' },
            type: { type: 'number' },
            // channel: { type: "number" },
            remark: { type: 'string' },
            // price: { type: "number" },
            is_dynamic: { type: 'number' },
            item_cate_id: { type: 'number' },
            level: { type: 'number' },
        }
        try {
            // 校验
            ctx.validate(rule)
            //service
            const requestBody: IuploadAvatarFrameRequest = ctx.request.body
            const responseBody: IuserAvatarFrame = await ctx.service.werewolf.avatarFrame.uploadAvatarFrame(requestBody)
            ctx.body = responseBody
            ctx.status = HttpErr.Success
        } catch (e) {
            logger.error(e)
            const err: IerrorMsg = {
                err_code: HttpErr.BadRequest,
                err_msg: '参数有误',
            }
            ctx.body = err
            ctx.status = HttpErr.BadRequest //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async uploadFrameComplete() {
        const { ctx, logger } = this
        // 校验规则
        const rule = {
            avatarId: { type: 'number' },
        }
        try {
            // 校验
            ctx.validate(rule)
            //service
            const requestBody: IframeCompleteRequest = ctx.request.body
            const responseBody: IuserAvatarFrame = await ctx.service.werewolf.avatarFrame.uploadFrameComplete(requestBody)
            ctx.body = responseBody
            ctx.status = HttpErr.Success
        } catch (e) {
            logger.error(e)
            const err: IerrorMsg = {
                err_code: HttpErr.BadRequest,
                err_msg: '参数有误',
            }
            ctx.body = err
            ctx.status = HttpErr.BadRequest //请求参数错误 401Unauthorized未鉴权
        }
    }


}
