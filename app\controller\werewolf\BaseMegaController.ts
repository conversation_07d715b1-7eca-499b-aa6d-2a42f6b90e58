/*
 * @Description: controller父类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-03-16 14:39:17
 * @LastEditors: hammercui
 * @LastEditTime: 2020-03-24 09:53:24
 */
import { Controller } from 'egg';
import { HttpErr, IerrorMsg } from '../../model/common';

export default class BaseMegaController extends Controller {

    public respSucc() {
        this.ctx.body = {
            err_code: HttpErr.Success,
            err_msg: "success",
        };
        this.ctx.status = HttpErr.Success;
    }
    public respSuccData(body) {
        this.ctx.body = body;
        this.ctx.status = HttpErr.Success;
    }

    public respFail(msg) {
        const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: msg };
        this.ctx.body = err;
        this.ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
}
