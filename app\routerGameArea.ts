/*
 * @Description: 商城道具路由
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhangyu
 * @Date: 2020-04-24 11:50:11
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-12-09 14:06:18
 */
import { Router, Application } from "egg";
import { AccessRouteId, AccessRouteName } from "./model/accessRouteCof";
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

    const { controller, router } = app;

    router.post(`${API_VERSION}/werewolf/gameArea/getGameList`, controller.werewolf.gameArea.getGameList)
    router.post(`${API_VERSION}/werewolf/gameArea/getGameShowList`, controller.werewolf.gameArea.getGameShowList)
    router.post(`${API_VERSION}/werewolf/gameArea/getGameConfigList`, controller.werewolf.gameArea.getGameConfigList)
    router.post(`${API_VERSION}/werewolf/gameArea/updateGameConfig`, controller.werewolf.gameArea.updateGameConfig)
    router.post(`${API_VERSION}/werewolf/gameArea/updateGameConfigFlag`, controller.werewolf.gameArea.updateGameConfigFlag)
    router.post(`${API_VERSION}/werewolf/gameArea/updateGameShow`, controller.werewolf.gameArea.updateGameShow)
    router.post(`${API_VERSION}/werewolf/gameArea/getAnimationList`, controller.werewolf.gameArea.getAnimationList)
    router.post(`${API_VERSION}/werewolf/gameArea/getAvatarFrameList`, controller.werewolf.gameArea.getAvatarFrameList)
    router.post(`${API_VERSION}/werewolf/gameArea/getUserList`, controller.werewolf.gameArea.getUserList)
    router.post(`${API_VERSION}/werewolf/gameArea/getJudgeUserList`, controller.werewolf.gameArea.getJudgeUserList)
    router.post(`${API_VERSION}/werewolf/gameArea/getSearchUserList`, controller.werewolf.gameArea.getSearchUserList)
    router.post(`${API_VERSION}/werewolf/gameArea/getSearchUserJudgeList`, controller.werewolf.gameArea.getSearchUserJudgeList)
    router.post(`${API_VERSION}/werewolf/gameArea/insertUserMatch`, controller.werewolf.gameArea.insertUserMatch)
    router.post(`${API_VERSION}/werewolf/gameArea/deleteUserMatch`, controller.werewolf.gameArea.deleteUserMatch)
    router.post(`${API_VERSION}/werewolf/gameArea/deleteAllUserMatch`, controller.werewolf.gameArea.deleteAllUserMatch)
    router.post(`${API_VERSION}/werewolf/gameArea/insertUserJudgeMatch`, controller.werewolf.gameArea.insertUserJudgeMatch)
    router.post(`${API_VERSION}/werewolf/gameArea/deleteUserJudgeMatch`, controller.werewolf.gameArea.deleteUserJudgeMatch)
    router.post(`${API_VERSION}/werewolf/gameArea/deleteAllUserJudgeMatch`, controller.werewolf.gameArea.deleteAllUserJudgeMatch)
    router.post(`${API_VERSION}/werewolf/gameArea/getGameArenaList`, controller.werewolf.gameArea.getGameArenaList)
    router.post(`${API_VERSION}/werewolf/gameArea/updateGameArena`, controller.werewolf.gameArea.updateGameArena)
    
}

export default load
