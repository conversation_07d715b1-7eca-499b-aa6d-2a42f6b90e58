/*
 * @Description: 首页弹窗服务类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhang<PERSON>
 * @Date: 2020-04-23 10:50:39
 * @LastEditors: zhanglu
 * @LastEditTime: 2020-12-30 14:24:40
 */
import BaseMegaService from './BaseMegaService';

export default class UserCreditService extends BaseMegaService {

    public async getUserCreditFlowList(req: any) {
        const { logger } = this;
        try {
            let sqlStr = `
                SELECT 
                    log.*, 
                    tt.\`name\` AS this_credit_type_name, 
                    lt.\`name\` AS last_credit_type_name,
                    g.starttime
                FROM user_credit_log log 
                LEFT JOIN credit_type tt ON tt.id = log.this_credit_type_id
                LEFT JOIN credit_type lt ON lt.id = log.last_credit_type_id
                LEFT JOIN tgamerecord g ON g.\`no\` = log.game_id
                WHERE log.user_id = ${req.userId}
                AND log.this_credit_type_id != 31
                AND log.last_credit_type_id != 31
                `;

            if (req.startTime && req.startTime != "" && req.endTime && req.endTime != "") {
                sqlStr += ` AND log.create_time BETWEEN ${req.startTime} AND ${req.endTime} `;
            }

            sqlStr += ` ORDER BY log.create_time DESC
                        LIMIT ${(req.current - 1) * req.pageCount} ,${req.pageCount} `;

            return await this.selectList(sqlStr, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getUserCreditFlowListCount(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
                SELECT 
                    COUNT(*) AS num
                FROM user_credit_log log 
                WHERE log.user_id = ${req.userId}
                `;

            if (req.startTime && req.startTime != "" && req.endTime && req.endTime != "") {
                sqlStr += ` AND log.create_time BETWEEN ${req.startTime} AND ${req.endTime} `;
            }
            return await this.selectOne(sqlStr, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getUserCreditInfo(req: any) {
        const { logger } = this;
        try {
            let sqlStr = `
            SELECT s.* , u.nickname
            FROM tuser u
            LEFT JOIN user_credit_score s ON u.\`no\` = s.user_id AND s.delsign = 0
            WHERE u.\`no\` = ? 
                `;
            return await this.selectList(sqlStr, [req.userId]);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getUserEscapeLogId(req: any) {
        const { logger } = this;
        try {
            let sqlStr = `
            SELECT 
                id AS logId 
            FROM user_credit_log 
            WHERE
                user_id = ?
                AND game_id = ?
                AND this_credit_type_id IN (7,8,12,16,20,24,28)
                `;
            return await this.selectList(sqlStr, [req.user_no, req.game_no]);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getCreditTypeList(req: any) {
        const { logger } = this;
        try {
            let sqlStr = `
                SELECT 
                    *
                FROM credit_type
                `;
            return await this.selectList(sqlStr, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getCreditList(req: any) {
        const { logger } = this;
        try {
            let sqlStr = `
                SELECT COUNT(*) AS num
                FROM trealtime_report
                WHERE time >= ? AND time <= ?`;
            const reportNum = await this.selectOne(sqlStr, [req.start,req.end],"werewolf_slave");
            sqlStr = `
                SELECT COUNT(*) AS num
                FROM user_credit_log
                WHERE create_date >= ?
                AND create_date <= ?
                AND last_credit_type_id IN (1,2,14,18,22,26)`;
            const curseNum = await this.selectOne(sqlStr, [req.start,req.end],"werewolf_slave");
            sqlStr = `
                SELECT COUNT(*) AS num
                FROM user_credit_log
                WHERE create_date >= ?
                AND create_date <= ?
                AND last_credit_type_id IN  (3,4,13,17,21,25)`;
            const faceNum = await this.selectOne(sqlStr, [req.start,req.end],"werewolf_slave");
            sqlStr = `
                SELECT COUNT(*) AS num
                FROM user_credit_log
                WHERE create_date >= ?
                AND create_date <= ?
                AND last_credit_type_id IN  (7,8,12,16,20,24,28)`;
            const runNum = await this.selectOne(sqlStr, [req.start,req.end],"werewolf_slave");
            sqlStr = `
                SELECT COUNT(*) AS num
                FROM user_credit_log
                WHERE create_date >= ?
                AND create_date <= ?
                AND last_credit_type_id IN  (5,6,11,15,19,23,27)`;
            const hangNum = await this.selectOne(sqlStr, [req.start,req.end],"werewolf_slave");
            sqlStr = `
                SELECT COUNT(DISTINCT user_id) AS num
                FROM user_credit_log
                WHERE create_date >= ?
                AND create_date <= ? AND cancel = 1`;
            const cancelNum = await this.selectOne(sqlStr, [req.start,req.end],"werewolf_slave");
            sqlStr = `
                SELECT COUNT(*) AS num
                FROM user_credit_log
                WHERE create_date >= ?
                AND create_date <= ? AND last_score < 0`;
            const subNum = await this.selectOne(sqlStr, [req.start,req.end],"werewolf_slave");
            return {
                reportNum: reportNum['num'],
                curseNum: curseNum['num'],
                faceNum: faceNum['num'],
                runNum: runNum['num'],
                hangNum: hangNum['num'],
                cancelNum: cancelNum['num'],
                subNum: subNum['num']
            };
        } catch (error) {
            logger.error(error);
            throw error
        }
    }
}
