/*
 * @Description: 游戏板子服务类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammer<PERSON><PERSON>
 * @Date: 2019-10-10 15:02:09
 * @LastEditors: zhanglu
 * @LastEditTime: 2022-09-13 11:41:01
 */

import { Service } from 'egg';
import { SendEmailsToUsersRequest, SearchNewUserListResponse, PushMessageResquest } from '../../model/werewolf';
import { Ipayload } from '../../model/common';
import {
    IupdateGameConfOpenReq,
    IcreateGameConfOpenReq,
    IupdateGameConfOpenSortReq,
    ItboxSeasonOpenReq,
    ItboxUpdateEndTimeReq,
    IpropsConfigItem,
    IanimationReq,
    IframeReq,
    IgiftBagInfo,
    IgiftBagContentInfo,
    IdocumentResourceReq,
    IavatarFramePeriod,
    IavatarFramePeriodv2,
    FrameRequestPeriod,
    FrameRequestPeriodid,
    FrameRequestPeriodSort
} from '../../model/werewolf2';
import moment = require("moment");
import { now } from "moment";
import BaseMegaService from './BaseMegaService';

export default class GameConfigService extends BaseMegaService {

    /**
     * @name: 获得open表全部板子
     */
    public async getOpenList(req: ItboxSeasonOpenReq) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        try {
            //查询板子列表
            let sql = `SELECT b.*,
                              i.item_cate_id
                       FROM tbox b
                                LEFT JOIN item_dic i ON b.item_dic_id = i.id
                                LEFT JOIN item_cate ic ON i.item_cate_id = ic.id
                       WHERE putaway_time <= ?
                         AND slotout_time >= ?
            `;
            let now = moment(req.endtime, 'YYYY-MM-DD HH:mm:ss').utcOffset(-1).format('YYYY-MM-DD HH:mm:ss');
            const openList = await db.query(sql, [now, now]);
            return { openList };

        } catch (error) {
            throw error;
        }
    }

    /**
     * @name: 获得open表全部板子
     */
    public async getBoxList() {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        try {
            //查询板子列表
            let sql = `SELECT *
                       FROM tbox
                       WHERE delsign = 0
                       GROUP BY img_name
                       ORDER BY \`no\` DESC
            `;
            const openList = await db.query(sql);
            return { openList };

        } catch (error) {
            throw error;
        }
    }

    /**
     * @name: 获得open表全部板子
     */
    public async getAllBoxList() {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        try {
            //查询板子列表
            let sql = `
                SELECT b.*,
                       i.item_cate_id
                FROM tbox b
                         LEFT JOIN item_dic i ON b.item_dic_id = i.id
                         LEFT JOIN item_cate ic ON i.item_cate_id = ic.id
                ORDER BY \`no\` DESC
            `;
            const openList = await db.query(sql);
            return { openList };

        } catch (error) {
            throw error;
        }
    }

    /**
     * @name: 获得open表全部板子
     */
    public async getTboxSeasonList() {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        try {
            //查询板子列表
            let sql = `SELECT *
                       FROM tbox_season_config
            `;
            const seasonList = await db.query(sql);
            return { seasonList };
        } catch (error) {
            throw error;
        }
    }

    /**
     * @name: 立即修改flag
     * @msg:
     * @param {type}
     * @return:
     */
    public async updateBox(req: IupdateGameConfOpenReq) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = `UPDATE tbox
                       SET \`name\`            = ?,
                           visual_no           = ?,
                           visual_type         = ?,
                           \`level\`           = ?,
                           price               = ?,
                           coin                = ?,
                           coin_e              = ?,
                           coin_min            = ?,
                           coin_max            = ?,
                           weight              = ?,
                           buy                 = ?,
                           exchange            = ?,
                           give                = ?,
                           achievement         = ?,
                           img_name            = ?,
                           type                = ?,
                           num                 = ?,
                           num_surplus         = ?,
                           num_user_limit      = ?,
                           num_user_once_limit = ?,
                           num_daily_limit     = ?,
                           \`desc\`            = ?,
                           wish                = ?,
                           delsign             = ?,
                           sql_text            = ?,
                           item_dic_id         = ?
                       WHERE \`no\` = ?; `;
            await conn.query(sql, [req.name, req.visual_no, req.visual_type, req.level, req.price, req.coin, req.coin_e, req.coin_min, req.coin_max, req.weight, req.buy, req.exchange, req.give, req.achievement, req.img_name, req.type, req.num, req.num_surplus, req.num_user_limit, req.num_user_once_limit, req.num_daily_limit, req.desc, req.wish, req.delsign, req.sql_text, req.item_dic_id, req.no]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    /**
     * @name: 立即修改flag
     * @msg:
     * @param {type}
     * @return:
     */
    public async updateEndTime(req: ItboxUpdateEndTimeReq) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = `UPDATE tbox
                       SET slotout_time = ?
                       WHERE slotout_time = ? `;
            await conn.query(sql, [req.seasonNew.endtime, req.seasonOri.endtime]);

            let sql3 = `UPDATE tbox
                        SET putaway_time = ?
                        WHERE putaway_time = ? `;
            await conn.query(sql3, [req.seasonNew.endtime, req.seasonOri.endtime]);

            let sql1 = `UPDATE tbox_season_config
                        SET endtime = ?
                        WHERE season = ? `;
            await conn.query(sql1, [req.seasonNew.endtime, req.seasonNew.season]);

            let sql2 = `UPDATE tbox_season_config
                        SET starttime = ?
                        WHERE season = ? `;
            await conn.query(sql2, [req.seasonNew.endtime, req.seasonNew.season + 1]);

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    /**
     * @name: 新建
     * @msg:
     * @param {type}
     * @return:
     */
    public async createOpen(req: IcreateGameConfOpenReq) {
        const { app, logger } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            //2 新增
            let sql = `INSERT
            IGNORE INTO tbox ( 
                        \`name\` ,
                        visual_no ,
                        visual_type ,
                        \`level\` ,
                        price ,
                        coin ,
                        coin_e ,
                        coin_min ,
                        coin_max ,
                        weight ,
                        buy ,
                        exchange ,
                        give ,
                        achievement ,
                        img_name ,
                        type ,
                        num ,
                        num_surplus ,
                        num_user_limit ,
                        num_user_once_limit ,
                        num_daily_limit ,
                        \`desc\` ,
                        wish ,
                        delsign ,
                        sql_text , putaway_time, slotout_time, item_dic_id ) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) `;
            await conn.query(sql, [req.name, req.visual_no, req.visual_type, req.level, req.price, req.coin, req.coin_e, req.coin_min, req.coin_max, req.weight, req.buy, req.exchange, req.give, req.achievement, req.img_name, req.type, req.num, req.num_surplus, req.num_user_limit, req.num_user_once_limit, req.num_daily_limit, req.desc, req.wish, req.delsign, req.sql_text, req.putaway_time, req.slotout_time, req.item_dic_id]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async createOpenList(reqList: IcreateGameConfOpenReq[]) {
        const { app, logger } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = `INSERT
            IGNORE INTO tbox ( 
                        \`name\` ,
                        visual_no ,
                        visual_type ,
                        \`level\` ,
                        price ,
                        coin ,
                        coin_e ,
                        coin_min ,
                        coin_max ,
                        weight ,
                        buy ,
                        exchange ,
                        give ,
                        achievement ,
                        img_name ,
                        type ,
                        num ,
                        num_surplus ,
                        num_user_limit ,
                        num_user_once_limit ,
                        num_daily_limit ,
                        \`desc\` ,
                        wish ,
                        delsign ,
                        sql_text , putaway_time, slotout_time, item_dic_id ) values ? `;

            let boxList: any[] = [];
            for (const req of reqList) {
                boxList.push([req.name, req.visual_no, req.visual_type, req.level, req.price, req.coin, req.coin_e, req.coin_min, req.coin_max, req.weight, req.buy, req.exchange, req.give, req.achievement, req.img_name, req.type, req.num, req.num_surplus, req.num_user_limit, req.num_user_once_limit, req.num_daily_limit, req.desc, req.wish, req.delsign, req.sql_text, req.putaway_time, req.slotout_time, req.item_dic_id]);
            }
            await conn.query(sql, [boxList]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    /**
     * @name: 新建赛季
     * @msg:
     * @param {type}
     * @return:
     */
    public async createNewSeason() {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            //2 新增
            let sql = ` INSERT INTO tbox_season_config (season, starttime, endtime)
                            (SELECT season + 1                         AS season,
                                    endtime                            AS starttime,
                                    date_add(endtime, interval 45 DAY) AS endtime
                             FROM tbox_season_config
                             ORDER BY season DESC LIMIT 1) `;

            await conn.query(sql);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async geUnOpenList() {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        try {
            let sql = `SELECT tg.id,
                              tg.\`desc\`,
                              tg.type,
                              tg.num AS people_num
                       FROM tgameconfig AS tg
                                LEFT JOIN tgameconfig_open AS tgo ON tgo.gameconfig_id = tg.id
                       WHERE tgo.gameconfig_id is NULL
                         AND tg.\`desc\` IS NOT NULL
            `;
            const unOpenRole = await db.query(sql);
            // let a = {};
            // //role存入map并排序
            // for (const iterator of unOpenRole) {
            //     let key = iterator.id.toString();
            //     //a 包含属性
            //     if (a.hasOwnProperty(key)) {
            //         a[key].push(iterator);
            //     } else {
            //         a[key] = [iterator];
            //     }
            // }
            return unOpenRole;
        } catch (error) {
            throw error;
        }
    }

    /**
     * @name: 获得open表全部板子
     */
    public async getFrame() {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        try {
            //查询板子列表
            let sql = `SELECT *,
                              id   AS item_id,
                              2010 AS cate_id
                       FROM avatarframe
                       ORDER by id DESC
            `;
            const frameList = await db.query(sql);
            return { frameList };

        } catch (error) {
            throw error;
        }
    }

    public async getFrameWithItemDic() {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        try {
            //查询板子列表
            let sql = `SELECT a.*,
                              i.id AS item_dic_id
                       FROM avatarframe a
                                INNER JOIN item_dic i
                                           ON a.id = i.item_id AND i.item_cate_id > 2000 AND i.item_cate_id < 3000
                       ORDER BY i.id DESC
            `;
            const frameList = await db.query(sql);
            return { frameList };

        } catch (error) {
            throw error;
        }
    }

    /**
     * @name: 获得open表全部板子
     */
    public async getProps() {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        try {
            //查询板子列表
            let sql = `SELECT *
                       FROM tprops
                       WHERE type IN (1, 2)
            `;
            const propsList = await db.query(sql);
            return { propsList };

        } catch (error) {
            throw error;
        }
    }

    /**
     * @name: 获得open表全部板子
     */
    public async getAllProps() {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        try {
            //查询板子列表
            let sql = `SELECT *
                       FROM tprops
                       ORDER BY \`no\` DESC
            `;
            const propsList = await db.query(sql);
            return { propsList };

        } catch (error) {
            throw error;
        }
    }

    /**
     * @name: 立即修改flag
     * @msg:
     * @param {type}
     * @return:
     */
    public async updateProps(req: IpropsConfigItem) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = `UPDATE tprops
                       SET type               = ?,
                           name               = ?,
                           unit               = ?,
                           unit_num           = ?,
                           price              = ?,
                           reprice            = ?,
                           timeprice          = ?,
                           charm              = ?,
                           prive_remark       = ?,
                           special_content    = ?,
                           bundle_info        = ?,
                           props_explain      = ?,
                           datatime           = ?,
                           img_name           = ?,
                           gif_name           = ?,
                           can_use            = ?,
                           priority           = ?,
                           props_text         = ?,
                           buy                = ?,
                           single_purchase    = ?,
                           box                = ?,
                           gift_source        = ?,
                           gift_type          = ?,
                           power_value        = ?,
                           delsign            = ?,
                           gift_level_one_num = ?,
                           gift_level_two_num = ?,
                           gift_super         = ?,
                           mask_buy           = ?,
                           gift_check_num     = ?,
                           nobleLevel         = ?,
                           \`show\`           = ?,
                           hot                = ?,
                           diamond            = ?
                       WHERE
                           no = ? `;
            await conn.query(sql, [
                req.type,
                req.name,
                req.unit,
                req.unit_num,
                req.price,
                req.reprice,
                req.timeprice,
                req.charm,
                req.prive_remark,
                req.special_content,
                req.bundle_info,
                req.props_explain,
                req.datatime,
                req.img_name,
                req.gif_name,
                req.can_use,
                req.priority,
                req.props_text,
                req.buy,
                req.single_purchase,
                req.box,
                req.gift_source,
                req.gift_type,
                req.power_value,
                req.delsign,
                req.gift_level_one_num,
                req.gift_level_two_num,
                req.gift_super,
                req.mask_buy,
                req.gift_check_num,
                req.nobleLevel,
                req.show,
                req.hot,
                req.diamond,
                req.no]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    /**
     * @name: 立即修改flag
     * @msg:
     * @param {type}
     * @return:
     */
    public async insertProps(req: IpropsConfigItem) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = `INSERT
            IGNORE INTO tprops (
                        type,
                        name,
                        unit,
                        unit_num,
                        price,
                        reprice,
                        timeprice,
                        charm,
                        prive_remark,
                        special_content,
                        bundle_info,
                        props_explain,
                        datatime,
                        img_name,
                        gif_name,
                        can_use,
                        priority,
                        props_text,
                        buy,
                        single_purchase,
                        box,
                        gift_source,
                        gift_type,
                        power_value,
                        gift_level_one_num,
                        gift_level_two_num,
                        gift_super,
                        mask_buy,
                        gift_check_num,
                        nobleLevel,
                        \`show\`,
                        hot,
                        diamond ,
                        delsign
                    )
                    VALUES
                        (
                            ?,
                            ?,
                            ?,
                            ?,
                            ?,
                            ?,
                            ?,
                            ?,
                            ?,
                            ?,
                            ?,
                            ?,
                            NOW(),
                            ?,
                            ?,
                            ?,
                            ?,
                            ?,
                            ?,
                            ?,
                            ?,
                            ?,
                            ?,
                            ?,
                            ?,
                            ?,
                            ?,
                            ?,
                            ?,
                            ?,
                            ?,
                            ?,
                            ?,
                            ?
                            
                        ); `;
            await conn.query(sql, [
                req.type,
                req.name,
                req.unit,
                req.unit_num,
                req.price,
                req.reprice,
                req.timeprice,
                req.charm,
                req.prive_remark,
                req.special_content,
                req.bundle_info,
                req.props_explain,
                req.img_name,
                req.gif_name,
                req.can_use,
                req.priority,
                req.props_text,
                req.buy,
                req.single_purchase,
                req.box,
                req.gift_source,
                req.gift_type,
                req.power_value,
                req.gift_level_one_num,
                req.gift_level_two_num,
                req.gift_super,
                req.mask_buy,
                req.gift_check_num,
                req.nobleLevel,
                req.show,
                req.hot,
                req.diamond,
                req.delsign]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    /**
     * @name: 获得open表全部板子
     */
    public async getAnimation() {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        try {
            //查询板子列表
            let sql = `SELECT *
                       FROM animation
                       WHERE bg != 2
            `;
            const animationList = await db.query(sql);
            return { animationList };

        } catch (error) {
            throw error;
        }
    }

    /**
     * @name:
     */
    public async getAnimationRole() {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        try {
            //查询板子列表
            let sql = `SELECT *
                       FROM animation_role
                       ORDER BY role
            `;
            const animationRoleList = await db.query(sql);
            return { animationRoleList };

        } catch (error) {
            throw error;
        }
    }

    /**
     * @name: 立即修改flag
     * @msg:
     * @param {type}
     * @return:
     */
    public async updateAni(req: IanimationReq) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = `UPDATE animation
                       SET \`name\` = ?,
                           role     = ?,
                           type     = ?,
                           bg       = ?,
                           timer    = ?,
                           price    = ?,
                           \`show\` = ?,
                           buy      = ?,
                           sort     = ?,
                           alert    = ?,
                           hot      = ?,
                           remark   = ?,
                           channel = ?,
                           delsign = ?,
                           isLight = ?,
                           ss_buff = ?
                       WHERE id = ? `;
            await conn.query(sql, [
                req.name,
                req.role,
                req.type,
                req.bg,
                req.timer,
                req.price,
                req.show,
                req.buy,
                req.sort,
                req.alert,
                req.hot,
                req.remark,
                req.channel,
                req.delsign,
                req.isLight,
                Number(req.ss_buff),
                req.id,
            ]);

            let sqlItemDic = `
                UPDATE item_dic
                SET \`name\` = ?,
                    remark   = ?
                WHERE item_id = ?
                  AND item_cate_id IN (1010, 1012, 1020, 1031, 1060, 1070, 1080, 1091, 1092, 1093, 3010, 3020)
            `
            await conn.query(sqlItemDic, [req.name, req.remark, req.id]);

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    /**
     * @name: 立即修改flag
     * @msg:
     * @param {type}
     * @return:
     */
    public async insertAni(req: IanimationReq) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = `INSERT IGNORE
                       INTO animation (\`name\`, role, type, bg, timer, price, \`show\`, buy, sort, alert, hot, remark,
                                       channel, isLight, delsign,ss_buff)
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?)`;
            const result = await conn.query(sql, [
                req.name,
                req.role,
                req.type,
                req.bg,
                req.timer,
                req.price,
                req.show,
                req.buy,
                req.sort,
                req.alert,
                req.hot,
                req.remark,
                req.channel,
                req.isLight,
                req.delsign,
                Number(req.ss_buff)]);

            req.id = result.insertId;
            let sqlInsertItemDic = `
                INSERT INTO \`item_dic\` (\`item_cate_id\`, \`item_id\`, \`name\`, \`version\`, \`delsign\`, remark)
                VALUES (?, ?, ?, ?, ?, ?);
            `;

            let cateId = 0;
            let role = req.role + '';
            switch (Number(role)) {
                case 999:
                    cateId = 1020;
                    break;
                case 992:
                    cateId = 1011;
                    break;
                case 990:
                    cateId = 1012;
                    break;
                case 995:
                    cateId = 1060;
                    break;
                case 994:
                    cateId = 1070;
                    break;
                case 993:
                    cateId = 1080;
                    break;
                case 998:
                    cateId = 1091;
                    break;
                case 997:
                    cateId = 1092;
                    break;
                case 996:
                    cateId = 1093;
                    break;
                case 1000:
                    cateId = 1010;
                    break;
                case 2000:
                case 2001:
                case 2002:
                case 2003:
                    cateId = 1031;
                    break;
                case 2004:
                    cateId = 1094;
                    break;
                case 2005:
                    cateId = 1095;
                    break;
                case 2006:
                    cateId = 1092;
                    break;
                default:
                    cateId = 0;
                    break;
            }

            if (req.role < 100) {
                cateId = 3010;
            } else if (req.role >= 100 && req.role < 200) {
                cateId = 3020;
            }

            await conn.query(sqlInsertItemDic, [cateId, req.id, req.name, 0, 0, req.remark]);

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    /**
     * @name: 立即修改flag
     * @msg:
     * @param {type}
     * @return:
     */
    public async updateFrame(req: any) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = `UPDATE avatarframe
                       SET \`name\`   = ?,
                           type       = ?,
                           level      = ?,
                           channel    = ?,
                           preview    = ?,
                           remark     = ?,
                           price      = ?,
                           is_dynamic = ?,
                           alert      = ?,
                           hot        = ?,
                           delsign    = ?,
                           createtime = NOW()
                       WHERE id = ? `;
            await conn.query(sql, [
                req.name,
                req.type,
                req.level,
                req.channel,
                req.preview,
                req.remark,
                req.price,
                req.is_dynamic,
                req.alert,
                req.hot,
                req.delsign,
                req.id]);

            let sqlItemDic = `
                UPDATE item_dic
                SET \`name\` = ?,
                    remark   = ?
                WHERE item_id = ?
                  AND item_cate_id IN (2010, 2020)
            `
            await conn.query(sqlItemDic, [req.name, req.remark, req.id]);

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    /**
     * @name: 获得open表全部板子
     */
    public async getGiftBagInfo() {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        try {
            //查询板子列表
            let sql = `
                SELECT bag.\`no\`,
                       bag.\`name\`         AS bagName,
                       bag.price,
                       bag.\`desc\`,
                       bag.img_name         AS bagImg,
                       bag.num_user_limit,
                       bag.putaway_time,
                       bag.slotout_time,
                       bag.duration,
                       bag.sort,
                       bag.delsign          AS bagDelsign,
                       content.\`no\`       AS contentNo,
                       content.item_dic_id,
                       content.img_name     AS contentImg,
                       content.num,
                       content.direct,
                       content.sql_text,
                       content.\`describe\` AS contentDesc,
                       content.delsign      AS contentDelsign,
                       i.\`name\`           AS contentName,
                       i.item_cate_id       AS cate_id,
                       i.item_id,
                       ni.img_name          AS normalImg,
                       ma.prive_remark      AS maskshowImg
                FROM gift_bag_item bag
                         LEFT JOIN gift_bag_content_item content ON bag.\`no\` = content.gift_bag_no
                         INNER JOIN item_dic i ON i.id = content.item_dic_id
                         LEFT JOIN normal_item ni ON ni.\`no\` = i.item_id
                         LEFT JOIN maskshow ma ON ma.\`no\` = i.item_id
                WHERE bag.invisible_type = 0
                ORDER BY bag.sort ASC,
                         bag.\`no\` ASC,
                         content.\`no\` ASC
            `;
            const giftBagList = await db.query(sql);
            return { giftBagList };

        } catch (error) {
            throw error;
        }
    }

    /**
     * @name: 立即修改flag
     * @msg:
     * @param {type}
     * @return:
     */
    public async createGiftBag(req: IgiftBagInfo) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = `INSERT INTO \`gift_bag_item\` (\`name\`, \`price\`, \`num_user_limit\`, \`putaway_time\`,
                                                      \`slotout_time\`, \`duration\`, \`img_name\`, \`desc\`, \`sort\`,
                                                      \`delsign\`)
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);`;
            await conn.query(sql, [
                req.name,
                req.price,
                req.num_user_limit,
                req.putaway_time,
                req.slotout_time,
                req.duration,
                req.img_name,
                req.desc,
                req.sort,
                req.delsign]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    /**
     * @name: 立即修改flag
     * @msg:
     * @param {type}
     * @return:
     */
    public async updateGiftBag(req: IgiftBagInfo) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = `UPDATE \`gift_bag_item\`
                       SET \`name\`           = ?,
                           \`price\`          = ?,
                           \`num_user_limit\` = ?,
                           \`putaway_time\`   = ?,
                           \`slotout_time\`   = ?,
                           \`duration\`       = ?,
                           \`img_name\`       = ?,
                           \`desc\`           = ?,
                           \`sort\`           = ?,
                           \`delsign\`        = ?
                       WHERE \`no\` = ? `;
            await conn.query(sql, [
                req.name,
                req.price,
                req.num_user_limit,
                req.putaway_time,
                req.slotout_time,
                req.duration,
                req.img_name,
                req.desc,
                req.sort,
                req.delsign,
                req.no]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    /**
     * @name: 立即修改flag
     * @msg:
     * @param {type}
     * @return:
     */
    public async createGiftBagContent(req: IgiftBagContentInfo) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = `INSERT INTO \`gift_bag_content_item\` (\`gift_bag_no\`, \`item_dic_id\`, \`num\`, \`direct\`,
                                                              \`describe\`, \`delsign\`)
                       VALUES (?, ?, ?, ?, ?, ?)`;
            await conn.query(sql, [
                req.gift_bag_no,
                req.item_dic_id,
                req.num,
                req.direct,
                req.describe,
                req.delsign]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async updateGiftBagContent(req: IgiftBagContentInfo) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` UPDATE \`gift_bag_content_item\`
                        SET \`gift_bag_no\` = ?,
                            \`item_dic_id\` = ?,
                            \`num\`         = ?,
                            \`direct\`      = ?,
                            \`describe\`    = ?,
                            \`delsign\`     = ?
                        WHERE \`no\` = ?`;
            await conn.query(sql, [
                req.gift_bag_no,
                req.item_dic_id,
                req.num,
                req.direct,
                req.describe,
                req.delsign,
                req.no]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    /**
     * @name: 立即修改flag
     * @msg:
     * @param {type}
     * @return:
     */
    public async insertDocumentResource(req: IdocumentResourceReq) {
        const { app, ctx } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = `INSERT INTO \`document_resource\` (\`document\`, \`name\`, \`extension\`, \`type\`, \`md5\`)
                       VALUES (?, ?, ?, ?, ?) ON DUPLICATE KEY
            UPDATE document =
            VALUES (document), \`name\` =
            VALUES (\`name\`), \`extension\` =
            VALUES (\`extension\`), \`type\` =
            VALUES (\`type\`), \`md5\` = ''
            `;
            await conn.query(sql, [
                req.document,
                req.name,
                req.extension,
                req.type,
                ""
            ]);

            if (req.fontMd5 != null) {

                sql = `INSERT INTO \`document_resource\` (\`document\`, \`name\`, \`extension\`, \`type\`, \`md5\`)
                       VALUES (?, ?, ?, ?, ?) ON DUPLICATE KEY
            UPDATE document =
            VALUES (document), \`name\` =
            VALUES (\`name\`), \`extension\` =
            VALUES (\`extension\`), \`type\` =
            VALUES (\`type\`), \`md5\` = VALUES (\`md5\`)
            `;
                await conn.query(sql, [
                    req.document,
                    req.name,
                    req.extension,
                    req.type,
                    req.fontMd5,
                ]);
            }

            this.logger.error("req " + req.document, req.name, req.extension, req.type, req.fontMd5);
            await conn.commit(); // 提交事务

            await ctx.curl('http://' + app.config.phpDomain + '/Werewolf/tools/resourceMD5new.php', {
                method: 'POST',
                // 通过 contentType 告诉 HttpClient 以 JSON 格式发送
                contentType: 'json',
                data: {},
                dataType: 'text', // 自动解析 JSON response
                timeout: 3000, // 3 秒超时
            });

        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    /**
     * @name: 获得用户匹配值及其他信息
     */
    public async getUserScoreList(req: string) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        try {
            //查询板子列表
            let sql = `SELECT u.\`no\`,
                              u.nickname,
                              u.headicon,
                              u.bug,
                              u.frame,
                              u.scorce,
                              u.m_score,
                              IFNULL(n.noble_no, 0)                    AS nobleLevel,
                              IFNULL(n.num, 0)                         AS nobleNum,
                              IFNULL(n.noble_date >= CURDATE(), FALSE) AS nobleBadge,
                              IFNULL(ans.animation_id, 0)              AS nobleAn,
                              IFNULL(a.note_id, 0)                     AS frameNote,
                              IFNULL(l.\`level\`, 0) AS 'level', IFNULL( ml.\`level\`, 0 ) AS 'mlevel', IFNULL( msl.\`level\`, 0 ) AS 'm_scorelevel', IF
                        (
                        t.tag_id IS NOT NULL,
                            TRUE,
                            FALSE
                        ) AS 'anchor', IFNULL(tu.user_coe, 0) AS userCoe,
                              IFNULL(cu.cp_id, 0)                      AS cp_id,
                              IFNULL(o.\`level\`, 0)                   AS \`cpLevel\`,
                              IFNULL(o.\`intimacy\`, 0)                AS intimacy,
                              IFNULL(o.an_id, 0)                       AS cpAn
                       FROM tuser u
                                LEFT JOIN user_noble n ON u.\`no\` = n.user_no
                                LEFT JOIN user_avatarframe a ON u.\`no\` = a.user_id
                           AND u.frame = a.avatarframe_id
                                LEFT JOIN tlevel l ON u.scorce > l.scorce
                           AND u.scorce < l.upscorce
                                LEFT JOIN mlevel ml ON u.scorce >= ml.score
                           AND u.scorce < ml.upscore
                                LEFT JOIN ms_level msl ON u.m_score > msl.m_score
                           AND u.m_score < msl.upm_score
                                LEFT JOIN user_tag t ON u.\`no\` = t.user_id
                           AND t.tag_id = 1
                           AND t.delsign = 0
                                LEFT JOIN user_tag tu ON u.\`no\` = tu.user_id
                           AND tu.tag_id = 0
                           AND tu.delsign = 0
                                LEFT JOIN cp_user cu ON \`no\` = cu.user_id
                           AND cu.delsign = 0
                                LEFT JOIN cp o ON cu.cp_id = o.id
                           AND o.delsign = 0
                                LEFT JOIN user_animation_state ans ON u.\`no\` = ans.user_id
                           AND role_id = 994
                       WHERE u.\`no\` IN (` + req + `)`;
            const userScoreArray = await db.query(sql, req);
            return { userScoreArray };

        } catch (error) {
            throw error;
        }
    }

    /**
     * @name: 获得20000头像框
     */
    public async getAvatarFramePeriodList() {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        try {
            let sql = ` SELECT p.id,
                               name,
                               error_num,
                               pic_mob,
                               pic_word,
                               pic_pc,
                               num,
                               period,
                               price,
                               starttime,
                               endtime,
                               delsign,
                               selled,
                               (SELECT COUNT(*) FROM transaction t WHERE date >= starttime AND date < endtime AND (productId = 'taobao.sdbean.19998rmb' OR productId = 'taobao.sdbean.29998rmb' )) AS sellNum
                        FROM
                            base20000_avatar_frame_period p
                        ORDER BY
                            endtime DESC,
                            id DESC
            `;
            const array = await db.query(sql);
            return { array };

        } catch (error) {
            throw error;
        }
    }

    public async updatePeriod(req: IavatarFramePeriod) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` UPDATE \`base20000_avatar_frame_period\`
                        SET \`name\`      = ?,
                            \`num\`       = ?,
                            \`period\`    = ?,
                            \`price\`     = ?,
                            \`starttime\` = ?,
                            \`endtime\`   = ?,
                            \`selled\`    = ?,
                            \`delsign\`   = ?
                        WHERE \`id\` = ? `;

            await conn.query(sql, [
                req.name,
                req.num,
                req.period,
                req.price,
                req.starttime,
                req.endtime,
                req.selled,
                req.delsign,
                req.id]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async updatePeriodImg(req: IavatarFramePeriod) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            if (req.pic_mob) {
                let sql = ` UPDATE \`base20000_avatar_frame_period\`
                            SET \`pic_mob\` = ?
                            WHERE \`id\` = ? `;

                await conn.query(sql, [
                    req.pic_mob,
                    req.id]);
                await conn.commit(); // 提交事务
            } else if (req.pic_pc) {
                let sql = ` UPDATE \`base20000_avatar_frame_period\`
                            SET \`pic_pc\` = ?
                            WHERE \`id\` = ? `;

                await conn.query(sql, [
                    req.pic_pc,
                    req.id]);
                await conn.commit(); // 提交事务
            } else {
                let sql = ` UPDATE \`base20000_avatar_frame_period\`
                            SET \`pic_word\` = ?
                            WHERE \`id\` = ? `;

                await conn.query(sql, [
                    req.pic_word,
                    req.id]);
                await conn.commit(); // 提交事务
            }
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async createPeriod(req: IavatarFramePeriod) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` INSERT INTO \`base20000_avatar_frame_period\` (\`name\`,
                                                                       \`num\`,
                                                                       \`period\`,
                                                                       \`price\`,
                                                                       \`starttime\`,
                                                                       \`endtime\`,
                                                                       \`delsign\`)
                        VALUES (?,
                                ?,
                                ?,
                                ?,
                                ?,
                                ?,
                                ?); `;

            const result = await conn.query(sql, [
                req.name,
                req.num,
                req.period,
                req.price,
                req.starttime,
                req.endtime,
                req.delsign]);
            req.id = result.insertId;

            // let sqlInsertItemDic = ` 
            //     INSERT INTO \`item_dic\` (\`item_cate_id\`, \`item_id\`, \`name\`, \`version\`, \`delsign\` )
            //     VALUES (?,?,?,?,?);
            // `;
            // await conn.query(sqlInsertItemDic, [2010, req.id, req.name, 0, 0]);

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }
}
