/*
 * @Description: 
 * @Company: sdbean
 * @Author: zhang<PERSON>
 * @Date: 2021-06-07 09:23:03
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-08-10 10:10:04
 */


import BaseMegaService from './BaseMegaService';

export default class BroadcastSendService extends BaseMegaService {

    public async broadcastSendByExchange(ctx, params) {
        const { app, logger } = this;
        let res = await ctx.curl(app.config.WerewolfJPExchange + 'item/send/admin/msg', {
            method: 'POST',
            headers: {
                "content-type": "application/json",
            },
            contentType: 'json',
            dataType: 'json',
            data: params,
            timeout: 3000, // 3 秒超时
        });
        return res
    }
}