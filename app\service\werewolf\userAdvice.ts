import BaseMegaService from "../../service/werewolf/BaseMegaService";

export default class UserAdviceService extends BaseMegaService {
    public async getList() {
        const {app, ctx, logger} = this;
        try {
            let sqlStr = `
                SELECT b.\`id\`,
                       b.user_id,
                       b.advice,
                       DATE_FORMAT(b.createtime, '%Y-%m-%d %H:%i:%s') create_time,
                       u.nickname
                FROM user_advice_record b
                         LEFT JOIN tuser u ON u.no = b.user_id
                ORDER BY b.\`id\` DESC
            `;
            return await this.selectList(sqlStr, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }
}