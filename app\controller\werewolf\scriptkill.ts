/*
 * @Description: 商城管理-控制类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhang<PERSON>
 * @Date: 2020-04-22 16:00:00
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-01-19 15:25:00
 */
import { Controller } from 'egg';
import BaseMegaController from './BaseMegaController';
import { IerrorMsg, HttpErr } from '../../model/common';

export default class ScriptkillController extends BaseMegaController {

    public async getMerchantList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.scriptkill.getMerchantList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getMerchantSimpleList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.scriptkill.getMerchantSimpleList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getScriptSimpleList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.scriptkill.getScriptSimpleList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getMerchantListCount() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.scriptkill.getMerchantListCount(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async confirmMerchant() {
        const { ctx, logger } = this;
        const rule = {
            id: { type: 'number' }
        }
        try {
            ctx.validate(rule);
            const request: any = ctx.request.body;
            await ctx.service.werewolf.scriptkill.confirmMerchant(request);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }

    public async refuseMerchant() {
        const { ctx, logger } = this;
        const rule = {
            id: { type: 'number' }
        }
        try {
            ctx.validate(rule);
            const request: any = ctx.request.body;
            await ctx.service.werewolf.scriptkill.refuseMerchant(request);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }

    public async updateMerchant() {
        const { ctx, logger } = this;
        const rule = {
            id: { type: 'number' }
        }
        try {
            ctx.validate(rule);
            const request: any = ctx.request.body;
            await ctx.service.werewolf.scriptkill.updateMerchant(request);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }
    
    public async getScriptList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.scriptkill.getScriptList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async confirmScript() {
        const { ctx, logger } = this;
        const rule = {
            id: { type: 'number' }
        }
        try {
            ctx.validate(rule);
            const request: any = ctx.request.body;
            await ctx.service.werewolf.scriptkill.confirmScript(request);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }

    public async refuseScript() {
        const { ctx, logger } = this;
        const rule = {
            id: { type: 'number' }
        }
        try {
            ctx.validate(rule);
            const request: any = ctx.request.body;
            await ctx.service.werewolf.scriptkill.refuseScript(request);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }

    public async getPictureList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.scriptkill.getPictureList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async updatePictureUrl() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.scriptkill.updatePictureUrl(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }

    public async updatePicture() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.scriptkill.updatePicture(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }

    public async updateScriptUrl() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.scriptkill.updateScriptUrl(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }

    public async insertPicture() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.scriptkill.insertPicture(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }

    public async getAuditFlowList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.scriptkill.getAuditFlowList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getAuditFlowListCount() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.scriptkill.getAuditFlowListCount(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getScriptEditAuditFlowList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.scriptkill.getScriptEditAuditFlowList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getScriptEditAuditFlowListCount() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.scriptkill.getScriptEditAuditFlowListCount(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getOperateUserList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.scriptkill.getOperateUserList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }
    
    public async getMerchantTempList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.scriptkill.getMerchantTempList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getMerchantTempListCount() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.scriptkill.getMerchantTempListCount(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async refuseMerchantTemp() {
        const { ctx, logger } = this;
        const rule = {
            id: { type: 'number' }
        }
        try {
            ctx.validate(rule);
            const request: any = ctx.request.body;
            await ctx.service.werewolf.scriptkill.refuseMerchantTemp(request);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }

    public async confirmMerchantTemp() {
        const { ctx, logger } = this;
        const rule = {
            id: { type: 'number' }
        }
        try {
            ctx.validate(rule);
            const request: any = ctx.request.body;
            let result = await ctx.service.werewolf.scriptkill.confirmMerchantTemp(request);
            if (result == 1) {
                this.respSucc();
            } else {
                this.respFail("数据错误 提交失败!");
            }

        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }


    public async updateScript() {
        const { ctx, logger } = this;
        const rule = {
            id: { type: 'number' }
        }
        try {
            ctx.validate(rule);
            const request: any = ctx.request.body;
            await ctx.service.werewolf.scriptkill.updateScript(request);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }

    public async getMerchantDicList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.scriptkill.getMerchantDicList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async refuseMerchantPassed() {
        const { ctx, logger } = this;
        const rule = {
            id: { type: 'number' }
        }
        try {
            ctx.validate(rule);
            const request: any = ctx.request.body;
            await ctx.service.werewolf.scriptkill.refuseMerchantPassed(request);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }

    public async getUserInfo() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.scriptkill.getUserInfo(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getUserInfoAvatar() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.scriptkill.getUserInfoAvatar(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async refuseUserAvatar() {
        const { ctx, logger } = this;
        try {
            const request: any = ctx.request.body;
            await ctx.service.werewolf.scriptkill.refuseUserAvatar(request);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }

    public async getUserInfoBg() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.scriptkill.getUserInfoBg(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async refuseUserBg() {
        const { ctx, logger } = this;
        try {
            const request: any = ctx.request.body;
            await ctx.service.werewolf.scriptkill.refuseUserBg(request);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }

    public async getReportInfo() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.scriptkill.getReportInfo(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getReportInfoCount() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.scriptkill.getReportInfoCount(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getScriptRecommendList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.scriptkill.getScriptRecommendList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getScript() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.scriptkill.getScript(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getScriptSourceList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.scriptkill.getScriptSourceList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async insertScriptRecommend() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.scriptkill.insertScriptRecommend(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async updateScriptRecommend() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.scriptkill.updateScriptRecommend(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getMomentList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.scriptkill.getMomentList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getMomentListCount() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.scriptkill.getMomentListCount(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async insertMoment() {
        const { ctx, logger } = this;
        try {
            const request: any = ctx.request.body;
            await ctx.service.werewolf.scriptkill.insertMoment(request);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }

    public async updateMoment() {
        const { ctx, logger } = this;
        try {
            const request: any = ctx.request.body;
            await ctx.service.werewolf.scriptkill.updateMoment(request);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }

    public async updateMomentDelsign() {
        const { ctx, logger } = this;
        try {
            const request: any = ctx.request.body;
            await ctx.service.werewolf.scriptkill.updateMomentDelsign(request);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }

    public async updateMomentSort() {
        const { ctx, logger } = this;
        try {
            const request: any = ctx.request.body;
            await ctx.service.werewolf.scriptkill.updateMomentSort(request);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }

    public async getMomentPicList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.scriptkill.getMomentPicList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }
    
    public async insertMomentPicture() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.scriptkill.insertMomentPicture(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }

    public async updateMomentPicture() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.scriptkill.updateMomentPicture(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }

    public async updateMomentPictureUrl() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.scriptkill.updateMomentPictureUrl(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }

    public async getMomentPictureCanShowCount() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.scriptkill.getMomentPictureCanShowCount(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getCityList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.scriptkill.getCityList();
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getMerchantBannerList() {
        const { ctx, logger } = this;
        try {
            let list = await ctx.service.werewolf.scriptkill.getMerchantBannerList();
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err);
        }
    }

    public async updateBannerUrl() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.scriptkill.updateMomentPictureUrl(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }

    public async delBanner() {
        const {ctx, logger} = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.scriptkill.delBanner(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (error) {
            this.respFail(error)
        }
    }

    public async helvesBanner() {
        const {ctx, logger} = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.scriptkill.helvesBanner(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (error) {
            this.respFail(error)
        }
    }

    public async updateBannerInfo() {
        const {ctx, logger} = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.scriptkill.updateBannerInfo(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (error) {
            this.respFail(error)
        }
    }

    public async updateMainPageBannerUrl() {
        const {ctx, logger} = this;
        try {
            const requestBosy = ctx.request.body;
            await ctx.service.werewolf.scriptkill.updateMainPageBannerUrl(requestBosy);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            this.respFail(error)
        }
    }

    public async insertMainPageBanner() {
        const {ctx, logger} = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.scriptkill.insertMainPageBanner(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            this.respFail(error)
        }
    }

    public async getOpenAdList() {
        const {ctx, logger} = this;
        try {
            let list = await ctx.service.werewolf.scriptkill.getOpenAdList();
            this.respSuccData(list);
        } catch (error) {
            this.respFail(error)
        }
    }

    public async shelvesOpenAd() {
        const {ctx, logger} = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.scriptkill.shelvesOpenAd(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            this.respFail(error)
        }
    }

    public async delOpenAd() {
        const {ctx, logger} = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.scriptkill.delOpenAd(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (error) {
            this.respFail(error)
        }
    }

    public async insertOpenAd() {
        const {ctx, logger} = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.scriptkill.insertOpenAd(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (error) {
            this.respFail(error)
        }
    }

    public async updateOpenAdImgUrl() {
        const {ctx, logger} = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.scriptkill.updateOpenAdImgUrl(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (error) {
            this.respFail(error)
        }
    }

    public async updateOpenAdInfo() {
        const {ctx, logger} = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.scriptkill.updateOpenAdInfo(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (error) {
            this.respFail(error)
        }
    }

}
