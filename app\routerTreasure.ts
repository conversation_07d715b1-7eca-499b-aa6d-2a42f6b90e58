/*
 * @Description: 资产管理路由
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-03-24 11:50:11
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-01-15 11:55:02
 */
import { Router, Application } from 'egg'
import { AccessRouteId } from './model/accessRouteCof'
import { once } from 'process'
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {
    const { controller, router } = app
    //【玩家资产】全部资产
    router.post(`${API_VERSION}/werewolf/props`, accCtr(AccessRouteId.wolf_treasure_all), controller.werewolf.props.all)
    router.post(`${API_VERSION}/werewolf/diamond/analysis/decrease`, accCtr(AccessRouteId.wolf_treasure_all), controller.werewolf.diamond.decrease)
    router.post(`${API_VERSION}/werewolf/diamond/analysis/increase`, accCtr(AccessRouteId.wolf_treasure_all), controller.werewolf.diamond.increase)
    router.post(`${API_VERSION}/werewolf/diamond/analysis/20000`, accCtr(AccessRouteId.wolf_treasure_all), controller.werewolf.diamond.twoWTransace)
    //【玩家资产】详细资产 accCtr(AccessRouteId.wolf_treasure_detail),
    router.post(
        `${API_VERSION}/werewolf/treasure/treasureList`,
        accCtr(AccessRouteId.wolf_treasure_detail),
        controller.werewolf.treasure.treasureList,
    )
    router.post(
        `${API_VERSION}/werewolf/treasure/userFrameSearch`,
        accCtr(AccessRouteId.wolf_treasure_detail),
        controller.werewolf.treasure.userFrameSearch,
    )

    router.post(`${API_VERSION}/werewolf/treasure/udidDetails`, accCtr(AccessRouteId.wolf_treasure_detail), controller.werewolf.treasure.udidDetails)
}

export default load
