import { Controller } from 'egg'
import BaseMegaController from './BaseMegaController'
import { IerrorMsg, HttpErr } from '../../model/common'

export default class MiningController extends BaseMegaController {
    public async getMiningList() {
        const { ctx, logger } = this
        try {
            const list = await ctx.service.werewolf.mining.getMiningList()
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    /*添加框石*/
    public async addMiningFrame() {
        const { ctx, logger } = this
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.mining.addMiningFrame(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    /*编辑框石*/
    public async editMiningFrame(req: any) {
        const { ctx, logger } = this
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.mining.editMiningFrame(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    /*获取奖池列表*/
    public async getAwardPoolList() {
        const { ctx, logger } = this
        try {
            const list = await ctx.service.werewolf.mining.getAwardPoolList()
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    /*添加奖池*/
    public async addAwardPool() {
        const { ctx, logger } = this
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.mining.addAwardPool(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    /*编辑奖池*/
    public async editAwardPool() {
        const { ctx, logger } = this
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.mining.editAwardPool(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    /*获取奖品列表*/
    public async getAwardList() {
        const { ctx, logger } = this
        try {
            const list = await ctx.service.werewolf.mining.getAwardList()
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    /*添加奖品*/
    public async addAward(req: any) {
        const { ctx, logger } = this
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.mining.addAward(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }
    public async editAward(req: any) {
        const { ctx, logger } = this
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.mining.editAward(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }
    public async updateAward(req: any) {
        const { ctx, logger } = this
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.mining.updateAward(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }
    /*上下架奖品*/
    public async updateAwardDelsign(req: any) {
        const { ctx, logger } = this
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.mining.updateAwardDelsign(requestBody)
            this.respSuccData(list)
            // ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            // ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error)
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' }
            ctx.body = err
            ctx.status = HttpErr.BadRequest
        }
    }

    /*获取奖池内奖品*/
    public async getAwardInPool() {
        const { ctx, logger } = this
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.mining.getAwardInPool(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    /*为奖池添加奖品*/
    public async addAwardInPool() {
        const { ctx, logger } = this
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.mining.addAwardInPool(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    /*获取奖池列表*/
    public async getAwardPool() {
        const { ctx, logger } = this
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.mining.getAwardPool(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    /*编辑奖池中的奖品*/
    public async editAwardInPool() {
        const { ctx, logger } = this
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.mining.editAwardInPool(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }
    // /*编辑框石*/
    // public async editMiningAwardPool() {
    //
    // }

    /*获取框石的奖池列表*/
    public async getMiningAwardPool() {
        const { ctx, logger } = this
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.mining.getMiningAwardPool(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    /*为框石添加奖池*/
    public async addAwardPoolToMining() {
        const { ctx, logger } = this
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.mining.addAwardPoolToMining(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    /*编辑框石的奖池*/
    public async editAwardPoolToMining() {
        const { ctx, logger } = this
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.mining.editAwardPoolToMining(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    /*获取框石掉率列表*/
    public async selectMiningDropList() {
        const { ctx, logger } = this
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.mining.selectMiningDropList();
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    /*添加颜色区间框石掉率*/
    public async addMiningColorTotalSection() {
        const { ctx, logger } = this
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.mining.addMiningColorTotalSection(requestBody)
            this.respSuccData(list);
        } catch (err) {
            this.respFail(err)
        }
    }

    /*删除颜色区间块*/
    public async deleteMiningColorSection() {
        const { ctx, logger } = this
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.mining.deleteMiningColorSection(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    /*编辑颜色区间内容*/
    public async editMiningColorSection() {
        const { ctx, logger } = this
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.mining.editMiningColorSection(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    /*编辑框石权重掉率*/
    public async editMiningDropItem() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.mining.editMiningDropItem(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    /*删除框石权重掉率*/
    public async deleteMiningDropItem() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.mining.deleteMiningDropItem(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async weekAwardList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.mining.weekAwardList(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async addWeekAward() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.mining.addWeekAward(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async updateWeekAward() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.mining.updateWeekAward(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async frameCrystalList(){
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body
            const resp = await ctx.service.werewolf.mining.frameCrystalList()
            this.respSuccData(resp)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async addFrameCrystal(){
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body
            const resp = await ctx.service.werewolf.mining.addFrameCrystal(requestBody)
            this.respSuccData(resp)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async updateFrameCrystal(){
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body
            const resp = await ctx.service.werewolf.mining.updateFrameCrystal(requestBody)
            this.respSuccData(resp)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async roleSparList(){
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body
            const resp = await ctx.service.werewolf.mining.roleSparList()
            this.respSuccData(resp)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async addRoleSpar(){
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body
            const resp = await ctx.service.werewolf.mining.addRoleSpar(requestBody)
            this.respSuccData(resp)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async updateRoleSpar(){
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body
            const resp = await ctx.service.werewolf.mining.updateRoleSpar(requestBody)
            this.respSuccData(resp)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async seasonList(){
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body
            const resp = await ctx.service.werewolf.mining.seasonList(requestBody)
            this.respSuccData(resp)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async insertSeason() {
        const {ctx, logger} = this;
        try {
            const requestBody = ctx.request.body
            const resp = await ctx.service.werewolf.mining.insertSeason(requestBody)
            this.respSuccData(resp)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async updateSeason() {
        const {ctx, logger} = this;
        try {
            const requestBody = ctx.request.body
            const resp = await ctx.service.werewolf.mining.updateSeason(requestBody)
            this.respSuccData(resp)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async illustratedList(){
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body
            const resp = await ctx.service.werewolf.mining.illustratedList(requestBody)
            this.respSuccData(resp)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async updateIllustration(){
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body
            const resp = await ctx.service.werewolf.mining.updateIllustration(requestBody)
            this.respSuccData(resp)
        } catch (err) {
            this.respFail(err)
        }
    }



    
}
