/*
 * @Description: 狼王集结
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-05-12 17:07:03
 * @LastEditors: hammercui
 * @LastEditTime: 2020-06-03 10:20:45
 */

//集结狼王活动进行中表
export interface IwfKingActivitingItem {
    quarter_id: number;
    aty_state: number;
}

//基础信息item
export interface IwfKingBaseItem {
    quarter_id: number;
    channel_id: number;
    vote_enable: number;
    graded_state: number;
    start_time?: string;
    end_time?: string;
    anchor_day?: string;
    popular_rank_state: number;
    crawler_state: number;
}

//主播排行榜请求类
export interface IwfKingAnchorRankReq{
    quarter_id: number;
}

//主播积分榜
export interface IwfKingScoreItem {
    anchor_id: number;
    user_id: number;
    anchor_name: string;
    avatar: string;
    room: number;
    room_url: string;
    is_wolfking: number;
    score: number;
    expectation: string;
}

//主播人气榜
export interface IwfKingPopularItem {
    anchor_id: number;
    user_id: number;
    anchor_name: string;
    avatar: string;
    room: number;
    room_url: string;
    popular: number;
}

//投票开关请求
export interface IwfKingVoteSwitchReq {
    quarter_id: number;
    switch: number;
}

//狼王开关
export interface IwfKingKingSwitchReq {
    quarter_id: number;
    anchor_id: number;
    switch: number;
}

//主播积分加1
export interface IwfKingScoreAddReq {
    quarter_id: number;
    anchor_id: number;
}
//主播积分减1
export interface IwfKingScoreSubReq {
    quarter_id: number;
    anchor_id: number;
}

//平分代币请求
export interface IwfKingGradeCoinReq{
    quarter_id: number;
}

export interface IcallGoGradeReq{
    total_coin: number;
    total_ticket: number;
    candidate_type: number;
    quarter_id: number;
    wolf_anchor_id: number;
}

export interface IcallGoResp{
    code: number;
    msg: string;
}

export interface IoldPopularItem{
    anchor_id: number;
    quarter_id: number;
    room_url: string;
    rank: number;
    popular: number;
}

export interface InewPopularItem{
    anchor_id: number;
    popular: number;
    rank: number;
}