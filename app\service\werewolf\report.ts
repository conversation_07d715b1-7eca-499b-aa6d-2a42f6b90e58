import BaseMegaService from './BaseMegaService';
import { IreportResponse, Ireport } from '../../model/werewolf';
import moment = require('moment');
/*
 * @Description: 查询被举报服务类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-03-26 13:19:44
 * @LastEditors: jiawen.wang
 * @LastEditTime: 2020-10-21 14:43:24
 */

export default class ReportClass extends BaseMegaService {

    /**
     * @name: 查询被系统扫描出的被举报视频
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async systemReport(playerId) {
        const { app, logger } = this;
        let sqlStr = `
        SELECT tin.user_no,
tin.createtime,
tte.content,
trvo.result,
trvo.keywords,
trvo.gameno,
trvo.url
FROM tuser_imprison tin 
LEFT JOIN treport_type as tte ON tte.id = tin.type
LEFT JOIN treportvideo_chn as trvo ON trvo.id = tin.videoreport_id AND trvo.userno = tin.user_no
WHERE
tin.user_no =  ${playerId}
AND tin.createtime > '2020-03-26 00:00:00'
AND tin.videoreport_id != 0 
AND trvo.url is NOT NULL
ORDER BY tin.time DESC;
        `;
        try {
            const result = await this.selectList(sqlStr)
            let retArray: Ireport[] = new Array();
            if (!result) {
                return [];
            }
            for (let i = 0, len = result.length; i < len; i++) {
                retArray.push({
                    datetime: moment(result[i].createtime).format("YYYY-MM-DD HH:mm:ss"),
                    type: result[i].content,
                    complete: 0,
                    gameNo: result[i].gameno,
                    reportUser: "系统扫描",
                    content: result[i].result,
                    version: 2,
                    videoUrl: result[i].url,
                    keyWords: result[i].keywords,
                });
            }
            return retArray;
        } catch (err) {
            logger.error("查询系统举报的视频", err);
            return [];
        }
    }
}
