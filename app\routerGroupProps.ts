/*
 * @Description: 商城管理路由
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: z<PERSON><PERSON>
 * @Date: 2020-04-22 11:50:11
 * @LastEditors: hammercui
 * @LastEditTime: 2020-05-18 17:30:32
 */
import { Router, Application } from "egg";
import { AccessRouteId, AccessRouteName } from "./model/accessRouteCof";
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

    const { controller, router } = app;
    //【商城管理】获取公会徽章
    router.post(`${API_VERSION}/werewolf/groupProps/getGroupBadgeList`, accCtr(AccessRouteId.wolf_group_props), controller.werewolf.groupProps.getGroupBadgeList)
    //【商城管理】获取公会徽章框
    router.post(`${API_VERSION}/werewolf/groupProps/getGroupFrameList`, accCtr(AccessRouteId.wolf_group_props), controller.werewolf.groupProps.getGroupFrameList)
    //【商城管理】获取公会徽章框
    router.post(`${API_VERSION}/werewolf/groupProps/getGroupBannerList`, accCtr(AccessRouteId.wolf_group_props), controller.werewolf.groupProps.getGroupBannerList)
    //【商城管理】获取公会等级
    router.post(`${API_VERSION}/werewolf/groupProps/getGroupLevelList`, accCtr(AccessRouteId.wolf_group_props), controller.werewolf.groupProps.getGroupLevelList)
    
    router.post(`${API_VERSION}/werewolf/groupProps/updateGroupBadge`, accCtr(AccessRouteId.wolf_group_props), controller.werewolf.groupProps.updateGroupBadge)

    router.post(`${API_VERSION}/werewolf/groupProps/updateGroupFrame`, accCtr(AccessRouteId.wolf_group_props), controller.werewolf.groupProps.updateGroupFrame)

    router.post(`${API_VERSION}/werewolf/groupProps/updateGroupBanner`, accCtr(AccessRouteId.wolf_group_props), controller.werewolf.groupProps.updateGroupBanner)

    router.post(`${API_VERSION}/werewolf/groupProps/insertGroupBadge`, accCtr(AccessRouteId.wolf_group_props), controller.werewolf.groupProps.insertGroupBadge)

    router.post(`${API_VERSION}/werewolf/groupProps/insertGroupFrame`, accCtr(AccessRouteId.wolf_group_props), controller.werewolf.groupProps.insertGroupFrame)

    router.post(`${API_VERSION}/werewolf/groupProps/insertGroupBanner`, accCtr(AccessRouteId.wolf_group_props), controller.werewolf.groupProps.insertGroupBanner)

}

export default load
