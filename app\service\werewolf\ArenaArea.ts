/**
 * <AUTHOR>
 */

import { Service } from 'egg';
import { IrecordList } from '../../model/randomRecordCof';
import * as moment from 'moment';
import * as Console from "console";
import {IrongCloudResponse} from "../../model/werewolf";
import {AreneAreaData} from "../../model/ArenaArea";

export default class ArenaAreaService extends Service {
    public async getArenaAreaList(req: any) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        Console.log("getArenaAreaList="+req.current)
        Console.log("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$="+req.pageCount)
        try {
            //查询竞技场
            let sql =`SELECT
                a.user_id,
                IFNULL(b.win, 0) AS win,
                IFNULL(b.lose, 0) AS lose,
                IFNULL(b.score, 0) AS score,
                a.createtime,
                a.content
            FROM
            activity_2021_user_arena_suggestion a
            LEFT JOIN user_arena_score b ON a.user_id = b.user_id
            WHERE 1 ORDER BY a.id DESC limit ${(req.current-1)*req.pageCount},${req.pageCount}` ;

            let count =`SELECT COUNT(*) as total FROM
            activity_2021_user_arena_suggestion a
            LEFT JOIN user_arena_score b ON a.user_id = b.user_id
            WHERE 1 ORDER BY a.id DESC`;

            let result = await db.query(count);

            const responseBody: AreneAreaData = {
                    dataArray: await db.query(sql),
                    count:result[0].total
             };
           // responseBody.dataArray =  await db.query(sql);
          //  responseBody.count = result[0].total;
          //  console.info("------------responseBody.count="+result[0].total);

          //  const ArenaAreaList = await db.query(sql);

            return responseBody;
        } catch (error) {
            throw error;
        }
    }
}
