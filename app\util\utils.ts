/*
 * @Description: 工具类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2018-12-28 11:11:11
 * @LastEditors: jiawen.wang
 * @LastEditTime: 2021-02-03 17:13:30
 */

/**
 * @name:包装头像url，因为有时候为number 
 * @msg: 
 * @param {type} 
 * @return: 
 */
export function wrapHeadIcon(url: string): string {
	// console.log();
	if (isNaN(Number(url))) {
		return url;
	} else {
		// return `http://img.53site.com/Werewolf/defaultAvatar/reg_default_avatar_2.6_${url}.png`;
		return `http://img.53site.com/Werewolf/defaultAvatar/${url}.png`;

	}
}

/**
 * @name: 非对称加密-解密
 * @msg: 
 * @param {type} 
 * @return: 
 */
// export function decryptedStr(source:string):string {
//     const buffer2 = Buffer.from(source, 'base64');
//     crypto
// 	const decrypted = crypto.privateDecrypt({key:privateKey,padding:RSA_PKCS1_OAEP_PADDING}, buffer2);
//     const decryptedStr = decrypted.toString('utf8');
//     return decryptedStr;
// }

/**
 * @name: 判断路由是否可进入
 * @msg:
 * @param {type}
 * @return:
 */
export function IsAccessEnable(authority: number, currentAuthority: number[]): boolean {
	if (!authority) return true;

	for (let index = 0, len = currentAuthority.length; index < len; index++) {
		const element = currentAuthority[index];
		if (element == authority) return true;
	}
	return false;
}

export function isAvatarFrameCateId(cateId: number) {
	return cateId === 2020 || cateId === 2010
}


//Knuth-Durstenfeld ShuffleFisher-Yates
export function shuffle(arr: any[]) {
	let length = arr.length;
	let tmp: any;
	let random: number;
	while(length != 0){
		random = Math.floor(Math.random() * length)
		length-- // n减一，方便下一趟循环继续交换
		// 交换
		tmp = arr[length]
		arr[length] = arr[random]
		arr[random] = tmp
	}
	return arr
}

