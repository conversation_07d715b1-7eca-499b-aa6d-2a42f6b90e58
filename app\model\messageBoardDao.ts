
// export interface IMessageBoardRepliesItem {

//     _id: string;
//     text: string;
//     sender: string;
//     receiver: string;
//     messageId: string;
//     createDate: string;
//     animationId: string;
//     animationType: string;
//     delsign: string;
// }

export interface IMessageBoardItem {
    
    _id: string;
    text: string;
    sender: string;
    owner: string;
    senderName: string;
    ownerName: string;
    createDate: string;
    updateDate: string;
    animationId: string;
    animationType: string;
    count: string;
    delsign: string;
    // replies: IMessageBoardRepliesItem[];

    messageId: string;
    receiver: string;
    receiverName: string;

}
