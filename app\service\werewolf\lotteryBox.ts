/*
 * @Description: 商城道具服务类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: z<PERSON><PERSON>
 * @Date: 2020-04-24 11:35:39
 * @LastEditors: zhanglu
 * @LastEditTime: 2022-09-13 14:25:36
 */
import BaseMegaService from './BaseMegaService';
import { MallItemBaseType, MallItemBaseInfo, CoinOperation, UploadTagInfoReq } from './../../model/mallItemManager';
export default class LotteryBoxService extends BaseMegaService {

    public async getActivityList() {
        const { app, ctx, logger } = this;
        try {
            let sql = ` SELECT activity.*, award.name AS awardName FROM lottery_activity activity
            INNER JOIN lottery_award award ON activity.box_award_id = award.id
            WHERE activity.id > 15
            ORDER BY end_time DESC , id DESC`;
            return await this.selectList(sql, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getAwardList() {
        const { app, ctx, logger } = this;
        try {
            let sql = ` SELECT * FROM lottery_award WHERE id > 0 ORDER BY id desc`;
            return await this.selectList(sql, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateActivity(req: any) {
        const { app, logger } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
            UPDATE lottery_activity 
            SET name = ?,
            box_award_id = ?,
            img = ?,
            is_up = ?,
            start_time = ?,
            end_time = ?,
            delsign = ?
            WHERE
                id = ?
                        `;
            await conn.query(sql, [
                req.name,
                req.box_award_id,
                req.img,
                req.is_up,
                req.start_time,
                req.end_time,
                req.delsign,
                req.id]);

            await conn.commit(); // 提交事务

        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！

            logger.error(error);

            throw error;
        }
    }

    public async insertActivity(req: any) {
        const { app, logger } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
            INSERT INTO werewolf.lottery_activity (
                name,
                type,
                box_award_id,
                is_up,
                start_time,
                end_time,
                delsign 
            )
            VALUES
                (?,?,?,?,?,?,?)
                        `;
            let result = await conn.query(sql, [
                req.name,
                req.type,
                req.box_award_id,
                req.is_up,
                req.start_time,
                req.end_time,
                req.delsign]);

            let activityId = Number(result.insertId);
            if (req.type == 1) {
                let sqlInsert1 = `INSERT INTO lottery_activity_coin(activity_id, coin_id, coin_num, num, delsign) VALUES (${activityId}, 1, '98', 1, 0)`;
                let sqlInsert2 = `INSERT INTO lottery_activity_coin(activity_id, coin_id, coin_num, num, delsign) VALUES (${activityId}, 1, '698', 8, 0)`;
                let sqlInsert3 = `INSERT INTO lottery_activity_coin(activity_id, coin_id, coin_num, num, delsign) VALUES (${activityId}, 10, '1', 1, 0)`;
                let sqlInsert4 = `INSERT INTO lottery_activity_coin(activity_id, coin_id, coin_num, num, delsign) VALUES (${activityId}, 10, '8', 8, 0)`;
                await conn.query(sqlInsert1, []);
                await conn.query(sqlInsert2, []);
                await conn.query(sqlInsert3, []);
                await conn.query(sqlInsert4, []);

                let sqlInsert5 = `INSERT INTO lottery_open_rule_activity(activity_id, open_rule_id, delsign) VALUES (${activityId}, 1, 0) `;
                let sqlInsert6 = `INSERT INTO lottery_open_rule_activity(activity_id, open_rule_id, delsign) VALUES (${activityId}, 3, 0) `;

                await conn.query(sqlInsert5, []);
                await conn.query(sqlInsert6, []);
            } else if (req.type == 2) {
                let sqlInsert1 = `INSERT INTO lottery_activity_coin(activity_id, coin_id, coin_num, num, delsign) VALUES (${activityId}, 1, '198', 1, 0)`;
                let sqlInsert2 = `INSERT INTO lottery_activity_coin(activity_id, coin_id, coin_num, num, delsign) VALUES (${activityId}, 1, '1498', 8, 0)`;
                let sqlInsert3 = `INSERT INTO lottery_activity_coin(activity_id, coin_id, coin_num, num, delsign) VALUES (${activityId}, 11, '1', 1, 0)`;
                let sqlInsert4 = `INSERT INTO lottery_activity_coin(activity_id, coin_id, coin_num, num, delsign) VALUES (${activityId}, 11, '8', 8, 0)`;
                await conn.query(sqlInsert1, []);
                await conn.query(sqlInsert2, []);
                await conn.query(sqlInsert3, []);
                await conn.query(sqlInsert4, []);
            } else {
                let sqlInsert1 = `INSERT INTO lottery_activity_coin(activity_id, coin_id, coin_num, num, delsign) VALUES (${activityId}, 1, '98', 1, 0)`;
                let sqlInsert2 = `INSERT INTO lottery_activity_coin(activity_id, coin_id, coin_num, num, delsign) VALUES (${activityId}, 1, '698', 8, 0)`;
                let sqlInsert3 = `INSERT INTO lottery_activity_coin(activity_id, coin_id, coin_num, num, delsign) VALUES (${activityId}, 10, '1', 1, 0)`;
                let sqlInsert4 = `INSERT INTO lottery_activity_coin(activity_id, coin_id, coin_num, num, delsign) VALUES (${activityId}, 10, '8', 8, 0)`;
                await conn.query(sqlInsert1, []);
                await conn.query(sqlInsert2, []);
                await conn.query(sqlInsert3, []);
                await conn.query(sqlInsert4, []);

                let sqlInsert5 = `INSERT INTO lottery_open_rule_activity(activity_id, open_rule_id, delsign) VALUES (${activityId}, 2, 0) `;
                let sqlInsert6 = `INSERT INTO lottery_open_rule_activity(activity_id, open_rule_id, delsign) VALUES (${activityId}, 3, 0) `;

                await conn.query(sqlInsert5, []);
                await conn.query(sqlInsert6, []);
            }

            let sqlInsertUpdate = ` INSERT INTO lottery_activity_update_record(activity_id, activity_type, create_time, delsign) VALUES ( ${activityId}, ${req.type}, NOW(), 0) `;
            await conn.query(sqlInsertUpdate, []);

            await conn.commit(); // 提交事务

        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！

            logger.error(error);

            throw error;
        }
    }

    public async insertLotteryBoxAward(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
                INSERT INTO lottery_award (\`name\`, remark)
                VALUES
                    ('${req.name}', '${req.remark}');
            `;
            let data = await this.execSql(sqlStr, [])

            return data;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateLotteryBoxAward(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
            UPDATE lottery_award 
            SET \`name\` = '${req.name}',
            remark = '${req.remark}' 
            WHERE
                id = ${req.id}
            `;
            let data = await this.execSql(sqlStr, [])

            return data;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getBoxList(req) {
        const { app, ctx, logger } = this;
        try {
            let sql = ` SELECT 
            i.item_id,
            b.*,
            date_format(b.activity_start_time, '%Y/%m/%d %H:%i:%s') activity_start_time,
            date_format(b.activity_end_time, '%Y/%m/%d %H:%i:%s')   activity_end_time,
            IFNULL(ic.id, 10000) AS cate_id,
            ni.img_name AS normalImg,
            c.img_name AS coinImg,
            m.prive_remark AS maskshowImg,
            dc.name AS discountCoinName,
            dc.img_name AS discountCoinImg,
            cou.pic AS couponPic,
            wc.pic AS clothPic
            FROM
                lottery_box b
            LEFT JOIN item_dic i ON i.id = b.item_dic_id
            LEFT JOIN item_cate ic ON ic.id = i.item_cate_id
            LEFT JOIN maskshow m ON m.no = i.item_id AND i.item_cate_id = 1040
            LEFT JOIN wedding_clothing wc ON wc.id = i.item_id AND i.item_cate_id = 11000
            LEFT JOIN coupon cou ON cou.id = i.item_id AND i.item_cate_id = 12000
            LEFT JOIN normal_item ni ON ni.no = i.item_id
            LEFT JOIN coin c ON c.id = i.item_id
            LEFT JOIN coin dc ON dc.id = b.discount_coin_id
            WHERE b.box_award_id = ${req.box_award_id}
            ORDER BY level ASC, weight ASC, id ASC, cumulative_num ASC
                  `;
            return await this.selectList(sql, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async insertLotteryBox(req) {
        const { app, logger } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            if (req.cateId == 11000) {
                let sqlSex = ` SELECT sex FROM 
                    wedding_clothing wc 
                    INNER JOIN item_dic id ON id.item_id = wc.id AND id.item_cate_id = 11000
                    WHERE id.id = ?
                `;
                let resultSex = await conn.query(sqlSex, [req.item_dic_id]);
                for (const item of resultSex) {
                    req.sex = item.sex;
                }
            } else if (req.cateId == 12000) {
                let sqlSex = ` SELECT c.sex FROM 
                    coupon c
                    INNER JOIN item_dic id ON id.item_id = c.id AND id.item_cate_id = 12000
                    WHERE id.id = ?
                `;
                let resultSex = await conn.query(sqlSex, [req.item_dic_id]);
                for (const item of resultSex) {
                    req.sex = item.sex;
                }
            }

            if (req.discount_coin_id) {
                let sqlCoin = ` SELECT id.id FROM 
                    coin c
                    INNER JOIN item_dic id ON id.item_id = c.id AND id.item_cate_id = 1
                    WHERE c.id = ?
                `;
                let resultSex = await conn.query(sqlCoin, [req.discount_coin_id]);
                for (const item of resultSex) {
                    req.discount_coin_item_dic_id = item.id;
                }
            }

            let check_num = 0
            if(req.num_surplus && req.num_surplus > 0) {
                check_num = 1
            }

            let sqlStr = ` 
            INSERT INTO lottery_box (
                name,
                item_dic_id,
                sex,
                box_award_id,
                level,
                show_level,
                discount_coin_id,
                discount_coin_item_dic_id,
                discount_coin_num,
                weight,
                gold_weight,
                weight_percent,
                is_up,
                cumulative_num,
                num,
                num_surplus,
                remark,
                delsign,
                sort,
                activity_start_time,
                activity_end_time,
                check_num,
                is_hide
            )
            VALUES
                (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
            `;
            let data = await this.execSql(sqlStr, [
                req.name,
                req.item_dic_id,
                req.sex,
                req.box_award_id,
                req.level,
                req.show_level,
                req.discount_coin_id,
                req.discount_coin_item_dic_id,
                req.discount_coin_num,
                req.weight,
                req.gold_weight,
                req.weight_percent,
                req.is_up,
                req.cumulative_num,
                req.num,
                req.num_surplus,
                req.remark,
                0,
                req.sort,
                req.activity_start_time,
                req.activity_end_time,
                check_num,
                req.is_hide
            ])

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            logger.error(error);
            throw error
        }
    }

    public async updateLotteryBox(req) {
        const { app, logger } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {

            if (req.cateId == 11000) {
                let sqlSex = ` SELECT sex FROM 
                    wedding_clothing wc 
                    INNER JOIN item_dic id ON id.item_id = wc.id AND id.item_cate_id = 11000
                    WHERE id.id = ?
                `;
                let resultSex = await conn.query(sqlSex, [req.item_dic_id]);
                for (const item of resultSex) {
                    req.sex = item.sex;
                }
            } else if (req.cateId == 12000) {
                let sqlSex = ` SELECT c.sex FROM 
                    coupon c
                    INNER JOIN item_dic id ON id.item_id = c.id AND id.item_cate_id = 12000
                    WHERE id.id = ?
                `;
                let resultSex = await conn.query(sqlSex, [req.item_dic_id]);
                for (const item of resultSex) {
                    req.sex = item.sex;
                }
            }

            if (req.discount_coin_id) {
                let sqlCoin = ` SELECT id.id FROM 
                    coin c
                    INNER JOIN item_dic id ON id.item_id = c.id AND id.item_cate_id = 1
                    WHERE c.id = ?
                `;
                let resultSex = await conn.query(sqlCoin, [req.discount_coin_id]);
                for (const item of resultSex) {
                    req.discount_coin_item_dic_id = item.id;
                }
            }

            let check_num = 0
            if(req.num_surplus && req.num_surplus > 0) {
                check_num = 1
            }

            let sqlStr = ` 
                UPDATE lottery_box 
                SET name = ?,
                item_dic_id = ?,
                sex = ?,
                level = ?,
                show_level = ?,
                discount_coin_id = ?,
                discount_coin_item_dic_id = ?,
                discount_coin_num = ?,
                weight = ?,
                gold_weight = ?,
                weight_percent = ?,
                is_up = ?,
                cumulative_num = ?,
                num = ?,
                num_surplus = ?,
                remark = ?,
                sort = ?,
                check_num = ?,
                activity_start_time = ?,
                activity_end_time = ?,
                is_hide = ?
                WHERE
                    id = ? AND box_award_id = ?
            `;
            await conn.query(sqlStr, [
                req.name,
                req.item_dic_id,
                req.sex,
                req.level,
                req.show_level,
                req.discount_coin_id,
                req.discount_coin_item_dic_id,
                req.discount_coin_num,
                req.weight,
                req.gold_weight,
                req.weight_percent,
                req.is_up,
                req.cumulative_num,
                req.num,
                req.num_surplus,
                req.remark,
                req.sort,
                check_num,
                req.activity_start_time,
                req.activity_end_time,
                req.is_hide,
                req.id,
                req.box_award_id
            ])

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            logger.error(error);
            throw error
        }
    }

    public async updateLotteryBoxDelsign(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
                UPDATE lottery_box 
                SET
                delsign = ?
                WHERE
                    id = ?
            `;
            let data = await this.execSql(sqlStr, [
                req.delsign,
                req.id
            ])

            return data;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateLotteryBoxImg(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
                UPDATE lottery_box 
                SET
                img = ?
                WHERE
                    id = ?
            `;
            let data = await this.execSql(sqlStr, [
                req.img,
                req.id
            ])

            return data;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getCoinList() {
        const { app, ctx, logger } = this;
        try {
            let sql = `
            SELECT * FROM coin 
            `;
            return await this.selectList(sql, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getRuleList() {
        const { app, ctx, logger } = this;
        try {
            let sql = `
                SELECT *, id AS ruleId FROM lottery_open_rule ORDER BY id ASC
            `;
            return await this.selectList(sql, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateLotteryBoxRule(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
            UPDATE lottery_open_rule 
            SET name = ?,
            type = ?,
            num = ?,
            start_time = ?,
            end_time = ?,
            delsign = 0 
            WHERE
                id = ?;
            `;
            let data = await this.execSql(sqlStr, [
                req.name,
                req.type,
                req.num,
                req.start_time,
                req.end_time,
                req.id
            ])

            return data;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async insertLotteryBoxRule(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
            INSERT INTO lottery_open_rule (
                name,
                type,
                num,
                start_time,
                end_time,
                delsign 
            )
            VALUES
                (?,?,?,?,?,0);
            `;
            let data = await this.execSql(sqlStr, [
                req.name,
                req.type,
                req.num,
                req.start_time,
                req.end_time
            ])

            return data;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getActivityRuleList() {
        const { app, ctx, logger } = this;
        try {
            let sql = `
            SELECT a.id, a.activity_id, a.open_rule_id, a.level, r.name, r.num, r.start_time, r.end_time, r.type, ac.name AS activityName, a.delsign
            FROM lottery_open_rule r
            INNER JOIN lottery_open_rule_activity a ON r.id = a.open_rule_id 
            INNER JOIN lottery_activity ac ON a.activity_id = ac.id
            ORDER BY a.activity_id DESC, a.id DESC
            `;
            return await this.selectList(sql, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async insertActivityRule(req) {
        const { app, ctx, logger } = this;
        try {
            let level = null;
            if (req.level) {
                level = req.level;
            }
            let sqlStr = ` 
                INSERT INTO lottery_open_rule_activity ( activity_id, open_rule_id, level, delsign )
                VALUES
                    ( ?, ?, ?, 1 )
            `;
            let data = await this.execSql(sqlStr, [
                req.activity_id,
                req.open_rule_id,
                level
            ])
            return data;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateActivityRule(req) {
        const { app, ctx, logger } = this;
        try {
            let level = null;
            if (req.level) {
                level = req.level;
            }

            let sqlStr = ` 
                UPDATE lottery_open_rule_activity 
                SET activity_id = ?,
                open_rule_id = ?,
                level = ?
                WHERE
                    id = ?;
            `;
            let data = await this.execSql(sqlStr, [
                req.activity_id,
                req.open_rule_id,
                level,
                req.id
            ])
            return data;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getBoxRuleList(req) {
        const { app, ctx, logger } = this;
        try {
            let sql = `
                SELECT b.id, b.box_id, b.open_rule_id, r.name, r.num, r.type, box.name AS boxName, b.delsign
                FROM lottery_open_rule r
                INNER JOIN lottery_open_rule_box b ON r.id = b.open_rule_id 
                INNER JOIN lottery_box box ON box.id = b.box_id
                WHERE box.box_award_id = ?
                ORDER BY b.box_id DESC
            `;
            return await this.selectList(sql, [req.box_award_id]);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async insertBoxRule(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
            INSERT INTO lottery_open_rule_box(box_id, open_rule_id, delsign) VALUES 
                    ( ?, ?, 1)
            `;
            let data = await this.execSql(sqlStr, [
                req.box_id,
                req.open_rule_id
            ])
            return data;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateBoxRule(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
                UPDATE lottery_open_rule_box 
                SET box_id = ?,
                open_rule_id = ?
                WHERE
                    id = ?;
            `;
            let data = await this.execSql(sqlStr, [
                req.box_id,
                req.open_rule_id,
                req.id
            ])
            return data;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateBoxRuleDelsign(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
                UPDATE lottery_open_rule_box 
                SET delsign = ?
                WHERE
                    id = ?;
            `;
            let data = await this.execSql(sqlStr, [
                req.delsign,
                req.id
            ])
            return data;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getBoxActivityList(req) {
        const { app, ctx, logger } = this;
        try {
            let sql = `
            SELECT * FROM lottery_activity ORDER BY id DESC
            `;
            return await this.selectList(sql, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }
    
    public async getBoxDiscountRuleList(req) {
        const { app, ctx, logger } = this;
        try {
            let sql = `
            SELECT l.id,l.activity_id,l.coin_id,l.coin_num,l.first_open_discount,l.first_open_cost_num,l.num,l.delsign,c.name AS coin_name FROM lottery_activity_coin l
            INNER JOIN coin c ON c.id = l.coin_id 
            WHERE activity_id = ?
            ORDER BY l.id ASC
            `;
            return await this.selectList(sql, [req.box_award_id]);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    
    public async updateBoxDiscountRule(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
                UPDATE lottery_activity_coin 
                SET first_open_discount = ?,
                first_open_cost_num = ?
                WHERE
                    id = ?;
            `;
            let data = await this.execSql(sqlStr, [
                req.first_open_discount,
                req.first_open_cost_num,
                req.id
            ])
            return data;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateBoxDiscountRuleDelsign(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
                UPDATE lottery_activity_coin 
                SET delsign = ?
                WHERE
                    id = ?;
            `;
            let data = await this.execSql(sqlStr, [
                req.delsign,
                req.id
            ])
            return data;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateActivityRuleDelsign(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
                UPDATE lottery_open_rule_activity
                SET delsign = ?
                WHERE
                    id = ?;
            `;
            let data = await this.execSql(sqlStr, [
                req.delsign,
                req.id
            ])
            return data;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getLeadBoxList(req) {
        const { app, ctx, logger } = this;
        try {
            let idList = "789,790,791,792,793,795,796,797,798,799";
            if (req.type == 2) {
                idList = "840,841,842,843,844,845,846,847,849,850,851,852,853,854,855,856,858,862";
            }
            let sql = `
            SELECT ${req.box_award_id} AS box_award_id
            ,b.name
            ,b.item_dic_id
            ,b.sex
            ,level
            ,b.discount_coin_id
            ,b.discount_coin_item_dic_id
            ,b.discount_coin_num
            ,b.weight
            ,b.gold_weight
            ,b.weight_percent
            ,b.img
            ,b.is_up
            ,b.is_hide
            ,b.cumulative_num
            ,b.num
            ,b.num_surplus
            ,b.remark
            ,0 AS delsign
            ,NULL AS sort,
            i.item_id,
            IFNULL(i.item_cate_id, 10000) AS cate_id,
            ni.img_name AS normalImg,
            c.img_name AS coinImg,
            m.prive_remark AS maskshowImg,
            dc.name AS discountCoinName,
            dc.img_name AS discountCoinImg,
            cou.pic AS couponPic,
            wc.pic AS clothPic
            FROM lottery_box b
            LEFT JOIN item_dic i ON i.id = b.item_dic_id
            LEFT JOIN item_cate ic ON ic.id = i.item_cate_id
            LEFT JOIN maskshow m ON m.no = i.item_id AND i.item_cate_id = 1040
            LEFT JOIN wedding_clothing wc ON wc.id = i.item_id AND i.item_cate_id = 11000
            LEFT JOIN coupon cou ON cou.id = i.item_id AND i.item_cate_id = 12000
            LEFT JOIN normal_item ni ON ni.no = i.item_id
            LEFT JOIN coin c ON c.id = i.item_id
            LEFT JOIN coin dc ON dc.id = b.discount_coin_id
            WHERE b.id IN (${idList})
            ORDER BY level ASC    
            `;
            
            return await this.selectList(sql, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async insertLeadBoxList(reqList: any) {
        const { app, logger } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = `INSERT INTO lottery_box (
                name,
                item_dic_id,
                box_award_id,
                level,
                discount_coin_id,
                discount_coin_item_dic_id,
                discount_coin_num,
                weight,
                gold_weight,
                weight_percent,
                is_up,
                is_hide,
                num,
                remark,
                delsign
            )
            VALUES
                 ? `;

            let boxList: any[] = [];
            for (const req of reqList) {
                boxList.push([
                    req.name, 
                    req.item_dic_id, 
                    req.box_award_id, 
                    req.level, 
                    req.discount_coin_id, 
                    req.discount_coin_item_dic_id, 
                    req.discount_coin_num, 
                    req.weight, 
                    req.gold_weight, 
                    req.weight_percent, 
                    req.is_up, 
                    req.is_hide, 
                    req.num, 
                    req.remark, 
                    0]);
            }
            await conn.query(sql, [boxList]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async getBoxSelectImgList(req) {
        const { app, ctx, logger } = this;
        try {
            
            let sql = `
                SELECT * FROM lottery_box WHERE item_dic_id IS NULL AND img IS NOT NULL AND img != "" ORDER BY id DESC 
            `;
            
            return await this.selectList(sql, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }
}
