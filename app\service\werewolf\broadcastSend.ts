/*
 * @Description: 商城道具服务类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: z<PERSON>yu
 * @Date: 2020-04-24 11:35:39
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-07-21 10:54:23
 */
import BaseMegaService from './BaseMegaService';
import { MallItemBaseType, MallItemBaseInfo, CoinOperation, UploadTagInfoReq } from './../../model/mallItemManager';
const managerDb = "manager";
import moment = require('moment');

export default class BroadcastSendService extends BaseMegaService {

    public async getBroadcastList() {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
                SELECT b.*, u.nickname FROM wf_admin_broadcast b
                INNER JOIN wf_admin_user u ON u.id = b.admin_id
                ORDER BY b.type ASC 
            `;
            return await this.selectList(sqlStr, [], managerDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateBroadcastType(req) {
        const { app, ctx, logger } = this;
        const manager = app.mysql.get(managerDb);
        const managerConn = await manager.beginTransaction();
        try {
            let sql;
            if (req.type == 0) {
                sql = `
            UPDATE
                wf_admin_broadcast 
            SET
                type = ?,
                send_time = DATE_ADD(NOW(),INTERVAL 1 MINUTE)
            WHERE
                id = ?
            `;
            } else {
                sql = `
            UPDATE
                wf_admin_broadcast 
            SET
                type = ?
            WHERE
                id = ?`;
            }

            await managerConn.query(sql, [req.type, req.id]);
            await managerConn.commit();
        } catch (error) {
            await managerConn.rollback();
            logger.error(error);
            throw error
        }
    }

    public async updateBroadcast(req) {
        const { app, ctx, logger } = this;
        const manager = app.mysql.get(managerDb);
        const managerConn = await manager.beginTransaction();
        try {
            let sql = ``;
            if (req.is_activity == 1) {
                sql = `
                UPDATE wf_admin_broadcast 
                SET admin_id = '${req.admin_id}',
                actDesc1 = '${req.actDesc1}',
                acTendUrl = '${req.acTendUrl}',
                tend_type = '${req.tend_type}',
                tend_page = '${req.tend_page}',
                send_time = '${req.send_time}',
                activity_name = '${req.activity_name}',
                type = 0
                WHERE
                    id = '${req.id}';
            `
            } else{
                sql = `
                UPDATE wf_admin_broadcast 
                SET admin_id = '${req.admin_id}',
                actDesc1 = '${req.actDesc1}',
                actDesc2 = '${req.actDesc2}',
                actDesc3 = '${req.actDesc3}',
                actDesc4 = '${req.actDesc4}',
                actUserId1 = '${req.actUserId1}',
                actUserId2 = '${req.actUserId2}',
                actItemName = '${req.actItemName}',
                acTendUrl = '${req.acTendUrl}'
                WHERE
                    id = '${req.id}';
            `;
            }

            await managerConn.query(sql, []);
            await managerConn.commit();
        } catch (error) {
            await managerConn.rollback();
            logger.error(error);
            throw error
        }
    }

    public async insertBroadcast(req) {
        const { app, ctx, logger } = this;
        const manager = app.mysql.get(managerDb);
        const managerConn = await manager.beginTransaction();
        try {
            let send_time = req.send_time && req.send_time != "" ? `'${req.send_time}'` : "DATE_ADD(NOW(),INTERVAL 1 MINUTE)";

            let sql = `
            INSERT INTO wf_admin_broadcast (
                admin_id,
                actDesc1,
                actDesc2,
                actDesc3,
                actDesc4,
                actUserId1,
                actUserId2,
                actItemName,
                acTendUrl,
                create_time,
                send_time,
                type,
                tend_type,
                tend_page,
                is_activity,
                activity_name
            )
            VALUES
                (
                    '${req.admin_id}',
                    '${req.actDesc1}',
                    '${req.actDesc2}',
                    '${req.actDesc3}',
                    '${req.actDesc4}',
                    '${req.actUserId1}',
                    '${req.actUserId2}',
                    '${req.actItemName}',
                    '${req.acTendUrl}',
                    NOW(),
                    ${send_time},
                    0,
                    '${req.tend_type}',
                    '${req.tend_page}',
                    '${req.is_activity}',
                    '${req.activity_name}'
                )
            `;

            await managerConn.query(sql, []);
            await managerConn.commit();
        } catch (error) {
            await managerConn.rollback();
            logger.error(error);
            throw error
        }
    }

    public async checkBroadcast() {
        const { app, ctx, logger } = this;
        const manager = app.mysql.get(managerDb);

        // logger.debug("检测世界频道待发送");

        if (this.app.config.broadcastRun != 1) {
            return;
        }

        const managerConn = await manager.beginTransaction();
        try {
            let sql = `
            SELECT 
                * 
            FROM 
                wf_admin_broadcast
            WHERE 
                type = 0 
                AND send_time <= NOW()               
            `;

            let list = await managerConn.query(sql, []);
            let resultList: any[] = new Array();
            for (const item of list) {
                const boardUser: any[] = new Array();
                if (item.actUserId1) {
                    boardUser.push(item.actUserId1);
                }
                if (item.actUserId2) {
                    boardUser.push(item.actUserId2);
                }
                const sendPara = {
                    hornType: 14,
                    acTendUrl: item.acTendUrl ? item.acTendUrl : "",
                    actDesc1: item.actDesc1 ? item.actDesc1 : "",
                    actDesc2: item.actDesc2 ? item.actDesc2 : "",
                    actDesc3: item.actDesc3 ? item.actDesc3 : "",
                    actDesc4: item.actDesc4 ? item.actDesc4 : "",
                    actItemName: item.actItemName ? item.actItemName : "",
                    page: item.tend_page,
                    boardUser,
                    imprison_type: 0,
                    impriso: '',
                }
                let result = await ctx.service.werewolf.broadcastSendService.broadcastSendByExchange(ctx, sendPara)
                if (!result || result.data.code != 1) {
                    logger.debug("世界频道发送失败 data:", item);
                } else {
                    logger.debug("世界频道发送成功 id:", item.id);
                    resultList.push(item);
                }
            }
            for (const item of resultList) {
                let updateSql = `
                UPDATE
                    wf_admin_broadcast 
                SET
                    type = 2
                WHERE
                    id = ${item.id}
                `;

                await managerConn.query(updateSql, []);
            }
            await managerConn.commit();
        } catch (error) {
            await managerConn.rollback();
            logger.error(error);
            throw error
        }
    }

}
