/*
 * @Description: 公共数据库建模
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2018-11-22 15:51:11
 * @LastEditors: hammercui
 * @LastEditTime: 2018-12-28 16:32:25
 */

export enum HttpErr {
	/**
       *无效的参数
       */
	InvalidParameter = 0,
	/**
	 * 管理员不存在
	 */
	UserNotExist,
	/**
	 * 密码错误
	 */
	PasswordError,
	/**
	 * 两次密码不一致
	 */
	PasswordNotEql,
	/**
	 * 玩家状态有误
	 */
	PlayerStatusErr,
	/**
       * 成功
       */
	Success = 200,
	/**
       * 请求参数错误
       */
	BadRequest = 400,

	/**
       * 未鉴权
       */
	Unauthorized = 401,

	/**
	 * 已授权，但访问禁止
	 */
	Forbidden = 403
}

export interface IerrorMsg {
	err_code: HttpErr;
	err_msg: string;
}

export interface Ipayload{
	uid: number;
	tokenId: number;
	ip: string;
	accessRoutes: number[]
	//host: string;
}

export interface IsuccessBody {
	code: number;
	msg?: string;
	data?: any;
}
