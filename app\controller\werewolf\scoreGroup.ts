/*
 * @Description: 团队刷分查询
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: 赵宝强
 * @Date: 2020-11-18 15:43
 * @LastEditors: 赵宝强
 * @LastEditTime:
 */

import BaseMegaController from './BaseMegaController';
import {AccountAbnormalRequest, ScoreGroupRequest, ScoreGroupRequest1, UdidRequest} from "../../model/werewolf2";


export default class ScoreGroupController extends BaseMegaController {

    public async getGroup() {

        const { ctx, logger } = this;
        const requestBody: ScoreGroupRequest = ctx.request.body;
        try {
            const list =  await ctx.service.werewolf.scoreGroup.getGroup(requestBody);
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async getGroup1() {

        const { ctx, logger } = this;

        try {
            const requestBody = ctx.request.body;
            logger.info(requestBody);
            const responseBody =  await ctx.service.werewolf.scoreGroup.getGroup1(requestBody);
            this.respSuccData(responseBody)
        } catch (err) {
            this.respFail(err)
        }
    }

}
