/*
 * @Author: ji<PERSON><PERSON>.<PERSON>
 * @Date: 2020-08-03 09:31:00
 * @LastEditTime: 2021-02-27 11:04:56
 * @LastEditors: jiawen.wang
 */

//bannerInfo
export interface IbannerInfo {
  id: number;
  banner_name: string;
  banner_img: string;
  url: string;
  type: number;
  page: number;
  start_time: string;
  end_time: string;
  is_show: number;
  is_delsign: number;
  sort_id: number;

}
//bannerBaseInfo
export interface IbannerBaseInfo {
  banner_name: string;
  url: string;
  show_type: number;
  type: number;
  page: number;
  start_time: string;
  end_time: string;
  sort_id: number;
  desc: string;
  show_in_eventview: number;
  show_in_hall: number;
  tend_type: number;
  activity_id: number;
  tab1_id: number;
  tab2_id: number;
}
export interface IUPbannerBaseInfo {
  banner_name: string;
  url: string;
  type: number;
  show_type: number;
  page: number;
  desc: string;
  start_time: string;
  end_time: string;
  id: number;
  sort_id: number;
  is_show: number;
  is_prod: number;
  show_in_eventview: number;
  show_in_hall: number;
  tend_type: number;
  tab1_id: number;
  tab2_id: number;
}
export interface IbannerImageInfo {
  id: number;
  banner_img: string;
}
