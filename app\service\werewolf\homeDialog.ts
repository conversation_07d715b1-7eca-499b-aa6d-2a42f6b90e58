/*
 * @Description: 首页弹窗服务类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-03-24 10:50:39
 * @LastEditors: hammercui
 * @LastEditTime: 2020-09-18 13:48:50
 */
import BaseMegaService from './BaseMegaService';
import { IhomeDialogConf, IhomeDialogEidtReq, IhomeDialogDeleteReq } from '../../model/wfHomeDIalog';
export default class HomeDialogService extends BaseMegaService {

    /**
     * @name: select home dialog list
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async list() {
        const { app, ctx, logger } = this;

        try {
            const sqlStr = `SELECT a.*,b.dialog_type_name  FROM home_dialog_conf AS a 
            LEFT JOIN home_dialog_type  AS b on a.dialog_type = b.dialog_type AND b.del_sign = 0
            WHERE a.del_sign = 0 
            ORDER BY a.show_time DESC;
            `;
            return await this.selectList(sqlStr, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    /**
     * 弹框与原生交互类型
     * @name: slect type list
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async typeList() {
        const { app, ctx, logger } = this;

        try {
            const sqlStr = `SELECT * FROM home_dialog_type WHERE del_sign = 0;
            `;
            return await this.selectList(sqlStr, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    /**
     * @name: create home dialog
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async create(req: IhomeDialogConf) {
        const { app, ctx, logger } = this;

        try {
            const sqlStr = `INSERT INTO home_dialog_conf (dialog_type,href_url,show_time,img_url)
            VALUES(?,?,?,?)`;
            await this.execSql(sqlStr, [req.dialog_type, req.href_url, req.show_time, req.img_url]);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    /**
     * @name: update dialog state
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async edit(req: IhomeDialogEidtReq) {
        const { app, ctx, logger } = this;

        try {
            // 关闭
            if (req.dialog_state == 0) {
                const sqlStr = `UPDATE home_dialog_conf SET dialog_state = 0 WHERE id = ?;`;
                await this.execSql(sqlStr, [req.id]);
            } else if (req.dialog_state == 1) {
                //开启
                //1 关闭其他所有
                let sqlStr = `UPDATE home_dialog_conf SET dialog_state = 0 WHERE id != ? AND del_sign = 0;`;
                await this.execSql(sqlStr, [req.id]);
                //2 开启我自己
                sqlStr = `UPDATE home_dialog_conf SET dialog_state = 1 WHERE id = ?;`;
                await this.execSql(sqlStr, [req.id]);
            }

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    /**
     * @name: delete the home dialog
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async delete(req: IhomeDialogDeleteReq) {
        const { app, ctx, logger } = this;

        try {
            const sqlStr = `UPDATE home_dialog_conf SET del_sign = 1 WHERE id = ?;`;
            await this.execSql(sqlStr, [req.id]);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }
}
