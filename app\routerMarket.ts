/*
 * @Author: chen peng 
 * @Date: 2021-08-18 14:43:04
 * @LastEditTime: 2021-08-18 17:32:06
 * @LastEditors: Please set LastEditors
 * @Description: 集市 
 * @FilePath: /MGKFHTServer/app/routerMarket.ts
 */

import { Router, Application } from "egg";
import { AccessRouteId } from './model/accessRouteCof';
import { once } from 'process';
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

    const { controller, router } = app;
    //集市-流通详情查询
    router.post(`${API_VERSION}/werewolf/market/getMarketCirculateList`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.market.getMarketCirculateList);
    //集市-每日大盘
    router.post(`${API_VERSION}/werewolf/market/getDailyMarketList`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.market.getDailyMarketList);
    //集市-更新集市列表隐藏显示
    router.post(`${API_VERSION}/werewolf/market/updateMarketState`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.market.updateMarketState);

    router.post(`${API_VERSION}/werewolf/market/getAvatarMapList`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.market.getAvatarMapList);
    router.post(`${API_VERSION}/werewolf/market/updateAvatarMap`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.market.updateAvatarMap);
    router.post(`${API_VERSION}/werewolf/market/insertAvatarMap`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.market.insertAvatarMap);
    router.post(`${API_VERSION}/werewolf/market/deleteAvatarMap`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.market.deleteAvatarMap);

    router.post(`${API_VERSION}/werewolf/market/getAvatarMapContentList`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.market.getAvatarMapContentList);
    router.post(`${API_VERSION}/werewolf/market/insertAvatarMapContent`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.market.insertAvatarMapContent);
    router.post(`${API_VERSION}/werewolf/market/deleteAvatarMapContent`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.market.deleteAvatarMapContent);
    router.post(`${API_VERSION}/werewolf/market/updateAvatarMapContent`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.market.updateAvatarMapContent);

    router.post(`${API_VERSION}/werewolf/market/getGemRecords`, accCtr(AccessRouteId.wolf_redbag), controller.werewolf.market.getGemRecords);




}

export default load