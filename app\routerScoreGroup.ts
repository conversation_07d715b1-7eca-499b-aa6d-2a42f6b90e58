/*
 * @Description: 团队刷分
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: 赵宝强
 * @Date: 2020-11-18
 * @LastEditors: 赵宝强
 * @LastEditTime:
 */
import {Application} from "egg";
import {AccessRouteId} from './model/accessRouteCof';

const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {
    const { controller, router } = app;
    router.post(`${API_VERSION}/werewolf/scoreGroup/getGroup`,accCtr(AccessRouteId.wolf_group_scoreGroup),controller.werewolf.scoreGroup.getGroup);
    router.post(`${API_VERSION}/werewolf/scoreGroup/getGroup1`,accCtr(AccessRouteId.wolf_group_scoreGroup),controller.werewolf.scoreGroup.getGroup1);

}

export default load
