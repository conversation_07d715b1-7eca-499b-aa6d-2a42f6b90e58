/*
 * @Description: 违规刷分
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: 赵宝强
 * @Date: 2020-11-07
 * @LastEditors: 赵宝强
 * @LastEditTime:
 */
import { Router, Application } from "egg";
import { AccessRouteId } from './model/accessRouteCof';
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

    const { controller, router } = app;
    //【用户违规】
    //违规用户信息
    router.post(`${API_VERSION}/werewolf/deductionRecord/getUserList`,accCtr(AccessRouteId.wolf_group_controller),controller.werewolf.deductionController.getUserList);

    //违规游戏局详情
    router.post(`${API_VERSION}/werewolf/deductionRecord/getGameMatch`,accCtr(AccessRouteId.wolf_group_controller),controller.werewolf.deductionController.getGameMatch);

    //根据gameid查询
    router.post(`${API_VERSION}/werewolf/deductionRecord/getLittleOpenBlack`, accCtr(AccessRouteId.wolf_group_controller), controller.werewolf.deductionController.getLittleOpenBlack);

}

export default load
