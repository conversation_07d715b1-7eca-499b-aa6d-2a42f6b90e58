/*
 * @Description: 游戏板子服务类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2019-10-10 15:02:09
 * @LastEditors: hammercui
 * @LastEditTime: 2019-11-25 14:17:42
 */

import { Service } from 'egg';
import { SendEmailsToUsersRequest, SearchNewUserListResponse, PushMessageResquest } from '../../model/werewolf';
import { Ipayload } from '../../model/common';
import { IupdateGameBoardOpenReq, IcreateGameBoardOpenReq, IupdateGameBoardOpenSortReq } from '../../model/werewolf3';
export default class GameBoardigService extends Service {

    /**
     * @name: 获得open表全部板子
     */
    public async getOpenList() {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        try {
            //查询板子列表
            let sql = `SELECT
                tgo.gameconfig_id,
                tgo.open_time,
                tgo.close_time,
                tgo.\`desc\`,
                tgo.flag AS flagInt,
                tgo.sort,
                tgo.hot,
                tg.num,
                tg.\`desc\` AS gname,
                tg.award,
                tg.type,
                tg.multiple,
                tg.vip 
            FROM
                tgameconfig_open AS tgo
                LEFT JOIN tgameconfig AS tg ON tg.id = tgo.gameconfig_id 
            WHERE
                tgo.is_delsign = 0
                ORDER BY tgo.sort
            `;
            const openList = await db.query(sql);

            sql = `SELECT
                tgr.gameconfig_id,
                tgr.num,
                ar.\`name\`,
                tgr.role_id,
                tgr.sort
            FROM
                tgameconfig_role AS tgr
                LEFT JOIN animation_role AS ar ON tgr.role_id = ar.role 
            WHERE
                tgr.delsign = 0
                AND ar.type = 0	    
            ORDER BY gameconfig_id
            `;
            const openRole = await db.query(sql);
            let a = {};
            //role存入map并排序
            for (const iterator of openRole) {
                let key = iterator.gameconfig_id.toString();
                //a 包含属性
                if (a.hasOwnProperty(key)) {
                    a[key].push(iterator);
                } else {
                    a[key] = [iterator];
                }
            }
            //查询板子人员
            return { openList, openRole: a };
        } catch (error) {
            throw error;
        }
    }

    /**
     * @name: 立即修改flag
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async updateOpenFlagByNow(req: IupdateGameBoardOpenReq) {
        const { newFlag, gameBoardId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = `UPDATE tgameconfig_open SET flag = ?,hot= ?,\`desc\`=?,open_time=NULL,close_time=NULL  WHERE gameconfig_id = ?`;
            // console.info("sql",sql);
            await conn.query(sql, [newFlag, hot, desc, gameBoardId]);

            sql = `DROP EVENT IF EXISTS gameconfig_open_on_flag_${gameBoardId}`;
            await conn.query(sql);

            sql = `DROP EVENT IF EXISTS gameconfig_open_off_flag_${gameBoardId}`;
            await conn.query(sql);

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    /**
     * @name: 根据时间修改
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async updateOpenFlagByTime(req: IupdateGameBoardOpenReq) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            //上架
            if (req.openTime) {
                //1 更新时间
                let sql = `UPDATE tgameconfig_open SET open_time=?,hot=?,\`desc\`=?  WHERE gameconfig_id = ?`;
                await conn.query(sql, [req.openTime, req.hot, req.desc, req.gameBoardId]);
                //2
                sql = `DROP EVENT IF EXISTS gameconfig_open_on_flag_${req.gameBoardId}`;
                await conn.query(sql);

                //3 新建新event
                sql = this.getEventSqlStr(1, req.gameBoardId, req.openTime);
                console.info("上架event", sql);
                await conn.query(sql)
            }
            //下架
            if (req.closeTime) {
                //1 更新时间
                let sql = `UPDATE tgameconfig_open SET close_time=?,hot=?,\`desc\`=?  WHERE gameconfig_id = ?`;
                await conn.query(sql, [req.closeTime, req.hot, req.desc, req.gameBoardId]);

                //2
                sql = `DROP EVENT IF EXISTS gameconfig_open_off_flag_${req.gameBoardId}`;
                await conn.query(sql);

                //3 新建新event
                sql = this.getEventSqlStr(0, req.gameBoardId, req.closeTime);
                console.info("下架event", sql);
                await conn.query(sql)
            }
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    /**
     * @name: 获得时间字符串
     * @msg: 
     * @param {type} 
     * @return: 
     */
    private getEventSqlStr(flag: number, id: number, time: string): string {
        let eventName = '';
        //上架
        if (flag == 1) {
            eventName = "gameconfig_open_on_flag_" + id
        }
        //下架
        else {
            flag = 0;
            eventName = "gameconfig_open_off_flag_" + id
        }

        return `CREATE EVENT ${eventName} ON SCHEDULE AT '${time}'  ON COMPLETION NOT PRESERVE 
        DO
        UPDATE tgameconfig_open SET flag = ${flag} WHERE tgameconfig_open.gameconfig_id = ${id};`;
    }

    /**
     * @name: 新建
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async createOpen(req: IcreateGameBoardOpenReq) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            //上架
            if (req.openTime) {
                //2
                let sql = `DROP EVENT IF EXISTS gameconfig_open_on_flag_${req.gameBoardId}`;
                await conn.query(sql);
                //新建新event
                sql = this.getEventSqlStr(1, req.gameBoardId, req.openTime);
                await conn.query(sql)
            }
            //下架
            if (req.closeTime) {
                //2
                let sql = `DROP EVENT IF EXISTS gameconfig_open_off_flag_${req.gameBoardId}`;
                await conn.query(sql);
                //新建新event
                sql = this.getEventSqlStr(0, req.gameBoardId, req.closeTime);
                await conn.query(sql)
            }
            // 1 顺序+1

            //查询open总数
            let sql = "UPDATE tgameconfig_open SET sort = sort+1;"
            await conn.query(sql);

            //2 新增
            sql = `INSERT IGNORE INTO tgameconfig_open (gameconfig_id,open_time,close_time,\`desc\`,flag,hot,sort) VALUES(?,?,?,?,?,?,?)`;
            let flag = 0;
            //不存在
            if (!req.openTime && !req.closeTime) {
                flag = 1;
            }
            await conn.query(sql, [req.gameBoardId, req.openTime, req.closeTime, req.desc, flag, req.hot, req.sort]);

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async geUnOpenList() {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        try {
            let sql = `SELECT
            tg.id,
            tg.\`desc\`,
		    tg.type,
            tg.num AS people_num
        FROM
            tgameconfig AS tg 
        LEFT JOIN  tgameconfig_open AS tgo ON tgo.gameconfig_id = tg.id
        WHERE tgo.gameconfig_id is NULL
        AND tg.\`desc\` IS NOT NULL
            `;
            const unOpenRole = await db.query(sql);
            // let a = {};
            // //role存入map并排序
            // for (const iterator of unOpenRole) {
            //     let key = iterator.id.toString();
            //     //a 包含属性
            //     if (a.hasOwnProperty(key)) {
            //         a[key].push(iterator);
            //     } else {
            //         a[key] = [iterator];
            //     }
            // }
            return unOpenRole;
        } catch (error) {
            throw error;
        }
    }

    /**
     * @name: 更新排序
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async updateOpenSort(req: IupdateGameBoardOpenSortReq) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {

            for (const iterator of req.newSort) {
                let sql = "UPDATE tgameconfig_open SET sort = ? WHERE gameconfig_id = ?"
                await conn.query(sql, [iterator.sort, iterator.gameBoardId]);
            }

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    /**
     * @name: 删除一个版型
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async deleteOpen(gameBoardId: number) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        try {
            let sql = "DELETE FROM tgameconfig_open WHERE gameconfig_id = ?"
            await db.query(sql, [gameBoardId]);
        } catch (error) {
            throw error;
        }
    }
}
