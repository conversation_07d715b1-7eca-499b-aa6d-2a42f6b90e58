/*
 * @Description: 配置文件
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2018-11-20 18:00:10
 * @LastEditors: zhanglu
 * @LastEditTime: 2020-11-17 14:31:38
 */
 import { EggAppConfig, EggAppInfo, PowerPartial } from 'egg';
const path = require('path');


export default (appInfo: EggAppInfo) => {
	// const config = {} as PowerPartial<EggAppConfig>;
	const config: PowerPartial<EggAppConfig> = {};
	// const config = {};
	// override config from framework / plugin
	// use for cookie sign key, should change to your own and keep security
	config.keys = appInfo.name + '_1542696390346_7298';
	config.proxy = true;
	// add your egg config in here
	// 配置需要的中间件，数组顺序即为中间件的加载顺序
	config.middleware = [ 'analysis', 'customJwt' ];

	// add your special config in here
	const bizConfig = {
		sourceUrl: `https://github.com/eggjs/examples/tree/master/${appInfo.name}`,
		//静态资源路径 默认增加public
		static: {
			prefix: '/headicon/',
			dir: path.join(appInfo.baseDir, 'headicon'),
			maxAge: 0,
			buffer: false, //默认true in prod env, false in other envs，先全关掉
			dynamic: true,
			preload: false
		},

		//自定义处理jwt
		customJwt: {
			enable: true,
			//ignore: /user\/login/,  //过滤掉登陆
			match: /api\/(manager|werewolf|scriptkill|dev)/
		},

		//埋点中间件
		analysis: {
			enable: true,
			match: /(user\/login)|(\/analysis\/)|(\/atyConf\/)/ // 匹配user/login 和 /u/路径
		},

		//jwt中间件配置
		jwt: {
			secret: 'mega2708666666',
			exTime: 604800 //30分钟=1800秒 准备改为8小时=28800秒
		},

		//安全规则配置
		security: {
			//csrf安全配置
			csrf: {
				enable: false,
				ignoreJSON: true, // 默认为 false，当设置为 true 时，将会放过所有 content-type 为 `application/json` 的请求
				// match: /api\/(manager|werewolf|scriptkill|dev)/
			}
		},

		//自定义日志
		customLogger: {
			analysisLogger: {
				file: path.join(appInfo.root, 'logs/analysis.log')
			},
			scheduleLogger: {
				consoleLevel: 'NONE',
			}
		},

		//OSS
		oss: {
			client: {
				accessKeyId: 'LTAI4FrMp2q7twEfV27orLvM',
				accessKeySecret: '******************************',
				bucket: 'werewolf-headicon',
				endpoint: 'oss-cn-hangzhou.aliyuncs.com',
				timeout: '60s',
			}
		},
		//日志级别
		logger: {
			level: 'DEBUG',
			consoleLevel: 'DEBUG',
		  },

		//存储配置的redis数据库
		confRedisDb: 8,
		serverEnv:"local",
		//爬虫路由
		crawelHost:"http://*************:4632/crawler/page",
		WerewolfJPExchangeGate: 'http://*************:9926/',
		// 天狼飞书bot 
		WerewolfFeiShuBot: 'https://open.feishu.cn/open-apis/bot/v2/hook/a9afdba4-e1d6-4fce-aaed-91d21b225d7f',
	};

	// the return config will combines to EggAppConfig
	return {
		...config,
		...bizConfig
	};
};
