/*
 * @Description: 头像框
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: 张宇
 * @Date: 2019-04-02 14:16:09
 * @LastEditors: jiawen.wang
 * @LastEditTime: 2022-01-07 10:09:41
 */
import { Service } from 'egg'
import {
    IuserSimInfo,
    IavFrameListRequest,
    IuserAvatarFrame,
    IgiveAvFrameRequest,
    IavFrameIsShowRequest,
    IavFrameListResponse,
    IuploadAvatarFrameRequest,
    IuploadAvatarFrameResponse,
    IopeAvaRecordListRequest,
    IopeAvaRecordListResponse,
    IavatarFrameImg,
    IframeCompleteRequest,
} from '../../model/werewolf'
import BaseMegaService from './BaseMegaService'

export default class AvatarFrameService extends BaseMegaService {
    /**
     * @name: 当前用户名称
     * @msg:
     * @param {type}
     * @return:
     */
    public async userName(userNoList: string[]): Promise<IuserSimInfo[]> {
        const { app } = this
        const db = app.mysql.get('werewolf')
        userNoList = db.escape(userNoList)
        const results = await db.query('SELECT tuser.`no`,tuser.nickname FROM tuser WHERE tuser.`no` IN (' + userNoList + ')')
        if (!!results && results.length > 0) {
            return results
        } else {
            return []
        }
    }
    /**
     * @name: 获取头像框
     * @msg:
     * @param {type}
     * @return:
     */
    public async getFrames(FrameListRequest: IavFrameListRequest): Promise<IavFrameListResponse> {
        const { app } = this
        const db = app.mysql.get('werewolf')
        const channel = db.escape(FrameListRequest.channel)
        const isDynamic = db.escape(FrameListRequest.isDynamic)
        const isComplete = db.escape(FrameListRequest.isComplete)
        const start = db.escape(FrameListRequest.start)
        const offset = db.escape(FrameListRequest.offset)
        const frameName = FrameListRequest.frameName
        const frameRemark = FrameListRequest.frameRemark
        let count = 0
        let sqlcount =
            'SELECT COUNT(*) AS total FROM avatarframe WHERE avatarframe.channel = ' +
            channel +
            ' AND avatarframe.is_dynamic = ' +
            isDynamic +
            ' AND avatarframe.is_complete = ' +
            isComplete
        if (frameName != null && frameName != undefined && frameName != '') {
            sqlcount += ` AND name LIKE  '%` + frameName + `%' `
        }
        if (frameRemark != null && frameRemark != undefined && frameRemark != '') {
            sqlcount += ` AND remark LIKE  '%` + frameRemark + `%' `
        }
        let result = await db.query(sqlcount)
        if (!!result && result.length > 0) {
            count = result[0].total
        }
        let sql =
            "SELECT a.id, a.`name`, IF(is_complete = 0,'', CONCAT('http://" +
            app.config.imgDomain +
            "/Werewolf/Frame/',a.id,IF(a.delsign = 0,IF(a.is_dynamic = 0,'_player.png','_player.webp')," +
            "IF(a.is_dynamic = 0,'_player_offline.png','_player_offline.webp'))))AS url," +
            'a.type, a.preview,a.remark, a.price,a.createtime, a.is_dynamic, a.is_complete,a.delsign FROM avatarframe a WHERE a.channel = ' +
            channel +
            ' AND a.is_dynamic = ' +
            isDynamic +
            ' AND a.is_complete = ' +
            isComplete
        if (frameName != null && frameName != undefined && frameName != '') {
            sql += ` AND name LIKE  '%` + frameName + `%' `
        }
        if (frameRemark != null && frameRemark != undefined && frameRemark != '') {
            sql += ` AND remark LIKE  '%` + frameRemark + `%' `
        }
        sql += ' ORDER BY ' + 'a.id DESC LIMIT ' + start + ', ' + offset

        const results = await db.query(sql)
        if (!!results && results.length > 0) {
            return {
                start: FrameListRequest.start,
                offset: FrameListRequest.offset,
                count: count,
                dataArray: results,
            }
        } else {
            return {
                start: FrameListRequest.start,
                offset: FrameListRequest.offset,
                count: 0,
                dataArray: [],
            }
        }
    }

    public async getStaticAvatarFrameList() {
        const { logger } = this

        try {
            const sqlStr = `
            SELECT id, name FROM avatarframe WHERE delsign = 0 AND is_dynamic = 0 ORDER BY id DESC
            `
            return await this.selectList(sqlStr, [])
        } catch (error) {
            logger.error(error)
            throw error
        }
    }

    /**
     * @name: 给与目标用户头像框
     * @msg:
     * @param {type}
     * @return:
     */
    public async giveFrame(giveAvFrame: IgiveAvFrameRequest) {
        const { app, ctx, logger } = this
        const werewolf = app.mysql.get('werewolf')
        const manager = app.mysql.get('manager')
        const werewolfConn = await werewolf.beginTransaction()
        const managerConn = await manager.beginTransaction()
        const id = werewolfConn.escape(giveAvFrame.id)
        let userid = giveAvFrame.userIds.split(';')
        let sqlStr = ''
        let rows: any[] = []
        for (let useri of userid) {
            let user = werewolfConn.escape(useri)
            user = user.replace(/\'/g, '')
            sqlStr += '(' + id + ',' + user + ',0,0),'
            let row = {
                admin_id: giveAvFrame.uid,
                user_id: user,
                avatar_frame_id: giveAvFrame.id,
                avatar_frame_name: giveAvFrame.frameName,
                type: 2,
            }
            rows.push(row)
        }
        // sqlStr = sqlStr.substring(0, sqlStr.length - 1);
        // const sql = "INSERT IGNORE INTO user_avatarframe (avatarframe_id,user_id,note_id,delsign) VALUES" + sqlStr;

        try {
            let sqlItemDic = `SELECT id FROM item_dic WHERE item_id = ${giveAvFrame.id} AND item_cate_id IN(2010,2020)`
            const itemDicIdReqult = await this.selectOne(sqlItemDic)
            if (itemDicIdReqult) {
                const itemDic = {
                    itemDicId: itemDicIdReqult.id,
                    num: 1,
                }
                let userList: any[] = []
                for (let useri of userid) {
                    let row = {
                        userId: useri,
                        itemDicList: [itemDic],
                    }
                    userList.push(row)
                }

                let jsonStr = JSON.stringify({
                    type: 0,
                    userList: userList,
                })

                let res = await ctx.curl(app.config.WerewolfJPExchange + 'item/obtain', {
                    method: 'POST',
                    headers: {
                        'content-type': 'application/json',
                    },
                    data: jsonStr,
                    timeout: 3000, // 3 秒超时
                })

                if (!!res && res.status == 200) {
                    let data = JSON.parse(res.data)
                    if (data.code == 1) {
                        for (const item of userid) {
                            if (item == '7') {
                                let updateSql = `UPDATE tuser SET frame=${giveAvFrame.id} WHERE no=7`
                                await werewolfConn.query(updateSql)
                                break
                            }
                        }

                        await managerConn.insert('wf_admin_avatar_frame', rows)
                        // await werewolfConn.query(sql);
                        await werewolfConn.commit() // 提交事务
                        await managerConn.commit()
                        return true
                    } else {
                        await werewolfConn.rollback() // 一定记得捕获异常后回滚事务！
                        await managerConn.rollback()
                        logger.error('失败，请求req', jsonStr)
                        logger.error('失败，resp', res.data)
                        return false
                    }
                } else {
                    await werewolfConn.rollback() // 一定记得捕获异常后回滚事务！
                    await managerConn.rollback()
                    logger.error('失败，请求req', jsonStr)
                    logger.error('失败，res.status', res.status)
                    return false
                }
            } else {
                await werewolfConn.rollback() // 一定记得捕获异常后回滚事务！
                await managerConn.rollback()
                logger.error('失败,sql', sqlItemDic)
                logger.error('失败，itemDicIdReqult为空')
                return false
            }
        } catch (err) {
            await werewolfConn.rollback() // 一定记得捕获异常后回滚事务！
            await managerConn.rollback()
            throw err
        }
    }
    /**
     * @name: 操作头像框(上架/下架)
     * @msg:
     * @param {type}
     * @return:
     */
    public async frameShow(IavFrameIsShow: IavFrameIsShowRequest) {
        const { app } = this
        const werewolf = app.mysql.get('werewolf')
        const manager = app.mysql.get('manager')
        const werewolfConn = await werewolf.beginTransaction()
        const managerConn = await manager.beginTransaction()

        if (IavFrameIsShow.delsign != 0 && IavFrameIsShow.delsign != 1) {
            return false
        }
        try {
            let type = 0
            if (IavFrameIsShow.delsign == 1) {
                type = 1
            }
            const wereRow = {
                delsign: IavFrameIsShow.delsign,
            }
            const wereOptions = {
                where: {
                    id: IavFrameIsShow.id,
                },
            }
            await werewolfConn.update('avatarframe', wereRow, wereOptions)
            await managerConn.insert('wf_admin_avatar_frame', {
                admin_id: IavFrameIsShow.uid,
                avatar_frame_id: IavFrameIsShow.id,
                avatar_frame_name: IavFrameIsShow.id,
                type: type,
            })
            await werewolfConn.commit() // 提交事务
            await managerConn.commit()
            return true
        } catch (err) {
            await werewolfConn.rollback() // 一定记得捕获异常后回滚事务！
            await managerConn.rollback()
            throw err
        }
    }

    public async uploadAvatarFrame(request: IuploadAvatarFrameRequest) {
        const { app } = this

        const werewolf = app.mysql.get('werewolf')
        const manager = app.mysql.get('manager')

        const werewolfConn = await werewolf.beginTransaction()
        const managerConn = await manager.beginTransaction()

        let avatarId = 0

        try {

            let sqlItemDic = `SELECT * FROM item_dic WHERE name = '${request.name}' AND item_cate_id in(2010,2020)`
            const itemDicIdReqult = await this.selectOne(sqlItemDic)

            if(itemDicIdReqult){

                await werewolfConn.commit()
              
                const returnResult = {
                   msg:"头像框重名"
                }

                return returnResult
            }

            //首先插入avatarframe表
            const avatarRows = {
                name: request.name,
                type: request.type,
                channel: 0,
                preview: 0,
                remark: request.remark,
                price: 0,
                is_dynamic: request.is_dynamic,
                delsign: 1,
                level: request.level,
            }
            const result = await werewolfConn.insert('avatarframe', avatarRows)
            avatarId = result.insertId

            let sqlInsertItemDic = ` 
            INSERT INTO \`item_dic\` (\`item_cate_id\`, \`item_id\`, \`name\`, \`version\`, \`delsign\`, remark)
            VALUES (?,?,?,?,?,?);
            `
            await werewolfConn.query(sqlInsertItemDic, [request.item_cate_id, avatarId, request.name, 0, 0, request.remark])

            //判断渠道 channel
            switch (request.channel) {
                case 1: //宝箱兑换商城
                    //todo level coin weight buy exchange give achievement(几百次) img_name(图片名) putaway_time(上架日期) slotout_time（下架日期）desc(二级描述) wish(赠送时的话)
                    //name=name,visual_no=avatarId,visual_type = 3,level=level,price=price,coin = coin,coin_e = coin_e,weight = weight,buy = buy,exchange = exchange,give = give
                    //achievement = achievement,img_name = img_name,putaway_time = putaway_time,slotout_time = slotout_time,desc = desc, wish = wish, delsign = 1
                    break
                case 2: //公会商城
                    break
                case 3: //cp商城
                    break
                case 4: //活动
                    break
            }

            await managerConn.insert('wf_admin_avatar_frame', {
                admin_id: request.uid,
                avatar_frame_id: avatarId,
                avatar_frame_name: avatarId,
                type: 0,
            })
            await werewolfConn.commit()
            await managerConn.commit()
            const infoResult = await werewolf.get('avatarframe', { id: avatarId })
            const url =
                infoResult.is_complete == 0
                    ? ''
                    : infoResult.is_dynamic == 0
                    ? 'http://' + app.config.imgDomain + '/Werewolf/Frame/' + infoResult.id + '_player.png'
                    : 'http://' + app.config.imgDomain + '/Werewolf/Frame/' + infoResult.id + '_player.webp'
            const returnResult: IuserAvatarFrame = {
                id: infoResult.id,
                name: infoResult.name,
                url: url,
                noteUrl: '',
                type: infoResult.type,
                channel: infoResult.channel,
                preview: infoResult.preview,
                remark: infoResult.remark,
                price: infoResult.price,
                createtime: infoResult.createtime,
                is_dynamic: infoResult.is_dynamic,
                is_complete: infoResult.is_complete,
                delsign: infoResult.delsign,
            }
            return returnResult
        } catch (error) {
            await werewolfConn.rollback()
            await managerConn.rollback()
            throw error
        }
    }

    /**
     * 改变为完成状态
     * @param request 头像框id
     */
    public async uploadFrameComplete(request: IframeCompleteRequest): Promise<IuserAvatarFrame> {
        const { app } = this
        const werewolf = app.mysql.get('werewolf')
        try {
            const wereRow = {
                is_complete: 1,
            }
            const wereOptions = {
                where: {
                    id: request.avatarId,
                },
            }
            await werewolf.update('avatarframe', wereRow, wereOptions)
            const infoResult = await werewolf.get('avatarframe', { id: request.avatarId })
            const url =
                infoResult.is_complete == 0
                    ? ''
                    : infoResult.is_dynamic == 0
                    ? 'http://' + app.config.imgDomain + '/Werewolf/Frame/' + infoResult.id + '_player.png'
                    : 'http://' + app.config.imgDomain + '/Werewolf/Frame/' + infoResult.id + '_player.webp'
            const returnResult: IuserAvatarFrame = {
                id: infoResult.id,
                name: infoResult.name,
                url: url,
                noteUrl: '',
                type: infoResult.type,
                channel: infoResult.channel,
                preview: infoResult.preview,
                remark: infoResult.remark,
                price: infoResult.price,
                createtime: infoResult.createtime,
                is_dynamic: infoResult.is_dynamic,
                is_complete: infoResult.is_complete,
                delsign: infoResult.delsign,
            }
            return returnResult
        } catch (error) {
            throw error
        }
    }
}
