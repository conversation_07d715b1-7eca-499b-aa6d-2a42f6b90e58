/*
 * @Description: 灰度配置
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2019-01-04 17:02:18
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-06-26 09:35:57
 */
import { EggAppConfig, PowerPartial } from "egg";

export default () => {
  console.info("加载config.mbeta.ts文件");
  const config: PowerPartial<EggAppConfig> = {};
  const bizConfig = {
    cluster: {
      listen: {
        port: 7001
      }
    },
    mysql: {
      clients: {
        manager: {
          // 数据库名-账号数据库
          database: "mega_manager"
        },
        werewolf: {
          // 数据库名
          database: "werewolf"
        },
        werewolf_slave: {
          // 数据库名
          database: "werewolf",
          host: "*************",
          //host: "coder.53site.com",
          // 端口号
          port: "3306",
          // 用户名
          user: "admin",
          // 密码
          password: "Mangosteen0!",
          //设置字符集
          charset: 'utf8mb4',
          //连接池2
          connectionLimit: 2,
        },
        scriptkill: {
          database: "scriptkill"
        },
        chatgpt: {
          database: "chatgpt"
        }
        // aiim: {
        //   // 数据库名
        //   database: "aiim"
        // }
      },
      // 测试库数据库
      default: {
        host: "coder.53site.com",
        //host: "coder.53site.com",
        // 端口号
        port: "3306",
        // 用户名
        user: "admin",
        // 密码
        password: "Mangosteen0!",
        //设置字符集
        charset: 'utf8mb4',
        //连接池2
        connectionLimit: 2,
      },
      // 是否加载到 app 上，默认开启
      app: true,
      // 是否加载到 agent 上，默认关闭
      agent: false
    },
    //redis
    redis: {
      client: {
        sentinels: [
          {          // Sentinel instances
            port: 27000,         // Sentinel port  
            host: '**************',   // Sentinel host  
          },
          {          // Sentinel instances
            port: 27000,         // Sentinel port  
            host: '************',   // Sentinel host  
          },
          {          // Sentinel instances
            port: 27001,         // Sentinel port  
            host: '************',   // Sentinel host  
          },
        ],
        name: 'mymaster',      // Master name
        // port: 6379,          // Redis port
        // host: '127.0.0.1',   // Redis host
        password: '',
        db: 3,
      },
    },
    // clickHouse
    clickhouse:{
      clients: {
        werewolf_coder: {
          queryOptions: {
            database: "werewolf_coder",
          },
        },
      },
      default: {
        host: "*************",
        port: "8123",
        user: "default",
        password: "Mangosteen0!"
      }
    },
    //mongo
    mongo: {
      client: {
        host: '**************,*************',
        port: '27017,27117',
        name: 'werewolf',
        user: 'admin',
        password: 'Mangosteen0!',
        options: {
          replicaSet: 'shard1',
        },
      }
    },

    //图片域名
    imgDomain: 'coder.53site.com',
    //接口域名
    phpDomain: 'werewolf.53site.com',
    //php推送到android
    phpPushAndoird: 'http://coder.53site.com/Werewolf/Apns/ApnsAPI_batch_push_android_v3.php',
    //php推送到Ios
    phpPushIos: 'http://coder.53site.com/Werewolf/Apns/ApnsAPI_batch_push_ios_v3.php',
    //下载headicon
    headIconDownUrl: "http://coder.53site.com/Werewolf/OSS/downloadheadicon.php",
    //日志级别
    logger: {
      level: 'INFO',
      consoleLevel: 'INFO',
    },
    //OSS
    oss: {
      client: {
        accessKeyId: 'LTAI4FrMp2q7twEfV27orLvM',
        accessKeySecret: '******************************',
        bucket: 'werewolf-headicon',
        endpoint: 'oss-cn-hangzhou-internal.aliyuncs.com',
        timeout: '60s',
      }
    },
    serverEnv:"beta",

    loopKingInterval: '30m',//5分钟间隔
     //2w框定时检测
     loopCoin2wInterval: '15s',//10s间隔
     //世界广播定时检测
     broadcastInterval: '10s',//10s间隔
    //新手辅助机器人定时检测
    newPlayerRobotInterval: '20s',//20s间隔
     //世界广播定时检测
     broadcastRun: 0,  //1开启 0关闭
    //新手辅助机器人定时检测
    newPlayerRobotRun: 1,  //1开启 0关闭
  };
  return {
    ...config,
    ...bizConfig
  };

};
