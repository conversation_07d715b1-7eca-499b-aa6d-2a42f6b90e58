
import { Router, Application } from "egg";
import { AccessRouteId } from './model/accessRouteCof';

const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

    const { controller, router } = app;
    //隐藏款产出记录
    router.post(`${API_VERSION}/werewolf/market/getHiddenFundOutputList`, controller.werewolf.hiddenFundOutput.getHiddenFundOutputList);

}

export default load