/*
 * @Description: 操作流水
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2018-11-30 16:05:52
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2020-06-18 16:32:37
 */

import {Service} from 'egg';
import {
    IbannedListRequest,
    IopeAchRecord,
    IopeAchRecordRequest,
    IopeAchRecordResponse,
    IopeAvaRecord,
    IopeAvaRecordListRequest,
    IopeAvaRecordListResponse,
    IoperationListRequest
} from './../../model/werewolf';
import * as moment from 'moment';

export default class OperationdService extends Service {
    //资产操作总数
    public async propCount(req: IoperationListRequest): Promise<number> {
        try {
            const {app, ctx, logger} = this;
            const manager = app.mysql.get('manager');
            const uid = manager.escape(req.uid);
            const playerId = manager.escape(req.playerId);
            const result = await manager.query(
                ' SELECT sum(cnt) as count FROM(' +
                //' (select COUNT(id)AS cnt from wf_deduct_diamond WHERE admin_id ='+ uid +' and user_id = '+playerId+
                this.genPropCountSelect('wf_deduct_diamond', uid, playerId) +
                ' UNION ALL' +
                //' select COUNT(id)AS cnt from wf_deduct_item_once WHERE admin_id ='+ uid +' and user_id = '+playerId+
                this.genPropCountSelect('wf_deduct_item_once', uid, playerId) +
                ' UNION ALL' +
                //' select COUNT(id)AS cnt from wf_deduct_item_own WHERE admin_id ='+ uid +' and user_id = '+playerId+
                this.genPropCountSelect('wf_deduct_item_own', uid, playerId) +
                ' UNION ALL' +
                //' select COUNT(id)AS cnt from wf_grant_diamond WHERE admin_id ='+ uid +' and user_id = '+playerId+
                this.genPropCountSelect('wf_grant_diamond', uid, playerId) +
                ' UNION ALL' +
                //' select COUNT(id)AS cnt from wf_grant_item_once WHERE admin_id ='+ uid +' and user_id = '+playerId+
                this.genPropCountSelect('wf_grant_item_once', uid, playerId) +
                ' UNION ALL' +
                //' select COUNT(id)AS cnt from wf_grant_item_own WHERE admin_id ='+ uid +' and user_id = '+playerId+')'+
                this.genPropCountSelect('wf_grant_item_own', uid, playerId) +
                ' UNION ALL' +
                //' select COUNT(id)AS cnt from wf_grant_item_own WHERE admin_id ='+ uid +' and user_id = '+playerId+')'+
                this.genPropCountSelect('wf_2w_transac_record', uid, playerId) +
                ') AS count'
            );
            return result[0].count;
        } catch (err) {
            throw err;
        }
    }

    genPropCountSelect(table: string, uid: number, playerId: number) {
        if (uid > 0 && playerId > 0) {
            return ` SELECT COUNT(id)AS cnt FROM ${table} WHERE admin_id = ${uid} and user_id = ${playerId} `;
        } else {
            if (uid > 0) {
                return ` SELECT COUNT(id)AS cnt FROM ${table} WHERE admin_id = ${uid}`;
            }
            if (playerId > 0) {
                return ` SELECT COUNT(id)AS cnt FROM ${table} WHERE user_id = ${playerId}`;
            }
            return ` SELECT COUNT(id)AS cnt FROM ${table}`;
        }
    }

    //资产操作列表
    public async propList(req: IoperationListRequest) {
        try {
            const {app, ctx, logger} = this;
            const manager = app.mysql.get('manager');
            const uid = manager.escape(req.uid);
            const playerId = manager.escape(req.playerId);
            const start = manager.escape(req.start);
            const offset = manager.escape(req.offset);
            const sql =
                'SELECT a.*,wf_admin_user.nickname from ( ' +
                this.genPropListSelect('wf_deduct_diamond', uid, playerId) +
                ' UNION ALL' +
                this.genPropListSelect('wf_grant_diamond', uid, playerId) +
                ' UNION ALL' +
                this.genPropListSelect('wf_2w_transac_record', uid, playerId) +
                ' ORDER BY createtime DESC' +
                ' LIMIT ' +
                start +
                ',' +
                offset +
                ' ) a' +
                ' LEFT JOIN wf_admin_user ON a.admin_id = wf_admin_user.id';
            console.info("sql", sql);
            const result = await manager.query(sql);
            return result;
        } catch (err) {
            throw err;
        }
    }

    genPropListSelect(table: string, uid: number, playerId: number) {
        if (uid > 0 && playerId > 0) {
            return ` SELECT * ,0 AS item_id FROM ${table} WHERE admin_id = ${uid} and user_id = ${playerId} `;
        } else {
            if (uid > 0) {
                return ` SELECT * ,0 AS item_id FROM ${table} WHERE admin_id = ${uid}`;
            }
            if (playerId > 0) {
                return ` SELECT * ,0 AS item_id FROM ${table} WHERE user_id = ${playerId}`;
            }
            return ` SELECT * ,0 AS item_id FROM ${table}`;
        }
    }

    /**
     * 返回sql语句
     * @param request 客户端请求参数
     * @param type 总数/详细
     */
    bannedSql(
        uid: number,
        playerId: number,
        type: number,
        operation: number,
        start: number,
        offset: number,
        requestType: number
    ) {
        if (requestType == 0) {
            if (uid > 0) {
                if (playerId > 0) {
                    if (type > -1) {
                        if (operation > -1) {
                            return `SELECT COUNT(*) AS total FROM wf_user_ban WHERE admin_id = ${uid} AND user_id = ${playerId} AND type = ${type} AND operation = ${operation}`;
                        } else {
                            return `SELECT COUNT(*) AS total FROM wf_user_ban WHERE admin_id = ${uid} AND user_id = ${playerId} AND type = ${type}`;
                        }
                    } else {
                        if (operation > -1) {
                            return `SELECT COUNT(*) AS total FROM wf_user_ban WHERE admin_id = ${uid} AND user_id = ${playerId} AND operation = ${operation}`;
                        } else {
                            return `SELECT COUNT(*) AS total FROM wf_user_ban WHERE admin_id = ${uid} AND user_id = ${playerId}`;
                        }
                    }
                } else {
                    if (type > -1) {
                        if (operation > -1) {
                            return `SELECT COUNT(*) AS total FROM wf_user_ban WHERE admin_id = ${uid} AND type = ${type} AND operation = ${operation}`;
                        } else {
                            return `SELECT COUNT(*) AS total FROM wf_user_ban WHERE admin_id = ${uid} AND type = ${type}`;
                        }
                    } else {
                        if (operation > -1) {
                            return `SELECT COUNT(*) AS total FROM wf_user_ban WHERE admin_id = ${uid} AND operation = ${operation}`;
                        } else {
                            return `SELECT COUNT(*) AS total FROM wf_user_ban WHERE admin_id = ${uid}`;
                        }
                    }
                }
            } else {
                if (playerId > 0) {
                    if (type > -1) {
                        if (operation > -1) {
                            return `SELECT COUNT(*) AS total FROM wf_user_ban WHERE user_id = ${playerId} AND type = ${type} AND operation = ${operation}`;
                        } else {
                            return `SELECT COUNT(*) AS total FROM wf_user_ban WHERE user_id = ${playerId} AND type = ${type}`;
                        }
                    } else {
                        if (operation > -1) {
                            return `SELECT COUNT(*) AS total FROM wf_user_ban WHERE user_id = ${playerId} AND operation = ${operation}`;
                        } else {
                            return `SELECT COUNT(*) AS total FROM wf_user_ban WHERE user_id = ${playerId}`;
                        }
                    }
                } else {
                    if (type > -1) {
                        if (operation > -1) {
                            return `SELECT COUNT(*) AS total FROM wf_user_ban WHERE type = ${type} AND operation = ${operation}`;
                        } else {
                            return `SELECT COUNT(*) AS total FROM wf_user_ban WHERE type = ${type}`;
                        }
                    } else {
                        if (operation > -1) {
                            return `SELECT COUNT(*) AS total FROM wf_user_ban WHERE operation = ${operation}`;
                        } else {
                            return `SELECT COUNT(*) AS total FROM wf_user_ban`;
                        }
                    }
                }
            }
        } else if (requestType == 1) {
            if (uid > 0) {
                if (playerId > 0) {
                    if (type > -1) {
                        if (operation > -1) {
                            return `SELECT wau.nickname,wub.* FROM wf_user_ban wub LEFT JOIN wf_admin_user wau ON wub.admin_id = wau.id WHERE wub.admin_id = ${uid} AND wub.user_id = ${playerId} AND wub.type = ${type} AND wub.operation = ${operation} ORDER BY createtime DESC LIMIT ${start},${offset}`;
                        } else {
                            return `SELECT wau.nickname,wub.* FROM wf_user_ban wub LEFT JOIN wf_admin_user wau ON wub.admin_id = wau.id WHERE wub.admin_id = ${uid} AND wub.user_id = ${playerId} AND wub.type = ${type} ORDER BY createtime DESC LIMIT ${start},${offset}`;
                        }
                    } else {
                        if (operation > -1) {
                            return `SELECT wau.nickname,wub.* FROM wf_user_ban wub LEFT JOIN wf_admin_user wau ON wub.admin_id = wau.id WHERE wub.admin_id = ${uid} AND wub.user_id = ${playerId} AND wub.operation = ${operation} ORDER BY createtime DESC LIMIT ${start},${offset}`;
                        } else {
                            return `SELECT wau.nickname,wub.* FROM wf_user_ban wub LEFT JOIN wf_admin_user wau ON wub.admin_id = wau.id WHERE wub.admin_id = ${uid} AND wub.user_id = ${playerId} ORDER BY createtime DESC LIMIT ${start},${offset}`;
                        }
                    }
                } else {
                    if (type > -1) {
                        if (operation > -1) {
                            return `SELECT wau.nickname,wub.* FROM wf_user_ban wub LEFT JOIN wf_admin_user wau ON wub.admin_id = wau.id WHERE wub.admin_id = ${uid} AND wub.type = ${type} AND wub.operation = ${operation} ORDER BY createtime DESC LIMIT ${start},${offset}`;
                        } else {
                            return `SELECT wau.nickname,wub.* FROM wf_user_ban wub LEFT JOIN wf_admin_user wau ON wub.admin_id = wau.id WHERE wub.admin_id = ${uid} AND wub.type = ${type} ORDER BY createtime DESC LIMIT ${start},${offset}`;
                        }
                    } else {
                        if (operation > -1) {
                            return `SELECT wau.nickname,wub.* FROM wf_user_ban wub LEFT JOIN wf_admin_user wau ON wub.admin_id = wau.id WHERE wub.admin_id = ${uid} AND wub.operation = ${operation} ORDER BY createtime DESC LIMIT ${start},${offset}`;
                        } else {
                            return `SELECT wau.nickname,wub.* FROM wf_user_ban wub LEFT JOIN wf_admin_user wau ON wub.admin_id = wau.id WHERE wub.admin_id = ${uid} ORDER BY createtime DESC LIMIT ${start},${offset}`;
                        }
                    }
                }
            } else {
                if (playerId > 0) {
                    if (type > -1) {
                        if (operation > -1) {
                            return `SELECT wau.nickname,wub.* FROM wf_user_ban wub LEFT JOIN wf_admin_user wau ON wub.admin_id = wau.id WHERE wub.user_id = ${playerId} AND wub.type = ${type} AND wub.operation = ${operation} ORDER BY createtime DESC LIMIT ${start},${offset}`;
                        } else {
                            return `SELECT wau.nickname,wub.* FROM wf_user_ban wub LEFT JOIN wf_admin_user wau ON wub.admin_id = wau.id WHERE wub.user_id = ${playerId} AND wub.type = ${type} ORDER BY createtime DESC LIMIT ${start},${offset}`;
                        }
                    } else {
                        if (operation > -1) {
                            return `SELECT wau.nickname,wub.* FROM wf_user_ban wub LEFT JOIN wf_admin_user wau ON wub.admin_id = wau.id WHERE wub.user_id = ${playerId} AND wub.operation = ${operation} ORDER BY createtime DESC LIMIT ${start},${offset}`;
                        } else {
                            return `SELECT wau.nickname,wub.* FROM wf_user_ban wub LEFT JOIN wf_admin_user wau ON wub.admin_id = wau.id WHERE wub.user_id = ${playerId} ORDER BY createtime DESC LIMIT ${start},${offset}`;
                        }
                    }
                } else {
                    if (type > -1) {
                        if (operation > -1) {
                            return `SELECT wau.nickname,wub.* FROM wf_user_ban wub LEFT JOIN wf_admin_user wau ON wub.admin_id = wau.id WHERE wub.type = ${type} AND wub.operation = ${operation} ORDER BY createtime DESC LIMIT ${start},${offset}`;
                        } else {
                            return `SELECT wau.nickname,wub.* FROM wf_user_ban wub LEFT JOIN wf_admin_user wau ON wub.admin_id = wau.id WHERE wub.type = ${type} ORDER BY createtime DESC LIMIT ${start},${offset}`;
                        }
                    } else {
                        if (operation > -1) {
                            return `SELECT wau.nickname,wub.* FROM wf_user_ban wub LEFT JOIN wf_admin_user wau ON wub.admin_id = wau.id WHERE wub.operation = ${operation} ORDER BY createtime DESC LIMIT ${start},${offset}`;
                        } else {
                            return `SELECT wau.nickname,wub.* FROM wf_user_ban wub LEFT JOIN wf_admin_user wau ON wub.admin_id = wau.id ORDER BY createtime DESC LIMIT ${start},${offset}`;
                        }
                    }
                }
            }
        }
    }

    /**
     * 获取当前条件下的用户操作流水总数
     * @param request
     */
    public async bannedCount(request: IbannedListRequest) {
        const {app} = this;
        const manager = app.mysql.get('manager');

        const uid = manager.escape(request.uid);
        const playerId = manager.escape(request.playerId);
        const type = manager.escape(request.type);
        const operation = manager.escape(request.operation);
        const start = manager.escape(request.start);
        const offset = manager.escape(request.offset);

        const sql = this.bannedSql(uid, playerId, type, operation, start, offset, 0);
        const result = await manager.query(sql);
        if (!!result && result.length > 0) {
            return result[0].total;
        }
        return 0;
    }

    /**
     * 获取当前条件下的操作流水详细信息
     * @param request
     */
    public async bannedList(request: IbannedListRequest) {
        const {app} = this;
        const manager = app.mysql.get('manager');

        const uid = manager.escape(request.uid);
        const playerId = manager.escape(request.playerId);
        const type = manager.escape(request.type);
        const operation = manager.escape(request.operation);
        const start = manager.escape(request.start);
        const offset = manager.escape(request.offset);

        const sql = this.bannedSql(uid, playerId, type, operation, start, offset, 1);
        const result = await manager.query(sql);
        if (!!result && result.length > 0) {
            let array = new Array();
            for (let item of result) {
                //let category = '';
                // switch (item.category) {
                // 	case 0:
                // 		category = '刷分';
                // 		break;
                // 	case 1:
                // 		category = '发言背景图';
                // 		break;
                // 	case 2:
                // 		category = '游戏中发言';
                // 		break;
                // 	case 3:
                // 		category = '逃跑';
                // 		break;
                // 	case 4:
                // 		category = '违规头像';
                // 		break;
                // }
                array.push({
                    userName: item.nickname,
                    playerId: item.user_id,
                    type: item.type,
                    category: item.category,
                    operation: item.operation,
                    desc: item.desc,
                    createtime: moment(item.createtime).format('YYYY-MM-DD HH:mm:ss'),
                    delsignStr: item.delsign
                });
            }
            return array;
        }
        return [];
    }

    /**
     * @name: 操作头像流水
     * @msg:
     * @param {type}
     * @return:
     */
    public async getAvaOpeRecord(IopeAvaRecordList: IopeAvaRecordListRequest): Promise<IopeAvaRecordListResponse> {
        const {app, logger} = this;
        const db = app.mysql.get('manager');
        const uid = db.escape(IopeAvaRecordList.uid);
        const playerid = db.escape(IopeAvaRecordList.playerId);
        const start = db.escape(IopeAvaRecordList.start);
        const offset = db.escape(IopeAvaRecordList.offset);
        const otype = db.escape(IopeAvaRecordList.otype);

        let count = 0;
        let sqlcount = getCountSql(uid, playerid, otype);
        let result = await db.query(sqlcount);
        if (!!result && result.length > 0) {
            count = result[0].total;
        }
        const results = await db.query(getListSql(uid, playerid, start, offset, otype));
        let array: IopeAvaRecord[] = new Array();
        if (!!results && results.length > 0) {
            for (let i = 0, len = results.length; i < len; i++) {
                array.push({
                    nickname: results[i].nickname,
                    user_id: results[i].user_id,
                    avatar_frame_id: results[i].avatar_frame_id,
                    avatar_frame_name: results[i].avatar_frame_name,
                    type: results[i].type,
                    create_time: moment(results[i].create_time).format("YYYY-MM-DD HH:mm:ss"),
                });
            }
        }
        if (!!results && results.length > 0) {
            return {
                start: IopeAvaRecordList.start,
                offset: IopeAvaRecordList.offset,
                count: count,
                list: array
            };
        } else {
            return {
                start: IopeAvaRecordList.start,
                offset: IopeAvaRecordList.offset,
                count: 0,
                list: []
            };
        }

        function getCountSql(uid, playerid, type) {
            if (uid <= 0) {
                if (playerid <= 0) {
                    if (type < 0) {
                        return `SELECT COUNT(*) AS total FROM wf_admin_avatar_frame`;
                    } else {
                        return `SELECT COUNT(*) AS total FROM wf_admin_avatar_frame WHERE type = ${type}`;
                    }
                } else {
                    if (type < 0) {
                        return `SELECT COUNT(*) AS total FROM wf_admin_avatar_frame WHERE user_id = ${playerid}`;
                    } else {
                        return `SELECT COUNT(*) AS total FROM wf_admin_avatar_frame WHERE user_id = ${playerid} AND type = ${type}`;
                    }
                }
            } else {
                if (playerid <= 0) {
                    if (type < 0) {
                        return `SELECT COUNT(*) AS total FROM wf_admin_avatar_frame WHERE admin_id = ${uid}`;
                    } else {
                        return `SELECT COUNT(*) AS total FROM wf_admin_avatar_frame WHERE admin_id = ${uid} AND type = ${type}`;
                    }
                } else {
                    if (type < 0) {
                        return `SELECT COUNT(*) AS total FROM wf_admin_avatar_frame WHERE user_id = ${playerid} AND admin_id = ${uid}`;
                    } else {
                        return `SELECT COUNT(*) AS total FROM wf_admin_avatar_frame WHERE user_id = ${playerid} AND admin_id = ${uid} AND type = ${type}`;
                    }
                }
            }
        }

        function getListSql(uid, playerid, start, offset, type) {
            if (uid <= 0) {
                if (playerid <= 0) {
                    if (type < 0) {
                        return `SELECT b.nickname,a.user_id,a.avatar_frame_id,a.avatar_frame_name,a.type,a.create_time FROM wf_admin_avatar_frame a  LEFT JOIN wf_admin_user b ON a.admin_id = b.id ORDER BY create_time DESC LIMIT ${start} , ${offset} `;
                    } else {
                        return `SELECT b.nickname,a.user_id,a.avatar_frame_id,a.avatar_frame_name,a.type,a.create_time FROM wf_admin_avatar_frame a  LEFT JOIN wf_admin_user b ON a.admin_id = b.id WHERE a.type = ${type} ORDER BY create_time DESC LIMIT ${start} , ${offset} `;
                    }
                } else {
                    if (type < 0) {
                        return `SELECT b.nickname,a.user_id,a.avatar_frame_id,a.avatar_frame_name,a.type,a.create_time FROM wf_admin_avatar_frame a  LEFT JOIN wf_admin_user b ON a.admin_id = b.id  WHERE a.user_id = ${playerid} ORDER BY create_time DESC LIMIT  ${start},${offset} `;
                    } else {
                        return `SELECT b.nickname,a.user_id,a.avatar_frame_id,a.avatar_frame_name,a.type,a.create_time FROM wf_admin_avatar_frame a  LEFT JOIN wf_admin_user b ON a.admin_id = b.id  WHERE a.user_id = ${playerid} AND a.type = ${type} ORDER BY create_time DESC LIMIT  ${start},${offset} `;
                    }
                }
            } else {
                if (playerid <= 0) {
                    if (type < 0) {
                        return `SELECT b.nickname,a.user_id,a.avatar_frame_id,a.avatar_frame_name,a.type,a.create_time FROM wf_admin_avatar_frame a  LEFT JOIN wf_admin_user b ON a.admin_id = b.id  WHERE a.admin_id = ${uid} ORDER BY create_time DESC  LIMIT ${start},${offset} `;
                    } else {
                        return `SELECT b.nickname,a.user_id,a.avatar_frame_id,a.avatar_frame_name,a.type,a.create_time FROM wf_admin_avatar_frame a  LEFT JOIN wf_admin_user b ON a.admin_id = b.id  WHERE a.admin_id = ${uid} AND a.type = ${type} ORDER BY create_time DESC  LIMIT ${start},${offset}`;
                    }
                } else {
                    if (type < 0) {
                        return `SELECT b.nickname,a.user_id,a.avatar_frame_id,a.avatar_frame_name,a.type,a.create_time FROM wf_admin_avatar_frame a  LEFT JOIN wf_admin_user b ON a.admin_id = b.id  WHERE a.user_id = ${playerid} AND a.admin_id = ${uid} ORDER BY create_time DESC LIMIT ${start},${offset}`;
                    } else {
                        return `SELECT b.nickname,a.user_id,a.avatar_frame_id,a.avatar_frame_name,a.type,a.create_time FROM wf_admin_avatar_frame a  LEFT JOIN wf_admin_user b ON a.admin_id = b.id  WHERE a.user_id = ${playerid} AND a.admin_id = ${uid} AND a.type = ${type}  ORDER BY create_time DESC LIMIT ${start},${offset}`;
                    }
                }
            }
        }
    }

    /**
     * @name: 操作成就流水
     * @msg:
     * @param {type}
     * @return:
     */
    public async getAchOpeRecord(IopeAchRecordList: IopeAchRecordRequest): Promise<IopeAchRecordResponse> {
        const {app, logger} = this;
        const db = app.mysql.get('manager');
        const uid = db.escape(IopeAchRecordList.uid);
        const playerId = db.escape(IopeAchRecordList.playerId);
        const start = db.escape(IopeAchRecordList.start);
        const offset = db.escape(IopeAchRecordList.offset);
        const type = db.escape(IopeAchRecordList.type);
        let count = 0;
        let sqlcount = getAchOpeCountSql(uid, playerId, type);
        let result = await db.query(sqlcount);
        if (!!result && result.length > 0) {
            count = result[0].total;
        }
        const results = await db.query(getAchOpeListSql(uid, playerId, start, offset, type));
        let array: IopeAchRecord[] = new Array();
        if (!!results && results.length > 0) {
            for (let i = 0, len = results.length; i < len; i++) {
                array.push({
                    nickname: results[i].nickname,
                    user_id: results[i].user_id,
                    achieve_id: results[i].achieve_id,
                    achieve_name: results[i].achieve_name,
                    type: results[i].type,
                    create_time: moment(results[i].create_time).format("YYYY-MM-DD HH:mm:ss"),
                });
            }
        }
        if (!!results && results.length > 0) {
            return {
                start: IopeAchRecordList.start,
                offset: IopeAchRecordList.offset,
                count: count,
                list: array
            };
        } else {
            return {
                start: IopeAchRecordList.start,
                offset: IopeAchRecordList.offset,
                count: 0,
                list: []
            };
        }

        function getAchOpeCountSql(uid, playerId, type) {
            if (uid <= 0) {
                if (playerId <= 0) {
                    if (type < 0) {
                        return 'SELECT COUNT(*) AS total FROM wf_admin_achieve';
                    } else {
                        return `SELECT COUNT(*) AS total FROM wf_admin_achieve WHERE type = ${type}`;
                    }
                } else {
                    if (type < 0) {
                        return `SELECT COUNT(*) AS total FROM wf_admin_achieve WHERE user_id = ${playerId}`;
                    } else {
                        return `SELECT COUNT(*) AS total FROM wf_admin_achieve WHERE user_id = ${playerId} AND type = ${type}`;
                    }
                }
            } else {
                if (playerId <= 0) {
                    if (type < 0) {
                        return `SELECT COUNT(*) AS total FROM wf_admin_achieve WHERE admin_id =  ${uid} `;
                    } else {
                        return `SELECT COUNT(*) AS total FROM wf_admin_achieve WHERE admin_id =  ${uid}  AND type = ${type}`;
                    }
                } else {
                    if (type < 0) {
                        return `SELECT COUNT(*) AS total FROM wf_admin_achieve WHERE user_id = ${playerId} + AND admin_id = ${uid}`;
                    } else {
                        return `SELECT COUNT(*) AS total FROM wf_admin_achieve WHERE user_id = ${playerId} + AND admin_id = ${uid} AND type = ${type}`
                    }
                }
            }
        }

        function getAchOpeListSql(uid, playerid, start, offset, type) {
            if (uid <= 0) {
                if (playerid <= 0) {
                    if (type < 0) {
                        return `SELECT b.nickname,a.user_id,a.achieve_id,a.achieve_name,a.type,a.create_time FROM wf_admin_achieve a  LEFT JOIN wf_admin_user b ON a.admin_id = b.id ORDER BY create_time DESC LIMIT ${start} , ${offset} `;
                    } else {
                        return `SELECT b.nickname,a.user_id,a.achieve_id,a.achieve_name,a.type,a.create_time FROM wf_admin_achieve a  LEFT JOIN wf_admin_user b ON a.admin_id = b.id WHERE a.type = ${type} ORDER BY create_time DESC LIMIT ${start} , ${offset} `;
                    }
                } else {
                    if (type < 0) {
                        return `SELECT b.nickname,a.user_id,a.achieve_id,a.achieve_name,a.type,a.create_time FROM wf_admin_achieve a  LEFT JOIN wf_admin_user b ON a.admin_id = b.id  WHERE a.user_id = ${playerid} ORDER BY create_time DESC LIMIT  ${start},${offset} `;
                    } else {
                        return `SELECT b.nickname,a.user_id,a.achieve_id,a.achieve_name,a.type,a.create_time FROM wf_admin_achieve a  LEFT JOIN wf_admin_user b ON a.admin_id = b.id  WHERE a.user_id = ${playerid} AND a.type = ${type} ORDER BY create_time DESC LIMIT  ${start},${offset} `;
                    }
                }
            } else {
                if (playerid <= 0) {
                    if (type < 0) {
                        return `SELECT b.nickname,a.user_id,a.achieve_id,a.achieve_name,a.type,a.create_time FROM wf_admin_achieve a  LEFT JOIN wf_admin_user b ON a.admin_id = b.id  WHERE a.admin_id = ${uid} ORDER BY create_time DESC  LIMIT ${start},${offset} `;
                    } else {
                        return `SELECT b.nickname,a.user_id,a.achieve_id,a.achieve_name,a.type,a.create_time FROM wf_admin_achieve a  LEFT JOIN wf_admin_user b ON a.admin_id = b.id  WHERE a.admin_id = ${uid} AND a.type = ${type} ORDER BY create_time DESC  LIMIT ${start},${offset} `;
                    }
                } else {
                    if (type < 0) {
                        return `SELECT b.nickname,a.user_id,a.achieve_id,a.achieve_name,a.type,a.create_time FROM wf_admin_achieve a  LEFT JOIN wf_admin_user b ON a.admin_id = b.id  WHERE a.user_id = ${playerid} AND a.admin_id = ${uid} ORDER BY create_time DESC LIMIT ${start},${offset} `;
                    } else {
                        return `SELECT b.nickname,a.user_id,a.achieve_id,a.achieve_name,a.type,a.create_time FROM wf_admin_achieve a  LEFT JOIN wf_admin_user b ON a.admin_id = b.id  WHERE a.user_id = ${playerid} AND a.admin_id = ${uid} AND a.type = ${type} ORDER BY create_time DESC LIMIT ${start},${offset} `;
                    }
                }
            }
        }
    }

    public async getOptionList(request) {
        const {app, logger} = this;
        const manager = app.mysql.get('werewolf');
        const uid = manager.escape(request.uid);
        const playerId = manager.escape(request.playerId);
        logger.info(uid);
        try {
            let sql = `SELECT idl.id AS id,t.nickname AS nickname,id.user_list AS userList,
					idl.msg AS msg,idl.create_time AS createTime,ida.num AS itemNum,
					idc.name AS itemName,id.reason AS reason
					FROM item_distribution_log idl
					LEFT JOIN mega_manager.wf_admin_user t ON idl.user_id = t.id
					LEFT JOIN item_distribution id ON idl.item_distribution_id = id.id
					LEFT JOIN item_distribution_award ida ON id.item_distribution_award_type = ida.type
					LEFT JOIN item_dic idc ON ida.item_dic_id = idc.id
					`;
            if (uid != -1 && playerId != 0) {
                sql += `WHERE idl.user_id = ${uid} AND id.user_list like '%${playerId}%'`;
            }else if (uid == -1 && playerId != 0){
                sql += `WHERE  id.user_list like '%${playerId}%'`;
            }else if (uid != -1 && playerId == 0){
                sql += `WHERE idl.user_id = ${uid}`;
            }
            sql += ` ORDER BY idl.create_time DESC`;
            let result = await manager.query(sql);
            logger.info(result);
            return result;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }
}
