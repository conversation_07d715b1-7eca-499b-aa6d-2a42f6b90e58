import { Application, Context, EggAppConfig } from 'egg';
import { IerrorMsg, HttpErr, Ipayload } from "../model/common";

const unAuthResp: IerrorMsg = { err_code: HttpErr.Unauthorized, err_msg: "未鉴权" };
/**
 * @name:自定义处理jwt 
 * @msg: 
 * @param {type} 
 * @return: 
 */

export default function customJwtMiddleWare(options: EggAppConfig['customJwt'], app: Application): any {
    return async (ctx: Context, next: () => Promise<any>) => {
        //ctx.logger.info('调用中间件customJwt'); 
        let token = ctx.request.get("Authorization");
        //存在token
        if (!!token) {
            token = token.replace("Bearer ", "");
            //console.info("中间件" + token);
            //校验token
            try {
                const decode = app['jwt'].verify(token, app.config.jwt.secret) as any;
                const payload: Ipayload = { ...decode };
                // console.info('ctx.request.origin',ctx.request.origin);
                // console.info('ctx.request.Referer',ctx.request.get('Referer'));
                //{uid: decode.uid, ip: decode.ip, host: decode.host};
                //判断ip
                // if (payload.ip !== ctx.request.ip){
                //     ctx.logger.error(`ip error,payloadIp:${payload.ip}, requestIp:${ctx.request.ip}`);
                //     handleUnAuth(ctx);
                //     return;
                // }
                //判断host
                // if (payload.host !== ctx.request.host){
                //     ctx.logger.error(`host error,payloadHost:${payload.host}, requestHost:${ctx.request.host}`);
                //     handleUnAuth(ctx);
                //     return;
                // }
                // ctx.logger.info("payload", payload);
                //判断从redis获得token
                const redisToekn = await ctx.service.user.getRedisToken(payload.uid, payload.tokenId);
                //console.info('redisToekn',redisToekn);
                if (!redisToekn) {
                    handleUnAuth(ctx);
                    return;
                }
                //payload添加到ctx，给业务层使用
                ctx.helper.exPayload = payload;

            } catch (err) {
                ctx.logger.info("err", err);
                handleUnAuth(ctx);
                return;
            }
        } else {
            handleUnAuth(ctx);
            return;
        }
        await next();
    };
}

function handleUnAuth(ctx: Context) {
    ctx.body = unAuthResp;
    ctx.status = HttpErr.Unauthorized;
}
