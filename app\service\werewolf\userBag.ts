/*
 * @Description: 商城道具服务类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: z<PERSON>yu
 * @Date: 2020-04-24 11:35:39
 * @LastEditors: jiawen.wang
 * @LastEditTime: 2021-12-23 13:45:43
 */
import BaseMegaService from './BaseMegaService'
export default class UserBagService extends BaseMegaService {
    public async getUserCoin(req) {
        const { app, ctx, logger } = this
        try {
            let dataList: any = []

            let sqlDiamond = `
                SELECT accountnum, coinnum FROM tuser_props WHERE userno = ?
            `
            let dataDiamond = await this.selectOne(sqlDiamond, [req.userId])
            dataList.push({
                id: 1,
                name: '钻石',
                img_name: 'diamond_icon.png',
                item_cate: 1,
                num: dataDiamond.accountnum,
            })
            dataList.push({
                id: 2,
                name: '兑换币',
                img_name: 'box_coin.png',
                item_cate: 1,
                num: dataDiamond.coinnum,
            })

            let sql = ` 
            SELECT 
                c.id AS id, c.\`name\`, IFNULL(u.num,0) AS num, c.img_name AS img_name, 1 AS item_cate
            FROM 
                user_coin u
            RIGHT JOIN coin c ON c.id = u.coin_id AND user_id = ? AND delsign = 0
                WHERE c.id > 2 AND c.id != 7
                ORDER BY c.id ASC`
            const coinData = await this.selectList(sql, [req.userId])

            dataList.push(...coinData)
            return dataList
        } catch (error) {
            logger.error(error)
            throw error
        }
    }

    public async getUserCoupon(req) {
        const { app, ctx, logger } = this
        try {
            let sql = ` 
            SELECT 
            c.id AS id, c.\`name\`, IFNULL(u.num,0) AS num, c.pic AS couponPic, c.sex, 12000 AS cate_id
            FROM 
                user_coupon u
            INNER JOIN coupon c ON c.id = u.coupon_id AND user_id = ? AND c.delsign = 0 AND u.delsign = 0 AND c.sex = ?
            ORDER BY u.update_time DESC
            `
            return await this.selectList(sql, [req.userId, req.sex])
        } catch (error) {
            logger.error(error)
            throw error
        }
    }

    public async getUserGift(req) {
        const { app, ctx, logger } = this
        try {
            let sql = ` 
            SELECT 
                g.\`no\` AS id, g.\`no\` AS item_id, g.\`name\`, IFNULL(u.num,0) AS num, id.item_cate_id AS cate_id
            FROM 
                user_gift u
            INNER JOIN gift g ON g.\`no\` = u.gift_id AND user_id = ? AND u.delsign = 0
            INNER JOIN item_dic id ON id.item_id = u.gift_id AND id.item_cate_id IN (4010,4020,4030,4040,9050)
            ORDER BY u.updatetime DESC
            `
            return await this.selectList(sql, [req.userId])
        } catch (error) {
            logger.error(error)
            throw error
        }
    }

    public async getUserItems(req) {
        const { app, ctx, logger } = this
        try {
            let sql = ` 
            SELECT 
            n.\`no\` AS id, n.\`name\`, IFNULL(u.num,0) AS num, n.img_name AS normalImg, 5010 AS cate_id
            FROM 
                user_items u
            INNER JOIN normal_item n ON n.\`no\` = u.item_id AND user_id = ? AND u.delsign = 0
            `
            return await this.selectList(sql, [req.userId])
        } catch (error) {
            logger.error(error)
            throw error
        }
    }

    public async getUserAvatarframe(req) {
        const { app, ctx, logger } = this
        try {
            let sql = ` 
            SELECT 
                a.\`id\` AS id, a.\`id\` AS item_id, a.\`name\`, 1 AS num, 2010 AS cate_id
            FROM 
                user_avatarframe u
            INNER JOIN avatarframe a ON a.\`id\` = u.avatarframe_id AND u.user_id = ? AND u.delsign = 0
            `
            return await this.selectList(sql, [req.userId])
        } catch (error) {
            logger.error(error)
            throw error
        }
    }

    public async getUserAchievement(req) {
        const { app, ctx, logger } = this
        try {
            let sql = ` 
            SELECT 
                a.\`id\` AS id, a.\`name\`, u.complete_num AS num, 9010 AS cate_id
            FROM 
                user_achievement u
            INNER JOIN achievement a ON a.\`id\` = u.achievement_id AND u.user_id = ? AND u.delsign = 0 AND a.uplevel <= u.\`level\`
            `
            return await this.selectList(sql, [req.userId])
        } catch (error) {
            logger.error(error)
            throw error
        }
    }

    public async getUserAnimation(req) {
        const { app, ctx, logger } = this
        try {
            let sql = ` 
            SELECT 
                a.\`id\` AS id, a.\`id\` AS item_id, a.\`name\`,  id.item_cate_id AS cate_id, a.role, 1 AS num
            FROM 
                user_animation u
            INNER JOIN animation a ON a.\`id\` = u.animation_id AND u.user_id = ? AND u.delsign = 0 AND a.role = ?
            INNER JOIN item_dic id ON id.item_id = u.animation_id AND id.item_cate_id IN (1010,1012,1020,1031,1060,1070,1080,1091,1092,1093,3010,3020)
            `
            return await this.selectList(sql, [req.userId, req.role])
        } catch (error) {
            logger.error(error)
            throw error
        }
    }
    public async getUserLiveAvatar(req) {
        const { app, ctx, logger } = this
        const db = app.mysql.get('werewolf')

        try {

            if(req.userId != ""){
                let sql = `SELECT id, headicon , isUse FROM live_avatar WHERE delsign = 0 AND user_id = ${req.userId}`
                // return await this.selectList(sql, [req.userId])
                const recordList = await db.query(sql)
                return recordList
            }

         
        } catch (error) {
            logger.error(error)
            throw error
        }
    }


    public async getGiftContent(req) {
        const { app, ctx, logger } = this
        const db = app.mysql.get('werewolf')

        try {

            if(req.userId != ""){
                let sql = `SELECT * FROM user_gift_msg_record WHERE user_id = ${req.user_id}`
                // return await this.selectList(sql, [req.userId])
                const recordList = await db.query(sql)
                return recordList
            }

         
        } catch (error) {
            logger.error(error)
            throw error
        }
    }

}
