/*
 * @Description: 新建推送模块
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-03-18 13:28:06
 * @LastEditors: hammercui
 * @LastEditTime: 2020-07-22 11:34:04
 */

import { Controller } from 'egg';
import BaseMegaController from './BaseMegaController';
import { IpushRecord, IpushDelReq } from '../../model/werewolfPush';

export default class PushCreateClass extends BaseMegaController {

    /**
     * @name: 新建推送
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async create() {
        const { ctx, logger } = this;
        const rule = {
            content: { type: 'string' },
            title: { type: 'string' },
            push_time: { type: 'string' },
            receive_type: { type: 'number' }
        }
        try {
            ctx.validate(rule);
            const request: IpushRecord = ctx.request.body;
            await ctx.service.werewolf.pushCreate.create(request);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }

    /**
     * @name: 推送列表
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async list() {
        const { ctx, logger } = this;
        try {
            const data: IpushRecord[] = await ctx.service.werewolf.pushCreate.list();
            this.respSuccData(data)
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }

    /**
     * @name: 编辑推送
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async edit() {
        const { ctx, logger } = this;
        const rule = {
            content: { type: 'string' },
            title: { type: 'string' },
            push_time: { type: 'string' },
            receive_type: { type: 'number' }
        }
        try {
            ctx.validate(rule);
            const request: IpushRecord = ctx.request.body;
            await ctx.service.werewolf.pushCreate.edit(request);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }

    public async delete() {
        const { ctx, logger } = this;
        const rule = {
            id: { type: 'number' },
        }
        try {
            ctx.validate(rule);
            const request: IpushDelReq = ctx.request.body;
            await ctx.service.werewolf.pushCreate.delete(request.id);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }
    public async test() {
        const { ctx, logger } = this;
        try {
            await ctx.service.werewolf.pushCreate.test();
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }
}
