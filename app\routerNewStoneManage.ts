import { Router, Application } from 'egg'
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {
    const { controller, router } = app
    router.post(`${API_VERSION}/werewolf/newStoneManage/getStoneList`, controller.werewolf.newStoneManage.getStoneList)
    router.post(`${API_VERSION}/werewolf/newStoneManage/createStoneList`, controller.werewolf.newStoneManage.createStoneList)
    router.post(`${API_VERSION}/werewolf/newStoneManage/updateStoneList`, controller.werewolf.newStoneManage.updateStoneList)

    router.post(`${API_VERSION}/werewolf/newStoneManage/getSeasonPoolList`, controller.werewolf.newStoneManage.getSeasonPoolList)
    router.post(`${API_VERSION}/werewolf/newStoneManage/createSeasonPoolList`, controller.werewolf.newStoneManage.createSeasonPoolList)
    router.post(`${API_VERSION}/werewolf/newStoneManage/updateSeasonPoolList`, controller.werewolf.newStoneManage.updateSeasonPoolList)

    router.post(`${API_VERSION}/werewolf/newStoneManage/getRuleList`, controller.werewolf.newStoneManage.getRuleList)
    router.post(`${API_VERSION}/werewolf/newStoneManage/createRuleList`, controller.werewolf.newStoneManage.createRuleList)
    router.post(`${API_VERSION}/werewolf/newStoneManage/updateRuleList`, controller.werewolf.newStoneManage.updateRuleList)

    router.post(`${API_VERSION}/werewolf/newStoneManage/getStoneRuleList`, controller.werewolf.newStoneManage.getStoneRuleList)
    router.post(`${API_VERSION}/werewolf/newStoneManage/createStoneRuleList`, controller.werewolf.newStoneManage.createStoneRuleList)
    router.post(`${API_VERSION}/werewolf/newStoneManage/updateStoneRuleList`, controller.werewolf.newStoneManage.updateStoneRuleList)

    
    
    
}

export default load
