/*
 * @Description: 商城道具路由
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: z<PERSON><PERSON>
 * @Date: 2020-04-24 11:50:11
 * @LastEditors: jiawen.wang
 * @LastEditTime: 2021-12-23 11:31:08
 */
import { Router, Application } from 'egg'
import { AccessRouteId, AccessRouteName } from './model/accessRouteCof'
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {
    const { controller, router } = app

    router.post(`${API_VERSION}/werewolf/userBag/getUserCoin`, controller.werewolf.userBag.getUserCoin)
    router.post(`${API_VERSION}/werewolf/userBag/getUserCoupon`, controller.werewolf.userBag.getUserCoupon)
    router.post(`${API_VERSION}/werewolf/userBag/getUserGift`, controller.werewolf.userBag.getUserGift)
    router.post(`${API_VERSION}/werewolf/userBag/getUserItems`, controller.werewolf.userBag.getUserItems)
    router.post(`${API_VERSION}/werewolf/userBag/getUserAvatarframe`, controller.werewolf.userBag.getUserAvatarframe)
    router.post(`${API_VERSION}/werewolf/userBag/getUserAchievement`, controller.werewolf.userBag.getUserAchievement)
    router.post(`${API_VERSION}/werewolf/userBag/getUserAnimation`, controller.werewolf.userBag.getUserAnimation)
    router.post(`${API_VERSION}/werewolf/userBag/getUserLiveAvatar`, controller.werewolf.userBag.getUserLiveAvatar)
    router.post(`${API_VERSION}/werewolf/userBag/getGiftContent`, controller.werewolf.userBag.getGiftContent)

}

export default load
