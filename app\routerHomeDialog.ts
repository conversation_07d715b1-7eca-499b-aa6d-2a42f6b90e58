/*
 * @Description: 首页弹框路由
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-03-24 11:50:11
 * @LastEditors: hammercui
 * @LastEditTime: 2020-03-24 13:34:39
 */
import { Router, Application } from "egg";
import { AccessRouteId, AccessRouteName } from "./model/accessRouteCof";
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

    const { controller, router } = app;
    //【应用管理】首页弹窗
    //新建
    router.post(`${API_VERSION}/werewolf/homeDialog/create`, accCtr(AccessRouteId.wolf_home_dialog), controller.werewolf.homeDialog.create)
    //列表
    router.post(`${API_VERSION}/werewolf/homeDialog/list`, accCtr(AccessRouteId.wolf_home_dialog), controller.werewolf.homeDialog.list)
    //弹窗类型列表
    router.post(`${API_VERSION}/werewolf/homeDialog/typeList`, accCtr(AccessRouteId.wolf_home_dialog), controller.werewolf.homeDialog.typeList)
    //编辑
    router.post(`${API_VERSION}/werewolf/homeDialog/edit`, accCtr(AccessRouteId.wolf_home_dialog), controller.werewolf.homeDialog.edit)
    //delete
    router.post(`${API_VERSION}/werewolf/homeDialog/delete`, accCtr(AccessRouteId.wolf_home_dialog), controller.werewolf.homeDialog.delete)
}

export default load
