export interface IaddWeekAwardParams {
    item_dic_id: number,
    item_cate_id: number,
    num: number,
    max: number,
    stock_num: number,
    rank: number,
    mining_frame_level: number,
    isFrame: number,
    sort: number,
    delsign: number,
    remark: string,
}

export interface IupdateWeekAwardParams extends IaddWeekAwardParams{
    id: number,
}

export interface IinsertFrameCrystalParams {
    name: string,
    pic: string,
    season_id: number,
    hole_exp: number,
    delsign: number,
    illustrated_guide_id: number,//图鉴id
    item_dic_remark: string,
}

export interface IupdateFrameCrystalParams extends IinsertFrameCrystalParams{
    id: number,
    item_dic_id: number,
}


export interface IinsertRoleSparParamsConfig {
    id: number,
    role_spar_id: number,
    min_hole_level: number,
    max_hole_level: number,
    weight: number,
    delsign: number,
}

export interface IinsertRoleSparParams {
    name: string,
    pic: string,
    season_id: number,
    level: number,

    hole_exp: number,
    delsign: number,
    illustrated_guide_id: number,//图鉴id
    item_dic_remark: string,

    configs: IinsertRoleSparParamsConfig[],
}

export interface IupdateRoleSparParams extends IinsertRoleSparParams{
    id: number,
    item_dic_id: number,
}

export interface IinsertSeasonParams {
    name: string,
    delsign: number,
    start_time: string,
    end_time: string,
}

export interface IupdateSeasonParams extends IinsertSeasonParams {
    id: number,
}

export interface IupdateIllustrationParams {
    id: number,
    name: string,
    level: string,
    s_no: string,
    desc: string,
    source: number,
    img: string,
}


