import { imWithDrawType } from "./staticEnum";

/*
 * @Description: 了了建模
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: leeou
 * @Date: 2019-02-02 11:25:02
 * @LastEditors: leeou
 * @LastEditTime: 2019-03-26 09:59:27
 */

/**
 * 提现列表请求
 */
export interface IwithDrawListRequest {
  uid: number; //管理员ID
  type: imWithDrawType;
  start: number; //分页开始
  offset: number; //分页数量
}

export interface IwithDrawListResponse {
  uid: number; //管理员ID
  type: imWithDrawType;
  start: number; //分页开始
  offset: number; //分页数量
  count: number; //总数
  list: IwithDrawList[];
}

export interface IwithDrawList {
  id: number; //操作ID
  alipayAccount: string; //支付宝账号
  alipayName: string; //支付宝名称
  money: number; //本次提现金额
  datetime: string; //日期
  status: imWithDrawType; //提现状态
}

//提现操作
export interface IwithDrawOperateRequest {
  uid: number; //管理员ID
  reportId: number; //记录ID
  type: imWithDrawType; //变更状态
}

export interface IwithDrawOperateResponse {
  item: IwithDrawList[]; //新元素状态
}
