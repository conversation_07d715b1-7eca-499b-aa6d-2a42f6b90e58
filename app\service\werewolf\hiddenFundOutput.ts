/*
 * @Author: chen peng
 * @Date: 2021-08-18 13:47:20
 * @LastEditTime: 2021-11-17 10:43:30
 * @LastEditors: Please set LastEditors
 * @Description: 集市
 * @FilePath: /MGKFHTServer/app/service/werewolf/market.ts
 */

import { Service } from 'egg'
import BaseMegaService from './BaseMegaService'

export default class HiddenFundOutputService extends BaseMegaService {
    // 集市 隐藏款产出记录
    public async getHiddenFundOutputList(request) {
        const { app, logger } = this
        const werewolf = app.mysql.get('werewolf')
        let count = 0
        try {
            //查询系列
            let projectSql = `SELECT * FROM mining_frame WHERE delsign = 0`
            let projectResult = await werewolf.query(projectSql)

            if (projectResult.length <= 0) {
                return { ...request, count: count, dataArray: [], projectList: projectResult }
            }

            let type = request.type
            let selResult = projectResult
            if (type != 0) {
                let selSql = `SELECT * FROM mining_frame WHERE id = ${type} AND delsign = 0`
                selResult = await werewolf.query(selSql)
            }

            let projectStrArrStr = ''
            for (let index = 0; index < selResult.length; index++) {
                const element = selResult[index].id
                if (index == selResult.length - 1) {
                    projectStrArrStr += element
                } else {
                    projectStrArrStr += element + ','
                }
            }

            //查询隐藏款
            let hideSql = `SELECT
            t3.item_dic_id,t2.stock_num
        FROM
            mining_frame_pool_relation t1
        LEFT JOIN mining_frame_pool_award_relation t2 ON t2.pool_id = t1.pool_id
        LEFT JOIN mining_frame_award_v2 t3 ON t2.award_id = t3.id
        WHERE
            t1.mining_frame_id IN (${projectStrArrStr}) AND t3.type = 1
        GROUP BY t3.item_dic_id;`
            let hideResult = await werewolf.query(hideSql)
            if (hideResult.length <= 0) {
                return { ...request, count: count, dataArray: [], projectList: projectResult }
            }
            let hideArrStr = ''
            for (let index = 0; index < hideResult.length; index++) {
                const element = hideResult[index].item_dic_id
                if (index == hideResult.length - 1) {
                    hideArrStr += element
                } else {
                    hideArrStr += element + ','
                }
            }

            // let date = '2021-12-03'
            // if (app.config.serverEnv != "prod") {
            //     date = '2021-11-03'
            // }

            //查询总数
            let countSql = `SELECT COUNT(*)AS total FROM item_produce_mining_frame_open_history WHERE item_dic_id IN (${hideArrStr})AND create_date > '2021-12-03'`
            let countResult = await werewolf.query(countSql)
            if (countResult.length <= 0) {
                return {...request, count: count, dataArray: [], projectList: projectResult}
            }
            count = countResult[0].total

            //查询详情
            let listSql = `SELECT ipmf.create_time,
                                  tu.nickname,
                                  ipmf.user_id,
                                  mf.name frame_name,
                                  idc.name,
                                  mfpar.stock_num
                           FROM item_produce_mining_frame_open_history ipmf
                                    LEFT JOIN tuser tu ON tu.no = ipmf.user_id
                                    LEFT JOIN item_consume_mining_frame_open_history icmf ON ipmf.tid = icmf.tid
                                    LEFT JOIN mining_frame mf ON mf.id = icmf.mining_frame_id
                                    LEFT JOIN item_dic idc ON idc.id = ipmf.item_dic_id
                                    LEFT JOIN mining_frame_award_v2 mfa2 ON mfa2.item_dic_id = ipmf.item_dic_id
                                    LEFT JOIN mining_frame_pool_award_relation mfpar ON mfpar.award_id = mfa2.id
                           WHERE ipmf.item_dic_id IN (${hideArrStr})
                             AND ipmf.create_date > '2021-12-03'
                             AND ipmf.user_id NOT IN (SELECT user_id FROM tuser_inner_total)
                           GROUP BY icmf.create_time
                           ORDER BY icmf.create_time DESC
                               LIMIT ${request.start}, ${request.offset};`
            let listResult = await werewolf.query(listSql)
            return { ...request, count: count, dataArray: listResult, projectList: projectResult }
        } catch (error) {
            throw error
        }
    }
}
