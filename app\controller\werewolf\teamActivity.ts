import { IactivityAwardConf, IactivityAwardGroup, IactivityIndex, IgetAwardConfReq, IgetAwardGroupReq } from '../../model/wfActivityAward';
/*
 * @Description: 无
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: musong
 */

import BaseMegaController from './BaseMegaController';

export default class TeamActivityController extends BaseMegaController {
    //1 创建团队活动
    public async createTeamActivity() {
        const { ctx, logger } = this;
        try {
            const req: any = ctx.request.body;
            const result = await ctx.service.werewolf.teamActivity.teamActivityCreate(req);
            this.respSucc()
        } catch (err) {
            this.respFail(err)
        }
    }

    public async updateTeamActivity() {
        const { ctx, logger } = this;
        try {
            const req: any = ctx.request.body;
            const result = await ctx.service.werewolf.teamActivity.teamActivityUpdate(req);
            this.respSucc()
        } catch (err) {
            this.respFail(err)
        }
    }




    public async teamActivityList() {
        const { ctx, logger } = this;
        try {
            const retList: IactivityIndex[] = await ctx.service.werewolf.teamActivity.teamActivityList();
            this.respSuccData(retList)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async teamActivityDetail() {
        const { ctx, logger } = this;
        try {
            const req: any = ctx.request.body;
            const result = await ctx.service.werewolf.teamActivity.teamActivityDetail(req);
            this.respSuccData(result)
        } catch (err) {
            this.respFail(err)
        }
    }
    

}
