/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-10-15 15:35:44
 * @LastEditTime: 2020-10-19 09:14:00
 * @LastEditors: jiawen.wang
 */
import BaseMegaController from './BaseMegaController';
import { IupdateContentItemRes, IcreateupdateContentReq, IeditUpdateContent } from '../../model/updateContent'
import { HttpErr, IerrorMsg } from '../../model/common';

export default class UpdateContentController extends BaseMegaController {
    public async getUpdateContentList() {
        const { ctx, logger } = this
        try {
            const resp: IupdateContentItemRes[] = await ctx.service.werewolf.updateContent.getUpdateContentList();
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async createUpdateContent() {
        const { ctx, logger } = this;
        const rule = {
            content: { type: "string" },
            start_time: { type: "string" },
            end_time: { type: "string" }
        }
        try {
            ctx.validate(rule)
            const request: IcreateupdateContentReq = ctx.request.body;
            await ctx.service.werewolf.updateContent.createUpdateContent(request);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    public async editUpdateContent() {
        const { ctx, logger } = this;
        const rule = {
            id: { type: "number" },
            content: { type: "string" },
            start_time: { type: "string" },
            end_time: { type: "string" }
        }
        try {
            ctx.validate(rule)
            const request: IeditUpdateContent = ctx.request.body;
            await ctx.service.werewolf.updateContent.editUpdateContent(request);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    public async delUpdateContent() {
        const { ctx, logger } = this;
        const rule = {
            id: { type: 'number' }
        }
        try {
            ctx.validate(rule)
            const request: number = ctx.request.body;
            await ctx.service.werewolf.updateContent.delUpdateContent(request);

            this.respSucc()
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
}