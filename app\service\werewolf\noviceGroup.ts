import BaseMegaService from "../../service/werewolf/BaseMegaService";
import {createResponseBody} from "../../util/responseUtils";
import {HttpErr} from "../../model/common";
import {OkPacket} from "../../../typings";

export default class NoviceGroupService extends BaseMegaService {
    public async getGroups(req) {
        const {app, ctx, logger} = this;
        try {
            let sql = `
                SELECT *,
                       DATE_FORMAT(createtime, '%Y-%m-%d %H:%i:%s') createtime
                FROM novice_group
                WHERE ${req.id < 1 ? '1' : 'id = ?'}
                ORDER BY id DESC
            `;
            return await this.selectList(sql, [req.id]);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getUsers(req) {
        const {app, ctx, logger} = this;
        try {


            let sql = `
                SELECT a.*,
                       DATE_FORMAT(a.createtime, '%Y-%m-%d %H:%i:%s') createtime,
                       b.nickname
                FROM novice_group_user a
                         LEFT JOIN tuser b ON a.user_id = b.\`no\`
                WHERE a.user_id = ?
                  AND a.delsign = 0
            `;
            return await this.selectList(sql, [req.user_id]);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async addUser(req) {
        const {app, ctx, logger} = this;

        const client = app.mysql.get("werewolf")
        const t = await client.beginTransaction()
        try {
            let sql = `
                SELECT 1
                FROM tuser
                WHERE \`no\` = ?
                  AND \`no\` NOT IN
                      (SELECT user_id FROM novice_group_user WHERE novice_group_id = ? AND user_id = ? AND delsign = 0)
                    LIMIT 1
            `
            const arr = await t.query(sql, [req.user_id, req.novice_group_id, req.user_id])

            if (arr.length < 1) {
                await t.commit()
                return createResponseBody(HttpErr.PlayerStatusErr, '用户不存在 或 重复添加！')
            }

            const params = {userNo: req.user_id, noviceGroupId: req.novice_group_id, type: 1}
            await this.doRongyunAction(params)

            sql = `
                INSERT INTO novice_group_user (novice_group_id, user_id)
                VALUES (?, ?) ON DUPLICATE KEY
                UPDATE delsign=0
            `;
            const res = await t.query(sql, [req.novice_group_id, req.user_id]) as OkPacket;
            await t.commit()
            return createResponseBody(HttpErr.Success, '')
        } catch (error) {
            await t.rollback
            logger.error(error);
            throw error
        }
    }

    public async kickUser(req) {
        const {app, ctx, logger} = this;

        const client = app.mysql.get("werewolf")
        const t = await client.beginTransaction()
        try {

            let sql = `
                SELECT 1
                FROM tuser a
                         LEFT JOIN novice_group_user b ON a.\`no\` = b.user_id
                WHERE a.\`no\` = ?
                  AND b.novice_group_id = ?
                  AND b.user_id = ?
                  AND b.delsign = 0 LIMIT 1`

            const arr = await t.query(sql, [req.user_id, req.novice_group_id, req.user_id])

            if (arr.length < 1) {
                await t.commit()
                return createResponseBody(HttpErr.PlayerStatusErr, '用户不存在 或 不在群组中！')
            }

            const params = {userNo: req.user_id, noviceGroupId: req.novice_group_id, type: 2}
            await this.doRongyunAction(params)

            sql = `
                UPDATE novice_group_user
                SET delsign=1
                WHERE novice_group_id = ?
                  AND user_id = ?
            `;
            const res = await t.query(sql, [req.novice_group_id, req.user_id]);
            await t.commit()
            return createResponseBody(HttpErr.Success, '')
        } catch (error) {
            await t.rollback
            logger.error(error);
            throw error
        }
    }

    // type  0创建  1加入  2退出  3解散   4改群名
    async doRongyunAction(params: { userNo: number, noviceGroupId: number, type: number }) {
        const {app, ctx, logger} = this;

        let url = 'http://' + app.config.phpDomain + '/Werewolf/v/noviceRongyunTool.php'
        if (app.config.serverEnv == 'prod'){
            url = 'http://' +  app.config.phpDomain + '/Werewolf/v7.6.0/noviceRongyunTool.php'
        }

        //需要写死 coder
        let res = await ctx.curl(url, {
            method: 'POST',
            headers: {
                "content-type": "application/json",
            },
            contentType: 'json',
            dataType: 'json',
            data: params,
            timeout: 3000, // 3 秒超时
        });

        if (res.data && res.data.sign == 1) {
            return res
        } else {
            const msg = `noviceRongyunTool.php error: ${JSON.stringify(params)} res: ${JSON.stringify(res)}`
            throw new Error(msg)
        }
    }
}