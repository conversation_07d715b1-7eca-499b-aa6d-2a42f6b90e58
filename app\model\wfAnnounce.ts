/*
 * @Description: 系统公告
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-04-15 15:04:31
 * @LastEditors: hammercui
 * @LastEditTime: 2020-04-15 16:25:50
 */

// 新建公告请求
export interface IcreateAnnounceReq {
    title: string;
    content: string;
    create_time: string;
}

// 编辑公告请求
export interface IeditAnnounceReq {
    id: number;
    title: string;
    content: string;
    create_time: string;
}
// 公告列表item
export interface IannounceItem {
    id: number;
    sort_id: number;
    title: string;
    content: string;
    create_time: string;
    show_state: number;
}

// 升序当前通知请求
export interface IupAnnounceSortReq {
    id: number;
}

// 降序当前通知请求
export interface IdownAnnounceSortReq {
    id: number;
}

// 更新显示状态请求 0显示1不显示
export interface IupdateShowStateReq {
    id: number;
    show_state: number;
}

// 删除公告请求
export interface IdelAnnounceReq {
    id: number;
}
