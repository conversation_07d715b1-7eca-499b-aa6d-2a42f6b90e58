import moment = require('moment')
import BaseMegaService from './BaseMegaService'

export default class FootballMatchRecordService extends BaseMegaService {
    parseResJSON(result) {
        let jsstr = JSON.stringify(result.data)
        let jsondata = JSON.parse(jsstr)
        let buf = Buffer.from(jsondata)
        let newData = buf.toString()
        let arr = []
        Object.keys(JSON.parse(newData).data).map(key => {
            JSON.parse(newData).data[key].map(async item => {
                arr.push(item)
            })
        })
        return arr
    }
    async checkFootballMatchRecord() {
        const { app, ctx, logger } = this

        try {
            logger.info('获取数据')
            let copaAmericaUrl = `http://matchweb.sports.qq.com/matchUnion/list?today=${moment().format('YYYY-MM-DD')}&startTime=${moment()
                .subtract(1, 'days')
                .format('YYYY-MM-DD')}&endTime=${moment().add(6, 'days').format(
                'YYYY-MM-DD',
            )}&columnId=128&index=5&isInit=true&timestamp=${new Date().getTime()}&callback=fetchScheduleListCallback128`
            let europeanCupUrl = `http://matchweb.sports.qq.com/matchUnion/list?today=${moment().format('YYYY-MM-DD')}&startTime=${moment()
                .subtract(1, 'days')
                .format('YYYY-MM-DD')}&endTime=${moment().add(6, 'days').format(
                'YYYY-MM-DD',
            )}&columnId=3&index=5&isInit=true&timestamp=${new Date().getTime()}&callback=fetchScheduleListCallback3`
            let bundesligaUrl = `http://matchweb.sports.qq.com/matchUnion/list?today=${moment().format('YYYY-MM-DD')}&startTime=${moment()
                .subtract(1, 'days')
                .format('YYYY-MM-DD')}&endTime=${moment().format(
                'YYYY-MM-DD',
            )}&columnId=22&index=5&isInit=true&timestamp=${new Date().getTime()}&callback=fetchScheduleListCallback22`
            // console.log('🚀 ~ bundesligaUrl ~ bundesligaUrl:', bundesligaUrl)
            // console.log('🚀 ~ FootballMatchRecordService ~ copaAmericaUrl ~ copaAmericaUrl:', copaAmericaUrl)

            // let copaAmericaUrl = `http://matchweb.sports.qq.com/matchUnion/list?today=${moment().format(
            //     'YYYY-MM-DD',
            // )}&startTime=2024-06-21&endTime=2024-07-15&columnId=128&index=5&isInit=true&timestamp=${new Date().getTime()}&callback=fetchScheduleListCallback20`
            // let europeanCupUrl = `http://matchweb.sports.qq.com/matchUnion/list?today=${moment().format(
            //     'YYYY-MM-DD',
            // )}&startTime=2024-06-15&endTime=2024-07-15&columnId=3&index=5&isInit=true&timestamp=${new Date().getTime()}&callback=fetchScheduleListCallback20`
            // let bundesligaUrl = `http://matchweb.sports.qq.com/matchUnion/list?today=${moment().format(
            //     'YYYY-MM-DD',
            // )}&startTime=2024-05-04&endTime=2024-05-18&columnId=22&index=5&isInit=true&timestamp=${new Date().getTime()}&callback=fetchScheduleListCallback20`
            const copaAmericaResult = await ctx.curl(copaAmericaUrl, {
                method: 'GET',
            })
            const europeanCupResult = await ctx.curl(europeanCupUrl, {
                method: 'GET',
            })
            const bundesligaResult = await ctx.curl(bundesligaUrl, {
                method: 'GET',
            })
            let europeanCuplist = this.parseResJSON(europeanCupResult)
            let copaAmericalist = this.parseResJSON(copaAmericaResult)
            let bundesligalist = this.parseResJSON(bundesligaResult)
            // let allArr = [...europeanCuplist, ...copaAmericalist, ...bundesligalist]
            let allArr = [...europeanCuplist, ...copaAmericalist]
            logger.info('获取数据成功', '一共获取到' + allArr.length + '条数据')

            this.updateFootballMatchRecord(allArr)
            // allArr.map(item => {
            //     this.setFootballMatchRecord(item)
            // })
        } catch (error) {
            logger.error('检测错误', error)
        }
    }

    async setFootballMatchRecord(item) {
        const { app, ctx, logger } = this
        const db = app.mysql.get('werewolf')
        const werewolfConn = await db.beginTransaction()
        try {
            // let sqlStr = `
            // INSERT INTO
            //     \`activity2024_game_match_spiders_record\` (startTime,matchDesc,matchPeriod,leftId,leftOldId,leftGoal,leftName,leftBadge,rightId,rightOldId,rightGoal,rightName,rightBadge,groupName,mid,columnId)
            // VALUES
            //     ('${item.startTime}','${item.matchDesc}',${item.matchPeriod},${item.leftId},${item.leftOldId ? item.leftOldId : null},${
            //     item.leftGoal
            // },'${item.leftName}','${item.leftBadge}',${item.rightId},${item.rightOldId ? item.rightOldId : null},${item.rightGoal},'${
            //     item.rightName
            // }','${item.rightBadge}','${item.groupName}','${item.mid}',${parseInt(item.mid.split(':')[0])})`
            let isFinal =
                item.matchDesc == '欧洲杯决赛'
                    ? 1
                    : item.matchDesc == '欧洲杯半决赛'
                    ? 10
                    : item.matchDesc == '欧洲杯1/4决赛'
                    ? 100
                    : item.matchDesc == '欧洲杯1/8决赛'
                    ? 1000
                    : 10000

            let sqlStr = `
            INSERT INTO
                \`activity2024_footballcup_game_config\` (game_type,game_group,round,team_id_a,team_id_b,game_date,game_time,columnId,mid)
            VALUES
                ('${isFinal}','${item.groupName}',${item.groupName ? item.matchDesc[6] : null},${item.leftId},${item.rightId},'${
                item.startTime.split(' ')[0]
            }','${item.startTime}',${parseInt(item.mid.split(':')[0])},'${item.mid}')`
            await werewolfConn.query(sqlStr)
            await werewolfConn.commit()
            logger.info('成功')
        } catch (error) {
            console.log('🚀 ~ FootBallMatchRecordService ~ checkFootballMatchRecord ~ error:', error)
        }
    }
    async updateFootballMatchRecord(list) {
        const { app, ctx, logger } = this
        const db = app.mysql.get('werewolf')

        if (list.length == 0) return

        const werewolfConn = await db.beginTransaction()
        // logger.info(`昨日比赛:一共${list.filter(item => moment(item.startTime).isBefore(moment().format('YYYY-MM-DD'))).length} 场`)
        // logger.info(`今日比赛:一共${list.filter(item => moment(item.startTime).isBefore(moment().add(1, 'days').format('YYYY-MM-DD'))).length} 场`)
        try {
            logger.info('开始更新比赛记录')

            let updateMatchPeriodSqlStr = ''
            let result_state_sql_str = ''
            let updateRightGoalSqlStr = ''
            let updateRightNameSqlStr = ''
            let updateRightIdSqlStr = ''
            let updateRightBadgeSqlStr = ''
            let updateLeftGoalSqlStr = ''
            let updateLeftNameSqlStr = ''
            let updateLeftIdSqlStr = ''

            let updateLeftBadgeSqlStr = ''
            let updateMidSqlStr = ''
            let updateResultSqlStr = ''

            list.map(item => {
                updateMatchPeriodSqlStr += `WHEN '${item.mid}' THEN ${item.matchPeriod} `
                result_state_sql_str += `WHEN '${item.mid}' THEN ${item.matchPeriod == 2 ? 1 : 0} `
                updateResultSqlStr += `WHEN '${item.mid}' THEN ${item.matchPeriod == 2 ? item.rightGoal > item.leftGoal ?1:item.rightGoal < item.leftGoal?0:2 : null} `
                updateRightGoalSqlStr += `WHEN '${item.mid}' THEN ${item.rightGoal} `
                updateRightNameSqlStr += `WHEN '${item.mid}' THEN '${item.rightName}' `
                updateRightIdSqlStr  += `WHEN '${item.mid}' THEN '${item.rightId}' `
                updateRightBadgeSqlStr += `WHEN '${item.mid}' THEN '${item.rightBadge}' `
                updateLeftGoalSqlStr += `WHEN '${item.mid}' THEN ${item.leftGoal} `
                updateLeftNameSqlStr += `WHEN '${item.mid}' THEN '${item.leftName}' `
                updateLeftIdSqlStr  += `WHEN '${item.mid}' THEN '${item.leftId}' `

                updateLeftBadgeSqlStr += `WHEN '${item.mid}' THEN '${item.leftBadge}' `
                updateMidSqlStr += `'${item.mid}', `
            })
            console.log(updateRightNameSqlStr)
            let sql = `UPDATE activity2024_game_match_spiders_record
                            SET matchPeriod = CASE mid
                                ${updateMatchPeriodSqlStr}
                            END,
                            rightGoal = CASE mid
                                ${updateRightGoalSqlStr}
                            END,
                            rightName = CASE mid
                                ${updateRightNameSqlStr}
                            END,
                            rightBadge = CASE mid
                                ${updateRightBadgeSqlStr}
                            END,
                            leftGoal = CASE mid
                                ${updateLeftGoalSqlStr}
                            END,
                            leftName = CASE mid
                                ${updateLeftNameSqlStr}
                            END,
                            leftBadge = CASE mid
                                ${updateLeftBadgeSqlStr}
                            END
                            WHERE mid IN (${updateMidSqlStr.substring(0, updateMidSqlStr.lastIndexOf(','))})`
               
            let footballCupSql = `UPDATE activity2024_footballcup_game_config
                            SET result_state = CASE mid
                                ${result_state_sql_str}
                            END,
                            result = CASE mid
                                ${updateResultSqlStr}
                            END,
                            score_a = CASE mid
                                ${updateLeftGoalSqlStr}
                            END,
                            score_b = CASE mid
                                ${updateRightGoalSqlStr}
                            END,
                            team_id_a = CASE mid
                                ${updateLeftIdSqlStr}
                            END,
                            team_id_b = CASE mid
                                ${updateRightIdSqlStr}
                            END
                            WHERE mid IN (${updateMidSqlStr.substring(0, updateMidSqlStr.lastIndexOf(','))})`
            await werewolfConn.query(sql)
            await werewolfConn.query(footballCupSql)

            await werewolfConn.commit()
            logger.info('更新数据成功')
        } catch (error) {
            await werewolfConn.rollback() // 一定记得捕获异常后回滚事务！
            logger.error('检测错误', error)
            throw error
        }
    }
}
