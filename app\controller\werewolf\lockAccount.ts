import BaseMegaController from './BaseMegaController';
import { error } from 'console';

export default class LockAccount extends BaseMegaController {

    public async getUserListByUserId() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            let userIds = requestBody.userNo
            if(!userIds){
                return this.respSucc()
            }
            //1.查询用户基本信息和充值总额
            const baseInfo = await ctx.service.werewolf.lockAccount.getUserBaseInfoListByUserIds(userIds);
            if(!baseInfo){
                return this.respSucc()
            }
            // //2.查询用户注册ip
            // const ipInfo = await ctx.service.werewolf.lockAccount.getUserIpInfolistUserIds(userIds);
            //3.查询用户身份证号
            const idCardInfo = await ctx.service.werewolf.lockAccount.getUserIdCardInfoListByUserIds(userIds);
            //4.查询用户阿里云id
            const aliIdInfo = await ctx.service.werewolf.lockAccount.getUserAliIdInfoListByUserIds(userIds);
            if (baseInfo) {
                baseInfo.forEach((baseInfoItem) => {
                    baseInfoItem['idCard'] = "";
                    baseInfoItem['aliId'] = "";
                    if (idCardInfo) {
                        idCardInfo.forEach((idCardInfoItem) => {
                            if (idCardInfoItem['userId'] == baseInfoItem['no']) {
                                baseInfoItem['idCard'] = idCardInfoItem['idCard'];
                            }
                        });
                    }
                    if (aliIdInfo) {
                        aliIdInfo.forEach((aliIdInfoItem) => {
                            if (aliIdInfoItem['userId'] == baseInfoItem['no']) {
                                baseInfoItem['aliId'] = aliIdInfoItem['aliId'];
                            }
                        });
                    }
                });
            }
            this.respSuccData(baseInfo)
        } catch (err) { this.respFail(err) }
    }
    public async getUserListByAliId() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            //根据阿里id查询用户id
            const aliIdInfo = await ctx.service.werewolf.lockAccount.getUserIdsByAliId(requestBody);
            if(!aliIdInfo){
                return this.respSucc()
             }
            let userIds = "";
            for (let index = 0; index < aliIdInfo.length; index++) {
                userIds += aliIdInfo[index]['userId']
                if (index < aliIdInfo.length - 1) {
                    userIds += ","
                }
            }
            //1.查询用户基本信息和充值总额
            const baseInfo = await ctx.service.werewolf.lockAccount.getUserBaseInfoListByUserIds(userIds);
            // //2.查询用户注册ip
            // const ipInfo = await ctx.service.werewolf.lockAccount.getUserIpInfolistUserIds(userIds);
            //3.查询用户身份证号
            const idCardInfo = await ctx.service.werewolf.lockAccount.getUserIdCardInfoListByUserIds(userIds);
            //4.查询用户阿里云id
            // const aliIdInfo = await ctx.service.werewolf.lockAccount.getUserAliIdInfoListByUserIds(userIds);
            if (baseInfo) {
                baseInfo.forEach((baseInfoItem) => {
                    baseInfoItem['idCard'] = "";
                    baseInfoItem['aliId'] = "";
                    if (idCardInfo) {
                        idCardInfo.forEach((idCardInfoItem) => {
                            if (idCardInfoItem['userId'] == baseInfoItem['no']) {
                                baseInfoItem['idCard'] = idCardInfoItem['idCard'];
                            }
                        });
                    }
                    if (aliIdInfo) {
                        aliIdInfo.forEach((aliIdInfoItem) => {
                            if (aliIdInfoItem['userId'] == baseInfoItem['no']) {
                                baseInfoItem['aliId'] = aliIdInfoItem['aliId'];
                            }
                        });
                    }
                });
            }
            this.respSuccData(baseInfo)
        } catch (err) { this.respFail(err) }
    }
    public async getUserListByUdid() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            //根据udid查询用户基本信息和充值总额  获取用户id
            const baseInfo = await ctx.service.werewolf.lockAccount.getUserBaseInfoListByUdid(requestBody);
            if(!baseInfo){
                return this.respSucc()
             }
            let userIds = "";
            for (let index = 0; index < baseInfo.length; index++) {
                userIds += baseInfo[index]['no']
                if (index < baseInfo.length - 1) {
                    userIds += ","
                }
            }
            // //2.查询用户注册ip
            // const ipInfo = await ctx.service.werewolf.lockAccount.getUserIpInfolistUserIds(userIds);
            //3.查询用户身份证号
            const idCardInfo = await ctx.service.werewolf.lockAccount.getUserIdCardInfoListByUserIds(userIds);
            //4.查询用户阿里云id
            const aliIdInfo = await ctx.service.werewolf.lockAccount.getUserAliIdInfoListByUserIds(userIds);
            if (baseInfo) {
                baseInfo.forEach((baseInfoItem) => {
                    baseInfoItem['idCard'] = "";
                    baseInfoItem['aliId'] = "";
                    if (idCardInfo) {
                        idCardInfo.forEach((idCardInfoItem) => {
                            if (idCardInfoItem['userId'] == baseInfoItem['no']) {
                                baseInfoItem['idCard'] = idCardInfoItem['idCard'];
                            }
                        });
                    }
                    if (aliIdInfo) {
                        aliIdInfo.forEach((aliIdInfoItem) => {
                            if (aliIdInfoItem['userId'] == baseInfoItem['no']) {
                                baseInfoItem['aliId'] = aliIdInfoItem['aliId'];
                            }
                        });
                    }
                });
            }
            this.respSuccData(baseInfo)
        } catch (err) { this.respFail(err) }
    }
    public async getUserListByIDNumber() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;

            //1.查询用户身份证号
            const idCardInfo = await ctx.service.werewolf.lockAccount.getUserIdCardListByIdCard(requestBody);
            if(!idCardInfo){
                return this.respSucc()
             }
            let userIds = "";
            for (let index = 0; index < idCardInfo.length; index++) {
                userIds += idCardInfo[index]['userId']
                if (index < idCardInfo.length - 1) {
                    userIds += ","
                }
            }
            //1.查询用户基本信息和充值总额
            const baseInfo = await ctx.service.werewolf.lockAccount.getUserBaseInfoListByUserIds(userIds);
            // //2.查询用户注册ip
            // const ipInfo = await ctx.service.werewolf.lockAccount.getUserIpInfolistUserIds(userIds);

            //4.查询用户阿里云id
            const aliIdInfo = await ctx.service.werewolf.lockAccount.getUserAliIdInfoListByUserIds(userIds);
            if (baseInfo) {
                baseInfo.forEach((baseInfoItem) => {
                    baseInfoItem['idCard'] = "";
                    baseInfoItem['aliId'] = "";
                    if (idCardInfo) {
                        idCardInfo.forEach((idCardInfoItem) => {
                            if (idCardInfoItem['userId'] == baseInfoItem['no']) {
                                baseInfoItem['idCard'] = idCardInfoItem['idCard'];
                            }
                        });
                    }
                    if (aliIdInfo) {
                        aliIdInfo.forEach((aliIdInfoItem) => {
                            if (aliIdInfoItem['userId'] == baseInfoItem['no']) {
                                baseInfoItem['aliId'] = aliIdInfoItem['aliId'];
                            }
                        });
                    }
                });
            }
            this.respSuccData(baseInfo)
        } catch (err) { this.respFail(err) }
    }
    public async getUserListByRegisterIP() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const ipInfo = await ctx.service.werewolf.lockAccount.getUserListByRegisterIP(requestBody);
            if(!ipInfo){
               return this.respSucc()
            }
            let userIds = "";
            for (let index = 0; index < ipInfo.length; index++) {
                userIds += ipInfo[index]['phone']
                if (index < ipInfo.length - 1) {
                    userIds += ","
                }
            }
            //1.查询用户基本信息和充值总额
            const baseInfo = await ctx.service.werewolf.lockAccount.getUserBaseInfoListByUserIds(userIds);
            //3.查询用户身份证号
            const idCardInfo = await ctx.service.werewolf.lockAccount.getUserIdCardInfoListByUserIds(userIds);
            //4.查询用户阿里云id
            const aliIdInfo = await ctx.service.werewolf.lockAccount.getUserAliIdInfoListByUserIds(userIds);
            if (baseInfo) {
                baseInfo.forEach((baseInfoItem) => {
                    baseInfoItem['idCard'] = "";
                    baseInfoItem['aliId'] = "";
                    if (idCardInfo) {
                        idCardInfo.forEach((idCardInfoItem) => {
                            if (idCardInfoItem['userId'] == baseInfoItem['no']) {
                                baseInfoItem['idCard'] = idCardInfoItem['idCard'];
                            }
                        });
                    }
                    if (aliIdInfo) {
                        aliIdInfo.forEach((aliIdInfoItem) => {
                            if (aliIdInfoItem['userId'] == baseInfoItem['no']) {
                                baseInfoItem['aliId'] = aliIdInfoItem['aliId'];
                            }
                        });
                    }
                });
            }
            this.respSuccData(baseInfo)
        } catch (err) { this.respFail(err) }
    }

}
