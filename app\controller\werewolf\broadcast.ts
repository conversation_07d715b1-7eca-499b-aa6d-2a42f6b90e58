/*
 * @Description: 世界频道
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2019-08-06 14:03:16
 * @LastEditors: jiawen.wang
 * @LastEditTime: 2021-06-07 15:13:03
 */

import { Controller } from 'egg';
import { IavFrameUserListRequest, IbroadcastListReq, IbroadcastBanReq, IbroadcastListResp, ItnoticeReq, IupdateIdCardInfoReq } from '../../model/werewolf';
import { IerrorMsg, HttpErr } from '../../model/common';

export default class Broadcast extends Controller {

    /**
     * @name: 聊天记录列表
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async recordList() {
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
            playerId: { type: 'number' },
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: IbroadcastListReq = ctx.request.body;
            const responseBody: IbroadcastListResp = await ctx.service.werewolf.broadcast.recordList(requestBody);
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    /**
    * @name: 屏蔽世界频道聊天记录
    * @msg: 
    * @param {type} 
    * @return: 
    */
    public async ban() {
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
            playerId: { type: 'number' },
            broadcastId: { type: 'number' }
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: IbroadcastBanReq = ctx.request.body;
            await ctx.service.werewolf.broadcast.ban(requestBody);
            ctx.body = { msg: "success" };
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    public async tnotice() {
        const { ctx, logger } = this;
        const rule = {
            playerId: { type: 'number' },
        }
        try {
            ctx.validate(rule);
            const request: ItnoticeReq = ctx.request.body;
            const resp = await ctx.service.werewolf.broadcast.getTnotice(request);
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (err) {
            logger.error(err);
        }
    }
    public async idCardInfoList() {
        const { ctx, logger } = this;
        const rule = {
            playerId: { type: 'number' },
        }
        try {
            ctx.validate(rule);
            const request: ItnoticeReq = ctx.request.body;
            const resp = await ctx.service.werewolf.broadcast.idCardInfoList(request);
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (err) {
            logger.error(err);
        }
    }
    public async updateIdCardInfo() {
        const { ctx, logger } = this;
        const rule = {
            playerId: { type: 'number' },
            idCard: { type: "string" },
            reason: { type: 'string' },
        }
        try {
            ctx.validate(rule);
            const request: IupdateIdCardInfoReq = ctx.request.body;
            await ctx.service.werewolf.broadcast.updateIdCardInfo(request);
            ctx.body = { msg: "success" };
        } catch (err) {
            logger.error(err);
        }
    }

}
