import BaseMegaService from "./BaseMegaService";
import {
  IinsertGiftBagItemParams,
  IinsertGiftBagParams,
  IupdateGiftBagItemParams,
  IupdateGiftBagParams,
} from "../../model/teamGiftBagDto";
import { convertTimestamp } from "../../util/convertUtils";

export default class TeamGiftBagService extends BaseMegaService {
  public async getTeamGiftBagList(params: any) {
    const sql = `SELECT *
                     FROM team_gift_bag
                     ORDER BY id DESC`;

    try {
      const res = await this.selectList(sql);
      return res;
    } catch (e) {
      this.logger.error(e);
    }
  }

  // 插入礼包
  public async insertTeamGiftBag(params: IinsertGiftBagParams) {
    try {
      const sheduleOnTime = convertTimestamp(params.shedule_on_time);
      const sheduleOffTime = convertTimestamp(params.shedule_off_time);
      const sql = `INSERT INTO team_gift_bag (\`name\`, remark, max_num, coin_id, coin_num, coin_item_dic_id, add_exp,
            sort, delsign,
            shedule_on_time, shedule_off_time)
VALUES (?, ?, ?, 1, ?, 1506, ?, ?, ?, ?, ?)`;
      const res = await this.execSql(sql, [
        params.name,
        params.remark,
        params.max_num,
        params.coin_num,
        params.add_exp,
        params.sort,
        params.delsign,
        sheduleOnTime,
        sheduleOffTime,
      ]);
    } catch (e) {
      this.logger.error(e);
    }
  }

  // 更新礼包
  public async updateTeamGiftBag(params: IupdateGiftBagParams) {
    try {
        const sheduleOnTime = convertTimestamp(params.shedule_on_time);
        const sheduleOffTime = convertTimestamp(params.shedule_off_time);
        const sql = `UPDATE team_gift_bag
        SET \`name\`=?,
            remark=?,
            max_num=?,
            coin_num=?,
            add_exp=?,
            sort=?,
            delsign=?,
            shedule_on_time = ?,
            shedule_off_time = ?
        WHERE id = ?`;
      const res = await this.execSql(sql, [
        params.name,
        params.remark,
        params.max_num,
        params.coin_num,
        params.add_exp,
        params.sort,
        params.delsign,
        sheduleOnTime,
        sheduleOffTime,
        params.id,
      ]);
    } catch (e) {
      this.logger.error(e);
    }
  }

  public async getTeamGiftBagItemList(params: IinsertGiftBagParams) {
    const sql = `SELECT t.*,
                            b.\`name\` AS gift_bag_name,
                            i.\`name\` AS item_dic_name,
                            c.\`name\` AS item_cate_name,
                            c.id       AS item_cate_id
                     FROM team_gift_bag_content t
                              LEFT JOIN team_gift_bag b ON t.gift_bag_id = b.id
                              LEFT JOIN item_dic i ON t.item_dic_id = i.id
                              LEFT JOIN item_cate c ON i.item_cate_id = c.id
                     ORDER BY t.sort ASC `;

    try {
      const res = await this.selectList(sql);
      return res;
    } catch (e) {
      this.logger.error(e);
    }
  }

  public async insertTeamGiftBagItem(params: IinsertGiftBagItemParams) {
    const sql = `INSERT INTO team_gift_bag_content (gift_bag_id, item_dic_id, \`name\`, num, weight, percent, sort,
                                                        delsign)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?)`;

    try {
      const res = await this.execSql(sql, [
        params.gift_bag_id,
        params.item_dic_id,
        params.name,
        params.num,
        params.weight,
        params.percent,
        params.sort,
        params.delsign,
      ]);
    } catch (e) {
      this.logger.error(e);
    }
  }

  public async updateTeamGiftBagItem(params: IupdateGiftBagItemParams) {
    const sql = `UPDATE team_gift_bag_content
                     SET gift_bag_id=?,
                         item_dic_id=?,
                         \`name\`=?,
                         num=?,
                         weight=?,
                         percent=?,
                         sort=?,
                         delsign=?
                     WHERE id = ?`;

    try {
      const res = await this.execSql(sql, [
        params.gift_bag_id,
        params.item_dic_id,
        params.name,
        params.num,
        params.weight,
        params.percent,
        params.sort,
        params.delsign,
        params.id,
      ]);
    } catch (e) {
      this.logger.error(e);
    }
  }

  public async getItemDicList() {
    const sql = `SELECT i.id, i.\`name\`, c.\`name\` AS item_cate_name, i.item_cate_id,i.item_id
                     FROM item_dic i
                              LEFT JOIN item_cate c ON i.item_cate_id = c.id`;

    try {
      const res = await this.selectList(sql);
      return res;
    } catch (e) {
      this.logger.error(e);
    }
  }

  /**
   * 检查礼包是否在上架时间内并更新状态
   */
  public async checkAndUpdateGiftBagStatus() {
    const { logger } = this;
    try {
      // 查询所有礼包
      const sql = `SELECT id, name, delsign, shedule_on_time, shedule_off_time 
                  FROM team_gift_bag 
                  WHERE shedule_on_time IS NOT NULL 
                  AND shedule_off_time IS NOT NULL`;
      
      const giftBags = await this.selectList(sql);
      const now = new Date();
      
      for (const bag of giftBags) {
        const onTime = new Date(bag.shedule_on_time);
        const offTime = new Date(bag.shedule_off_time);
        
        // 判断当前时间是否在上架时间范围内
        const shouldBeOnline = now >= onTime && now <= offTime;
        // 当前状态
        const isOnline = bag.delsign === 0;
        
        // 如果状态不一致,需要更新
        if (shouldBeOnline !== isOnline) {
          const newDelsign = shouldBeOnline ? 0 : 1;
          const updateSql = `UPDATE team_gift_bag SET delsign = ? WHERE id = ?`;
          await this.execSql(updateSql, [newDelsign, bag.id]);
          
          logger.info(`Updated gift bag ${bag.name}(ID:${bag.id}) status to ${shouldBeOnline ? 'online' : 'offline'}`);
        }
      }
    } catch (error) {
      logger.error('Failed to check and update gift bag status:', error);
      throw error;
    }
  }
}
