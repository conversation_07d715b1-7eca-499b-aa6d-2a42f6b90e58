/*
 * @Description: 商城管理路由
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: z<PERSON><PERSON>
 * @Date: 2020-04-22 11:50:11
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-08-19 15:27:45
 */
import { Router, Application } from "egg";
import { AccessRouteId, AccessRouteName } from "./model/accessRouteCof";
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

    const { controller, router } = app;
    //【商城管理】获取礼物
    router.post(`${API_VERSION}/werewolf/mall/getGiftList`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.getGiftList)
    //【商城管理】更改礼物
    router.post(`${API_VERSION}/werewolf/mall/updateGift`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.updateGift)
    //【商城管理】创建礼物
    router.post(`${API_VERSION}/werewolf/mall/createGift`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.createGift)
    //【商城管理】获取服装类型
    router.post(`${API_VERSION}/werewolf/mall/getClothingCateList`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.getClothingCateList)
    //【商城管理】获取服装
    router.post(`${API_VERSION}/werewolf/mall/getClothingList`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.getClothingList)
    //【商城管理】更改服装
    router.post(`${API_VERSION}/werewolf/mall/updateClothing`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.updateClothing)
    //【商城管理】更改服装ICON
    router.post(`${API_VERSION}/werewolf/mall/updateClothingIcon`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.updateClothingIcon)
    //【商城管理】创建服装
    router.post(`${API_VERSION}/werewolf/mall/createClothing`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.createClothing)

    //【商城管理】获取称号
    router.post(`${API_VERSION}/werewolf/mall/getTitleList`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.getTitleList)
    //【商城管理】更改称号
    router.post(`${API_VERSION}/werewolf/mall/updateTitle`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.updateTitle)
    //【商城管理】刷新称号
    router.post(`${API_VERSION}/werewolf/mall/refreshTitle`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.refreshTitle)
    //【商城管理】更改称号上下架
    router.post(`${API_VERSION}/werewolf/mall/updateDelsignTitle`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.updateDelsignTitle)
    //【商城管理】创建称号
    router.post(`${API_VERSION}/werewolf/mall/createTitle`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.createTitle)

    //【商城管理】获取自定义称号-底图
    router.post(`${API_VERSION}/werewolf/mall/getCustomTagBgList`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.getCustomTagBgList)
    //【商城管理】更改自定义称号-底图
    router.post(`${API_VERSION}/werewolf/mall/updateCustomTagBg`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.updateCustomTagBg)
    //【商城管理】刷新自定义称号-底图
    router.post(`${API_VERSION}/werewolf/mall/refreshCustomTagBg`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.refreshCustomTagBg)
    //【商城管理】更改自定义称号-底图上下架
    router.post(`${API_VERSION}/werewolf/mall/updateDelsignCustomTagBg`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.updateDelsignCustomTagBg)
    //【商城管理】创建自定义称号-底图
    router.post(`${API_VERSION}/werewolf/mall/createCustomTagBg`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.createCustomTagBg)

    //【商城管理】获取自定义称号-装饰1
    router.post(`${API_VERSION}/werewolf/mall/getCustomTagDecorationList1`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.getCustomTagDecorationList1)
    //【商城管理】更改自定义称号-装饰1
    router.post(`${API_VERSION}/werewolf/mall/updateCustomTagDecoration1`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.updateCustomTagDecoration1)
    //【商城管理】刷新自定义称号-装饰1
    router.post(`${API_VERSION}/werewolf/mall/refreshCustomTagDecoration1`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.refreshCustomTagDecoration1)
    //【商城管理】更改自定义称号-装饰1上下架
    router.post(`${API_VERSION}/werewolf/mall/updateDelsignCustomTagDecoration1`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.updateDelsignCustomTagDecoration1)
    //【商城管理】创建自定义称号-装饰1
    router.post(`${API_VERSION}/werewolf/mall/createCustomTagDecoration1`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.createCustomTagDecoration1)

    //【商城管理】获取自定义称号-装饰2
    router.post(`${API_VERSION}/werewolf/mall/getCustomTagDecorationList2`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.getCustomTagDecorationList2)
    //【商城管理】更改自定义称号-装饰2
    router.post(`${API_VERSION}/werewolf/mall/updateCustomTagDecoration2`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.updateCustomTagDecoration2)
    //【商城管理】刷新自定义称号-装饰2
    router.post(`${API_VERSION}/werewolf/mall/refreshCustomTagDecoration2`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.refreshCustomTagDecoration2)
    //【商城管理】更改自定义称号-装饰2上下架
    router.post(`${API_VERSION}/werewolf/mall/updateDelsignCustomTagDecoration2`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.updateDelsignCustomTagDecoration2)
    //【商城管理】创建自定义称号-装饰2
    router.post(`${API_VERSION}/werewolf/mall/createCustomTagDecoration2`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.createCustomTagDecoration2)

    //【商城管理】获取自定义称号-装饰3
    router.post(`${API_VERSION}/werewolf/mall/getCustomTagDecorationList3`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.getCustomTagDecorationList3)
    //【商城管理】更改自定义称号-装饰3
    router.post(`${API_VERSION}/werewolf/mall/updateCustomTagDecoration3`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.updateCustomTagDecoration3)
    //【商城管理】刷新自定义称号-装饰3
    router.post(`${API_VERSION}/werewolf/mall/refreshCustomTagDecoration3`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.refreshCustomTagDecoration3)
    //【商城管理】更改自定义称号-装饰3上下架
    router.post(`${API_VERSION}/werewolf/mall/updateDelsignCustomTagDecoration3`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.updateDelsignCustomTagDecoration3)
    //【商城管理】创建自定义称号-装饰3
    router.post(`${API_VERSION}/werewolf/mall/createCustomTagDecoration3`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.createCustomTagDecoration3)

    //【商城管理】获取自定义称号-文字
    router.post(`${API_VERSION}/werewolf/mall/getCustomTagTextList`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.getCustomTagTextList)
    //【商城管理】更改自定义称号-文字
    router.post(`${API_VERSION}/werewolf/mall/updateCustomTagText`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.updateCustomTagText)
    //【商城管理】刷新自定义称号-文字
    router.post(`${API_VERSION}/werewolf/mall/refreshCustomTagText`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.refreshCustomTagText)
    //【商城管理】更改自定义称号-文字上下架
    router.post(`${API_VERSION}/werewolf/mall/updateDelsignCustomTagText`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.updateDelsignCustomTagText)
    //【商城管理】创建自定义称号-文字
    router.post(`${API_VERSION}/werewolf/mall/createCustomTagText`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.createCustomTagText)

    //【商城管理】获取自定义称号-字体
    router.post(`${API_VERSION}/werewolf/mall/getCustomTagFontList`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.getCustomTagFontList)
    router.post(`${API_VERSION}/werewolf/mall/getCustomTagHaveFontList`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.getCustomTagHaveFontList)
    //【商城管理】更改自定义称号-字体
    router.post(`${API_VERSION}/werewolf/mall/updateCustomTagFont`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.updateCustomTagFont)
    //【商城管理】刷新自定义称号-字体
    router.post(`${API_VERSION}/werewolf/mall/refreshCustomTagFont`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.refreshCustomTagFont)
    //【商城管理】创建自定义称号-字体
    router.post(`${API_VERSION}/werewolf/mall/createCustomTagFont`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.createCustomTagFont)

    //【商城管理】获取信物列表
    router.post(`${API_VERSION}/werewolf/mall/getKeepSakeList`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.getKeepSakeList)
    //【商城管理】更改信物
    router.post(`${API_VERSION}/werewolf/mall/updateKeepSake`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.updateKeepSake)
    //【商城管理】刷新信物
    router.post(`${API_VERSION}/werewolf/mall/refreshKeepSake`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.refreshKeepSake)
    //【商城管理】创建信物
    router.post(`${API_VERSION}/werewolf/mall/createKeepSake`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.createKeepSake)
    //【商城管理】更改信物上下架
    router.post(`${API_VERSION}/werewolf/mall/updateDelsignKeepSake`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.updateDelsignKeepSake)
    //【商城管理】获取信物记录列表
    router.post(`${API_VERSION}/werewolf/mall/getKeepSakeInfoList`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.getKeepSakeInfoList)
    //【商城管理】显示隐藏按钮
    router.post(`${API_VERSION}/werewolf/mall/updateShowInfoKeepSake`, accCtr(AccessRouteId.wolf_props_manager), controller.werewolf.mall.updateShowInfoKeepSake)
    

    //【商城管理】获取道具库
    router.post(`${API_VERSION}/werewolf/mall/getItemDicList`, controller.werewolf.mall.getItemDicList)
    //【商城管理】创建道具库
    router.post(`${API_VERSION}/werewolf/mall/createItemDic`, accCtr(AccessRouteId.wolf_mall_item_dic), controller.werewolf.mall.createItemDic)
    //【商城管理】更改道具库
    router.post(`${API_VERSION}/werewolf/mall/updateItemDic`, accCtr(AccessRouteId.wolf_mall_item_dic), controller.werewolf.mall.updateItemDic)
    //【商城管理】获取道具库
    router.post(`${API_VERSION}/werewolf/mall/getItemCateList`, controller.werewolf.mall.getItemCateList)
    //【商城管理】获取 animation 基础表
    router.post(`${API_VERSION}/werewolf/mall/getAnimationList`, accCtr(AccessRouteId.wolf_mall_item_dic), controller.werewolf.mall.getAnimationList)
    //【商城管理】获取 天狼秀 基础表
    router.post(`${API_VERSION}/werewolf/mall/getMaskshowList`, accCtr(AccessRouteId.wolf_mall_item_dic), controller.werewolf.mall.getMaskshowList)
    //【商城管理】获取 公会徽 基础表
    router.post(`${API_VERSION}/werewolf/mall/getGroupBadgeList`, accCtr(AccessRouteId.wolf_mall_item_dic), controller.werewolf.mall.getGroupBadgeList)
    //【商城管理】获取 公会头像框 基础表
    router.post(`${API_VERSION}/werewolf/mall/getGroupFrameList`, accCtr(AccessRouteId.wolf_mall_item_dic), controller.werewolf.mall.getGroupFrameList)
    //【商城管理】获取 公会banner 基础表
    router.post(`${API_VERSION}/werewolf/mall/getGroupBannerList`, accCtr(AccessRouteId.wolf_mall_item_dic), controller.werewolf.mall.getGroupBannerList)
    //【商城管理】获取 头像框 基础表
    router.post(`${API_VERSION}/werewolf/mall/getAvatarFrameList`, accCtr(AccessRouteId.wolf_mall_item_dic), controller.werewolf.mall.getAvatarFrameList)
    //【商城管理】获取 道具 基础表
    router.post(`${API_VERSION}/werewolf/mall/getNormalItemList`, accCtr(AccessRouteId.wolf_mall_item_dic), controller.werewolf.mall.getNormalItemList)
    //【商城管理】获取 公会道具 基础表
    router.post(`${API_VERSION}/werewolf/mall/getGroupPropsList`, accCtr(AccessRouteId.wolf_mall_item_dic), controller.werewolf.mall.getGroupPropsList)
    //【商城管理】获取 礼包 基础表
    router.post(`${API_VERSION}/werewolf/mall/getGiftBagList`, accCtr(AccessRouteId.wolf_mall_item_dic), controller.werewolf.mall.getGiftBagList)
    //【商城管理】获取 天狼秀 基础表
    router.post(`${API_VERSION}/werewolf/mall/getAllMaskshowList`, accCtr(AccessRouteId.wolf_mall_item_dic), controller.werewolf.mall.getAllMaskshowList)
    //【商城管理】插入 天狼秀 基础表
    router.post(`${API_VERSION}/werewolf/mall/insertMaskshow`, accCtr(AccessRouteId.wolf_mall_item_dic), controller.werewolf.mall.insertMaskshow)
    //【商城管理】更改 天狼秀 基础表
    router.post(`${API_VERSION}/werewolf/mall/updateMaskshow`, accCtr(AccessRouteId.wolf_mall_item_dic), controller.werewolf.mall.updateMaskshow)
    //【商城管理】获取 角色 基础表
    router.post(`${API_VERSION}/werewolf/mall/getRoleList`, accCtr(AccessRouteId.wolf_mall_item_dic), controller.werewolf.mall.getRoleList)
    //【商城管理】插入 道具 基础表
    router.post(`${API_VERSION}/werewolf/mall/insertNormalItem`, accCtr(AccessRouteId.wolf_mall_item_dic), controller.werewolf.mall.insertNormalItem)
    //【商城管理】更改 道具 基础表
    router.post(`${API_VERSION}/werewolf/mall/updateNormalItem`, accCtr(AccessRouteId.wolf_mall_item_dic), controller.werewolf.mall.updateNormalItem)

    router.post(`${API_VERSION}/werewolf/mall/getAchievementList`, accCtr(AccessRouteId.wolf_mall_item_dic), controller.werewolf.mall.getAchievementList)
    router.post(`${API_VERSION}/werewolf/mall/getAchievementListCount`, accCtr(AccessRouteId.wolf_mall_item_dic), controller.werewolf.mall.getAchievementListCount)
    router.post(`${API_VERSION}/werewolf/mall/createAchievement`, accCtr(AccessRouteId.wolf_mall_item_dic), controller.werewolf.mall.createAchievement)
    router.post(`${API_VERSION}/werewolf/mall/updateAchievement`, accCtr(AccessRouteId.wolf_mall_item_dic), controller.werewolf.mall.updateAchievement)
    router.post(`${API_VERSION}/werewolf/mall/updateAchievementDelsign`, accCtr(AccessRouteId.wolf_mall_item_dic), controller.werewolf.mall.updateAchievementDelsign)
    router.post(`${API_VERSION}/werewolf/mall/updateAchievementComplete`, accCtr(AccessRouteId.wolf_mall_item_dic), controller.werewolf.mall.updateAchievementComplete)


    router.post(`${API_VERSION}/werewolf/mall/getCouponList`, controller.werewolf.mall.getCouponList)
    router.post(`${API_VERSION}/werewolf/mall/createCoupon`, controller.werewolf.mall.createCoupon)
    router.post(`${API_VERSION}/werewolf/mall/updateCoupon`, controller.werewolf.mall.updateCoupon)
    router.post(`${API_VERSION}/werewolf/mall/updateCouponImage`, controller.werewolf.mall.updateCouponImage)
    router.post(`${API_VERSION}/werewolf/mall/updateCouponDelsign`, controller.werewolf.mall.updateCouponDelsign)

    router.post(`${API_VERSION}/werewolf/mall/getFrameConditionList`, controller.werewolf.mall.getFrameConditionList)
    router.post(`${API_VERSION}/werewolf/mall/getAllFrameList`, controller.werewolf.mall.getAllFrameList)
    router.post(`${API_VERSION}/werewolf/mall/updateFrameCondition`, controller.werewolf.mall.updateFrameCondition)
    router.post(`${API_VERSION}/werewolf/mall/insertFrameCondition`, controller.werewolf.mall.insertFrameCondition)
    router.post(`${API_VERSION}/werewolf/mall/updateFrameConditionDelsign`, controller.werewolf.mall.updateFrameConditionDelsign)

}

export default load
