/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-10-15 13:24:36
 * @LastEditTime: 2020-10-15 17:14:58
 * @LastEditors: jiawen.wang
 */
import { Application } from "egg";
import { AccessRouteId } from './model/accessRouteCof';

const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {
    const { router, controller } = app;
    router.post(`${API_VERSION}/werewolf/updateContent/getUpdateContentList`, accCtr(AccessRouteId.wolf_tsys_bvrs), controller.werewolf.updateContent.getUpdateContentList);
    router.post(`${API_VERSION}/werewolf/updateContent/createUpdateContent`, accCtr(AccessRouteId.wolf_tsys_bvrs), controller.werewolf.updateContent.createUpdateContent);
    router.post(`${API_VERSION}/werewolf/updateContent/delUpdateContent`, accCtr(AccessRouteId.wolf_tsys_bvrs), controller.werewolf.updateContent.delUpdateContent);
    router.post(`${API_VERSION}/werewolf/updateContent/editUpdateContent`, accCtr(AccessRouteId.wolf_tsys_bvrs), controller.werewolf.updateContent.editUpdateContent);

}
export default load