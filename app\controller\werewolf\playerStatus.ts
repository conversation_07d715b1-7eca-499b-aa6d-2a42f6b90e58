/*
 * @Description: 用户状态
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: Lee<PERSON>
 * @Date: 2018-12-24 13:30:00
 * @LastEditors: jiawen.wang
 * @LastEditTime: 2021-05-15 13:41:46
 */
import { Controller } from "egg";
import { HttpErr, IerrorMsg, Ipayload } from "../../model/common";
import BaseMegaController from './BaseMegaController';
import {
  IplayerStatusRequest,
  IplayerStatusResponse,
  IbrushScoreRequest,
  IbrushScoreResponse,
  IescapeRequest,
  IescapeResponse,
  IgameSpeakRequest,
  IgameSpeakResponse,
  IplayerBgRequest,
  IplayerBgResponse,
  IbannedPlayerRequest,
  IshutterPlayerRequest,
  IbannedBgRequest,
  IbannedBgResponse,
  IremoveBannedRequest,
  IremoveBannedResponse,
  IreportRequest,
  IreportResponse,
  IillegalImagesRequest,
  IillegalImagesResponse,
  IshutterResponse,
  IshutterRequest,
  IliftShutterRequest,
  IreportVideoListRequest,
  IreportVideoListResponse,
  IboardcastImpListRequest,
  IboardcastImpListResponse,
  BanUserAbilityRequest,
  RemoveForeverBlockRequest,
  InewBrushScoreResponse,
  InewBrushScoreRequest,
  ImemberChangeSearch,
  ImemberChange,
  IbarragePlayerRequest,
  IbanEntertainmentRequest,
  IentertainmentSearchUserRequest,
  IentertainmentSearchUserDetailRequest,
  IentertainmentSearchRoomRequest,
  IentertainmentSearchRoomUsersRequest,
  IentertainmentSearchRoomReportsRequest,
  IentertainmentSearchRoomChatsRequest,
  IentertainmentSearchGroupRoomsRequest,
  IentertainmentKickOutRoomRequest
} from "../../model/werewolf";

export default class PlayerStatusController extends BaseMegaController {

  /**
   * @name:充值玩家头像为1 
   * @msg: 
   * @param {type} 
   * @return: 
   */
  public async resetAvatar() {
    const { ctx, logger } = this;
    // 校验规则
    const rule = {
      playerId: { type: "number" },
      // uid: { type: "number" }
    };
    try {
      // 校验
      ctx.validate(rule);
      const requestBody: IplayerStatusRequest = ctx.request.body;
      await ctx.service.werewolf.playerStatus.resetAvatar(requestBody.playerId)
      ctx.body = { err_code: HttpErr.Success, err_msg: "ok" };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "server error"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async updateNickname() {
    const { ctx, logger } = this;
    // 校验规则
    const rule = {
      playerId: { type: "number" },
      nickname: { type: "string" },
      uid: { type: "number" }
    };
    try {
      // 校验
      ctx.validate(rule);
      const requestBody = ctx.request.body;
      await ctx.service.werewolf.playerStatus.updateNickname(requestBody.playerId, requestBody.nickname)
      ctx.body = { err_code: HttpErr.Success, err_msg: "ok" };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "server error"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async updateScore() {
    const { ctx, logger } = this;
    // 校验规则
    const rule = {
      playerId: { type: "number" },
      score: { type: "number" },
      uid: { type: "number" }
    };
    try {
      // 校验
      ctx.validate(rule);
      const requestBody = ctx.request.body;
      await ctx.service.werewolf.playerStatus.updateScore(requestBody.playerId, requestBody.score)
      ctx.body = { err_code: HttpErr.Success, err_msg: "ok" };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "server error"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async getPlayerId() {
    const { ctx, logger } = this;
    try {
      const requestBody: IplayerStatusRequest = ctx.request.body;
      const list = await ctx.service.werewolf.playerStatus.getPlayerId(requestBody);
      this.respSuccData(list)
    } catch (err) { this.respFail(err) }
  }
  public async getPhoneNumber() {
    const { ctx, logger } = this;
    try {
      const requestBody: IplayerStatusRequest = ctx.request.body;
      const list = await ctx.service.werewolf.playerStatus.getPhoneNumber(requestBody);
      this.respSuccData(list)
    } catch (err) { this.respFail(err) }
  }

  /**
   * 用户状态查询基础信息
   */
  public async defalut() {
    const { ctx, logger } = this;
    // 校验规则
    const rule = {
      playerId: { type: "number" },
      uid: { type: "number" }
    };
    try {
      // 校验
      ctx.validate(rule);
      //service
      const requestBody: IplayerStatusRequest = ctx.request.body;
      const responseBody: IplayerStatusResponse = await ctx.service.werewolf.playerStatus.default(
        requestBody
      );
      ctx.body = !!responseBody ? responseBody : "";
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  /**
   * 查询用户刷分记录
   */
  public async brushScore() {
    const { ctx, logger } = this;
    // 校验规则
    const rule = {
      playerId: { type: "number" },
      uid: { type: "number" },
      start: { type: "number" },
      offset: { type: "number" }
    };
    try {
      // 校验
      ctx.validate(rule);
      //service
      const requestBody: IbrushScoreRequest = ctx.request.body;
      const responseBody: IbrushScoreResponse = await ctx.service.werewolf.playerStatus.brushScore(
        requestBody
      );
      ctx.body = responseBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  /**
   * 新版刷分记录
   */
  public async newBrushScore() {
    const { ctx, logger } = this;
    // 校验规则
    const rule = {
      playerId: { type: "number" },
      uid: { type: "number" }
    };
    try {
      // 校验
      ctx.validate(rule);
      //service
      const requestBody: InewBrushScoreRequest = ctx.request.body;
      const responseBody: InewBrushScoreResponse = await ctx.service.werewolf.playerStatus.newBrushScore(
        requestBody
      );
      ctx.body = responseBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  /**
   * 用户状态逃跑记录
   */
  public async escape() {
    const { ctx, logger } = this;
    // 校验规则
    const rule = {
      playerId: { type: "number" },
      uid: { type: "number" },
      start: { type: "number" },
      offset: { type: "number" }
    };
    try {
      // 校验
      ctx.validate(rule);
      //service
      const requestBody: IescapeRequest = ctx.request.body;
      const responseBody: IescapeResponse = await ctx.service.werewolf.playerStatus.escape(
        requestBody
      );
      ctx.body = responseBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  /**
   * 用户状态游戏中发言记录
   */
  public async gameSpeak() {
    const { ctx, logger } = this;
    // 校验规则
    const rule = {
      playerId: { type: "number" },
      uid: { type: "number" },
      start: { type: "number" },
      offset: { type: "number" }
    };
    try {
      // 校验
      ctx.validate(rule);
      //service
      const requestBody: IgameSpeakRequest = ctx.request.body;
      const responseBody: IgameSpeakResponse = await ctx.service.werewolf.playerStatus.gameSpeak(
        requestBody
      );
      ctx.body = responseBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  /**
   * 查询用户背景图
   */
  public async playerBg() {
    const { ctx, logger } = this;
    // 校验规则
    const rule = {
      playerId: { type: "number" },
      uid: { type: "number" },
      start: { type: "number" },
      offset: { type: "number" }
    };
    try {
      // 校验
      ctx.validate(rule);
      //service
      const requestBody: IplayerBgRequest = ctx.request.body;
      const responseBody: IplayerBgResponse = await ctx.service.werewolf.playerStatus.playerBg(
        requestBody
      );
      ctx.body = responseBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async dobarrage() {
    const { ctx, logger } = this;
    const rule = {
      playerId: { type: "number" },
      uid: { type: "number" },
      type: { type: "number" },
      day: { type: "number" },
      reason: { type: "string" },
    };
    try {
      // 校验
      ctx.validate(rule);
      //service
      const requestBody: IshutterPlayerRequest = ctx.request.body;
      await ctx.service.werewolf.playerStatus.dobarrage(requestBody);
      ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }
  public async doMobile() {
    const { ctx, logger } = this;
    const rule = {
      playerId: { type: "number" },
    };
    try {
      // 校验
      ctx.validate(rule);
      //service
      const requestBody: IbarragePlayerRequest = ctx.request.body;
      let udid = await ctx.service.werewolf.playerStatus.selUserUdid(requestBody);
      //查询udid是否已被封禁
      let count = await ctx.service.werewolf.playerStatus.selUdidState(udid);
      if (count == 0) {
        await ctx.service.werewolf.playerStatus.doMobile(udid);
      }
      ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }


  /*娱乐模式: 用户id查询*/
  public async entertainmentSearchReportRoom() {
    const { ctx, logger } = this;
    const rule = {
      showId: { type: "number" },
      uid: { type: "number" },
    };
    try {
      // 校验
      ctx.validate(rule);
      //service
      const requestBody: IentertainmentSearchRoomRequest = ctx.request.body;
      const respBody = await ctx.service.werewolf.playerStatus.entertainmentSearchReportRoom(requestBody);
      ctx.body = respBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };

      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  /*娱乐模式: 房间id 查users*/
  public async entertainmentSearchRoomUsers() {
    const { ctx, logger } = this;
    const rule = {
      roomId: { type: "number" },
      uid: { type: "number" },
    };
    try {
      // 校验
      ctx.validate(rule);
      //service
      const requestBody: IentertainmentSearchRoomUsersRequest = ctx.request.body;
      const respBody = await ctx.service.werewolf.playerStatus.entertainmentSearchRoomUsers(requestBody);
      ctx.body = respBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };

      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  /*娱乐模式: 查询房间举报记录*/
  public async entertainmentSearchRoomReports() {
    const { ctx, logger } = this;
    const rule = {
      roomId: { type: "number" },
      uid: { type: "number" },
    };
    try {
      // 校验
      ctx.validate(rule);
      //service
      const requestBody: IentertainmentSearchRoomReportsRequest = ctx.request.body;
      const respBody = await ctx.service.werewolf.playerStatus.entertainmentSearchRoomReports(requestBody);
      ctx.body = respBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };

      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  /*娱乐模式: 查询房间举报记录*/
  public async entertainmentSearchRoomChats() {
    const { ctx, logger } = this;
    const rule = {
      roomId: { type: "number" },
      uid: { type: "number" },
    };
    try {
      // 校验
      ctx.validate(rule);
      //service
      const requestBody: IentertainmentSearchRoomChatsRequest = ctx.request.body;
      const respBody = await ctx.service.werewolf.playerStatus.entertainmentSearchRoomChats(requestBody);
      ctx.body = respBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };

      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }


  /*娱乐模式: 查询房间举报记录*/
  public async entertainmentSearchGroupRooms() {
    const { ctx, logger } = this;
    const rule = {
      groupId: { type: "number" },
      uid: { type: "number" },
    };
    try {
      // 校验
      ctx.validate(rule);
      //service
      const requestBody: IentertainmentSearchGroupRoomsRequest = ctx.request.body;
      const respBody = await ctx.service.werewolf.playerStatus.entertainmentSearchGroupRooms(requestBody);
      ctx.body = respBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };

      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  /*娱乐模式: 用户id查询*/
  public async entertainmentSearchReportUser() {
    const { ctx, logger } = this;
    const rule = {
      playerId: { type: "number" },
      uid: { type: "number" },
    };
    try {
      // 校验
      ctx.validate(rule);
      //service
      const requestBody: IentertainmentSearchUserRequest = ctx.request.body;
      const responseBody = await ctx.service.werewolf.playerStatus.entertainmentSearchReportUser(requestBody);
      ctx.body = responseBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };

      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  /*娱乐模式: 用户id查询详情*/
  public async entertainmentSearchReportUserDetail() {
    const { ctx, logger } = this;
    const rule = {
      playerId: { type: "number" },
      uid: { type: "number" },
    };
    try {
      // 校验
      ctx.validate(rule);
      //service
      const requestBody: IentertainmentSearchUserDetailRequest = ctx.request.body;
      const responseBody = await ctx.service.werewolf.playerStatus.entertainmentSearchReportUserDetail(requestBody);
      ctx.body = responseBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };

      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async entertainmentKickOutRoom(){
    const { ctx, logger } = this;

    const rule = {
      roomId: { type: "number" },
      uid: { type: "number" },
    };
    try {
      // 校验
      ctx.validate(rule);
      //service
      const requestBody: IentertainmentKickOutRoomRequest = ctx.request.body;
      const responseBody = await ctx.service.werewolf.playerStatus.entertainmentKickOutRoom(requestBody);
      ctx.body = responseBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };

      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  /*封禁娱乐模式*/
  public async doBanEntertainment() {
    const { ctx, logger } = this;
    const rule = {
      playerId: { type: "number" },
      uid: { type: "number" },
      reason: { type: "string" },
    };
    try {
      // 校验
      ctx.validate(rule);
      //service
      const requestBody: IbanEntertainmentRequest = ctx.request.body;
      await ctx.service.werewolf.playerStatus.doBanEntertainment(requestBody);
      ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };

      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  /**
   * 用户状态操作封号
   */
  public async doBanned() {
    const { ctx, logger } = this;
    // 校验规则
    const rule = {
      playerId: { type: "number" },
      uid: { type: "number" },
      reason: { type: "string" }
    };
    try {
      // 校验
      ctx.validate(rule);
      //service
      const requestBody: IbannedPlayerRequest = ctx.request.body;
      const responseBody = await ctx.service.werewolf.playerStatus.doBanned(
        requestBody
      );
      ctx.body = responseBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async getUserBanId() {
    const { ctx, logger } = this;
    try {
      //service
      const requestBody: any = ctx.request.body;
      const responseBody = await ctx.service.werewolf.playerStatus.getUserBanId(requestBody);
      ctx.body = responseBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  /**
   * 关用户禁闭
   */
  public async doShutter() {
    const { ctx, logger } = this;
    // 校验规则
    const rule = {
      playerId: { type: "number" },
      uid: { type: "number" },
      type: { type: "number" },
      day: { type: "number" },
      reason: { type: "string" }
    };
    try {
      // 校验
      ctx.validate(rule);
      //service
      const requestBody: IshutterPlayerRequest = ctx.request.body;
      const responseBody = await ctx.service.werewolf.playerStatus.doShutter(
        requestBody
      );
      ctx.body = responseBody;
      ctx.status = HttpErr.Success;
      this.respSucc();
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  /**
   * 封禁用户背景图
   */
  public async bannedBg() {
    const { ctx, logger } = this;
    // 校验规则
    const rule = {
      playerId: { type: "number" },
      uid: { type: "number" },
      animationId: { type: "number" }
    };
    try {
      // 校验
      ctx.validate(rule);
      //service
      const requestBody: IbannedBgRequest = ctx.request.body;
      const responseBody: IbannedBgResponse = await ctx.service.werewolf.playerStatus.bannedBg(
        requestBody
      );
      ctx.body = responseBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  /**
   * 清除用户状态
   */
  public async remove() {
    const { ctx, logger } = this;
    // 校验规则
    const rule = {
      playerId: { type: "number" },
      uid: { type: "number" },
      state: { type: "number" }
    };
    try {
      // 校验
      ctx.validate(rule);
      //service
      const requestBody: IremoveBannedRequest = ctx.request.body;
      const responseBody: IremoveBannedResponse = await ctx.service.werewolf.playerStatus.remove(
        requestBody
      );
      //状态错误
      // if (!!responseBody.err_msg) {
      //   const err: IerrorMsg = {
      //     err_code: HttpErr.PlayerStatusErr,
      //     err_msg: responseBody.err_msg
      //   };
      //   ctx.body = err;
      //   ctx.status = HttpErr.BadRequest;
      // } else {
      ctx.body = responseBody;
      ctx.status = HttpErr.Success;
      // }
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  /**
   * 查看举报记录
   */
  public async report() {
    const { ctx, logger } = this;
    // 校验规则
    const rule = {
      playerId: { type: "number" },
      uid: { type: "number" },
      // start: { type: "number" },
      // offset: { type: "number" }
    };
    try {
      // 校验
      ctx.validate(rule);
      //service
      const requestBody: IreportRequest = ctx.request.body;
      const responseBody: IreportResponse = await ctx.service.werewolf.playerStatus.report(
        requestBody
      );
      ctx.body = responseBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  /**
   * 查看违规图片
   */
  public async illegalImages() {
    const { ctx, logger } = this;
    // 校验规则
    const rule = {
      playerId: { type: "number" },
      uid: { type: "number" },
      start: { type: "number" },
      offset: { type: "number" }
    };
    try {
      // 校验
      ctx.validate(rule);
      //service
      const requestBody: IillegalImagesRequest = ctx.request.body;
      const responseBody: IillegalImagesResponse = await ctx.service.werewolf.playerStatus.illegalImages(
        requestBody
      );
      ctx.body = responseBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  /**
   * 查询被封禁列表
   */
  public async getShutterList() {
    const { ctx, logger } = this;
    // 校验规则
    const rule = {
      playerId: { type: "number" },
      uid: { type: "number" }
    };
    try {
      // 校验
      ctx.validate(rule);
      //service
      const requestBody: IshutterRequest = ctx.request.body;
      const responseBody: IshutterResponse = await ctx.service.werewolf.playerStatus.getShutterList(
        requestBody
      );
      ctx.body = responseBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  /**
   * 解除新的禁闭
   */
  public async leftShutter() {
    const { ctx, logger } = this;
    // 校验规则
    const rule = {
      playerId: { type: "number" },
      uid: { type: "number" },
      type: { type: "number" }
    };
    try {
      // 校验
      ctx.validate(rule);
      //service
      const requestBody: IliftShutterRequest = ctx.request.body;
      const responseBody = await ctx.service.werewolf.playerStatus.liftShutter(
        requestBody
      );
      ctx.body = responseBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  /**
   * 被举报视频
   */
  public async reportVideoList() {
    const { ctx, logger } = this;
    // 校验规则
    const rule = {
      playerId: { type: "number" },
      uid: { type: "number" },
      gameId: { type: "number" }
    };
    try {
      // 校验
      ctx.validate(rule);
      //service
      const requestBody: IreportVideoListRequest = ctx.request.body;
      const responseBody: IreportVideoListResponse = await ctx.service.werewolf.playerStatus.reportVideoList(
        requestBody
      );
      ctx.body = responseBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  /**
  * 查询被禁言世界频道记录
  */
  public async getBoardcastImpList() {
    const { ctx, logger } = this;
    // 校验规则
    const rule = {
      playerId: { type: "number" },
      uid: { type: "number" },
      start: { type: "number" },
      offset: { type: "number" }
    };
    try {
      // 校验
      ctx.validate(rule);
      //service
      const requestBody: IboardcastImpListRequest = ctx.request.body;
      const responseBody: IboardcastImpListResponse = await ctx.service.werewolf.playerStatusList.boardcastImpList(
        requestBody
      );
      ctx.body = responseBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  /**
   * @name: 封禁功能
   */
  public async banUserAbility() {
    const { ctx, logger } = this;
    // 校验规则
    const rule = {
      uid: { type: "number" },
      playerId: { type: "number" },
      abilityId: { type: "number" },
      banTime: { type: "number" },
      reason: { type: "string" },
    };
    try {
      // 校验
      ctx.validate(rule);
      const request: BanUserAbilityRequest = ctx.request.body;
      await ctx.service.werewolf.playerStatusList.banUserAbility(request);
      const body: IerrorMsg = {
        err_code: HttpErr.Success,
        err_msg: "操作成功"
      };
      ctx.body = body;
      ctx.status = HttpErr.Success;
    } catch (error) {
      logger.error(error);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }
  /**
   * @name: 解封永久功能封禁
   */
  public async removeForeverBlock() {
    const { ctx, logger } = this;
    // 校验规则
    const rule = {
      uid: { type: "number" },
      playerId: { type: "number" },
      abilityId: { type: "number" },
    };
    try {
      ctx.validate(rule);
      const request: RemoveForeverBlockRequest = ctx.request.body;
      await ctx.service.werewolf.playerStatusList.removeForeverBlock(request);
      const body: IerrorMsg = {
        err_code: HttpErr.Success,
        err_msg: "操作成功"
      };
      ctx.body = body;
      ctx.status = HttpErr.Success;
    } catch (error) {
      logger.error(error);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  /**
   * @name:充值玩家头像为1
   * @msg:
   * @param {type}
   * @return:
   */
  public async getMemberChange() {
    const { ctx, logger } = this;
    // 校验规则
    // const rule = {
    //   playerId: { type: "number" },
    //   // uid: { type: "number" }
    // };
    try {
      // 校验
      // ctx.validate(rule);
      const request: ImemberChangeSearch = ctx.request.body;
      const resp = await ctx.service.werewolf.playerStatus.getMemberChange(request);
      ctx.body = resp;
      ctx.status = HttpErr.Success;

    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "server error"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  /**
     * @name:
     * @msg:
     * @param {type}
     * @return:
     */
  public async checkUserExist() {
    const { ctx, logger } = this;
    // 校验规则
    // const rule = {
    //   playerId: { type: "number" },
    //   // uid: { type: "number" }
    // };
    try {
      // 校验
      // ctx.validate(rule);
      const request: ImemberChange = ctx.request.body;
      const resp = await ctx.service.werewolf.playerStatus.checkUserExist(request);
      ctx.body = resp;
      ctx.status = HttpErr.Success;

    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "server error"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  /**
   * @name:
   * @msg:
   * @param {type}
   * @return:
   */
  public async insertMemberChange() {
    const { ctx, logger } = this;
    // 校验规则
    // const rule = {
    //   playerId: { type: "number" },
    //   // uid: { type: "number" }
    // };
    try {
      // 校验
      // ctx.validate(rule);
      const request: ImemberChange = ctx.request.body;
      const resp = await ctx.service.werewolf.playerStatus.insertMemberChange(request);
      ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
      ctx.status = HttpErr.Success;

    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "server error"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  /**
   * @name:
   * @msg:
   * @param {type}
   * @return:
   */
  public async updateMemberChange() {
    const { ctx, logger } = this;
    // 校验规则
    // const rule = {
    //   playerId: { type: "number" },
    //   // uid: { type: "number" }
    // };
    try {
      // 校验
      // ctx.validate(rule);
      const request: ImemberChange = ctx.request.body;
      const resp = await ctx.service.werewolf.playerStatus.updateMemberChange(request);
      ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
      ctx.status = HttpErr.Success;

    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "server error"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  /**
   * @name:
   * @msg:
   * @param {type}
   * @return:
   */
  public async updateMemberChangeAndUserName() {
    const { ctx, logger } = this;
    // 校验规则
    // const rule = {
    //   playerId: { type: "number" },
    //   // uid: { type: "number" }
    // };
    try {
      // 校验
      // ctx.validate(rule);
      const request: ImemberChange = ctx.request.body;
      const resp = await ctx.service.werewolf.playerStatus.updateMemberChangeAndUserName(request);
      ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
      ctx.status = HttpErr.Success;

    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "server error"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  /**
     * @name:
     * @msg:
     * @param {type}
     * @return:
     */
  public async checkUserExistName() {
    const { ctx, logger } = this;
    // 校验规则
    // const rule = {
    //   playerId: { type: "number" },
    //   // uid: { type: "number" }
    // };
    try {
      // 校验
      // ctx.validate(rule);
      const request: ImemberChange = ctx.request.body;
      const resp = await ctx.service.werewolf.playerStatus.checkUserExistName(request);
      ctx.body = resp;
      ctx.status = HttpErr.Success;

    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "server error"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }
  public async downloadRestoreOSS(request) {
    const { ctx, logger } = this;
    // 校验规则
    const rule = {
      url: { type: "string" },
      // uid: { type: "number" }
    };
    try {
      // 校验
      // ctx.validate(rule);
      const request = ctx.request.body;
      const resp = await ctx.service.werewolf.playerStatus.downloadRestoreOSS(request);
      ctx.body = resp;
      ctx.status = HttpErr.Success;

    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "server error"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }
  public async getIdCardList(request) {
    const { ctx, logger } = this;
    // 校验规则
    const rule = {
      idNumber: { type: "string" },
      // uid: { type: "number" }
    };
    try {
      // 校验
      // ctx.validate(rule);
      const request = ctx.request.body;
      const resp = await ctx.service.werewolf.playerStatus.getIdCardList(request);
      ctx.body = resp;
      ctx.status = HttpErr.Success;

    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "server error"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async setIdCardBan(request) {
    const { ctx, logger } = this;
    // 校验规则
    const rule = {
      idNumber: { type: "string" },
      // uid: { type: "number" }
    };
    try {
      // 校验
      ctx.validate(rule);
      const request = ctx.request.body;
      const resp = await ctx.service.werewolf.playerStatus.setIdCardBan(request);
      ctx.body = resp;
      ctx.status = HttpErr.Success;
      // this.respSucc()

    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "server error"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }
  public async delIdCardBan(request) {
    const { ctx, logger } = this;
    // 校验规则
    const rule = {
      idNumber: { type: "string" },
      // uid: { type: "number" }
    };
    try {
      // 校验
      ctx.validate(rule);
      const request = ctx.request.body;
      await ctx.service.werewolf.playerStatus.delIdCardBan(request);
      // ctx.body = resp;
      // ctx.status = HttpErr.Success;
      this.respSucc()


    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "server error"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }
}
