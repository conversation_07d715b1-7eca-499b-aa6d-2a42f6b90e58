import BaseMegaService from "./BaseMegaService";
import {IgiftItemParams, IupdateGiftItemParams} from "../../model/giftItemListDto";
import {isAvatarFrameCateId} from "../../util/utils";

export default class GiftItemListService extends BaseMegaService {

    public async getGiftItems(params: any) {

        const sql = `SELECT a.id,
                            a.gift_id,
                            b.\`name\` AS gift_name,
                            a.item_dic_id,
                            c.\`name\` AS item_dic_name,
                            c.item_cate_id,
                            a.num,
                            a.weight,
                            a.sex,
                            a.\`repeat\`,
                            a.rate,
                            a.delsign,
                            a.avatarframe_flag
                     FROM gift_item a
                              LEFT JOIN gift b ON a.gift_id = b.\`no\`
                              LEFT JOIN item_dic c ON a.item_dic_id = c.id
                     ORDER BY id DESC`

        try {
            const res = await this.selectList(sql)
            return res
        } catch (e) {
            this.logger.error(e)
            return false
        }
    }

    public async addGiftItem(params: IgiftItemParams) {

        // 2010 2020
        const sql = `INSERT INTO gift_item (gift_id, item_dic_id, num, weight, \`repeat\`, rate, delsign, avatarframe_flag)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?)`

        try {
            const avatarframe_flag = isAvatarFrameCateId(params.item_cate_id) ? 1 : 0
            const res = await this.execSql(sql, [params.gift_id, params.item_dic_id, params.num, params.weight, params.repeat, params.rate, params.delsign, avatarframe_flag])
            return res
        } catch (e) {
            this.logger.error(e)
            return false
        }
    }

    public async updateGiftItem(params: IupdateGiftItemParams) {

        const avatarframe_flag = isAvatarFrameCateId(params.item_cate_id) ? 1 : 0
        const sql = `UPDATE gift_item
                     SET gift_id=?,
                         item_dic_id=?,
                         num=?,
                         weight=?,
                         \`repeat\`=?,
                         rate=?,
                         delsign=?,
                         avatarframe_flag=?
                     WHERE id = ?`

        try {
            const res = await this.execSql(sql, [params.gift_id, params.item_dic_id, params.num, params.weight, params.repeat, params.rate, params.delsign, avatarframe_flag, params.id])
            return res
        } catch (e) {
            this.logger.error(e)
            return false
        }
    }


    public async getGifts(params: any) {

        const sql = `SELECT \`no\`, \`name\`
                     FROM gift ORDER BY \`no\` DESC`

        try {
            const res = await this.selectList(sql)
            return res
        } catch (e) {
            this.logger.error(e)
            return false
        }
    }
}