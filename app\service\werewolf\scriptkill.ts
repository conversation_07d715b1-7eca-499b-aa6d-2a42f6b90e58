import { time } from 'console';
/*
 * @Description: 
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhanglu
 * @Date: 2020-11-17 10:50:39
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-01-22 11:55:21
 */
import BaseMegaService from './BaseMegaService';
const srpiptkillDb = "scriptkill";
const managerDb = "manager";

export default class ScriptkillService extends BaseMegaService {

    /**
     * @name: 
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async getMerchantList(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
            SELECT
            m.id                    AS id,
            m.audit_time,
            m.id                    AS merchant_id,
            m.merchant_name         AS \`name\`,
            m.tel                   AS tel,
            m.create_time           AS \`date\`,
            m.\`status\`              AS \`status\`,
            ma.address              AS address,
            ma.latitude             AS latitude,
            ma.longitude            AS longitude,
            bp.Base_ProvincesName   AS provinceName,
            bc.city_name 		    AS cityName,
            bc.city_code            AS cityCode,
            m.delsign,
            m.audit_time,         
            m.update_time,
            
            GROUP_CONCAT(DISTINCT mpt.tag_desc ORDER BY mt.sort_id) AS tagStr,
            GROUP_CONCAT(DISTINCT mbt.day_of_week, '-', mbt.business_start_time, '-', mbt.business_end_time ORDER BY mbt.day_of_week) AS timeStr,
            GROUP_CONCAT(DISTINCT tel.tel) AS telStr
           
            FROM merchant m
            LEFT JOIN merchant_address ma ON m.id = ma.merchant_id AND ma.delsign = 0
            LEFT JOIN base_provinces bp ON bp.Base_ProvincesID = ma.province_code
            LEFT JOIN merchant_city_list bc ON bc.city_code = ma.city_code
            
            LEFT JOIN merchant_tag mt ON m.id = mt.merchant_id AND mt.delsign = 0
            LEFT JOIN merchant_public_tag mpt ON mt.tag_id = mpt.id AND mpt.delsign = 0
                
            LEFT JOIN merchant_business_time mbt ON m.id = mbt.merchant_id AND mbt.delsign = 0
            LEFT JOIN merchant_tel tel ON m.id = tel.merchant_id AND tel.delsign = 0
            `;
            if (req.where && req.where != "") {
                sqlStr += ` WHERE ` + req.where;
                sqlStr += ` GROUP BY m.id ORDER BY m.id DESC `;
                sqlStr += ` LIMIT ?, ? `;
            } else if (req.currentMerchantId && req.currentMerchantId != "") {
                sqlStr += ` WHERE m.id = ` + req.currentMerchantId;
                sqlStr += ` GROUP BY m.id ORDER BY m.id DESC `;
            }

            let result = await this.selectList(sqlStr, [(req.current - 1) * req.pageCount, req.pageCount], srpiptkillDb);

            if (result != null) {

                for (const item of result) {
                    if (item.tel != null && item.tel != "") {
                        if (item.telStr != null) {
                            item.telStr = item.telStr + "," + item.tel;
                        } else {
                            item.telStr = item.tel;
                        }

                    }
                }

                let merchantIdList: any = [];
                for (const item of result) {
                    merchantIdList.push(item.id);
                }
                let sqlImg = `
                    SELECT * FROM merchant_picture WHERE merchant_id IN (${merchantIdList.toString()}) AND delsign = 0 ORDER BY sort_id 
                `;
                let imgList = await this.selectList(sqlImg, [], srpiptkillDb);
                if (imgList != null && imgList.length > 0) {
                    for (let item of result) {
                        item.imgList = [];
                        for (const imgItem of imgList) {
                            if (item.id == imgItem.merchant_id) {
                                item.imgList.push(imgItem.oss_url);
                            }
                        }
                    }
                }

                let sqlOpe = `
                    SELECT r.operate_user_id, r.refuse_mes, r.type, u.nickname, r.merchant_id FROM sk_merchant_record r
                    INNER JOIN (SELECT MAX(createtime) AS createtime  , merchant_id FROM sk_merchant_record WHERE merchant_id IN (${merchantIdList.toString()}) GROUP BY merchant_id) t
                    ON r.merchant_id = t.merchant_id AND t.createtime = r.createtime
                    INNER JOIN wf_admin_user u ON u.id = r.operate_user_id
                    WHERE r.merchant_id IN (${merchantIdList.toString()})
                `;
                let operateList = await this.selectList(sqlOpe, [], managerDb);
                if (operateList != null && operateList.length > 0) {
                    for (let item of result) {
                        for (const imgItem of operateList) {
                            if (item.id == imgItem.merchant_id) {
                                item.operate = {
                                    operate_user_id: imgItem.operate_user_id,
                                    refuse_mes: imgItem.refuse_mes,
                                    type: imgItem.type,
                                    nickname: imgItem.nickname
                                };
                            }
                        }
                    }
                }
            }


            return result;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getMerchantListCount(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
            SELECT
            COUNT(*) AS num
            FROM merchant m
            `;
            if (req.where && req.where != "") {
                sqlStr += ` WHERE ` + req.where;
            } else if (req.currentMerchantId && req.currentMerchantId != "") {
                sqlStr += ` WHERE m.id = ` + req.currentMerchantId;
            }

            return await this.selectOne(sqlStr, [], srpiptkillDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async confirmMerchant(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get(srpiptkillDb);
        const manager = app.mysql.get(managerDb);

        //开启事物
        const conn = await db.beginTransaction();
        const managerConn = await manager.beginTransaction();
        try {
            let sqlStr = `
            UPDATE merchant
            SET 
            \`status\` = 10,
            audit_time = NOW(),
            fail_reason = ""
            WHERE
                \`id\` = ?;
            `;
            await conn.query(sqlStr, [req.id]);

            let sqlConfirm = `
            INSERT INTO sk_merchant_record(operate_user_id, merchant_id, type) VALUES (?, ?, 10) `;
            await managerConn.query(sqlConfirm, [req.operateUserId, req.id]);

            await conn.commit(); // 提交事务
            await managerConn.commit();

        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            await managerConn.rollback();
            logger.error(error);
            throw error
        }
    }

    public async refuseMerchant(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get(srpiptkillDb);
        const manager = app.mysql.get(managerDb);

        //开启事物
        const conn = await db.beginTransaction();
        const managerConn = await manager.beginTransaction();
        try {
            let sqlStr = `
                UPDATE merchant
                SET 
                \`status\` = 2,
                fail_reason = ?,
                audit_time = NOW()
                WHERE
                    \`id\` = ?
            `;
            await conn.query(sqlStr, [req.refuseMes, req.id]);

            let sqlRefuse = `
                INSERT INTO sk_merchant_record(operate_user_id, refuse_mes, merchant_id, type) VALUES ( ?, ?, ?, 2)
            `;
            await managerConn.query(sqlRefuse, [req.operateUserId, req.refuseMes, req.id]);

            await conn.commit(); // 提交事务
            await managerConn.commit();
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            await managerConn.rollback();
            logger.error(error);
            throw error
        }
    }

    public async updateMerchant(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get(srpiptkillDb);
        const manager = app.mysql.get(managerDb);

        //开启事物
        const conn = await db.beginTransaction();

        try {
            // tel = '${req.telStr}',

            let sqlStr = `
            UPDATE merchant 
            SET 
            merchant_name = '${req.name}',
            update_time = NOW()
            WHERE
                id = ${req.id}
            `;
            
            await conn.query(sqlStr, []);

            let sqlUpdateAddress = `
                UPDATE merchant_address 
                SET 
                address = '${req.address}',
                city_code = ${req.cityCode}
                WHERE
                    merchant_id = ${req.id};
            `
            await conn.query(sqlUpdateAddress, []);

            let sqlUpdateTag = `
                UPDATE merchant_tag 
                SET 
                delsign = 1 
                WHERE
                    merchant_id = ${req.id}
            `;
            await conn.query(sqlUpdateTag, []);

            for (const tag of req.tagList) {
                let tag_desc = tag.content;
                let sqlSelectTag = `
                    SELECT id FROM merchant_public_tag WHERE tag_desc = '${tag_desc}'
                `;
                let tagIdResult = await conn.query(sqlSelectTag, []);
                if (tagIdResult && tagIdResult.length > 0) {
                    const tagId = tagIdResult[0].id
                    let sqlInsertUpdateTag = `
                        INSERT INTO merchant_tag(tag_id, merchant_id, delsign) VALUES (${tagId}, ${req.id}, 0) ON DUPLICATE KEY UPDATE delsign = 0
                `;
                    await conn.query(sqlInsertUpdateTag, []);
                } else {
                    let sqlInsetTag = `
                        INSERT INTO merchant_public_tag(tag_desc, merchant_id, sort_id, create_time, delsign) VALUES 
                        ('${tag_desc}', ${req.id}, 0, NOW(), 0)
                    `;
                    let insertIdReslt = await conn.query(sqlInsetTag, []);
                    const tagId = insertIdReslt.insertId;
                    let sqlInsertUpdateTag = `
                        INSERT INTO merchant_tag(tag_id, merchant_id, delsign) VALUES (${tagId}, ${req.id}, 0) ON DUPLICATE KEY UPDATE delsign = 0
                    `;
                    await conn.query(sqlInsertUpdateTag, []);
                }
            }
            await conn.commit(); // 提交事务

        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            logger.error(error);
            throw error
        }
    }


    public async getMerchantSimpleList(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
            SELECT id, merchant_name AS \`name\` FROM merchant WHERE merchant_name IS NOT NULL AND status != 0 ORDER BY id DESC
            `;
            return await this.selectList(sqlStr, [], srpiptkillDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getScriptList(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
            SELECT 
            ms.id AS id,
            msr.merchant_id,
            ms.NAME AS \`name\`,
            ms.delsign,
            ms.cover_oss_url AS img,
            ms.difficulty AS difficulty,
            ms.script_desc AS \`desc\`,
            ms.role_num AS roleNum,
            ms.hot AS hot,
            ms.tips AS tips,
            ms.publisher,
            ms.\`status\` AS \`status\`,
            GROUP_CONCAT(DISTINCT md.dic_desc ORDER BY mst.sort_id ) AS themeList,
            GROUP_CONCAT(DISTINCT md.dic_desc,'-mega-', md.code ORDER BY mst.sort_id ) AS themeStr,
            GROUP_CONCAT( msrole.id, '-mega-', msrole.oss_url, '-mega-', msrole.role_desc, '-mega-', msrole.name, '-mega-', msrole.delsign ORDER BY msrole.sort_id ) AS roleStr
            FROM merchant_script ms
            LEFT JOIN merchant_script_theme mst ON ms.id = mst.script_id AND mst.delsign = 0
            LEFT JOIN merchant_script_ref msr on ms.id = msr.script_id AND msr.delsign = 0
            LEFT JOIN merchant_address ma on msr.merchant_id = ma.merchant_id AND ma.delsign = 0
            LEFT JOIN merchant_dic md ON md.code = mst.code AND md.type = 3
            LEFT JOIN merchant_script_role msrole ON msrole.script_id = ms.id
            `;
            let searchMerchant = false;
            if (req.where != undefined && req.where != null && req.where != "") {
                sqlStr += ` WHERE ` + req.where;
                sqlStr += ` GROUP BY ms.id `;
            } else if (req.merchant_id != undefined && req.merchant_id != null && req.merchant_id > 0) {
                sqlStr += ` WHERE msr.merchant_id = ` + req.merchant_id;
                sqlStr += ` AND ms.status != 0 `;
                sqlStr += ` GROUP BY ms.id `;
                searchMerchant = true;
            } else if (req.id != undefined && req.id != null && req.id > 0) {
                sqlStr += ` WHERE ms.id = ` + req.id;
                sqlStr += ` AND ms.status != 0 `;
                sqlStr += ` GROUP BY ms.id `;
            }
            sqlStr += ` ORDER BY ms.status ASC `;


            let result: any = [];

            if (searchMerchant) {
                let sqlSelectTempMerchant = `
                    SELECT id AS merchant_temp_id FROM merchant_temp WHERE merchant_id = ? AND delsign = 0 LIMIT 1
                `;
                let resultTemp = await this.selectOne(sqlSelectTempMerchant, [req.merchant_id], srpiptkillDb);
                if (resultTemp != null) {
                    const merchant_temp_id = resultTemp.merchant_temp_id;
                    if (merchant_temp_id != null) {
                        let sqlSelectTempScript = `
                            SELECT 
                            ms.id AS id,
                            ${req.merchant_id} AS merchant_id,
                            ms.NAME AS name,
                            ms.delsign,
                            ms.cover_oss_url AS img,
                            ms.difficulty AS difficulty,
                            ms.script_desc AS \`desc\`,
                            ms.role_num AS roleNum,
                            ms.hot AS hot,
                            ms.tips AS tips,
                            ms.publisher,
                            ms.status AS status,
                            GROUP_CONCAT(DISTINCT md.dic_desc ORDER BY mst.sort_id ) AS themeList,
                            GROUP_CONCAT(DISTINCT md.dic_desc,'-mega-', md.code ORDER BY mst.sort_id ) AS themeStr,
                            GROUP_CONCAT( msrole.id, '-mega-', msrole.oss_url, '-mega-', msrole.role_desc, '-mega-', msrole.name, '-mega-', msrole.delsign ORDER BY msrole.sort_id ) AS roleStr
                            FROM merchant_script ms
                            LEFT JOIN merchant_script_theme mst ON ms.id = mst.script_id AND mst.delsign = 0
                            LEFT JOIN merchant_temp_script_ref mtsr on ms.id = mtsr.script_id AND mtsr.delsign = 0
                            LEFT JOIN merchant_dic md ON md.code = mst.code AND md.type = 3
                            LEFT JOIN merchant_script_role msrole ON msrole.script_id = ms.id
                            WHERE mtsr.merchant_id = ?
                            AND ms.status != 0 
                            GROUP BY ms.id    
                        `;
                        result = await this.selectList(sqlSelectTempScript, [merchant_temp_id], srpiptkillDb);
                    } else {
                        result = await this.selectList(sqlStr, [], srpiptkillDb);
                    }
                } else {
                    result = await this.selectList(sqlStr, [], srpiptkillDb);
                }
            } else {
                result = await this.selectList(sqlStr, [], srpiptkillDb);
            }

            if (result && result.length > 0) {
                let scriptIdList: any = [];
                for (const item of result) {
                    scriptIdList.push(item.id);
                }

                let sqlOpe = `
                SELECT r.operate_user_id, r.refuse_mes, r.type, u.nickname, r.script_id, r.createtime FROM sk_merchant_record r
                INNER JOIN (SELECT MAX(createtime) AS createtime , script_id FROM sk_merchant_record WHERE script_id IN (${scriptIdList.toString()}) GROUP BY script_id) t
                ON r.script_id = t.script_id AND t.createtime = r.createtime
                INNER JOIN wf_admin_user u ON u.id = r.operate_user_id
                WHERE r.script_id IN (${scriptIdList.toString()})
            `;
                let operateList = await this.selectList(sqlOpe, [], managerDb);

                if (operateList != null && operateList.length > 0) {
                    for (let item of result) {
                        for (const imgItem of operateList) {
                            if (item.id == imgItem.script_id) {
                                item.operate = {
                                    operate_user_id: imgItem.operate_user_id,
                                    refuse_mes: imgItem.refuse_mes,
                                    type: imgItem.type,
                                    nickname: imgItem.nickname,
                                    createtime: imgItem.createtime,
                                };
                            }
                        }
                    }
                }

            }

            return result;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async confirmScript(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get(srpiptkillDb);
        const manager = app.mysql.get(managerDb);

        //开启事物
        const conn = await db.beginTransaction();
        const managerConn = await manager.beginTransaction();

        try {
            let sqlStr = `
            UPDATE merchant_script
            SET 
            \`status\` = 10
            WHERE
                \`id\` = ?;
            `;
            await conn.query(sqlStr, [req.id]);

            let sqlConfirm = `
            INSERT INTO sk_merchant_record(operate_user_id, script_id, type) VALUES ( ?, ?, 10) `;

            await managerConn.query(sqlConfirm, [req.operateUserId, req.id]);

            await conn.commit(); // 提交事务
            await managerConn.commit();

        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            await managerConn.rollback();
            logger.error(error);
            throw error
        }
    }

    public async refuseScript(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get(srpiptkillDb);
        const manager = app.mysql.get(managerDb);

        //开启事物
        const conn = await db.beginTransaction();
        const managerConn = await manager.beginTransaction();

        try {
            let sqlStr = `
            UPDATE merchant_script
            SET 
            \`status\` = 2
            WHERE
                \`id\` = ?;
            `;
            await conn.query(sqlStr, [req.id]);

            let sqlRefuse = `
            INSERT INTO sk_merchant_record(operate_user_id, refuse_mes, script_id, type) VALUES ( ?, ?, ?, 2) `;
            await managerConn.query(sqlRefuse, [req.operateUserId, req.refuseMes, req.id]);

            await conn.commit(); // 提交事务
            await managerConn.commit();

        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            await managerConn.rollback();
            logger.error(error);
            throw error
        }
    }

    public async getScriptSimpleList(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
            SELECT id, \`name\` FROM merchant_script WHERE \`name\` IS NOT NULL AND status != 0 ORDER BY id DESC
            `;
            return await this.selectList(sqlStr, [], srpiptkillDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getPictureList(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
                SELECT * FROM merchant_picture WHERE merchant_id = ? ORDER BY merchant_id DESC, sort_id ASC
            `;
            return await this.selectList(sqlStr, [req.merchant_id], srpiptkillDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updatePictureUrl(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
            UPDATE 
                merchant_picture
            SET 
                oss_url = ?
            WHERE
                \`id\` = ?
            `;
            let result = await this.execSql(sqlStr, [req.oss_url, req.id], srpiptkillDb);
            return result;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updatePicture(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
            UPDATE 
                merchant_picture
            SET 
                delsign = ?,
                sort_id = ?
            WHERE
                \`id\` = ?
            `;
            let result = await this.execSql(sqlStr, [req.delsign, req.sort_id, req.id], srpiptkillDb);
            return result;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async insertPicture(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
            INSERT INTO merchant_picture (
                merchant_id,
                create_time,
                sort_id,
                delsign,
                type 
            ) VALUES (?, NOW(), ?, ?, 0)
            `;
            let result = await this.execSql(sqlStr, [req.merchant_id, req.sort_id, req.delsign], srpiptkillDb);
            return result;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getAuditFlowList(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
            SELECT r.*, u.nickname FROM sk_merchant_record r
            INNER JOIN wf_admin_user u ON u.id = r.operate_user_id
            `;
            let needScript = false;
            if (req.merchant_id != undefined && req.merchant_id != null && req.merchant_id > 0) {
                sqlStr += ` WHERE merchant_id = ` + req.merchant_id;
            } else if (req.script_id != undefined && req.script_id != null && req.script_id > 0) {
                sqlStr += ` WHERE script_id = ` + req.script_id;
            } else if (req.operate_user_id != undefined && req.operate_user_id != null && req.operate_user_id > 0) {
                sqlStr += ` WHERE operate_user_id = ` + req.operate_user_id;
                needScript = true;
            }
            sqlStr += ` ORDER BY createtime DESC `;
            sqlStr += ` LIMIT ?, ? `;

            let list = await this.selectList(sqlStr, [(req.current - 1) * req.pageCount, req.pageCount], managerDb);
            if (needScript && list != null && list.length > 0) {
                let scriptIdList: any[] = [];
                let merchantIdList: any[] = [];
                for (const item of list) {
                    if (item.script_id != undefined && item.script_id != null && item.script_id != "") {
                        scriptIdList.push(item.script_id);
                    }
                    if (item.merchant_id != undefined && item.merchant_id != null && item.merchant_id != "") {
                        merchantIdList.push(item.merchant_id);
                    }
                }

                if (scriptIdList.length > 0) {
                    let sqlScript = ` SELECT id, script_desc, \`name\` FROM merchant_script WHERE id IN (${scriptIdList.toString()}) `;
                    let scriptList = await this.selectList(sqlScript, [], srpiptkillDb);
                    for (let item of list) {
                        for (const script of scriptList) {
                            if (item.script_id == script.id) {
                                item.script_desc = script.script_desc;
                                item.script_name = script.name;
                            }
                        }
                    }
                }

                if (merchantIdList.length > 0) {
                    let sqlMerchant = ` SELECT id, merchant_name FROM merchant WHERE id IN (${merchantIdList.toString()}) `;
                    let merchantList = await this.selectList(sqlMerchant, [], srpiptkillDb);
                    if (merchantList && merchantList.length > 0) {
                        for (let item of list) {
                            for (const merchant of merchantList) {
                                if (item.merchant_id == merchant.id) {
                                    item.merchant_name = merchant.merchant_name;
                                }
                            }
                        }
                    }
                }
            }
            return list;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getAuditFlowListCount(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
            SELECT COUNT(*) AS num FROM sk_merchant_record r
            INNER JOIN wf_admin_user u ON u.id = r.operate_user_id
            `;
            if (req.merchant_id != undefined && req.merchant_id != null && req.merchant_id > 0) {
                sqlStr += ` WHERE merchant_id = ` + req.merchant_id;
            } else if (req.script_id != undefined && req.script_id != null && req.script_id > 0) {
                sqlStr += ` WHERE script_id = ` + req.script_id;
            } else if (req.operate_user_id != undefined && req.operate_user_id != null && req.operate_user_id > 0) {
                sqlStr += ` WHERE operate_user_id = ` + req.operate_user_id;
            }

            return await this.selectOne(sqlStr, [], managerDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getScriptEditAuditFlowList(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
                SELECT r.*, u.nickname FROM sk_script_operate_record r
                INNER JOIN wf_admin_user u ON u.id = r.operate_user_id
            `;

            if (req.script_id != undefined && req.script_id != null && req.script_id > 0) {
                sqlStr += ` WHERE script_id = ` + req.script_id;
            } else if (req.operate_user_id != undefined && req.operate_user_id != null && req.operate_user_id > 0) {
                sqlStr += ` WHERE operate_user_id = ` + req.operate_user_id;
            }
            sqlStr += ` ORDER BY r.createtime DESC `;
            sqlStr += ` LIMIT ?, ? `;

            let list = await this.selectList(sqlStr, [(req.current - 1) * req.pageCount, req.pageCount], managerDb);
            return list;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getScriptEditAuditFlowListCount(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
                SELECT COUNT(*) AS num FROM sk_script_operate_record r
                INNER JOIN wf_admin_user u ON u.id = r.operate_user_id
            `;

            if (req.script_id != undefined && req.script_id != null && req.script_id > 0) {
                sqlStr += ` WHERE script_id = ` + req.script_id;
            } else if (req.operate_user_id != undefined && req.operate_user_id != null && req.operate_user_id > 0) {
                sqlStr += ` WHERE operate_user_id = ` + req.operate_user_id;
            }

            let list = await this.selectOne(sqlStr, [], managerDb);
            return list;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getOperateUserList(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
                SELECT u.id, u.nickname AS \`name\` FROM wf_admin_user u
                INNER JOIN wf_admin_route_user r ON u.id = r.admin_id AND r.route_id IN (49)
                WHERE u.delsign = 0 AND u.id != 100000
            `;
            return await this.selectList(sqlStr, [], managerDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getMerchantTempList(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
            SELECT
            m.id                    AS id,
            m.audit_time,
            m.merchant_id,
            m.merchant_name         AS \`name\`,
            m.create_time           AS \`date\`,
            m.\`status\`              AS \`status\`,
            ma.address              AS address,
            ma.latitude             AS latitude,
            ma.longitude            AS longitude,
            bp.Base_ProvincesName   AS provinceName,
            bc.city_name 		    AS cityName,
            m.delsign,
            m.audit_time,         
            m.update_time,
            
            GROUP_CONCAT(DISTINCT mpt.tag_desc ORDER BY mt.sort_id) AS tagStr,
            GROUP_CONCAT(DISTINCT mbt.day_of_week, '-', mbt.business_start_time, '-', mbt.business_end_time ORDER BY mbt.day_of_week) AS timeStr,
            GROUP_CONCAT(DISTINCT tel.tel) AS telStr
           
            FROM merchant_temp m
            LEFT JOIN merchant_temp_address ma ON m.id = ma.merchant_id AND ma.delsign = 0
            LEFT JOIN base_provinces bp ON bp.Base_ProvincesID = ma.province_code
            LEFT JOIN merchant_city_list bc ON bc.city_code = ma.city_code
            
            LEFT JOIN merchant_temp_tag mt ON m.id = mt.merchant_id AND mt.delsign = 0
            LEFT JOIN merchant_public_tag mpt ON mt.tag_id = mpt.id AND mpt.delsign = 0
                
            LEFT JOIN merchant_temp_business_time mbt ON m.id = mbt.merchant_id AND mbt.delsign = 0
            LEFT JOIN merchant_temp_tel tel ON m.id = tel.merchant_id AND tel.delsign = 0
            `;

            if (req.where && req.where != "") {
                sqlStr += ` WHERE m.delsign = 0 AND ` + req.where;
                sqlStr += ` GROUP BY m.id ORDER BY m.id DESC `;
                sqlStr += ` LIMIT ?, ? `;
            } else if (req.currentMerchantId && req.currentMerchantId != "") {
                sqlStr += ` WHERE m.delsign = 0 AND m.merchant_id = ` + req.currentMerchantId;
                sqlStr += ` GROUP BY m.merchant_id ORDER BY m.id DESC `;
            }

            let result = await this.selectList(sqlStr, [(req.current - 1) * req.pageCount, req.pageCount], srpiptkillDb);
            if (result != null) {
                let merchantIdList: any = [];
                for (const item of result) {
                    merchantIdList.push(item.id);
                }
                let sqlImg = `
                    SELECT * FROM merchant_temp_picture WHERE merchant_id IN (${merchantIdList.toString()}) AND delsign = 0 ORDER BY sort_id 
                `;
                let imgList = await this.selectList(sqlImg, [], srpiptkillDb);
                if (imgList != null && imgList.length > 0) {
                    for (let item of result) {
                        item.imgList = [];
                        for (const imgItem of imgList) {
                            if (item.id == imgItem.merchant_id) {
                                item.imgList.push(imgItem.oss_url);
                            }
                        }
                    }
                }

                let sqlOpe = `
                    SELECT r.operate_user_id, r.refuse_mes, r.type, u.nickname, r.merchant_id FROM sk_merchant_record r
                    INNER JOIN (SELECT MAX(createtime) AS createtime  , merchant_id FROM sk_merchant_record WHERE merchant_id IN (${merchantIdList.toString()}) GROUP BY merchant_id) t
                    ON r.merchant_id = t.merchant_id AND t.createtime = r.createtime
                    INNER JOIN wf_admin_user u ON u.id = r.operate_user_id
                    WHERE r.merchant_id IN (${merchantIdList.toString()})
                `;
                let operateList = await this.selectList(sqlOpe, [], managerDb);
                if (operateList != null && operateList.length > 0) {
                    for (let item of result) {
                        for (const imgItem of operateList) {
                            if (item.id == imgItem.merchant_id) {
                                item.operate = {
                                    operate_user_id: imgItem.operate_user_id,
                                    refuse_mes: imgItem.refuse_mes,
                                    type: imgItem.type,
                                    nickname: imgItem.nickname
                                };
                            }
                        }
                    }
                }
            }

            return result;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getMerchantTempListCount(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
            SELECT
            COUNT(*) AS num
            FROM merchant_temp m
            `;
            if (req.where && req.where != "") {
                sqlStr += ` WHERE m.delsign = 0 AND ` + req.where;
            } else if (req.currentMerchantId && req.currentMerchantId != "") {
                sqlStr += ` WHERE m.delsign = 0 AND m.merchant_id = ` + req.currentMerchantId;
            }

            return await this.selectOne(sqlStr, [], srpiptkillDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async refuseMerchantTemp(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get(srpiptkillDb);
        const manager = app.mysql.get(managerDb);

        //开启事物
        const conn = await db.beginTransaction();
        const managerConn = await manager.beginTransaction();
        try {
            let sqlStr = `
                UPDATE merchant_temp
                SET 
                \`status\` = 2,
                fail_reason = ?,
                audit_time = NOW()
                WHERE
                    \`id\` = ?
            `;
            await conn.query(sqlStr, [req.refuseMes, req.id]);

            let sqlRefuse = `
                INSERT INTO sk_merchant_record(operate_user_id, refuse_mes, merchant_id, type) VALUES ( ?, ?, ?, 2)
            `;
            await managerConn.query(sqlRefuse, [req.operateUserId, req.refuseMes, req.merchant_id]);

            await conn.commit(); // 提交事务
            await managerConn.commit();
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            await managerConn.rollback();
            logger.error(error);
            throw error
        }
    }

    public async confirmMerchantTemp(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get(srpiptkillDb);
        const manager = app.mysql.get(managerDb);

        //开启事物
        const conn = await db.beginTransaction();
        const managerConn = await manager.beginTransaction();
        try {
            let sqlSelectMerchantTemp = `
            SELECT
                merchant_id,
                user_id,
                merchant_name,
                \`status\`,
                fail_reason,
                audit_time,
                create_time,
                update_time,
                delsign 
            FROM
                merchant_temp 
            WHERE
                id = ?
                AND delsign = 0
            `;
            let merchantTempList = await conn.query(sqlSelectMerchantTemp, [req.id]);
            if (merchantTempList && merchantTempList.length > 0) {
                let merchantTemp = merchantTempList[0];

                const merchantId = merchantTemp.merchant_id;
                if (merchantId != req.merchant_id) {
                    await conn.rollback(); // 一定记得捕获异常后回滚事务！
                    await managerConn.rollback();
                    return 0;
                }
                const merchantTempId = req.id;

                let sqlUpdateMerchantTemp = `
                    UPDATE merchant_temp 
                    SET 
                    \`status\` = 10,
                    fail_reason = "",
                    audit_time = NOW(),
                    delsign = 1
                    WHERE
                        id = ?;
            `;
                await conn.query(sqlUpdateMerchantTemp, [merchantTempId]);

                let sqlUpdateMerchant = `
                    UPDATE merchant 
                    SET 
                    user_id = ?,
                    merchant_name = ?,
                    tel = "",
                    \`status\` = 10,
                    fail_reason = "",
                    audit_time = NOW(),
                    delsign = 0 
                    WHERE
                        id = ?;
            `
                await conn.query(sqlUpdateMerchant, [
                    merchantTemp.user_id,
                    merchantTemp.merchant_name,
                    merchantId]);

                let sqlSelectMerchantTempAddress = `
                    SELECT
                        id,
                        merchant_id,
                        address,
                        longitude,
                        latitude,
                        province_code,
                        city_code,
                        geohash,
                        create_time,
                        delsign 
                    FROM
                        merchant_temp_address
                    WHERE merchant_id = ? AND delsign = 0  
            `;
                let merchantTempAddressList = await conn.query(sqlSelectMerchantTempAddress, [merchantTempId]);
                if (merchantTempAddressList && merchantTempAddressList.length > 0) {
                    let merchantTempAddress = merchantTempAddressList[0];
                    let sqlUpdateTempAddress = `
                        UPDATE merchant_temp_address 
                        SET 
                        delsign = 1
                        WHERE
                            merchant_id = ?
                    `
                    await conn.query(sqlUpdateTempAddress, [merchantTempId])

                    let sqlUpdateMerchantAddress = `
                        UPDATE merchant_address 
                        SET 
                        address = ?,
                        longitude = ?,
                        latitude = ?,
                        province_code = ?,
                        city_code = ?,
                        geohash = ?,
                        delsign = 0 
                        WHERE
                            merchant_id = ?;
                    `;
                    await conn.query(sqlUpdateMerchantAddress, [
                        merchantTempAddress.address,
                        merchantTempAddress.longitude,
                        merchantTempAddress.latitude,
                        merchantTempAddress.province_code,
                        merchantTempAddress.city_code,
                        merchantTempAddress.geohash,
                        merchantId]);

                    let sqlSelectCityList = `
                        SELECT * FROM merchant_city_list WHERE city_code = ? AND delsign = 0
                    `;
                    let cityList = await conn.query(sqlSelectCityList, [merchantTempAddress.city_code]);
                    if (!cityList || cityList.length <= 0) {
                        let sqlSelectCity = `
                            SELECT
                            id,
                            province_code,
                            city_name,
                            city_code,
                            latitude,
                            longitude,
                            delsign 
                        FROM
                            merchant_city 
                        WHERE
                            city_code = ?
                            AND delsign = 0
                        `;
                        let cityListNew = await conn.query(sqlSelectCity, [merchantTempAddress.city_code]);
                        if (cityListNew && cityListNew.length > 0) {
                            let city = cityListNew[0];
                            let sqlInsertCityList = `
                                INSERT INTO merchant_city_list(city_code, city_name, create_time, delsign) VALUES
                                (?,?,NOW(),0);
                            `;
                            await conn.query(sqlInsertCityList, [city.city_code, city.city_name]);
                        }
                    }
                }

                let sqlSelectTempBusinessTime = `
                    SELECT  
                    id, merchant_id, day_of_week, business_start_time, business_end_time, create_time, update_time, delsign
                    FROM merchant_temp_business_time
                    WHERE merchant_id = ? AND delsign = 0
                `;
                let tempBusinessTimeList = await conn.query(sqlSelectTempBusinessTime, [merchantTempId]);
                if (tempBusinessTimeList && tempBusinessTimeList.length > 0) {
                    let sqlUpdateTempBusinessTime = `
                        UPDATE merchant_temp_business_time 
                        SET 
                        delsign = 1
                        WHERE
                            merchant_id = ?
                    `;
                    await conn.query(sqlUpdateTempBusinessTime, [merchantTempId]);
                    let sqlUpdateBusinessTime = `
                        UPDATE merchant_business_time 
                        SET 
                        delsign = 1
                        WHERE
                            merchant_id = ?
                    `;
                    await conn.query(sqlUpdateBusinessTime, [merchantId]);
                    for (const item of tempBusinessTimeList) {
                        let sqlInsertUpdateBusinessTime = `
                            INSERT INTO merchant_business_time(merchant_id, day_of_week, business_start_time, business_end_time, create_time, update_time, delsign) VALUES 
                            (?, ?, ?, ?, NOW(), NOW(), 0)
                            ON DUPLICATE KEY UPDATE 
                            merchant_id = VALUES(merchant_id), 
                            day_of_week = VALUES(day_of_week), 
                            business_start_time = VALUES(business_start_time), 
                            business_end_time = VALUES(business_end_time),
                            delsign = 0                       
                        `;
                        await conn.query(sqlInsertUpdateBusinessTime, [
                            merchantId,
                            item.day_of_week,
                            item.business_start_time,
                            item.business_end_time
                        ]);
                    }
                }

                let sqlSelectTempPic = `
                    SELECT
                        id,
                        merchant_id,
                        oss_url,
                        create_time,
                        sort_id,
                        delsign,
                        type 
                    FROM
                        merchant_temp_picture 
                    WHERE
                        merchant_id = ? AND delsign = 0
                `;
                let picTempList = await conn.query(sqlSelectTempPic, [merchantTempId]);
                if (picTempList && picTempList.length > 0) {
                    let sqlUpdateTempPic = `
                        UPDATE merchant_temp_picture 
                        SET 
                        delsign = 1
                        WHERE
                            merchant_id = ?
                    `;
                    await conn.query(sqlUpdateTempPic, [merchantTempId]);
                    let sqlUpdatePic = `
                        UPDATE merchant_picture 
                        SET 
                        delsign = 1
                        WHERE
                            merchant_id = ?
                    `;
                    await conn.query(sqlUpdatePic, [merchantId]);

                    for (const item of picTempList) {
                        let sqlInsertPic = `
                            INSERT INTO merchant_picture(merchant_id, oss_url, sort_id, delsign, type) VALUES 
                            (?, ?, ?, 0, ?)
                            ON DUPLICATE KEY UPDATE 
                            merchant_id = VALUES(merchant_id), 
                            oss_url = VALUES(oss_url), 
                            sort_id = VALUES(sort_id), 
                            type = VALUES(type),
                            delsign = 0  
                        `;

                        await conn.query(sqlInsertPic, [
                            merchantId,
                            item.oss_url,
                            item.sort_id,
                            item.type]);
                    }
                }

                let sqlSelectTempScript = `
                    SELECT
                    id,
                    merchant_id,
                    script_id,
                    price,
                    create_time,
                    delsign 
                FROM
                    merchant_temp_script_ref 
                WHERE
                    merchant_id = ?
                    AND delsign = 0
                `;
                let tempScriptList = await conn.query(sqlSelectTempScript, [merchantTempId]);
                if (tempScriptList && tempScriptList.length > 0) {
                    let sqlUpdateTempScript = `
                        UPDATE merchant_temp_script_ref 
                        SET 
                        delsign = 1
                        WHERE
                            merchant_id = ?
                    `;
                    await conn.query(sqlUpdateTempScript, [merchantTempId]);
                    let sqlUpdateScript = `
                        UPDATE merchant_script_ref 
                        SET 
                        delsign = 1
                        WHERE
                            merchant_id = ?
                    `;
                    await conn.query(sqlUpdateScript, [merchantId]);

                    for (const item of tempScriptList) {
                        let sqlInsertScript = `
                        INSERT INTO merchant_script_ref (
                            merchant_id,
                            script_id,
                            price,
                            create_time,
                            delsign 
                        )
                        VALUES (?, ?, ?, NOW(), 0) 
                        ON DUPLICATE KEY UPDATE 
                        merchant_id = VALUES(merchant_id), 
                        script_id = VALUES(script_id), 
                        price = VALUES(price), 
                        create_time = NOW(),
                        delsign = 0   
                        `;
                        await conn.query(sqlInsertScript, [
                            merchantId,
                            item.script_id,
                            item.price]);
                    }
                }

                let sqlSelectTempTag = `
                        SELECT
                        id,
                        tag_id,
                        merchant_id,
                        sort_id,
                        create_time,
                        delsign 
                    FROM
                        merchant_temp_tag 
                    WHERE
                        merchant_id = ?
                        AND delsign = 0
                `;
                let tempTagList = await conn.query(sqlSelectTempTag, [merchantTempId]);
                if (tempTagList && tempTagList.length > 0) {
                    let sqlUpdateTempTag = `
                        UPDATE merchant_temp_tag 
                        SET 
                        delsign = 1 
                        WHERE
                            merchant_id = ?
                    `;
                    await conn.query(sqlUpdateTempTag, [merchantTempId]);
                    let sqlUpdateTag = `
                        UPDATE merchant_tag 
                        SET 
                        delsign = 1 
                        WHERE
                            merchant_id = ?
                    `;
                    await conn.query(sqlUpdateTag, [merchantId]);
                    for (const item of tempTagList) {
                        let sqlInsertUpdateTag = `
                            INSERT INTO merchant_tag(tag_id, merchant_id, sort_id, create_time, delsign) VALUES 
                            (?, ?, ?, NOW(), 0) 
                            ON DUPLICATE KEY UPDATE 
                            tag_id = VALUES(tag_id), 
                            merchant_id = VALUES(merchant_id), 
                            sort_id = VALUES(sort_id), 
                            create_time = NOW(),
                            delsign = 0   
                        `;
                        await conn.query(sqlInsertUpdateTag, [
                            item.tag_id,
                            merchantId,
                            item.sort_id]);
                    }
                }

                let sqlSelectTempTel = `
                        SELECT
                        merchant_id,
                        tel,
                        create_time,
                        delsign 
                    FROM
                        merchant_temp_tel 
                    WHERE
                        merchant_id = ?
                        AND delsign = 0
                `;
                let tempTelList = await conn.query(sqlSelectTempTel, [merchantTempId]);
                if (tempTelList && tempTelList.length > 0) {
                    let sqlUpdateTempTel = `
                        UPDATE merchant_temp_tel 
                        SET 
                        delsign = 1
                        WHERE
                            merchant_id = ?
                    `;
                    await conn.query(sqlUpdateTempTel, [merchantTempId]);
                    let sqlUpdateTel = `
                        UPDATE merchant_tel 
                        SET 
                        delsign = 1
                        WHERE
                            merchant_id = ?
                    `;
                    await conn.query(sqlUpdateTel, [merchantId]);

                    let sqlInsertTel = `
                        INSERT INTO merchant_tel (merchant_id, tel, delsign) VALUES ?
                    `;
                    let valueInsertTel: any = [];
                    for (const item of tempTelList) {
                        valueInsertTel.push([
                            merchantId,
                            item.tel,
                            0]);
                    }
                    if (valueInsertTel.length > 0) {
                        await conn.query(sqlInsertTel, [valueInsertTel]);
                    }
                }

                let sqlConfirm = `
                INSERT INTO sk_merchant_record(operate_user_id, merchant_id, type) VALUES (?, ?, 10) `;
                await managerConn.query(sqlConfirm, [req.operateUserId, req.merchant_id]);

                await conn.commit(); // 提交事务
                await managerConn.commit();
                return 1;
            } else {
                await conn.rollback(); // 一定记得捕获异常后回滚事务！
                await managerConn.rollback();
                return 0;
            }

        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            await managerConn.rollback();
            logger.error(error);
            throw error
        }
    }

    public async updateScript(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get(srpiptkillDb);
        const manager = app.mysql.get(managerDb);

        //开启事物
        const conn = await db.beginTransaction();
        const managerConn = await manager.beginTransaction();

        try {
            let sqlStr = `
                UPDATE merchant_script 
                SET 
                    name = ?,
                    script_desc = ?,
                    tips = ?,
                    hot = ?,
                    publisher = ?,
                    difficulty = ?,
                    role_num = ?,
                    delsign = ?,
                    update_time = NOW()
                WHERE
                    id = ?;
            `;
            await conn.query(sqlStr, [
                req.name,
                req.script_desc,
                req.tips,
                req.hot,
                req.publisher,
                req.difficulty,
                req.role_num,
                req.delsign,
                req.id]);

            if (req.delsign == 1) {//清空 order
                const sqlUpdateCollect = `
                    UPDATE 
                        merchant_user_collect_script 
                    SET 
                        delsign = 1
                    WHERE
                        script_id = ?
                `;
                await conn.query(sqlUpdateCollect, [req.id]);

                const sqlUpdateOrder = `
                    UPDATE 
                        merchant_order 
                    SET 
                        delsign = 1
                    WHERE
                        script_id = ?
                `;
                await conn.query(sqlUpdateOrder, [req.id]);

                const sqlUpdateRef = `
                UPDATE 
                    merchant_script_ref 
                SET 
                    delsign = 1
                WHERE
                    script_id = ?
            `;
                await conn.query(sqlUpdateRef, [req.id]);
            }

            if (req.themeDicList != undefined && req.themeDicList != null && req.themeDicList.length > 0) {
                let sqlUpdateTheme = `
                    UPDATE merchant_script_theme 
                    SET 
                    delsign = 1
                    WHERE
                    script_id = ?
                `;
                await conn.query(sqlUpdateTheme, [req.id]);
                for (const item of req.themeDicList) {
                    let sqlInsertUpdateTag = `
                    INSERT INTO merchant_script_theme(script_id, code, sort_id, create_time, delsign) VALUES 
                    (?, ?, ?, NOW(), 0) 
                    ON DUPLICATE KEY UPDATE 
                    script_id = VALUES(script_id), 
                    code = VALUES(code), 
                    sort_id = VALUES(sort_id), 
                    create_time = NOW(),
                    delsign = 0   
                `;
                    await conn.query(sqlInsertUpdateTag, [
                        req.id,
                        item.id,
                        item.ketIndex]);
                }

            }
            let sqlConfirm = `
            INSERT INTO sk_script_operate_record (
                script_id,
                new_name,
                name,
                script_desc,
                publisher,
                role_num,
                tips,
                hot,
                difficulty,
                operate_user_id,
                theme,
                delsign
            )
            VALUES
                (?,?,?,?,?,?,?,?,?,?,?,?); `;

            await managerConn.query(sqlConfirm, [
                req.id,
                req.name,
                req.operateInfo.name,
                req.operateInfo.script_desc,
                req.operateInfo.publisher,
                req.operateInfo.role_num,
                req.operateInfo.tips,
                req.operateInfo.hot,
                req.operateInfo.difficulty,
                req.operateInfo.operate_user_id,
                req.operateInfo.theme,
                req.operateInfo.delsign
            ]);

            await conn.commit(); // 提交事务
            await managerConn.commit();

        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            await managerConn.rollback();
            logger.error(error);
            throw error
        }
    }

    public async getMerchantDicList(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
                SELECT code AS id, type, dic_desc AS name, sort_id, delsign FROM merchant_dic WHERE type = ?
            `;
            return await this.selectList(sqlStr, [req.type], srpiptkillDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateScriptUrl(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
            UPDATE 
                merchant_script
            SET 
                cover_oss_url = ?
            WHERE
                \`id\` = ?
            `;
            let result = await this.execSql(sqlStr, [req.cover_oss_url, req.id], srpiptkillDb);
            return result;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async refuseMerchantPassed(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get(srpiptkillDb);
        const manager = app.mysql.get(managerDb);

        //开启事物
        const conn = await db.beginTransaction();
        const managerConn = await manager.beginTransaction();
        try {
            let sqlStr = `
                UPDATE merchant
                SET 
                \`status\` = 2,
                fail_reason = ?,
                audit_time = NOW()
                WHERE
                    \`id\` = ?
            `;
            await conn.query(sqlStr, [req.refuseMes, req.id]);

            const sqlUpdateCollect = `
                    UPDATE 
                        merchant_user_collect 
                    SET 
                        delsign = 1
                    WHERE
                        merchant_id = ?
                `;
            await conn.query(sqlUpdateCollect, [req.id]);

            const sqlUpdateOrder = `
                    UPDATE 
                        merchant_order 
                    SET 
                        delsign = 1
                    WHERE
                        merchant_id = ?
                `;
            await conn.query(sqlUpdateOrder, [req.id]);

            let sqlRefuse = `
                INSERT INTO sk_merchant_record(operate_user_id, refuse_mes, merchant_id, type) VALUES ( ?, ?, ?, 2)
            `;
            await managerConn.query(sqlRefuse, [req.operateUserId, req.refuseMes, req.id]);

            await conn.commit(); // 提交事务
            await managerConn.commit();
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            await managerConn.rollback();
            logger.error(error);
            throw error
        }
    }

    public async getUserInfo(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
            SELECT A.id, A.createtime, C.nickname, C.avatar, C.sex, B.account
                    , B.\`local\`, B.exp, B.\`level\`, B.script_num, B.credit
                    , B.escape_num, B.score, B.find, B.diamond, B.gold
                    , B.frame, B.charm, COUNT(M.id) > 0 AS merchantFlag
            FROM user A
            LEFT JOIN user_data_info B ON A.id = B.user_id
            LEFT JOIN user_self_info C ON A.id = C.user_id
            LEFT JOIN merchant M ON A.id = M.user_id AND M.\`status\` = 10
            WHERE B.user_id = ${req.userId}
            `;
            let userData = await this.selectOne(sqlStr, [], srpiptkillDb);

            let sqlCity = `SELECT A.Base_CitiesName AS city, B.Base_ProvincesName AS province FROM base_cities A LEFT JOIN base_provinces B ON A.Base_ProvincesID = B.Base_ProvincesID WHERE Base_CitiesID = ${userData.local}`;
            let localInfo = await this.selectOne(sqlCity, [], srpiptkillDb);
            if (localInfo && localInfo.city) {
                userData.city = localInfo.city;
            }
            if (localInfo && localInfo.province) {
                userData.province = localInfo.province;
            }

            return userData;

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getUserInfoAvatar(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
                SELECT r.* , IFNULL(p.id,0) AS report, p.report_text, p.\`status\`, p.id AS report_id
                FROM user_avatar_upload_record r 
                LEFT JOIN user_normal_report p ON p.report_user_id = r.user_id AND p.source_id = r.id AND p.delsign = 0 AND p.report_type = 1
                WHERE r.user_id =  ${req.userId}
                ORDER BY r.create_time DESC, p.id DESC, p.create_time DESC
            `;
            let userData = await this.selectList(sqlStr, [], srpiptkillDb);
            return userData;

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async refuseUserAvatar(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get(srpiptkillDb);
        const manager = app.mysql.get(managerDb);

        //开启事物
        const conn = await db.beginTransaction();
        const managerConn = await manager.beginTransaction();
        try {
            let sqlStr = `
                UPDATE user_self_info 
                SET 
                avatar = 'http://img.53site.com/ScriptKill/DefaultAvatar/1.png'
                WHERE
                    user_id = ?
            `;
            await conn.query(sqlStr, [req.userId]);
            let sqlDel = `
                UPDATE user_avatar_upload_record 
                SET 
                delsign = 1
                WHERE
                    id = ?
            `;
            await conn.query(sqlDel, [req.id]);
            let sqlUpdateStr = `
                UPDATE user_normal_report 
                SET 
                status = 1
                WHERE
                    id = ?
            `;
            await conn.query(sqlUpdateStr, [req.report_id]);

            let sqlRefuse = `
            INSERT INTO sk_avatar_bg_operate_record (
                source_id,
                report_id,
                user_id,
                url,
                operate_user_id,
                type
            )
            VALUES
                (?,?,?,?,?,1);
            `;
            await managerConn.query(sqlRefuse, [req.id, req.report_id, req.userId, req.url, req.operate_user_id]);

            await conn.commit(); // 提交事务
            await managerConn.commit();
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            await managerConn.rollback();
            logger.error(error);
            throw error
        }
    }

    public async getUserInfoBg(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
                SELECT b.*, IFNULL(p.id,0) AS report, p.report_text, p.\`status\`, p.id AS report_id
                FROM user_background_info b 
                LEFT JOIN user_normal_report p ON p.report_user_id = b.user_id AND p.source_id = b.id AND p.delsign = 0 AND p.report_type = 2
                WHERE b.user_id = ${req.userId} 
                ORDER BY b.is_use DESC, p.id DESC, b.create_time DESC
            `;
            let userData = await this.selectList(sqlStr, [], srpiptkillDb);
            return userData;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async refuseUserBg(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get(srpiptkillDb);
        const manager = app.mysql.get(managerDb);

        //开启事物
        const conn = await db.beginTransaction();
        const managerConn = await manager.beginTransaction();
        try {
            let sqlStr = `
                UPDATE user_background_info 
                SET 
                delsign = 1 ,
                is_use = 0
                WHERE
                    id = ?
            `;
            await conn.query(sqlStr, [req.id]);
            let sqlUpdateStr = `
                UPDATE user_normal_report 
                SET 
                status = 1
                WHERE
                    id = ?
            `;
            await conn.query(sqlUpdateStr, [req.report_id]);

            let sqlRefuse = `
            INSERT INTO sk_avatar_bg_operate_record (
                source_id,
                report_id,
                user_id,
                url,
                operate_user_id,
                type
            )
            VALUES
                (?,?,?,?,?,2);
            `;
            await managerConn.query(sqlRefuse, [req.id, req.report_id, req.userId, req.url, req.operate_user_id]);

            await conn.commit(); // 提交事务
            await managerConn.commit();
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            await managerConn.rollback();
            logger.error(error);
            throw error
        }
    }

    public async getReportInfo(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
                SELECT p.*, i.avatar, i.nickname AS reportNickname, it.nickname AS nickname, r.id AS upload_record_id, r.img_url AS avatarUrl, b.id AS background_info_id, b.img_url AS bgUrl, b.is_use 
                FROM user_normal_report p 
                LEFT JOIN user_self_info i ON i.user_id = p.report_user_id
                LEFT JOIN user_self_info it ON it.user_id = p.user_id
                LEFT JOIN user_avatar_upload_record r ON p.report_user_id = r.user_id AND p.source_id = r.id AND p.report_type = 1
                LEFT JOIN user_background_info b ON b.user_id = p.report_user_id AND p.source_id = b.id AND p.report_type = 2
            `;
            if (req.report_type && req.report_type > 0) {
                sqlStr += ` WHERE p.report_type = ${req.report_type} `;
            }
            sqlStr += ` ORDER BY p.create_time DESC `;
            sqlStr += ` LIMIT ?, ? `;
            let list = await this.selectList(sqlStr, [(req.current - 1) * req.pageCount, req.pageCount], srpiptkillDb);
            return list;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getReportInfoCount(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
                SELECT COUNT(*) AS num FROM
                user_normal_report p 
                LEFT JOIN user_self_info i ON i.user_id = p.report_user_id
                LEFT JOIN user_avatar_upload_record r ON p.report_user_id = r.user_id AND p.source_id = r.id AND p.report_type = 1
                LEFT JOIN user_background_info b ON b.user_id = p.report_user_id AND p.source_id = b.id AND p.report_type = 2
            `;
            if (req.report_type && req.report_type > 0) {
                sqlStr += ` WHERE p.report_type = ${req.report_type} `;
            }
            return await this.selectOne(sqlStr, [], srpiptkillDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getScriptRecommendList(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
                SELECT msr.\`name\` AS \`name\` ,ms.cover_oss_url AS img ,msr.publisher AS publisher,msr.author AS author,
                       msr.id AS id, msr.script_id AS scriptId,msr.theme AS theme,msr.num AS num, msr.url AS url,msr.sign As sign,
                       msr.play_time AS playTime,DATE_FORMAT(msr.publisher_time,'%Y-%m-%d') AS publisherTime,
                       msr.\`show\` AS \`show\`, msr.delsign AS delsign,msr.sort AS sort
                FROM merchant_script_recommend msr
                LEFT JOIN merchant_script ms ON msr.script_id = ms.id
                ORDER BY sort DESC,msr.publisher_time DESC
            `;
            return await this.selectList(sqlStr, [], srpiptkillDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getScript(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
                SELECT msr.\`name\` AS \`name\` ,ms.cover_oss_url AS img ,msr.publisher AS publisher,msr.author AS author,
                       msr.id AS id, msr.script_id AS scriptId,msr.theme AS theme,msr.num AS num, msr.url AS url,msr.sign As sign,
                       msr.play_time AS playTime,DATE_FORMAT(msr.publisher_time,'%Y-%m-%d') AS publisherTime,
                       msr.\`show\` AS \`show\`, msr.delsign AS delsign,msr.sort AS sort,msr.oss_path_key AS oss_path_key
                FROM merchant_script_recommend msr
                LEFT JOIN merchant_script ms ON msr.script_id = ms.id
                WHERE msr.id = ?
                ORDER BY sort DESC,msr.publisher_time DESC
            `;
            return await this.selectOne(sqlStr, [req.id], srpiptkillDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getScriptSourceList(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
                SELECT \`name\` AS \`name\`,id AS id
                FROM merchant_script
                WHERE delsign = 0
                ORDER BY id
            `;
            return await this.selectList(sqlStr, [], srpiptkillDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async insertScriptRecommend(req) {
        const { app, ctx, logger } = this;
        try {
            logger.info(req)
            let sqlStr = `
                INSERT INTO merchant_script_recommend(script_id,name,theme,play_time
                ,num,publisher,author,publisher_time,url,sort,\`show\`,sign,oss_path_key)
                VALUE(?,?,?,?,?,?,?,?,?,?,?,?,?)
            `;
            return await this.execSql(sqlStr,
                [req.scriptId, req.name, req.theme, req.playTime,
                req.num, req.publisher, req.author, req.publisherTime, req.url, req.sort, req.show, req.sign, req.oss_path_key], srpiptkillDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateScriptRecommend(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
                UPDATE merchant_script_recommend
                SET script_id = ?,name = ?,theme = ?,play_time = ?,num = ?,
                publisher = ?,author = ?,publisher_time = ?,url = ?,sort = ?,\`show\` = ?,sign = ?
                WHERE id = ?
            `;
            return await this.execSql(sqlStr, [req.scriptId, req.name, req.theme, req.playTime,
            req.num, req.publisher, req.author, req.publisherTime, req.url, req.sort, req.show,
            req.sign, req.id], srpiptkillDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getMomentList(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
                SELECT t.*, m.id AS material_id, m.material_url, m.sort_id, n.topic_name FROM merchant_moment t
                LEFT JOIN merchant_moment_material m ON t.id = m.moment_id AND m.material_type = 3 AND m.delsign = 0
                LEFT JOIN merchant_moment_topic_ref r ON r.moment_id = t.id 
                LEFT JOIN merchant_topic n ON r.topic_id = n.id AND n.delsign = 0
            `;
            // if (req.report_type && req.report_type > 0) {
            //     sqlStr += ` WHERE p.report_type = ${req.report_type} `;
            // }
            sqlStr += ` ORDER BY t.is_recommend DESC, t.audit_time DESC `;
            sqlStr += ` LIMIT ?, ? `;
            let list = await this.selectList(sqlStr, [(req.current - 1) * req.pageCount, req.pageCount], srpiptkillDb);
            return list;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getMomentListCount(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
                SELECT COUNT(*) AS num FROM merchant_moment t
                LEFT JOIN merchant_moment_material m ON t.id = m.moment_id AND m.material_type = 3 AND m.delsign = 0
                LEFT JOIN merchant_moment_topic_ref r ON r.moment_id = t.id 
                LEFT JOIN merchant_topic n ON r.topic_id = n.id AND n.delsign = 0
            `;
            // if (req.report_type && req.report_type > 0) {
            //     sqlStr += ` WHERE p.report_type = ${req.report_type} `;
            // }
            return await this.selectOne(sqlStr, [], srpiptkillDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async insertMoment(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get(srpiptkillDb);

        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sqlStr = `
            INSERT INTO merchant_moment (
                create_user_id,
                title,
                edit_time,
                audit_time,
                \`status\`,
                delsign,
                oss_path_key 
            ) VALUES
            (100,'${req.title}',NOW(),NOW(),10,1,'${req.oss_path_key}');
            `;
            let result = await conn.query(sqlStr, []);
            let sqlInsertStr = `
            INSERT INTO merchant_moment_material (
                material_type,
                material_url,
                moment_id,
                \`status\`,
                sort_id,
                create_time,
                update_time,
                delsign
            ) VALUES
            (3,'${req.material_url}',${Number(result.insertId)},10,1,NOW(),NOW(),0);           
            `;
            await conn.query(sqlInsertStr, []);
            let moment_id = result.insertId;

            if (req.topic_name && req.topic_name != "") {

                let sqlTopicStr = `
                    SELECT * FROM merchant_topic WHERE topic_name = ?;
                `;

                let result = await conn.query(sqlTopicStr, [req.topic_name]);

                if (!result || result.length <= 0) {
                    let sqlInsertstr = `
                        INSERT INTO merchant_topic (
                            topic_name,
                            create_time,
                            update_time,
                            delsign 
                        )
                        VALUES
                        ('${req.topic_name}', NOW(), NOW(), 0) `;
                    let result = await conn.query(sqlInsertstr, []);

                    let sqlStr = `
                    SELECT * FROM merchant_moment_topic_ref WHERE moment_id = ?;
                `;
                    let resultTopic = await conn.query(sqlStr, [moment_id]);
                    if (!resultTopic || resultTopic.length <= 0) {
                        let sqlInsert = `
                        INSERT INTO merchant_moment_topic_ref ( topic_id, moment_id )
                        VALUES
                        (${Number(result.insertId)}, ${moment_id} ) `;
                        await conn.query(sqlInsert, []);
                    } else {
                        let sqlUpdate = `
                            UPDATE merchant_moment_topic_ref 
                            SET topic_id = ${Number(result.insertId)}
                            WHERE
                                moment_id = ${moment_id}
                            `;
                        await conn.query(sqlUpdate, []);
                    }
                } else {
                    let topic_id = result[0].id;
                    let sqlStr = `
                    SELECT * FROM merchant_moment_topic_ref WHERE moment_id = ?;
                `;
                    let resultTopic = await conn.query(sqlStr, [moment_id]);
                    if (!resultTopic || resultTopic.length <= 0) {
                        let sqlInsert = `
                        INSERT INTO merchant_moment_topic_ref ( topic_id, moment_id )
                        VALUES
                        (${Number(topic_id)}, ${moment_id} ) `;
                        await conn.query(sqlInsert, []);
                    } else {
                        let sqlUpdate = `
                            UPDATE merchant_moment_topic_ref 
                            SET topic_id = ${Number(topic_id)}
                            WHERE
                                moment_id = ${moment_id}
                            `;
                        await conn.query(sqlUpdate, []);
                    }
                }
            }

            await conn.commit(); // 提交事务

        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            logger.error(error);
            throw error
        }
    }

    public async updateMoment(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get(srpiptkillDb);
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sqlStr = `
            UPDATE merchant_moment 
            SET 
            title = '${req.title}',
            edit_time = NOW(),
            update_time = NOW()
            WHERE
                id = ${req.id};
            `;
            await conn.query(sqlStr, []);
            let sqlUpdateStr = `
            UPDATE merchant_moment_material 
            SET
            material_url = '${req.material_url}'
            WHERE
                id = ${req.material_id};         
            `;
            await conn.query(sqlUpdateStr, []);

            if (req.topic_name && req.topic_name != "") {
                let sqlTopicStr = `
                    SELECT * FROM merchant_topic WHERE topic_name = ?;
                `;

                let result = await conn.query(sqlTopicStr, [req.topic_name]);

                if (!result || result.length <= 0) {
                    let sqlInsertstr = `
                        INSERT INTO merchant_topic (
                            topic_name,
                            create_time,
                            update_time,
                            delsign 
                        )
                        VALUES
                        ('${req.topic_name}', NOW(), NOW(), 0) `;
                    let result = await conn.query(sqlInsertstr, []);

                    let sqlStr = `
                    SELECT * FROM merchant_moment_topic_ref WHERE moment_id = ?;
                `;
                    let resultTopic = await conn.query(sqlStr, [req.id]);
                    if (!resultTopic || resultTopic.length <= 0) {
                        let sqlInsert = `
                        INSERT INTO merchant_moment_topic_ref ( topic_id, moment_id )
                        VALUES
                        (${Number(result.insertId)}, ${req.id} ) `;
                        await conn.query(sqlInsert, []);
                    } else {
                        let sqlUpdate = `
                            UPDATE merchant_moment_topic_ref 
                            SET topic_id = ${Number(result.insertId)}
                            WHERE
                                moment_id = ${req.id}
                            `;
                        await conn.query(sqlUpdate, []);
                    }
                } else {
                    let topic_id = result[0].id;
                    let sqlStr = `
                    SELECT * FROM merchant_moment_topic_ref WHERE moment_id = ?;
                `;
                    let resultTopic = await conn.query(sqlStr, [req.id]);
                    if (!resultTopic || resultTopic.length <= 0) {
                        let sqlInsert = `
                        INSERT INTO merchant_moment_topic_ref ( topic_id, moment_id )
                        VALUES
                        (${Number(topic_id)}, ${req.id} ) `;
                        await conn.query(sqlInsert, []);
                    } else {
                        let sqlUpdate = `
                            UPDATE merchant_moment_topic_ref 
                            SET topic_id = ${Number(topic_id)}
                            WHERE
                                moment_id = ${req.id}
                            `;
                        await conn.query(sqlUpdate, []);
                    }
                }
            }


            await conn.commit(); // 提交事务

        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            logger.error(error);
            throw error
        }
    }

    public async updateMomentDelsign(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
            UPDATE merchant_moment 
            SET 
                delsign = ${req.delsign}
            WHERE
                id = ${req.id};
            `;
            return await this.selectOne(sqlStr, [], srpiptkillDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateMomentSort(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
            UPDATE merchant_moment 
            SET 
            is_recommend = ${req.is_recommend}
            WHERE
                id = ${req.id};
            `;
            return await this.selectOne(sqlStr, [], srpiptkillDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getMomentPicList(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
                SELECT * FROM merchant_moment_material WHERE material_type = 1 AND moment_id = ${req.moment_id}
            `;
            return await this.selectList(sqlStr, [], srpiptkillDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async insertMomentPicture(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
            INSERT INTO merchant_moment_material (
                material_type,
                moment_id,
                \`status\`,
                sort_id,
                create_time,
                update_time,
                delsign,
                show_width,
                show_height 
            )
            VALUES (1, ${req.moment_id}, 10, ${req.sort_id}, NOW(), NOW(), ${req.delsign}, 600, ${req.show_height})
            `;
            let result = await this.execSql(sqlStr, [], srpiptkillDb);
            return result;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateMomentPicture(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
            UPDATE merchant_moment_material 
            SET
            sort_id = ${req.sort_id},
            update_time = NOW(),
            show_height = ${req.show_height},
            delsign = ${req.delsign}
            WHERE
                id = ${req.id} 
            `;

            let result = await this.execSql(sqlStr, [], srpiptkillDb);
            return result;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateMomentPictureUrl(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
            UPDATE 
                merchant_moment_material
            SET 
                material_url = ?
            WHERE
                \`id\` = ?
            `;
            let result = await this.execSql(sqlStr, [req.oss_url, req.id], srpiptkillDb);
            return result;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getMomentPictureCanShowCount(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
                SELECT COUNT(*) AS num FROM merchant_moment_material WHERE material_type = 1 
                    AND \`status\` = 10 AND delsign = 0 AND material_url IS NOT NULL AND material_url != ""
                    AND moment_id = ${req.moment_id}
            `;
            return await this.selectOne(sqlStr, [], srpiptkillDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getCityList() {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
                SELECT city_code AS id, city_name AS name FROM merchant_city_list 
            `;
            return await this.selectList(sqlStr, [], srpiptkillDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getMerchantBannerList(){
        const { app, ctx, logger } = this;
        try {
            logger.info("--*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-")
            let sqlStr = `SELECT id, banner_url, create_time, delsign, sort_id, banner_name, type, href_url, banner_starttime, banner_endtime FROM merchant_banner;`;
            let array = await this.selectList(sqlStr,[],srpiptkillDb);
            return array;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async delBanner(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
               update merchant_banner set delsign = 1 where id = ?
            `;
            await this.execSql(sqlStr, [req.id], srpiptkillDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async helvesBanner(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
               update merchant_banner set delsign = 0 where id = ? ;
            `;
            await this.execSql(sqlStr, [req.id], srpiptkillDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateBannerInfo(req) {
        const {app, ctx, logger} = this;
        try {
            let sqlStr = ` 
               update merchant_banner set banner_url = ?, create_time = NOW(), delsign = 1, sort_id = ?, banner_name = ?, type = ?, href_url = ?, banner_starttime = ?, banner_endtime = ?  where id = ?
            `;
            await this.execSql(sqlStr, [req.banner_url, req.sort_id, req.banner_name, req.type, req.href_url, req.banner_starttime, req.banner_endtime,req.id], srpiptkillDb);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateMainPageBannerUrl(req) {
        const {app, ctx, logger} = this;
        try {
            let sqlStr = ` 
               update merchant_banner set banner_url = ? where id = ?
            `;
            await this.execSql(sqlStr, [req.cover_oss_url, req.id], srpiptkillDb);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async insertMainPageBanner(req) {
        const {app, ctx, logger} = this;
        try {
            let sqlStr = ` 
               insert merchant_banner(create_time, delsign, sort_id, banner_name, type, href_url, banner_starttime, banner_endtime)
               values(now(), 1, ?, ?, ?, ?, ?, ?)
            `;
            await this.execSql(sqlStr, [req.sort_id, req.banner_name, req.type, req.url, req.start_time, req.end_time], srpiptkillDb);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getOpenAdList() {
        const {app, ctx, logger} = this;
        try {
            let sqlStr = ` 
               SELECT moa.id, moa.open_ad_url, moa.open_ad_name, moa.type, moa.href_url, moa.open_ad_start_time, moa.open_ad_end_time, moa.delsign
                    FROM merchant_open_ad moa ;
            `;
            return await this.execSql(sqlStr, [], srpiptkillDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async shelvesOpenAd(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
               update merchant_open_ad set delsign = 0 where id = ? ;
            `;
            await this.execSql(sqlStr, [req.id], srpiptkillDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async delOpenAd(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
               update merchant_open_ad set delsign = 1 where id = ?
            `;
            await this.execSql(sqlStr, [req.id], srpiptkillDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async insertOpenAd(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
               insert merchant_open_ad( delsign, open_ad_name, type, href_url, open_ad_start_time, open_ad_end_time)
               values( 1, ?, ?, ?, ?, ?)
            `;
            await this.execSql(sqlStr, [req.open_ad_name, req.type, req.url, req.start_time, req.end_time], srpiptkillDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateOpenAdImgUrl(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
               update merchant_open_ad set open_ad_url = ? where id = ?
            `;
            await this.execSql(sqlStr, [req.cover_oss_url, req.id], srpiptkillDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateOpenAdInfo(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
               update merchant_open_ad set open_ad_name = ?, type= ?, href_url = ?, open_ad_start_time = ?, open_ad_end_time = ? where id = ?
            `;
            await this.execSql(sqlStr, [req.open_ad_name, req.type, req.href_url, req.open_ad_start_time, req.open_ad_end_time, req.id], srpiptkillDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

}
