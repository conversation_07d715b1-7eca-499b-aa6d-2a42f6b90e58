/*
 * @Description: 操作流水
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2018-11-30 16:20:03
 * @LastEditors: hammercui
 * @LastEditTime: 2018-11-30 17:03:47
 */
import {Controller} from 'egg';
import {HttpErr, IerrorMsg} from '../../model/common';
import {
	IbannedListRequest,
	IbannedListResponse,
	IopeAchRecordRequest,
	IopeAchRecordResponse,
	IopeAvaRecordListRequest,
	IopeAvaRecordListResponse,
	IoperationListRequest,
	IoperationListResponse
} from './../../model/werewolf';

export default class OperationController extends Controller {
	public async propCount() {
        // 校验规则
		const rule = {
			playerId: { type: 'number' },
			uid: { type: 'number' },
			start: { type: 'int',},
			offset: { type: 'int',min:1 }
		};
		try {
			const { ctx, logger, app } = this;
			// 校验
			ctx.validate(rule);
			const requestBody: IoperationListRequest = ctx.request.body;
			const responseBody = await ctx.service.werewolf.operation.propCount(requestBody);
			ctx.body = responseBody;
			ctx.status = HttpErr.Success;
		} catch (e) {
			this.logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			this.ctx.body = err;
			this.ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
    }

    public async propList() {
        // 校验规则
		const rule = {
			playerId: { type: 'number' },
			uid: { type: 'number' },
			start: { type: 'int'},
			offset: { type: 'int', min: 1 }
		};
		try {
			const { ctx, logger, app } = this;
			// 校验
			ctx.validate(rule);
            const requestBody: IoperationListRequest = ctx.request.body;
            const count = await ctx.service.werewolf.operation.propCount(requestBody);
            const list  = await ctx.service.werewolf.operation.propList(requestBody);
            const responseBody: IoperationListResponse = {
				uid: requestBody.uid,
				playerId: requestBody.playerId,
				start: requestBody.start,
				offset: requestBody.offset,
				count: count,
				list: list
            };
			ctx.body = responseBody;
			ctx.status = HttpErr.Success;
		} catch (e) {
			this.logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			this.ctx.body = err;
			this.ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
    }
    
    public async bannedList(){
        // 校验规则
		const rule = {
            playerId:{ type: 'int' },
            uid:{ type: 'number' },
            type:{ type: 'number' },
            operation:{ type: 'number' },
            start:{ type: 'int' },
            offset:{ type: 'int', min: 1 }
		};
		try {
			const { ctx, logger, app } = this;
			// 校验
			ctx.validate(rule);
            const requestBody: IbannedListRequest = ctx.request.body;
            const count = await ctx.service.werewolf.operation.bannedCount(requestBody);
            const list  = await ctx.service.werewolf.operation.bannedList(requestBody);
            const responseBody: IbannedListResponse = {
				...requestBody,
                count:count,
                list:list
            };
			ctx.body = responseBody;
			ctx.status = HttpErr.Success;
		} catch (e) {
			this.logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			this.ctx.body = err;
			this.ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}

	public async getOptionList(){
		try {
			const { ctx, logger, app } = this;
			// 校验
			const requestBody: IbannedListRequest = ctx.request.body;
			ctx.body = await ctx.service.werewolf.operation.getOptionList(requestBody);
			ctx.status = HttpErr.Success;
		} catch (e) {
			this.logger.error(e);
			this.ctx.body = {err_code: HttpErr.BadRequest, err_msg: '参数有误'};
			this.ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}
	/**
	 * 操作头像流水
	 */
	public async avatarFrameList() {
		const { ctx, logger } = this;
		// 校验规则
		const rule = {
			uid: { type: "number" },
			playerId: { type: "number" },
			start: { type: 'number' },
			offset: { type: 'number' },
			otype:{ type: 'number' },
		};
		try {
			// 校验
			ctx.validate(rule);
			//service
			const requestBody: IopeAvaRecordListRequest = ctx.request.body;
			const responseBody: IopeAvaRecordListResponse = await ctx.service.werewolf.operation.getAvaOpeRecord(requestBody);
			ctx.body = responseBody;
			ctx.status = HttpErr.Success;
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = {
				err_code: HttpErr.BadRequest,
				err_msg: "参数有误"
			};
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}
	/**
	 * 操作成就流水
	 */
	public async achieveList() {
		const { ctx, logger } = this;
		// 校验规则
		const rule = {
			uid: { type: "number" },
			playerId: { type: "number" },
			start: { type: 'number' },
			offset: { type: 'number' },
			type:{ type: 'number' },
		};
		try {
			// 校验
			ctx.validate(rule);
			//service
			const requestBody: IopeAchRecordRequest = ctx.request.body;
			const responseBody: IopeAchRecordResponse = await ctx.service.werewolf.operation.getAchOpeRecord(requestBody);
			ctx.body = responseBody;
			ctx.status = HttpErr.Success;
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = {
				err_code: HttpErr.BadRequest,
				err_msg: "参数有误"
			};
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}
}
