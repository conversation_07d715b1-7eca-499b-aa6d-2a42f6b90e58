/*
 * @Description: 商城道具管理-控制类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhangyu
 * @Date: 2020-04-24 13:00:00
 * @LastEditors: zhanglu
 * @LastEditTime: 2020-10-23 17:13:33
 */
import { Controller } from 'egg';
import BaseMegaController from './BaseMegaController';
import { IerrorMsg, HttpErr } from '../../model/common';

export default class GroupPropsController extends BaseMegaController {

    public async getGroupBadgeList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.groupProps.getGroupBadgeList();
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getGroupFrameList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.groupProps.getGroupFrameList();
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getGroupBannerList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.groupProps.getGroupBannerList();
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getGroupLevelList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.groupProps.getGroupLevelList();
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async updateGroupBadge() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.groupProps.updateGroupBadge(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }

    public async updateGroupFrame() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.groupProps.updateGroupFrame(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }
    
    public async updateGroupBanner() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.groupProps.updateGroupBanner(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }

    public async insertGroupBadge() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.groupProps.insertGroupBadge(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async insertGroupFrame() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.groupProps.insertGroupFrame(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async insertGroupBanner() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.groupProps.insertGroupBanner(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async getPropList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.mallItem.getTypeList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }
    public async getMallTagList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.mallItem.getMallTagList();
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }
    public async getMallConditionList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.mallItem.getMallConditionList();
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }
    public async insertMallItem() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mallItem.insertMallItem(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }
    public async updateMallItem() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mallItem.updateMallItem(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }
    public async getJumpTypeList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.mallItem.getJumpTypeList();
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }
    public async getOpreateList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.mallItem.getOpreateList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }
    
    public async uploadTagInfo() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const result = await ctx.service.werewolf.mallItem.uploadTagInfo(requestBody);
            if (!result) {
                ctx.body = { err_code: HttpErr.BadRequest, err_msg: '上传图片信息重复' };
            } else {
                ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            }
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }
    public async delMallTag() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mallItem.delMallTag(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }
    public async changeMallItemDelsign() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mallItem.changeMallItemDelsign(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }
    public async changeEditManagerNum() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mallItem.changeEditManagerNum(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }

}
