/*
 * @Description: 安全模块-非对称加密
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2018-12-29 14:33:10
 * @LastEditors: hammercui
 * @LastEditTime: 2020-06-08 20:50:44
 */

const NodeRSA = require('node-rsa');
const key = new NodeRSA({ b: 512 });
key.setOptions({ encryptionScheme: 'pkcs1' });
const publicKey = key.exportKey('public'); //生成公钥

/**
 * @name: 非对称加密-解密
 * @msg: 
 * @param {type} 
 * @return: 
 */
const decryptedStr = (source): string => {
	// source = source.replace(/\s+/g, '+');
	try {
		let decrypted = key.decrypt(source, 'utf8'); //解密
		return decrypted;
	} catch (err) {
		//console.error('非对称加密,解密', err);
		throw err;
	}
};

//导出
export { decryptedStr, publicKey };
