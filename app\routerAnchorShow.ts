/*
 * @Description: 周末秀场
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammer
 * @Date: 2021-02-10
 * @LastEditors: 
 * @LastEditTime:
 */
import {Application} from "egg";
import {AccessRouteId} from './model/accessRouteCof';

const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {
    const { controller, router } = app;

    router.post(`${API_VERSION}/werewolf/anshorShow/getInfo`,controller.werewolf.anchorShow.getInfo);
    router.post(`${API_VERSION}/werewolf/anshorShow/getInfoByDay`,controller.werewolf.anchorShow.getInfoByDay);
    router.post(`${API_VERSION}/werewolf/anshorShow/changeAnchorReady`,controller.werewolf.anchorShow.changeAnchorReady);
    router.post(`${API_VERSION}/werewolf/anshorShow/changeVotable`,controller.werewolf.anchorShow.changeVotable);
    router.post(`${API_VERSION}/werewolf/anshorShow/changeKing`,controller.werewolf.anchorShow.changeKing);
    router.post(`${API_VERSION}/werewolf/anshorShow/incScore`,controller.werewolf.anchorShow.incScore);
    router.post(`${API_VERSION}/werewolf/anshorShow/decScore`, controller.werewolf.anchorShow.decScore);
    router.post(`${API_VERSION}/werewolf/anshorShow/changeWinCamp`,controller.werewolf.anchorShow.changeWinCamp);
    
}

export default load
