import BaseMegaController from './BaseMegaController';
import { HttpErr, IerrorMsg } from '../../model/common';
import Coin2wTradeService from '../../service/werewolf/coin2wTrade';
import { Icoin2wListReq, Icoin2wItem, Icoin2wUpdateInfoReq, Icoin2wUploadNoteSuccReq } from '../../model/wf2wCoin';
/*
 * @Description: 2w框订单
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-08-29 13:32:30
 * @LastEditors: zhanglu
 * @LastEditTime: 2022-09-15 13:51:58
 */

 export default class Coin2wTradeController extends BaseMegaController {

    public async getTwoWCoinUserList() {
        const { ctx, logger } = this;
        try {
            const req: Icoin2wListReq = ctx.request.body;
            const resp: Icoin2wItem[] = await ctx.service.werewolf.coin2wTrade.getTwoWCoinUserList(req);
            this.respSuccData(resp);
        } catch (error) {
            this.respFail(error.message);
        }
    }

    /**
     * 获得订单全部列表
     */
    public async getTwoWCoinList() {
        const { ctx, logger } = this;
        try {
            const req: Icoin2wListReq = ctx.request.body;
            const resp: Icoin2wItem[] = await ctx.service.werewolf.coin2wTrade.getTwoWCoinList(req);
            this.respSuccData(resp);
        } catch (error) {
            this.respFail(error.message);
        }
    }

    /**
     * 更新订单信息
     */
    public async updateCoin2winfo() {
        const { ctx, logger } = this;
        try {
            const req: Icoin2wUpdateInfoReq = ctx.request.body;
            await ctx.service.werewolf.coin2wTrade.updateCoin2winfo(req);
            this.respSucc();
        } catch (error) {
            this.respFail(error.message);
        }
    }

    public async uploadNoteIdSucc(){
        const { ctx, logger } = this;
        try {
            const req: Icoin2wUploadNoteSuccReq = ctx.request.body;
            await ctx.service.werewolf.coin2wTrade.uploadCoin2wNoteSuccess(req);
            this.respSucc();
        } catch (error) {
            this.respFail(error.message);
        }
    }  
    
    public async sendAllRes(req: Icoin2wItem) {
        const { ctx, logger } = this;
        try {
            const req: Icoin2wItem = ctx.request.body;
            await ctx.service.werewolf.coin2wTrade.sendAllRes(req);
            this.respSucc();
        } catch (error) {
            this.respFail(error.message);
        }
    }
 }