import { Router, Application } from "egg";
import { AccessRouteId } from './model/accessRouteCof';
import { once } from 'process';
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

  const { controller, router } = app;
  //【应用管理】新banner图
  router.post(`${API_VERSION}/werewolf/bannerControl/uploadNewBannerBaseInfo`, controller.werewolf.bannerControl.uploadNewBannerBaseInfo);//上传banner基本信息
  router.post(`${API_VERSION}/werewolf/bannerControl/uploadNewBannerImageInfo`, controller.werewolf.bannerControl.uploadNewBannerImageInfo);//上传banner图片信息
  router.post(`${API_VERSION}/werewolf/bannerControl/uploadNewHotImageInfo`, controller.werewolf.bannerControl.uploadNewHotImageInfo);//上传热门图片信息
  router.post(`${API_VERSION}/werewolf/bannerControl/updateNewBannerBaseIn<PERSON>`, controller.werewolf.bannerControl.updateNewBannerBaseInfo);//更新banner基本信息
  //   router.post(`${API_VERSION}/werewolf/bannerControl/delBannerInfo`, controller.werewolf.bannerControl.delBannerInfo);//删除banner信息
}

export default load
