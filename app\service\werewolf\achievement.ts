/*
 * @Description: 无
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: leeou
 * @LastEditors: Please set LastEditors
 * @Date: 2019-04-08 10:36:11
 * @LastEditTime: 2021-09-08 17:23:52
 */

import { Service } from 'egg';
import { IachievementListRequest, IachievementListResponse, IupdateAchieveDelsignRequest, IupdateAchieveDelsignResponse, IsendAchieveToUserRequest, IuploadAchieveBaseRequest, Iachievement, IuploadAchieveCompleteRequest, IuploadAchieveCompleteResponse } from '../../model/werewolf';
import BaseMegaService from './BaseMegaService';

export default class AchievementService extends BaseMegaService {

    /**
     * 查询成就列表
     * @param request 
     */
    public async getList(request: IachievementListRequest): Promise<IachievementListResponse> {
        const { app, ctx, logger } = this;
        const werewolf = app.mysql.get("werewolf");
        try {
            const start = werewolf.escape(request.start);
            const offset = werewolf.escape(request.offset);
            const isComplete = werewolf.escape(request.isComplete);
            let count = 0;
            let sql = "SELECT COUNT(*) AS total FROM achievement WHERE mode = 1 AND uplevel = 0 AND is_complete = " + isComplete;
            let result = await werewolf.query(sql);
            if (!!result && result.length > 0) {
                count = result[0].total;
            }
            sql =
                "SELECT id,`name`,CONCAT('http://" + app.config.imgDomain + "/Werewolf/achieveNew/achieve_',id,'.png') AS url,remark,is_complete,delsign FROM achievement WHERE `mode` = 1" +
                " AND uplevel = 0 AND is_complete = " +
                isComplete +
                " ORDER BY id DESC LIMIT " +
                start +
                "," +
                offset;
            result = await werewolf.query(sql);
            let array: Iachievement[] = new Array();
            for (let item of result) {
                let url = item.url;
                if (isComplete == 0) {
                    let res = await ctx.curl('http://' + app.config.phpDomain + '/Werewolf/achieveNew/checkImg.php', {
                        method: 'POST',
                        // 通过 contentType 告诉 HttpClient 以 JSON 格式发送
                        contentType: 'json',
                        data: { achieveId: item.id },
                        dataType: 'text', // 自动解析 JSON response
                        timeout: 3000, // 3 秒超时
                    });
                    if (!!res && res.status == 200) {
                        if (res.data == 'fail') {
                            url = '';
                        }
                    } else {
                        logger.error('werewolf_achieve', res);
                        url = '';
                    }
                }
                array.push({
                    id: item.id,
                    name: item.name,
                    url: url,
                    remark: item.remark,
                    is_complete: item.is_complete,
                    delsign: item.delsign
                });
            }
            return {
                ...request,
                count: count,
                dataArray: array
            }
        } catch (error) {
            throw error;
        }
    }
    /**
     * 上架/下架应用内成就
     * @param request 
     */
    public async updateDelsign(request: IupdateAchieveDelsignRequest): Promise<IupdateAchieveDelsignResponse> {
        const { app } = this;
        const werewolf = app.mysql.get("werewolf");
        const manager = app.mysql.get("manager");
        const werewolfConn = await werewolf.beginTransaction();
        const managerConn = await manager.beginTransaction();
        try {
            let type = 1;
            if (request.delsign == 1) {
                type = 2;
            }
            const wereRow = {
                delsign: request.delsign
            };
            const wereOptions = {
                where: {
                    id: request.achieveId
                }
            };
            await werewolfConn.update("achievement", wereRow, wereOptions);
            await managerConn.insert("wf_admin_achieve", {
                admin_id: request.uid,
                achieve_id: request.achieveId,
                achieve_name: request.achieveId,
                type: type
            });
            await werewolfConn.commit(); // 提交事务
            await managerConn.commit();
            const sql = "SELECT id,`name`,CONCAT('http://" + app.config.imgDomain + "/Werewolf/achieveNew/achieve_',id,'.png') AS url,remark,is_complete,delsign FROM achievement WHERE `mode` = 1 AND id = " + request.achieveId
            const result = await werewolf.query(sql);
            return {
                dataArray: result
            }
        } catch (error) {
            await werewolfConn.rollback(); // 一定记得捕获异常后回滚事务！
            await managerConn.rollback();
            throw error;
        }
    }

    /**
     * 发成就
     * @param request 
     */
    public async sendAchieve(request: IsendAchieveToUserRequest): Promise<boolean> {
        const { app } = this;
        const werewolf = app.mysql.get("werewolf");
        const manager = app.mysql.get("manager");
        const werewolfConn = await werewolf.beginTransaction();
        const managerConn = await manager.beginTransaction();
        try {
            const achieveId = werewolfConn.escape(request.achieveId);
            let userId = request.playerId.split(';');
            let sqlStr = '';
            let rows: any[] = [];
            for (let item of userId) {
                let user = werewolfConn.escape(item);
                user = user.replace(/\'/g, '');
                sqlStr += "(" + user + "," + achieveId + "),"
                let row = {
                    admin_id: request.uid,
                    user_id: user,
                    achieve_id: request.achieveId,
                    achieve_name: request.achieveId,
                    type: 3
                }
                rows.push(row);
            }
            sqlStr = sqlStr.substring(0, sqlStr.length - 1);
            const sql = "INSERT IGNORE INTO user_achievement (user_id,achievement_id) VALUES" + sqlStr;
            await werewolfConn.query(sql);
            await managerConn.insert("wf_admin_achieve", rows);
            await werewolfConn.commit(); // 提交事务
            await managerConn.commit();
            return true;
        } catch (error) {
            await werewolfConn.rollback(); // 一定记得捕获异常后回滚事务！
            await managerConn.rollback();
            throw error;
        }
    }

    /**
     * 上传成就基本信息
     * @param request 
     */
    public async uploadBase(request: IuploadAchieveBaseRequest): Promise<Iachievement> {
        const { app } = this;
        const werewolf = app.mysql.get("werewolf");
        const manager = app.mysql.get("manager");
        const werewolfConn = await werewolf.beginTransaction();
        const managerConn = await manager.beginTransaction();
        try {
            let achieveId = 0;
            const result = await werewolfConn.insert('achievement', {
                name: request.name,
                type: 0,
                role: 0,
                camp: 0,
                remark: request.remark,
                mode: 1,
                delsign: 0
            });
            achieveId = result.insertId;
            await managerConn.insert("wf_admin_achieve", {
                admin_id: request.uid,
                achieve_id: achieveId,
                achieve_name: achieveId,
                type: 0
            });
            await werewolfConn.commit(); // 提交事务
            await managerConn.commit();
            const sql = "SELECT id,`name`,remark,is_complete,delsign FROM achievement WHERE id = " + achieveId;
            const baseResult = await werewolf.query(sql);
            return {
                id: baseResult[0].id,
                name: baseResult[0].name,
                url: '',
                remark: baseResult[0].remark,
                is_complete: baseResult[0].is_complete,
                delsign: baseResult[0].delsign,
            }
        } catch (error) {
            await werewolfConn.rollback(); // 一定记得捕获异常后回滚事务！
            await managerConn.rollback();
            throw error;
        }
    }

    /**
     * 上传完成
     * @param request 
     */
    public async complete(request: IuploadAchieveCompleteRequest): Promise<IuploadAchieveCompleteResponse> {
        const { app, ctx, logger } = this;
        const werewolf = app.mysql.get("werewolf");
        try {
            let res = await ctx.curl('http://' + app.config.phpDomain + '/Werewolf/achieveNew/checkImg.php', {
                method: 'POST',
                // 通过 contentType 告诉 HttpClient 以 JSON 格式发送
                contentType: 'json',
                data: { achieveId: request.achieveId },
                dataType: 'text', // 自动解析 JSON response
                timeout: 3000, // 3 秒超时
            });
            if (!!res && res.status == 200) {
                if (res.data == 'fail') {
                    return {
                        is_complete: 0
                    };
                }
            } else {
                logger.error('werewolf_achieve', res);
                return {
                    is_complete: 0
                };
            }
            const wereRow = {
                is_complete: 1
            };
            const wereOptions = {
                where: {
                    id: request.achieveId
                }
            };
            await werewolf.update("achievement", wereRow, wereOptions);
            //todo 更新
            if(request.selectCoin2w){
                await this.execSql(`UPDATE coin_2w SET achievement_id = ? WHERE id = ?;`,[
                    request.achieveId,
                    request.selectCoin2w.id
                ]);
            }
            return {
                is_complete: 1
            };
        } catch (error) {
            throw error;
        }
    }

    /**
     * 上传完成 头像框刻字订单
     * @param request 
     */
    public async completeNoteOrder(request: IuploadAchieveCompleteRequest): Promise<IuploadAchieveCompleteResponse> {
    const { app, ctx, logger } = this;
    const werewolf = app.mysql.get("werewolf");
    try {
        let res = await ctx.curl('http://' + app.config.phpDomain + '/Werewolf/achieveNew/checkImg.php', {
            method: 'POST',
            // 通过 contentType 告诉 HttpClient 以 JSON 格式发送
            contentType: 'json',
            data: { achieveId: request.achieveId },
            dataType: 'text', // 自动解析 JSON response
            timeout: 3000, // 3 秒超时
        });
        if (!!res && res.status == 200) {
            if (res.data == 'fail') {
                return {
                    is_complete: 0
                };
            }
        } else {
            logger.error('werewolf_achieve', res);
            return {
                is_complete: 0
            };
        }
        const wereRow = {
            is_complete: 1
        };
        const wereOptions = {
            where: {
                id: request.achieveId
            }
        };
        await werewolf.update("achievement", wereRow, wereOptions);
        //todo 更新
        if(request.selectCoin2w){
            await this.execSql(`UPDATE coin_2w_note SET achievement_id = ? WHERE id = ?;`,[
                request.achieveId,
                request.selectCoin2w.id
            ]);
        }
        return {
            is_complete: 1
        };
    } catch (error) {
        throw error;
    }
}
}
