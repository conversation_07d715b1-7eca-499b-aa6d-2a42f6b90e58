/*
 * @Description: 道具管理
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: z<PERSON>yi
 * @Date: 2020-11-03 11:50:11
 */
import { Router, Application } from "egg";
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

    const { controller, router } = app;
    //【道具管理轮】获取道具类别,
    router.post(`${API_VERSION}/werewolf/item/getItemTypeList`, controller.werewolf.item.getItemTypeList)
    //【道具管理轮】获取道具,
    router.post(`${API_VERSION}/werewolf/item/getItemList`, controller.werewolf.item.getItemList)
    //【道具管理轮】插入预发送记录,
    router.post(`${API_VERSION}/werewolf/item/createItemRecord`, controller.werewolf.item.createItemRecord)
    //【道具管理轮】获取预发送记录,
    router.post(`${API_VERSION}/werewolf/item/getRecordList`, controller.werewolf.item.getRecordList)
    //【道具管理轮】获取更新记录,
    router.post(`${API_VERSION}/werewolf/item/getItem`, controller.werewolf.item.getItem)
    //【道具管理轮】 删除记录,
    router.post(`${API_VERSION}/werewolf/item/deleteItemRecord`, controller.werewolf.item.deleteItemRecord)
    //【道具管理轮】 发送奖励,
    router.post(`${API_VERSION}/werewolf/item/sendReward`, controller.werewolf.item.sendReward)
    //【道具管理轮】获取发送用户列表,
    router.post(`${API_VERSION}/werewolf/item/getUserList`, controller.werewolf.item.getUserList)
    //【道具管理轮】检测用户列表,
    router.post(`${API_VERSION}/werewolf/item/checkUserList`, controller.werewolf.item.checkUserList)

    
}

export default load