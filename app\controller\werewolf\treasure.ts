/*
 * @Description: 无
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: leeou
 * @Date: 2019-01-14 14:01:20
 * @LastEditors: jiawen.wang
 * @LastEditTime: 2022-02-15 17:42:09
 */
import { Controller } from 'egg'
import { HttpErr, IerrorMsg } from '../../model/common'
import { treasureRecord } from '../../model/staticEnum'
import {
    ItreasureRequest,
    ItransactionResponse,
    IpropsBuyResponse,
    IpropsPayResponse,
    IgroupBuyResponse,
    IgroupPayResponse,
    IcpBuyResponse,
    IcpPayResponse,
    IcpGiveResponse,
    IboxOpenReponse,
    IudidDetailsRequest,
    IudidDetailsResponse,
    IgiftGiveResponse,
    IgiftReceiveResponse,
    IboxGiveReponse,
    IuserFrameResponse,
    IupdateUserFrameDelsignRequest,
    IupdateUserFrameDelsignResponse,
    IuploadLetteringRequest,
    IuploadLetteringResponse,
    IupdateUserLetteringRequest,
    IuserAchieveRequest,
    IuserAchieveResponse,
    IuserAchieveUpdateDelsignRequest,
    IuserAchieveUpdateDelsignResponse,
    IshutterRequest,
    ItreasureRedbagResponse,
    ItreasureBGAuthorityResponse,
    ItreasureSpecialEffectBuyResponse,
    ItreasureSpecialEffectGiveResponse,
    ItreasureSpecialEffectReceiveResponse,
    ItreasureCanonRecordResponse,
    ItabnormalDescItem,
    ImallBuyRecord,
    ItreasuremallBuyRecordResponse,
    IgiftbagReciveRecordResponse,
    IgiftbagItemReceiveRecordResponse,
    IusePropsRecordResponse,
} from '../../model/werewolf'

export default class TreasureController extends Controller {
    public async treasureList() {
        const { ctx, logger } = this
        // 校验规则
        const rule = {
            uid: { type: 'number' },
            playerId: { type: 'number' },
            type: { type: 'number' },
            start: { type: 'number' },
            offset: { type: 'number' },
        }
        try {
            // 校验
            // ctx.validate(rule)
            //service
            const requestBody: ItreasureRequest = ctx.request.body
            switch (requestBody.type) {
                case treasureRecord.transaction: //充值记录查询
                    const transactionBody: ItransactionResponse = await ctx.service.werewolf.treasure.transaction(requestBody)
                    ctx.body = transactionBody
                    ctx.status = HttpErr.Success
                    break
                case treasureRecord.tabnormalNew:
                    const normalListNew = await ctx.service.werewolf.treasure.tabnormalNew(requestBody)
                    logger.info('normalListNew', normalListNew)
                    ctx.body = normalListNew
                    ctx.status = HttpErr.Success
                    break
                case treasureRecord.tabnormal:
                    const normalList = await ctx.service.werewolf.treasure.tabnormalV2(requestBody)
                    logger.info('normalList', normalList)
                    ctx.body = normalList
                    ctx.status = HttpErr.Success
                    break
                case treasureRecord.defaultPropsBuy: //道具商城购买
                    const propsBuyBody: IpropsBuyResponse = await ctx.service.werewolf.treasure.propsBuy(requestBody)
                    ctx.body = propsBuyBody
                    ctx.status = HttpErr.Success
                    break

                case treasureRecord.defaultPropsPay: //道具商城使用
                    const propsPay: IpropsPayResponse = await ctx.service.werewolf.treasure.propsPay(requestBody)
                    ctx.body = propsPay
                    ctx.status = HttpErr.Success
                    break

                case treasureRecord.groupPropsBuy: //公会商城购买
                    const groupBuy: IgroupBuyResponse = await ctx.service.werewolf.treasure.groupBuy(requestBody)
                    ctx.body = groupBuy
                    ctx.status = HttpErr.Success
                    break

                case treasureRecord.groupPropsPay: //公会商城使用
                    const groupPay: IgroupPayResponse = await ctx.service.werewolf.treasure.groupPay(requestBody)
                    ctx.body = groupPay
                    ctx.status = HttpErr.Success
                    break

                case treasureRecord.cpPropsBuy: //CP商城购买
                    const cpBuy: IcpBuyResponse = await ctx.service.werewolf.treasure.cpBuy(requestBody)
                    ctx.body = cpBuy
                    ctx.status = HttpErr.Success
                    break

                case treasureRecord.cpPropsPay: //CP商城使用
                    const cpPay: IcpPayResponse = await ctx.service.werewolf.treasure.cpPay(requestBody)
                    ctx.body = cpPay
                    ctx.status = HttpErr.Success
                    break

                case treasureRecord.cpPropsGive: //CP游戏内使用
                    const cpGive: IcpGiveResponse = await ctx.service.werewolf.treasure.cpGive(requestBody)
                    ctx.body = cpGive
                    ctx.status = HttpErr.Success
                    break

                case treasureRecord.openBox: //开宝箱
                    const boxOpen: IboxOpenReponse = await ctx.service.werewolf.treasure.boxOpen(requestBody)
                    ctx.body = boxOpen
                    ctx.status = HttpErr.Success
                    break
                case treasureRecord.giftGive: //礼物赠送
                    const giftGive: IgiftGiveResponse = await ctx.service.werewolf.treasure.giftGive(requestBody)
                    ctx.body = giftGive
                    ctx.status = HttpErr.Success
                    break
                case treasureRecord.giftReceive: //礼物接收
                    const giftReceive: IgiftReceiveResponse = await ctx.service.werewolf.treasure.giftReceive(requestBody)
                    ctx.body = giftReceive
                    ctx.status = HttpErr.Success
                    break
                case treasureRecord.boxGive: //宝箱赠送
                    const boxGive: IboxGiveReponse = await ctx.service.werewolf.treasure.boxGive(requestBody)
                    ctx.body = boxGive
                    ctx.status = HttpErr.Success
                    break
                case treasureRecord.avatarFrame: //头像框
                    const avatarFrame: IuserFrameResponse = await ctx.service.werewolf.treasure.getUserFrame(requestBody)
                    ctx.body = avatarFrame
                    ctx.status = HttpErr.Success
                    break
                case treasureRecord.achievement: //头像框
                    const achievement: IuserAchieveResponse = await ctx.service.werewolf.treasure.achieveList(requestBody)
                    ctx.body = achievement
                    ctx.status = HttpErr.Success
                    break
                case treasureRecord.backgroundAuthority: //背景板上传权限
                    const bgAuthority: ItreasureBGAuthorityResponse = await ctx.service.werewolf.treasure.backgroundAuthority(requestBody)
                    ctx.body = bgAuthority
                    ctx.status = HttpErr.Success
                    break
                case treasureRecord.specialEffectBuy: //动效购买查询
                    const specialEffectBuy: ItreasureSpecialEffectBuyResponse = await ctx.service.werewolf.treasure.specialEffectBuy(requestBody)
                    ctx.body = specialEffectBuy
                    ctx.status = HttpErr.Success
                    break
                case treasureRecord.specialEffectGive: //动效赠送查询
                    const specialEffectGive: ItreasureSpecialEffectGiveResponse = await ctx.service.werewolf.treasure.specialEffectGive(requestBody)
                    ctx.body = specialEffectGive
                    ctx.status = HttpErr.Success
                    break
                case treasureRecord.specialEffectReceive: //动效接收查询
                    const specialEffectReceive: ItreasureSpecialEffectReceiveResponse = await ctx.service.werewolf.treasure.specialEffectReceive(
                        requestBody,
                    )
                    ctx.body = specialEffectReceive
                    ctx.status = HttpErr.Success
                    break
                case treasureRecord.canonRecord: //盛典商城
                    const canonRecord: ItreasureCanonRecordResponse = await ctx.service.werewolf.treasure.canonRecord(requestBody)
                    ctx.body = canonRecord
                    ctx.status = HttpErr.Success
                    break
                case treasureRecord.mallBuyRecord: //商城购买记录
                    const mallBuyRecord: ItreasuremallBuyRecordResponse = await ctx.service.werewolf.treasure.mallBuyRecord(requestBody)
                    ctx.body = mallBuyRecord
                    ctx.status = HttpErr.Success
                    break
                case treasureRecord.giftbagReceiveRecord: //商城购买记录
                    const giftbagReceiveRecord: IgiftbagReciveRecordResponse = await ctx.service.werewolf.treasure.giftbagReciveRecord(requestBody)
                    ctx.body = giftbagReceiveRecord
                    ctx.status = HttpErr.Success

                    break
                case treasureRecord.giftbagItemReceiveRecord: //商城购买记录
                    const giftbagItemReceiveRecord: IgiftbagItemReceiveRecordResponse = await ctx.service.werewolf.treasure.giftbagItemReciveRecord(
                        requestBody,
                    )
                    ctx.body = giftbagItemReceiveRecord
                    ctx.status = HttpErr.Success
                    break
                case treasureRecord.usePropsRecord: //商城购买记录
                    const usePropsRecord: IusePropsRecordResponse = await ctx.service.werewolf.treasure.usePropsRecord(requestBody)
                    ctx.body = usePropsRecord
                    ctx.status = HttpErr.Success
                    break
            }
        } catch (e) {
            logger.error(e)
            const err: IerrorMsg = {
                err_code: HttpErr.BadRequest,
                err_msg: '参数有误',
            }
            ctx.body = err
            ctx.status = HttpErr.BadRequest //请求参数错误 401Unauthorized未鉴权
        }
    }
    public async userFrameSearch() {
        const { ctx, logger } = this
        // 校验规则
        const rule = {
            playerId: { type: 'number' },
            searchWord: { type: 'string' },
        }
        try {
            // 校验
            ctx.validate(rule)
            const requestBody: ItreasureRequest = ctx.request.body
            const avatarFrame: IuserFrameResponse = await ctx.service.werewolf.treasure.userFrameSearch(requestBody)
            ctx.body = avatarFrame
            ctx.status = HttpErr.Success
        } catch (e) {
            logger.error(e)
            const err: IerrorMsg = {
                err_code: HttpErr.BadRequest,
                err_msg: '参数有误',
            }
            ctx.body = err
            ctx.status = HttpErr.BadRequest //请求参数错误 401Unauthorized未鉴权
        }
    }
    public async udidDetails() {
        const { ctx, logger } = this
        // 校验规则
        const rule = {
            uid: { type: 'number' },
            udid: { type: 'string' },
            start: { type: 'number' },
            offset: { type: 'number' },
        }
        try {
            // 校验
            ctx.validate(rule)
            //service
            const requestBody: IudidDetailsRequest = ctx.request.body
            const responseBody: IudidDetailsResponse = await ctx.service.werewolf.treasure.udidDetails(requestBody)
            ctx.body = responseBody
            ctx.status = HttpErr.Success
        } catch (e) {
            logger.error(e)
            const err: IerrorMsg = {
                err_code: HttpErr.BadRequest,
                err_msg: '参数有误',
            }
            ctx.body = err
            ctx.status = HttpErr.BadRequest //请求参数错误 401Unauthorized未鉴权
        }
    }

    /**
     * 更新用户头框框状态
     */
    public async updateFrameDelsign() {
        const { ctx, logger } = this
        // 校验规则
        const rule = {
            uid: { type: 'number' },
            playerId: { type: 'number' },
            avatarId: { type: 'number' },
            delsign: { type: 'number' },
        }
        try {
            // 校验
            ctx.validate(rule)
            //service
            const requestBody: IupdateUserFrameDelsignRequest = ctx.request.body
            const responseBody: IupdateUserFrameDelsignResponse = await ctx.service.werewolf.treasure.updateFrameDelsign(requestBody)
            ctx.body = responseBody
            ctx.status = HttpErr.Success
        } catch (e) {
            logger.error(e)
            const err: IerrorMsg = {
                err_code: HttpErr.BadRequest,
                err_msg: '参数有误',
            }
            ctx.body = err
            ctx.status = HttpErr.BadRequest //请求参数错误 401Unauthorized未鉴权
        }
    }

    /**
     * 添加刻字
     */
    public async uploadLettering() {
        const { ctx, logger } = this
        // 校验规则
        const rule = {
            name: { type: 'string' },
        }
        try {
            // 校验
            ctx.validate(rule)
            //service
            const requestBody: IuploadLetteringRequest = ctx.request.body
            const responseBody: IuploadLetteringResponse = await ctx.service.werewolf.treasure.uploadLettering(requestBody)
            ctx.body = responseBody
            ctx.status = HttpErr.Success
        } catch (e) {
            logger.error(e)
            const err: IerrorMsg = {
                err_code: HttpErr.BadRequest,
                err_msg: '参数有误',
            }
            ctx.body = err
            ctx.status = HttpErr.BadRequest //请求参数错误 401Unauthorized未鉴权
        }
    }

    /**
     * 取消刻字
     */
    public async delLettering() {
        const { ctx, logger } = this
        // 校验规则
        const rule = {
            user_id: { type: 'number' },
            avatarframe_id: { type: 'number' },
        }
        try {
            // 校验
            ctx.validate(rule)
            //service
            const req = ctx.request.body
            const resp = await ctx.service.werewolf.treasure.delLettering(req)
            ctx.body = resp
            ctx.status = HttpErr.Success
        } catch (e) {
            logger.error(e)
            const err: IerrorMsg = {
                err_code: HttpErr.BadRequest,
                err_msg: '参数有误',
            }
            ctx.body = err
            ctx.status = HttpErr.BadRequest //请求参数错误 401Unauthorized未鉴权
        }
    }

    /**
     * 发刻字
     */
    public async updateUserLettering() {
        const { ctx, logger } = this
        // 校验规则
        const rule = {
            playerId: { type: 'number' },
            avatarId: { type: 'number' },
            letteringId: { type: 'number' },
        }
        try {
            // 校验
            ctx.validate(rule)
            //service
            const requestBody: IupdateUserLetteringRequest = ctx.request.body
            await ctx.service.werewolf.treasure.updateUserLettering(requestBody)
            await ctx.service.werewolf.treasure.updateUserLetteringPHP(requestBody)

            ctx.body = { code: 200 }
            ctx.status = HttpErr.Success
        } catch (e) {
            logger.error(e)
            const err: IerrorMsg = {
                err_code: HttpErr.BadRequest,
                err_msg: '参数有误',
            }
            ctx.body = err
            ctx.status = HttpErr.BadRequest //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateAchieveDelsign() {
        const { ctx, logger } = this
        // 校验规则
        const rule = {
            uid: { type: 'number' },
            playerId: { type: 'number' },
            achieveId: { type: 'number' },
            delsign: { type: 'number' },
        }
        try {
            // 校验
            ctx.validate(rule)
            //service
            const requestBody: IuserAchieveUpdateDelsignRequest = ctx.request.body
            const responseBody: IuserAchieveUpdateDelsignResponse = await ctx.service.werewolf.treasure.updateAchieveDelsign(requestBody)
            ctx.body = responseBody
            ctx.status = HttpErr.Success
        } catch (e) {
            logger.error(e)
            const err: IerrorMsg = {
                err_code: HttpErr.BadRequest,
                err_msg: '参数有误',
            }
            ctx.body = err
            ctx.status = HttpErr.BadRequest //请求参数错误 401Unauthorized未鉴权
        }
    }
    //红包详情
    public async redbagDetail() {
        const { ctx, logger } = this
        // 校验规则
        const rule = {
            uid: { type: 'number' },
            playerId: { type: 'number' },
        }
        try {
            // 校验
            ctx.validate(rule)
            //service
            const requestBody: IshutterRequest = ctx.request.body
            const responseBody: ItreasureRedbagResponse = await ctx.service.werewolf.treasure.redbagDetail(requestBody)
            ctx.body = responseBody
            ctx.status = HttpErr.Success
        } catch (e) {
            logger.error(e)
            const err: IerrorMsg = {
                err_code: HttpErr.BadRequest,
                err_msg: '参数有误',
            }
            ctx.body = err
            ctx.status = HttpErr.BadRequest //请求参数错误 401Unauthorized未鉴权
        }
    }

    //红包详情
    public async tabnormalDescs() {
        const { ctx, logger } = this
        try {
            const responseBody: ItabnormalDescItem[] = await ctx.service.werewolf.treasure.tabnormalDescs()
            ctx.body = responseBody
            ctx.status = HttpErr.Success
        } catch (e) {
            logger.error(e)
            const err: IerrorMsg = {
                err_code: HttpErr.BadRequest,
                err_msg: '参数有误',
            }
            ctx.body = err
            ctx.status = HttpErr.BadRequest //请求参数错误 401Unauthorized未鉴权
        }
    }
}
