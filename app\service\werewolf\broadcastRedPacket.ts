/*
 * @Description: 商城道具服务类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: z<PERSON><PERSON>
 * @Date: 2020-04-24 11:35:39
 * @LastEditors: zhang<PERSON>
 * @LastEditTime: 2021-07-21 10:54:23
 */
import BaseMegaService from './BaseMegaService';
import {
    IbroadcastRedPacketInsertParams,
    IbroadcastRedPacketUpdateParams,
    IbroadcastRedPacketUpdateTypeParams
} from "../../model/broadcastRedPacketDto";
import {shuffle} from "../../util/utils";

const managerDb = "manager";

export default class BroadcastRedPacketService extends BaseMegaService {
 
    public async getList() {
        const {app, ctx, logger} = this;
        try {
            let sqlStr = `
                SELECT b.\`id\`,
                       b.admin_id,
                       b.sum,
                       b.\`name\`,
                       b.\`desc\`,
                       DATE_FORMAT(b.create_time, '%Y-%m-%d %H:%i:%s') create_time,
                       DATE_FORMAT(b.send_time, '%Y-%m-%d %H:%i:%s')   send_time,
                       b.type,
                       u.nickname AS                                   admin_nickname
                FROM wf_admin_broadcast_red_packet b
                         INNER JOIN wf_admin_user u ON u.id = b.admin_id
                ORDER BY b.type ASC, b.send_time ASC
            `;
            return await this.selectList(sqlStr, [], managerDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateType(req: IbroadcastRedPacketUpdateTypeParams) {
        const {app, ctx, logger} = this;
        const manager = app.mysql.get(managerDb);
        const managerConn = await manager.beginTransaction();

        //'0：等待广播  1：已经取消 2：已经广播 3：发送中'


        try {

            if (req.type == 1) {
                // 禁止修改已经发送红包的 type
                const sql = `SELECT type
                             FROM wf_admin_broadcast_red_packet
                             WHERE id = ?`;
                const arr = await managerConn.query(sql, [req.id]);
                // logger.info('arrrrrr:',arr)
                if (arr[0] && arr[0].type == 2) {
                    return '该红包已经发送'
                }
            }


            let sql;
            if (req.type == 0) {
                // 立即发送
                sql = `
                    UPDATE
                        wf_admin_broadcast_red_packet
                    SET type      = ?,
                        admin_id  = ?,
                        send_time = DATE_ADD(NOW(), INTERVAL 1 MINUTE)
                    WHERE id = ?
                `;
            } else {
                sql = `
                    UPDATE
                        wf_admin_broadcast_red_packet
                    SET type     = ?,
                        admin_id = ?
                    WHERE id = ?`;
            }

            await managerConn.query(sql, [req.type, req.admin_id, req.id]);
            await managerConn.commit();
        } catch (error) {
            await managerConn.rollback();
            logger.error(error);
            throw error
        }
    }

    public async update(req: IbroadcastRedPacketUpdateParams) {
        const {app, ctx, logger} = this;
        const manager = app.mysql.get(managerDb);
        const managerConn = await manager.beginTransaction();
        try {
            let sql = `
                UPDATE wf_admin_broadcast_red_packet
                SET admin_id  = '${req.admin_id}',
                    \`sum\`   = '${req.sum}',
                    \`name\`  = '${req.name}',
                    \`desc\`  = '${req.desc}',
                    send_time = '${req.send_time}',
                    type      = 0
                WHERE id = '${req.id}';
            `

            await managerConn.query(sql, []);
            await managerConn.commit();
        } catch (error) {
            await managerConn.rollback();
            logger.error(error);
            throw error
        }
    }

    public async insert(req: IbroadcastRedPacketInsertParams) {
        const {app, ctx, logger} = this;
        const manager = app.mysql.get(managerDb);
        const managerConn = await manager.beginTransaction();
        try {
            // let send_time = req.send_time && req.send_time != "" ? `'${req.send_time}'` : "DATE_ADD(NOW(),INTERVAL 1 MINUTE)";

            let sql = `
                INSERT INTO wf_admin_broadcast_red_packet (admin_id,
                                                           sum,
                                                           \`name\`,
                                                           \`desc\`,
                                                           send_time,
                                                           type)
                VALUES (?, ?, ?, ?, ?, ?)
            `;

            await managerConn.query(sql, [req.admin_id, req.sum, req.name, req.desc, req.send_time, 0]);
            await managerConn.commit();
        } catch (error) {
            await managerConn.rollback();
            logger.error(error);
            throw error
        }
    }

    public async checkBroadcast() {
        const {app, ctx, logger} = this;
        const manager = app.mysql.get(managerDb);


        if (this.app.config.broadcastRun != 1) {
            return;
        }

        logger.info('检测世界红包发送！')

        const managerConn = await manager.beginTransaction();
        try {
            let sql = `
                SELECT *
                FROM wf_admin_broadcast_red_packet
                WHERE type = 0
                  AND send_time <= NOW()
            `;

            const sumMap = {
                888: 11,
                1888: 12,
                5888: 13,
            }

            let list = await managerConn.query(sql, []);
            let resultList: any[] = new Array();
            for (const item of list) {

                if (!item.sum) {
                    logger.error("世界红包发送失败1 data:", item);
                    await ctx.service.werewolf.feiShu.sendMessage('运营活动-世界红包警告', `【运营活动-世界红包警告】世界红包发送失败1 data: ${JSON.stringify(item)}`);
                    continue
                }

                if (!sumMap[item.sum]) {
                    logger.error("世界红包发送失败2 data:", item);
                    await ctx.service.werewolf.feiShu.sendMessage('运营活动-世界红包警告', `【运营活动-世界红包警告】世界红包发送失败2 data: ${JSON.stringify(item)}`);
                    continue
                }

                const sendPara = {
                    userId: 7,
                    groupRedBoxId: sumMap[item.sum],
                    channel: 3,
                    id: 7,
                    msg: item.desc ?? '',
                    num: 100,
                    imprison_type: 0,
                    impriso: '',
                }
                let success = await this.sendRedPacketByExchange(ctx, sendPara)
                if (!success) {
                    logger.debug("世界红包发送失败4 param:", sendPara);
                    await ctx.service.werewolf.feiShu.sendMessage('运营活动-世界红包警告', `【运营活动-世界红包警告】世界红包发送失败4 param: ${JSON.stringify(sendPara)}`);
                } else {
                    logger.debug("世界红包发送成功 id:", item.id);
                    resultList.push(item);
                }
            }
            for (const item of resultList) {
                let updateSql = `
                    UPDATE
                        wf_admin_broadcast_red_packet
                    SET type = 2
                    WHERE id = ${item.id}
                `;

                await managerConn.query(updateSql, []);
            }
            await managerConn.commit();
        } catch (error) {
            await managerConn.rollback();
            logger.error(error);
            throw error
        }
    }

    async sendRedPacketByExchange(ctx, params) {
        const {app, logger} = this;
        let res = await ctx.curl(app.config.WerewolfJPExchange + '/group/luckybox/send', {
            method: 'POST',
            headers: {
                "content-type": "application/json",
            },
            contentType: 'json',
            dataType: 'json',
            data: params,
            timeout: 3000, // 3 秒超时
        });

        if (res && res.data.code == 1) {
            let arr = res.data.data.groupRedbagGetRecordList.map((v) => ({id: Number(v), s: 0}))
            arr = shuffle(arr)
            let redis = this.app.redis.get('tokenRedis');
            const success = await redis.hset('wf_group_luckybox', res.data.data.groupRedBagRecordId, JSON.stringify(arr))
            return success == 1
        } else {
            logger.error("世界红包发送失败3 param:", params, 'result: ', res);
            await ctx.service.werewolf.feiShu.sendMessage('运营活动-世界红包警告', `【运营活动-世界红包警告】世界红包发送失败3 param: ${JSON.stringify(params)} result: ${JSON.stringify(res)}`);
        }

        return false
    }

}
