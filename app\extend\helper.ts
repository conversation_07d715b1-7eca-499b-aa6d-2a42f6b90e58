/*
 * @Description: 无
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2018-11-20 18:00:10
 * @LastEditors: hammercui
 * @LastEditTime: 2018-11-20 18:00:10
 */
import {Ipayload} from "../model/common"; 
module.exports = {
    //生成token
    createToken(uid: number,tokenId: number,accessRoutes: number[]): string {
      const {ctx, app, logger} = this;
      //const tokenId = Date.now();
      //const payload: Ipayload = {uid: uid, ip: ctx.request.ip, host: ctx.request.host,tokenId:tokenId};
      const payload: Ipayload = {uid: uid, ip: ctx.request.ip,tokenId:tokenId,accessRoutes:accessRoutes};
      //payload加密到jwt并设置超时时间30分钟
      const token = app.jwt.sign(payload, app.config.jwt.secret, {expiresIn: app.config.jwt.exTime });
      logger.info("生成token", token);
      return token;
    },

    exPayload:undefined,

};
