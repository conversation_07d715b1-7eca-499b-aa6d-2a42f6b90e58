/*
 * @Description: 公会战-控制类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2020-10-28 13:00:00
 */
import { Controller } from 'egg';
import BaseMegaController from './BaseMegaController';
import { IerrorMsg, HttpErr } from '../../model/common';
import {IhomeDialogConf} from "../../model/wfHomeDIalog";

export default class GroupVersusController extends BaseMegaController {

    public async getSeasonList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.groupVersus.getSeasonList();
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getGroupList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.groupVersus.getGroupList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getVersusList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            logger.info(requestBody);
            const list = await ctx.service.werewolf.groupVersus.getVersusList(requestBody);
            logger.info(list);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getVersusDetail() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            logger.info(requestBody);
            const list = await ctx.service.werewolf.groupVersus.getVersusDetail(requestBody);
            logger.info(list);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }
}
