import BaseMegaController from "./BaseMegaController";
import {HttpErr, IerrorMsg} from "../../model/common";

export default class UserMoodController extends BaseMegaController {
    public async getList() {
        const {ctx, logger, app} = this;
        // 校验规则
        const rule = {};
        try {
            // 校验
            ctx.validate(rule);
            const responseBody = await ctx.service.werewolf.userMood.getList(ctx.request.body);
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = {err_code: HttpErr.BadRequest, err_msg: '参数有误'};
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async update() {
        const {ctx, logger, app} = this;
        // 校验规则
        const rule = {};
        try {
            // 校验
            ctx.validate(rule);
            const responseBody = await ctx.service.werewolf.userMood.update(ctx.request.body);
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = {err_code: HttpErr.BadRequest, err_msg: '参数有误'};
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
}