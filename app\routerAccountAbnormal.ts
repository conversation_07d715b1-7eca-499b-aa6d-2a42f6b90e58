/*
 * @Description: 违规刷分
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: 赵宝强
 * @Date: 2020-11-07
 * @LastEditors: 赵宝强
 * @LastEditTime:
 */
import {Application} from "egg";
import {AccessRouteId} from './model/accessRouteCof';

const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {
    const { controller, router } = app;
    router.post(`${API_VERSION}/werewolf/accountAbnormal/getCheatGameSts`,accCtr(AccessRouteId.wolf_group_abnormal),controller.werewolf.accountAbnormal.getCheatGameSts);

}

export default load
