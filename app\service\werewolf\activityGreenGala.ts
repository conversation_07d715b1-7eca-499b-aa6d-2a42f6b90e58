import BaseMegaService from './BaseMegaService'

export default class ActivityGreenGalaService extends BaseMegaService {

    public async findTeamConfig(params) {
        const {logger} = this
        try {
            const sql = `SELECT *
                         FROM activity2024_footballcup_team_config`
            const results = await this.app.mysql.get('werewolf').query(sql)
            return results
        } catch (err) {
            logger.error(err)
            throw err
        }
    }


    public async findItems(params) {
        const {logger} = this
        try {
            if (params.date) {
                const sql = `SELECT a.*, b.name name_a, c.name name_b, b.badge badge_a, c.badge badge_b
                             FROM activity2024_footballcup_game_config a
                                      LEFT JOIN activity2024_footballcup_team_config b ON a.team_id_a = b.id
                                      LEFT JOIN activity2024_footballcup_team_config c ON a.team_id_b = c.id
                             WHERE a.columnId = ? AND DATE (a.game_time) =?
                             ORDER BY a.game_time ASC`

                const results = await this.app.mysql.get('werewolf').query(sql, [params.columnId, params.date])
                return results
            } else {
                const sql = `SELECT a.*, b.name name_a, c.name name_b, b.badge badge_a, c.badge badge_b
                             FROM activity2024_footballcup_game_config a
                                      LEFT JOIN activity2024_footballcup_team_config b ON a.team_id_a = b.id
                                      LEFT JOIN activity2024_footballcup_team_config c ON a.team_id_b = c.id
                             WHERE a.columnId = ?
                             ORDER BY a.game_time ASC`

                const results = await this.app.mysql.get('werewolf').query(sql, [params.columnId])
                return results
            }
        } catch (err) {
            logger.error(err)
            throw err
        }
    }

    public async updateItem(params) {
        const {logger} = this
        const db = this.app.mysql.get('werewolf')
        const transaction = await db.beginTransaction()

        try {
            const gameSql = `UPDATE activity2024_footballcup_game_config
                             SET score_a   = ?,
                                 score_b   = ?,
                                 team_id_a=?,
                                 team_id_b=?,
                                 game_state=?
                             WHERE mid = ?`

            const results = await transaction.query(gameSql, [params.score_a, params.score_b, params.team_id_a, params.team_id_b, params.game_state, params.mid])

            await transaction.commit()

            return results
        } catch (err) {
            await transaction.rollback()

            logger.error(err)
            throw err
        }
    }

    public async updateResultState(params) {
        const {logger} = this
        const db = this.app.mysql.get('werewolf')
        const transaction = await db.beginTransaction()

        try {

            const records = await transaction.query(`SELECT *
                                                     FROM activity2024_footballcup_game_config
                                                     WHERE id = ? LIMIT 1`, [params.id])
            const record = records[0]
            let result = null
            if (record.score_a > record.score_b) {
                result = 0
            } else if (record.score_a < record.score_b) {
                result = 1
            } else if (record.score_a == record.score_b) {
                result = 2
            }

            const gameSql = `UPDATE activity2024_footballcup_game_config
                             SET result_state = ?,
                                 result=?
                             WHERE id = ?`

            const results = await transaction.query(gameSql, [params.result_state, result, params.id])

            await transaction.commit()

            return results
        } catch (err) {
            await transaction.rollback()

            logger.error(err)
            throw err
        }
    }

    public async findDateList(params) {
        const {logger} = this
        try {
            const sql = `SELECT DATE_FORMAT(game_time, '%Y-%m-%d') start_date, COUNT(id) num
                         FROM activity2024_footballcup_game_config
                         WHERE columnId = ?
                         GROUP BY start_date
                         ORDER BY start_date ASC`
            const results = await this.app.mysql.get('werewolf').query(sql, params.columnId)
            return results
        } catch (err) {
            logger.error(err)
            throw err
        }
    }
}
