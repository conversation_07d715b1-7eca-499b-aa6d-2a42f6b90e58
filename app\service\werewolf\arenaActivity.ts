/*
 * @Author: your name
 * @Date: 2021-07-29 13:54:11
 * @LastEditTime: 2021-08-02 13:27:24
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /MGKFHTServer/app/service/werewolf/arenaActivity.ts
 */
import { Service } from 'egg';
import { IarenaUserInfo , IarenaActivityList ,IarenaUserInfoReq ,IarenaResp} from '../../model/wfArenaActivity';

 export default class ArenaActivityService extends Service {
   
    public async getArenaActivityList(req: IarenaUserInfoReq): Promise<IarenaResp> {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        const user_no = db.escape(req.user_id);
        try {
            // 查询 个人用户信息 和 竞技场对战记录
            let sql = `SELECT
            a.user_id,
            b.nickname,
            a.score,
            a.win,
            a.lose,
            a.mvp,
            a.current_level
           FROM
            user_arena_score a
           LEFT JOIN tuser b ON a.user_id = b.\`no\`
           WHERE
            a.user_id = ${user_no}
            `;   
            const resultInfo = await db.query(sql);

            sql = `SELECT
            CONCAT(
             'https://datacenter.53site.com/Werewolf/img/rolecards/card_',
             tugr.role_no,
             '.png'
            ) AS role_img,
            tugr.role_no,
            tugr.win,
            tugr.\`escape\`,
            tugr.valid,
            uasc.score,
            tg.starttime AS create_time
           FROM
            tusergamerecord AS tugr,
            tgamerecord AS tg,
            user_arena_score_record AS uasc
           WHERE
            tugr.game_no = uasc.game_id AND tugr.game_no = tg.\`no\`
           AND tugr.user_no = ${user_no}
           AND uasc.user_id = ${user_no}
            `;
            const arenaList = await db.query(sql);

            return { info: resultInfo, list: arenaList }
        } catch (error) {
            throw error;
        }
    }
 }