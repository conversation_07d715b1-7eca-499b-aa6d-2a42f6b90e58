/*
 * @Description: 钻石
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2018-11-26 16:30:09
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2020-06-19 14:23:02
 */
import { Service } from 'egg';
import { IuserModel, IloginResponse } from '../../model/manager';
import { HttpErr, IerrorMsg } from '../../model/common';
import { IdiamodIncRequest, IdiamodDecRequest, IdiamodIncResponse, IdiamodDecResponse } from './../../model/werewolf';
import * as moment from 'moment';
import { I2WTransacRecord } from '../../model/wfDaimond';
import BaseMegaService from './BaseMegaService';

export default class DiamondService extends BaseMegaService {
	/**
     * @name: 当前钻石数
     * @msg: 
     * @param {type} 
     * @return: 
     */
	public async count(playerId: number) {
		const { app } = this;
		const werewolf = app.mysql.get('werewolf');
		const results = await werewolf.select('tuser_props', {
			where: { userno: playerId },
			columns: ['accountnum'],
			limit: 1
		});

		if (!!results && results.length > 0) {
			//console.info("results[0]", results[0]);
			return results[0];
		} else {
			return undefined;
		}
	}

	/**
     * @name: 增加钻石
     * @msg: 
     * @param {type} 
     * @return: 
     */
	public async increase(request: IdiamodIncRequest): Promise<IdiamodIncResponse> {
		const { app, ctx } = this;
		//当前钻石数
		const count = await this.count(request.playerId);
		if (count === undefined) {
			throw new Error(`playerId：${request.playerId}在tuser_props表不存在`);
		}
		const oldCount: number = count.accountnum;
		const newCount: number = request.increase + oldCount;
		//todo 事物
		// const werewolf = app.mysql.get('werewolf');
		const manager = app.mysql.get('manager');

		// const werewolfConn = await werewolf.beginTransaction();
		const managerConn = await manager.beginTransaction();
		try {
			const itemDic = {
				itemDicId: 1506,
				num: request.increase
			}
			const userList = {
				userId: request.playerId,
				itemDicList: [itemDic]
			}
			let jsonStr = JSON.stringify({
				type: 0,
				userList: [userList]
			});
			let res = await ctx.curl(app.config.WerewolfJPExchange + 'item/obtain', {
				method: 'POST',
				headers: {
					"content-type": "application/json",
				},
				data: jsonStr,
				timeout: 3000, // 3 秒超时
			});

			if (!!res && res.status == 200) {
				//写入数据库事物
				await managerConn.insert('wf_grant_diamond', {
					admin_id: request.uid,
					user_id: request.playerId,
					type: 1, //1增加钻石
					num: request.increase,
					comment: request.comment,
					createtime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
				});
				// await werewolfConn.commit();
				await managerConn.commit();
				return { diamond: newCount };
			} else {
				await managerConn.rollback();
				return { diamond: count.accountnum };
			}

			//更新钻石事物
			// const wereRow = {
			// 	accountnum: newCount //todo 判断绝对值
			// };
			// const wereOptions = {
			// 	where: {
			// 		userno: request.playerId
			// 	}
			// };
			// await werewolfConn.update('tuser_props', wereRow, wereOptions);

		} catch (err) {
			// await werewolfConn.rollback();
			await managerConn.rollback();
			throw err;
		}
	}

	/**
     * @name: 减少钻石
     * @msg: 
     * @param {type} 
     * @return: 
     */
	public async decrease(request: IdiamodDecRequest): Promise<IdiamodDecResponse> {
		const { app, ctx } = this;
		//当前钻石数
		const count = await this.count(request.playerId);
		if (count === undefined) {
			throw new Error(`playerId：${request.playerId}在tuser_props表不存在`);
		}
		const oldCount: number = count.accountnum;
		let newCount: number = oldCount - request.decrease;
		if (newCount < 0) newCount = 0;
		//todo 事物
		// const werewolf = app.mysql.get('werewolf');
		const manager = app.mysql.get('manager');

		// const werewolfConn = await werewolf.beginTransaction();
		const managerConn = await manager.beginTransaction();
		try {
			const itemDic = {
				itemDicId: 1506,
				num: request.decrease
			}
			const userList = {
				userId: request.playerId,
				itemDicList: [itemDic]
			}
			let jsonStr = JSON.stringify({
				type: 1,
				userList: [userList]
			});
			let res = await ctx.curl(app.config.WerewolfJPExchange + 'item/obtain', {
				method: 'POST',
				headers: {
					"content-type": "application/json",
				},
				data: jsonStr,
				timeout: 3000, // 3 秒超时
			});

			if (!!res && res.status == 200) {
				//写入数据库事物
				await managerConn.insert('wf_deduct_diamond', {
					admin_id: request.uid,
					user_id: request.playerId,
					type: 0, //0减少钻石
					num: request.decrease,
					comment: request.comment,
					createtime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
				});
				// await werewolfConn.commit();
				await managerConn.commit();
				return { diamond: newCount };
			} else {
				await managerConn.rollback();
				return { diamond: count.accountnum };
			}
			//更新钻石事物
			// const wereRow = {
			// 	accountnum: newCount //todo 判断绝对值
			// };
			// const wereOptions = {
			// 	where: {
			// 		userno: request.playerId
			// 	}
			// };
			// await werewolfConn.update('tuser_props', wereRow, wereOptions);

		} catch (err) {
			// await werewolfConn.rollback();
			await managerConn.rollback();
			throw err;
		}
	}

	/**
  * @description: 插入2w充值记录
  * @param {type} 
  * @return: 
  */
	public async insertTwoWTransRecord(req: I2WTransacRecord) {
		const { app, logger, ctx } = this;
		try {
			const sqlStr = `INSERT INTO wf_2w_transac_record(admin_id,user_id,comment,type)
			VALUES(?,?,?,?)`;
			await this.execSql(sqlStr, [req.admin_id, req.user_id, req.comment, req.type], "manager");
		} catch (err) {
			logger.error("插入充值2w记录失败");
		}
	}
}
