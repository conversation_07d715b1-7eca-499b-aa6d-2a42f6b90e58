/*
 * @Description: 小号黑名单
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: 赵宝强
 * @Date: 2020-11-09
 * @LastEditors: 赵宝强
 * @LastEditTime:
 */
import {Application} from "egg";
import {AccessRouteId} from './model/accessRouteCof';

const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

    const { controller, router } = app;
    //【用户违规】
    //小号黑名单查询
    router.post(`${API_VERSION}/werewolf/accountBlacklist/getLittleBlacklist`,accCtr(AccessRouteId.wolf_group_blacklist),controller.werewolf.accountBlacklist.getLittleBlacklist);

    //删除小号黑名单
    router.post(`${API_VERSION}/werewolf/accountBlacklist/delLittleBlacklist`, accCtr(AccessRouteId.wolf_group_blacklist), controller.werewolf.accountBlacklist.delLittleBlacklist);
}

export default load
