/*
 * @Description: 资产详细记录查询service
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: leeou
 * @Date: 2019-01-14 14:01:47
 * @LastEditors: jiawen.wang
 * @LastEditTime: 2022-02-15 17:38:01
 */

import * as moment from 'moment'
import {
    ItreasureRequest,
    Itransaction,
    ItransactionResponse,
    IpropsBuy,
    IpropsBuyResponse,
    IpropsPay,
    IpropsPayResponse,
    IgroupBuy,
    IgroupBuyResponse,
    IgroupPay,
    IgroupPayResponse,
    IcpBuy,
    IcpBuyResponse,
    IcpPay,
    IcpPayResponse,
    IcpGive,
    IcpGiveResponse,
    IboxOpen,
    IboxOpenReponse,
    IudidDetailsRequest,
    IudidDetailsResponse,
    IgiftGive,
    IgiftGiveResponse,
    IgiftReceive,
    IgiftReceiveResponse,
    IboxGiveReponse,
    IboxGive,
    IuserFrameResponse,
    IupdateUserFrameDelsignRequest,
    IupdateUserFrameDelsignResponse,
    IuploadLetteringRequest,
    IuploadLetteringResponse,
    IupdateUserLetteringRequest,
    IuserAchieveRequest,
    IuserAchieveResponse,
    IuserAchieveUpdateDelsignRequest,
    IuserAchieveUpdateDelsignResponse,
    IshutterRequest,
    ItreasureRedbagResponse,
    ItreasureBGAuthorityResponse,
    ItreasureSpecialEffectBuyResponse,
    ItreasureSpecialEffectGiveResponse,
    ItreasureSpecialEffectReceiveResponse,
    ItreasureCanonRecordResponse,
    ItabnormalDescItem,
    ImallBuyRecord,
    ItreasuremallBuyRecordResponse,
    IgiftbagReciveRecordResponse,
    IgiftbagItemReceiveRecordResponse,
    IupdateUserLetteringPHPRequest,
    IupdateUserLetteringPHPRequestName,
    IusePropsRecordResponse,
} from '../../model/werewolf'
import BaseMegaService from './BaseMegaService'

export default class TreasureService extends BaseMegaService {
    /**
     * @name:
     * @msg: 充值记录查询
     * @param {type}
     * @return:
     */
    public async transaction(request: ItreasureRequest): Promise<ItransactionResponse> {
        const { app } = this
        const werewolf = app.mysql.get('werewolf')
        const playerId = werewolf.escape(request.playerId)
        const start = werewolf.escape(request.start)
        const offset = werewolf.escape(request.offset)
        let count = 0
        let sql = 'SELECT COUNT(*) AS total FROM `transaction` WHERE `user` = ' + playerId
        let result = await werewolf.query(sql)
        if (!!result && result.length > 0) {
            count = result[0].total
        }
        sql =
            'SELECT * FROM `transaction` t LEFT JOIN product p ON t.productId = p.`name` WHERE t.`user` = ' +
            playerId +
            ' ORDER BY t.id DESC LIMIT ' +
            start +
            ',' +
            offset
        result = await werewolf.query(sql)
        let array: Itransaction[] = new Array()
        if (!!result && result.length > 0) {
            let udid = ''
            for (let i = 0, len = result.length; i < len; i++) {
                let platform = '苹果内购'
                if (result[i].productId.indexOf('taobao') != -1) {
                    platform = '支付宝'
                } else if (result[i].productId.indexOf('wechat') != -1) {
                    platform = '微信'
                } else if (result[i].productId.indexOf('shunwang') != -1) {
                    platform = '顺网'
                }
                let udidDetail = 0
                if (udid == '') {
                    udid = result[i].udid
                    if (udid && udid.length > 10) {
                        let detailSql =
                            "SELECT COUNT(DISTINCT user) AS total FROM `transaction` WHERE `udid` = '" + result[i].udid + "' ORDER BY `id` DESC"
                        let detailResult = await werewolf.query(detailSql)
                        if (!!detailResult && detailResult.length > 0) {
                            udidDetail = detailResult[0].total
                        }
                    }
                } else {
                    if (udid != result[i].udid) {
                        udid = result[i].udid
                        if (result[i].udid.length > 10) {
                            let detailSql =
                                "SELECT COUNT(DISTINCT user) AS total FROM `transaction` WHERE `udid` = '" + result[i].udid + "' ORDER BY `id` DESC"
                            let detailResult = await werewolf.query(detailSql)
                            if (!!detailResult && detailResult.length > 0) {
                                udidDetail = detailResult[0].total
                            }
                        }
                    }
                }
                if (udidDetail == 1) {
                    udidDetail = 0
                }
                array.push({
                    uid: result[i].user,
                    price: result[i].price,
                    platform: platform,
                    num: result[i].currency,
                    identifier: result[i].identifier,
                    udid: result[i].udid,
                    udidDetail: udidDetail,
                    bvrs: result[i].bvrs,
                    datetime: moment(result[i].date).format('YYYY-MM-DD HH:mm:ss'),
                    status: result[i].status_code,
                })
            }
        }
        return {
            ...request,
            count: count,
            dataArray: array,
        }
    }

    /**
     * clickhouse查询钻石变动
     * @param request
     */
    public async tabnormalNew(request) {
        const { app, logger } = this
        const werewolf_coder = app.clickhouse.get('werewolf_coder')
        const werewolf = app.mysql.get('werewolf')
        let sql = `SELECT createTime,channelName,itemDicId,num,inOut,'钻石' AS coinName,currencyInOut,currencyNum,tableName,dataId
                FROM item_flow_wide_table
                WHERE userId = ${request.playerId}
                AND (currencyItemDicId = 0 OR currencyItemDicId = 1506)
                AND toDateTime(createTime) > subtractDays(today(), 7)
                ORDER BY createTime DESC`
        try {
            const playerId = await werewolf_coder.querying(sql)
            let itemDicIdList: any[] = []
            sql = 'SELECT id,name FROM item_dic'
            let result = await werewolf.query(sql)
            for (let data of result) {
                itemDicIdList[data.id] = data.name
            }
            for (let data of playerId.data) {
                data.itemName = itemDicIdList[data.itemDicId]
            }
            return playerId.data
        } catch (e) {
            throw e
        }
    }

    /**
     * @name: 查询
     * @msg:
     * @param {type}
     * @return:
     */
    public async tabnormalV2(request) {
        const { app } = this
        const werewolf = app.mysql.get('werewolf')
        const playerId = werewolf.escape(request.playerId)
        try {
            let flowSql = `SELECT
      user_no,
      date,
      mysql_user,
      accountnum_new,
      accountnum_old
    FROM
      tabnormal_2
    WHERE
    user_no = ${playerId}
    AND	type = 0
    AND date > DATE_SUB(CURDATE(), INTERVAL 7 DAY)
    ORDER BY date DESC;`
            let flowResult = await werewolf.query(flowSql)
            return flowResult
        } catch (e) {
            throw e
        }
    }

    public async udidDetails(request: IudidDetailsRequest): Promise<IudidDetailsResponse> {
        const { app } = this
        const werewolf = app.mysql.get('werewolf')
        const udid = werewolf.escape(request.udid)
        const start = werewolf.escape(request.start)
        const offset = werewolf.escape(request.offset)
        let count = 0
        let sql = 'SELECT COUNT(DISTINCT user) FROM `transaction` WHERE `udid` = ' + udid + ' ORDER BY `id` DESC'
        let result = await werewolf.query(sql)
        if (!!result && result.length > 0) {
            count = result[0].total
        }
        sql =
            'SELECT * FROM `transaction` t LEFT JOIN product p ON t.productId = p.`name` WHERE t.`udid` = ' +
            udid +
            ' GROUP BY t.`user` ORDER BY t.id DESC LIMIT ' +
            start +
            ',' +
            offset
        result = await werewolf.query(sql)
        let array: Itransaction[] = new Array()
        if (!!result && result.length > 0) {
            for (let i = 0, len = result.length; i < len; i++) {
                let platform = '苹果内购'
                if (result[i].productId.indexOf('taobao') != -1) {
                    platform = '支付宝'
                } else if (result[i].productId.indexOf('wechat') != -1) {
                    platform = '微信'
                } else if (result[i].productId.indexOf('shunwang') != -1) {
                    platform = '顺网'
                }
                array.push({
                    uid: result[i].user,
                    price: result[i].price,
                    platform: platform,
                    num: result[i].currency,
                    identifier: result[i].identifier,
                    udid: result[i].udid,
                    udidDetail: 0,
                    bvrs: result[i].bvrs,
                    datetime: moment(result[i].date).format('YYYY-MM-DD HH:mm:ss'),
                    status: result[i].status_code,
                })
            }
        }
        return {
            ...request,
            count: count,
            dataArray: array,
        }
    }

    /**
     * @name:
     * @msg: 道具商城购买
     * @param {type}
     * @return:
     */
    public async propsBuy(request: ItreasureRequest): Promise<IpropsBuyResponse> {
        const { app } = this
        const werewolf = app.mysql.get('werewolf')
        const playerId = werewolf.escape(request.playerId)
        const start = werewolf.escape(request.start)
        const offset = werewolf.escape(request.offset)
        let count = 0
        let sql = 'SELECT COUNT(*) AS total FROM tuser_props_buy WHERE userno = ' + playerId + ' ORDER BY `no` DESC'
        let result = await werewolf.query(sql)
        if (!!result && result.length > 0) {
            count = result[0].total
        }
        sql =
            'SELECT tpb.*,t.`name`,t.props_explain FROM tuser_props_buy tpb LEFT JOIN tprops t ON tpb.propsno = t.`no` WHERE tpb.userno = ' +
            playerId +
            ' ORDER BY tpb.`no` DESC LIMIT ' +
            start +
            ',' +
            offset
        result = await werewolf.query(sql)
        let array: IpropsBuy[] = new Array()
        if (!!result && result.length > 0) {
            for (let i = 0, len = result.length; i < len; i++) {
                array.push({
                    name: result[i].name,
                    desc: result[i].props_explain,
                    price: result[i].buyprice,
                    num: result[i].buynum,
                    datetime: moment(result[i].buytime).format('YYYY-MM-DD HH:mm:ss'),
                })
            }
        }
        return {
            ...request,
            count: count,
            dataArray: array,
        }
    }

    /**
     * @name:
     * @msg: 道具商城普通道具使用
     * @param {type}
     * @return:
     */
    public async propsPay(request: ItreasureRequest): Promise<IpropsPayResponse> {
        const { app } = this
        const werewolf = app.mysql.get('werewolf')
        const playerId = werewolf.escape(request.playerId)
        const start = werewolf.escape(request.start)
        const offset = werewolf.escape(request.offset)
        let count = 0
        let sql =
            'SELECT COUNT(*) AS total FROM (SELECT propsno, paytime FROM tuser_props_pay WHERE userno = ' +
            playerId +
            ' UNION ALL SELECT propsno, paytime FROM tuser_props_game WHERE userno = ' +
            playerId +
            ' ORDER BY paytime DESC) tpp'
        let result = await werewolf.query(sql)
        if (!!result && result.length > 0) {
            count = result[0].total
        }
        sql =
            'SELECT tpp.*,t.`name`,t.props_explain FROM (SELECT propsno, paytime FROM tuser_props_pay WHERE userno = ' +
            playerId +
            ' UNION ALL SELECT propsno, paytime FROM tuser_props_game WHERE userno = ' +
            playerId +
            ' ORDER BY paytime DESC) tpp LEFT JOIN tprops t ON tpp.propsno = t.`no` ORDER BY tpp.paytime DESC LIMIT ' +
            start +
            ',' +
            offset
        result = await werewolf.query(sql)
        let array: IpropsPay[] = new Array()
        if (!!result && result.length > 0) {
            for (let i = 0, len = result.length; i < len; i++) {
                array.push({
                    name: result[i].name,
                    desc: result[i].props_explain,
                    num: 1,
                    datetime: moment(result[i].paytime).format('YYYY-MM-DD HH:mm:ss'),
                })
            }
        }
        return {
            ...request,
            count: count,
            dataArray: array,
        }
    }

    /**
     * @name:
     * @msg: 公会商城购买
     * @param {type}
     * @return:
     */
    public async groupBuy(request: ItreasureRequest): Promise<IgroupBuyResponse> {
        const { app } = this
        const werewolf = app.mysql.get('werewolf')
        const playerId = werewolf.escape(request.playerId)
        const start = werewolf.escape(request.start)
        const offset = werewolf.escape(request.offset)
        let count = 0
        let sql =
            'SELECT COUNT(*) AS total FROM group_props_buy gpb LEFT JOIN group_props gp ON gpb.group_props_id = gp.id WHERE gpb.user_id = ' +
            playerId +
            ' ORDER BY gpb.id DESC'
        let result = await werewolf.query(sql)
        if (!!result && result.length > 0) {
            count = result[0].total
        }
        sql =
            'SELECT gpb.*,gp.`name`,gp.`desc` FROM group_props_buy gpb LEFT JOIN group_props gp ON gpb.group_props_id = gp.id WHERE gpb.user_id = ' +
            playerId +
            ' ORDER BY gpb.id DESC LIMIT ' +
            start +
            ',' +
            offset
        result = await werewolf.query(sql)
        let array: IgroupBuy[] = new Array()
        if (!!result && result.length > 0) {
            for (let i = 0, len = result.length; i < len; i++) {
                array.push({
                    groupPropsName: result[i].name,
                    desc: result[i].desc,
                    diamondNum: result[i].price,
                    coinNum: result[i].active_coin,
                    num: result[i].buy_num,
                    datetime: moment(result[i].buy_time).format('YYYY-MM-DD HH:mm:ss'),
                })
            }
        }
        return {
            ...request,
            count: count,
            dataArray: array,
        }
    }

    /**
     * @name:
     * @msg: 公会商城使用
     * @param {type}
     * @return:
     */
    public async groupPay(request: ItreasureRequest): Promise<IgroupPayResponse> {
        const { app } = this
        const werewolf = app.mysql.get('werewolf')
        const playerId = werewolf.escape(request.playerId)
        const start = werewolf.escape(request.start)
        const offset = werewolf.escape(request.offset)
        let count = 0
        let sql =
            'SELECT COUNT(*) AS total FROM group_props_pay gpp LEFT JOIN group_props gp ON gpp.group_props_id = gp.id WHERE gpp.user_id = ' +
            playerId +
            ' ORDER BY gpp.id DESC'
        let result = await werewolf.query(sql)
        if (!!result && result.length > 0) {
            count = result[0].total
        }
        sql =
            'SELECT gpp.*,gp.`name`,gp.`desc` FROM group_props_pay gpp LEFT JOIN group_props gp ON gpp.group_props_id = gp.id WHERE gpp.user_id = ' +
            playerId +
            ' ORDER BY gpp.id DESC LIMIT ' +
            start +
            ',' +
            offset
        result = await werewolf.query(sql)
        let array: IgroupPay[] = new Array()
        if (!!result && result.length > 0) {
            for (let i = 0, len = result.length; i < len; i++) {
                array.push({
                    groupPropsName: result[i].name,
                    desc: result[i].desc,
                    num: result[i].pay_num,
                    datetime: moment(result[i].buy_time).format('YYYY-MM-DD HH:mm:ss'),
                })
            }
        }
        return {
            ...request,
            count: count,
            dataArray: array,
        }
    }

    /**
     * @name:
     * @msg: CP商城购买
     * @param {type}
     * @return:
     */
    public async cpBuy(request: ItreasureRequest): Promise<IcpBuyResponse> {
        const { app } = this
        const werewolf = app.mysql.get('werewolf')
        const playerId = werewolf.escape(request.playerId)
        const start = werewolf.escape(request.start)
        const offset = werewolf.escape(request.offset)
        let count = 0
        let sql =
            'SELECT COUNT(*) AS total FROM cp_buy cb LEFT JOIN cp_props cp ON cb.cp_props_no = cp.id WHERE cb.user_id = ' +
            playerId +
            ' ORDER BY cb.id DESC'
        let result = await werewolf.query(sql)
        if (!!result && result.length > 0) {
            count = result[0].total
        }
        sql =
            'SELECT cb.*,cp.`name`,cp.`desc` FROM cp_buy cb LEFT JOIN cp_props cp ON cb.cp_props_no = cp.id WHERE cb.user_id = ' +
            playerId +
            ' ORDER BY cb.id DESC LIMIT ' +
            start +
            ',' +
            offset
        result = await werewolf.query(sql)
        let array: IcpBuy[] = new Array()
        if (!!result && result.length > 0) {
            for (let i = 0, len = result.length; i < len; i++) {
                array.push({
                    propsName: result[i].name,
                    desc: result[i].desc,
                    addCharm: result[i].add_in,
                    price: result[i].price,
                    type: result[i].type,
                    num: result[i].num,
                    datetime: moment(result[i].createtime).format('YYYY-MM-DD HH:mm:ss'),
                })
            }
        }
        return {
            ...request,
            count: count,
            dataArray: array,
        }
    }

    /**
     * @name:
     * @msg: CP商城使用
     * @param {type}
     * @return:
     */
    public async cpPay(request: ItreasureRequest): Promise<IcpPayResponse> {
        const { app } = this
        const werewolf = app.mysql.get('werewolf')
        const playerId = werewolf.escape(request.playerId)
        const start = werewolf.escape(request.start)
        const offset = werewolf.escape(request.offset)
        let count = 0
        let sql =
            'SELECT COUNT(*) AS total FROM cp_give cg LEFT JOIN cp_props cp ON cg.cp_props_no = cp.id WHERE cg.user_id = ' +
            playerId +
            ' ORDER BY cg.id DESC'
        let result = await werewolf.query(sql)
        if (!!result && result.length > 0) {
            count = result[0].total
        }
        sql =
            'SELECT cg.*,cp.`name`,cp.`desc` FROM cp_give cg LEFT JOIN cp_props cp ON cg.cp_props_no = cp.id WHERE cg.user_id = ' +
            playerId +
            ' ORDER BY cg.id DESC LIMIT ' +
            start +
            ',' +
            offset
        result = await werewolf.query(sql)
        let array: IcpPay[] = new Array()
        if (!!result && result.length > 0) {
            for (let i = 0, len = result.length; i < len; i++) {
                array.push({
                    propsName: result[i].name,
                    desc: result[i].desc,
                    cpId: result[i].re_user_id,
                    addCharm: result[i].add_in,
                    price: result[i].price,
                    type: result[i].type,
                    num: result[i].num,
                    datetime: moment(result[i].createtime).format('YYYY-MM-DD HH:mm:ss'),
                })
            }
        }
        return {
            ...request,
            count: count,
            dataArray: array,
        }
    }

    /**
     * @name:
     * @msg: CP游戏内使用
     * @param {type}
     * @return:
     */
    public async cpGive(request: ItreasureRequest): Promise<IcpGiveResponse> {
        const { app } = this
        const werewolf = app.mysql.get('werewolf')
        const playerId = werewolf.escape(request.playerId)
        const start = werewolf.escape(request.start)
        const offset = werewolf.escape(request.offset)
        let count = 0
        let sql =
            'SELECT COUNT(*) AS total FROM cp_give_gift cgg LEFT JOIN cp_props cp ON cgg.cp_props_no = cp.props_no WHERE cgg.user_id = ' +
            playerId +
            ' ORDER BY cgg.id DESC'
        let result = await werewolf.query(sql)
        if (!!result && result.length > 0) {
            count = result[0].total
        }
        sql =
            'SELECT cgg.*,cp.`name`,cp.`desc` FROM cp_give_gift cgg LEFT JOIN cp_props cp ON cgg.cp_props_no = cp.props_no WHERE cgg.user_id = ' +
            playerId +
            ' ORDER BY cgg.id DESC LIMIT ' +
            start +
            ',' +
            offset
        result = await werewolf.query(sql)
        let array: IcpGive[] = new Array()
        if (!!result && result.length > 0) {
            for (let i = 0, len = result.length; i < len; i++) {
                array.push({
                    propsName: result[i].name,
                    desc: result[i].desc,
                    cpId: result[i].re_user_id,
                    addCharm: result[i].add_charm,
                    addDiamond: result[i].add_diamond,
                    price: result[i].price,
                    type: result[i].type,
                    num: result[i].num,
                    datetime: moment(result[i].createtime).format('YYYY-MM-DD HH:mm:ss'),
                })
            }
        }
        return {
            ...request,
            count: count,
            dataArray: array,
        }
    }

    /**
     * @name:
     * @msg: 开宝箱
     * @param {type}
     * @return:
     */
    public async boxOpen(request: ItreasureRequest): Promise<IboxOpenReponse> {
        const { app } = this
        const werewolf = app.mysql.get('werewolf')
        const playerId = werewolf.escape(request.playerId)
        const start = werewolf.escape(request.start)
        const offset = werewolf.escape(request.offset)
        let count = 0
        //1 查询总数
        let result = await this.selectOne(
            `SELECT COUNT(*) as total FROM item_consume_lottery_box_user_record_history WHERE user_id = ? AND delsign = 0;
    `,
            [request.playerId],
        )
        if (result && result.total > 0) {
            count = result.total
        }
        //2 查询分页
        let sql = `
    SELECT a.*,b.name as coin_name
    ,c.name as box_name
    ,c.type as box_type
    FROM 
    item_consume_lottery_box_user_record_history as a
    LEFT JOIN lottery_activity as c on a.lottery_activity_id = c.id
		LEFT JOIN item_dic as b on a.item_dic_id = b.id
    WHERE a.user_id = ?
    AND a.delsign = 0
    AND a.lottery_activity_id = c.id
    ORDER BY a.create_time DESC LIMIT ?,?;
    `
        result = await this.selectList(sql, [request.playerId, request.start, request.offset])
        let array: IboxOpen[] = new Array()
        //3 封装返回信息
        if (!!result && result.length > 0) {
            for (let i = 0, len = result.length; i < len; i++) {
                let awardNames = await this.selectOne(`SELECT GROUP_CONCAT(b.name) as award_names FROM lottery_box_user_record as a,
        lottery_box as b
        WHERE a.id in (${result[i].lottery_box_user_record_id_list})
        AND a.box_id = b.id;;`)
                array.push({
                    awardNames: awardNames.award_names,
                    boxName: result[i].box_name,
                    boxType: result[i].box_type,
                    // gainType: result[i].gain_type,
                    openType: result[i].open_type,
                    coinName: result[i].coin_name,
                    // gainCoin: result[i].gain_coin,
                    // coin: result[i].coin,
                    price: result[i].coin_num,
                    // num: result[i].coin_num,
                    orderNo: result[i].id,
                    datetime: moment(result[i].create_time).format('YYYY-MM-DD HH:mm:ss'),
                })
            }
        }

        return {
            ...request,
            count: count,
            dataArray: array,
        }
    }

    /**
     * @name:
     * @msg: 宝箱赠送
     * @param {type}
     * @return:
     */
    public async boxGive(request: ItreasureRequest): Promise<IboxGiveReponse> {
        const { app } = this
        const werewolf = app.mysql.get('werewolf')
        const playerId = werewolf.escape(request.playerId)
        const start = werewolf.escape(request.start)
        const offset = werewolf.escape(request.offset)
        let count = 0
        let sql =
            'SELECT tbr.*,tbg.recieve_no AS receiveNo,t.`name`,t.`desc` FROM tuser_box_record tbr LEFT JOIN tuser_box_give tbg ON tbr.`no` = tbg.box_record_no LEFT JOIN tbox t ON tbr.tbox_no = t.`no` WHERE tbg.user_no = ' +
            playerId +
            ' ORDER BY tbr.`no` DESC'
        let result = await werewolf.query(sql)
        if (!!result && result.length > 0) {
            count = result[0].total
        }
        sql =
            'SELECT tbr.*,tbg.recieve_no AS receiveNo,t.`name`,t.`desc` FROM tuser_box_record tbr LEFT JOIN tuser_box_give tbg ON tbr.`no` = tbg.box_record_no LEFT JOIN tbox t ON tbr.tbox_no = t.`no` WHERE tbg.user_no = ' +
            playerId +
            ' ORDER BY tbr.`no` DESC LIMIT ' +
            start +
            ',' +
            offset
        result = await werewolf.query(sql)
        let array: IboxGive[] = new Array()
        if (!!result && result.length > 0) {
            for (let i = 0, len = result.length; i < len; i++) {
                array.push({
                    name: result[i].name,
                    receiveNo: result[i].receiveNo,
                    coin: result[i].coin,
                    price: result[i].price,
                    num: result[i].num,
                    datetime: moment(result[i].datetime).format('YYYY-MM-DD HH:mm:ss'),
                })
            }
        }
        return {
            ...request,
            count: count,
            dataArray: array,
        }
    }

    public async giftGive(request: ItreasureRequest): Promise<IgiftGiveResponse> {
        const { app } = this
        const werewolf = app.mysql.get('werewolf')
        const playerId = werewolf.escape(request.playerId)
        const start = werewolf.escape(request.start)
        const offset = werewolf.escape(request.offset)
        let count = 0
        let sql =
            'SELECT COUNT(*) AS total FROM item_consume_gift_history gg LEFT JOIN item_dic t ON gg.item_dic_id = t.id WHERE gg.user_id = ' +
            playerId +
            ' ORDER BY gg.id DESC'
        let result = await werewolf.query(sql)
        if (!!result && result.length > 0) {
            count = result[0].total
        }
        sql =
            'SELECT gg.*,g.name,g.price,IF (g.props_explain IS NULL OR g.props_explain="",t.remark,g.props_explain) AS props_explain FROM item_consume_gift_history gg LEFT JOIN item_dic t ON gg.item_dic_id=t.id LEFT JOIN gift g ON g.`no`=t.item_id AND ((t.item_cate_id> 4000 AND t.item_cate_id< 5000) OR t.item_cate_id=9050) WHERE gg.user_id =  ' +
            playerId +
            ' ORDER BY gg.id DESC LIMIT ' +
            start +
            ',' +
            offset
        result = await werewolf.query(sql)
        let array: IgiftGive[] = new Array()
        if (!!result && result.length > 0) {
            for (let i = 0, len = result.length; i < len; i++) {
                array.push({
                    name: result[i].name,
                    price: result[i].price,
                    desc: result[i].props_explain,
                    receiveId: result[i].to_user_id,
                    addDiamond: result[i].reprice_total,
                    addCharm: result[i].charm_total,
                    num: result[i].num,
                    room_id: result[i].room_id,
                    datetime: moment(result[i].create_time).format('YYYY-MM-DD HH:mm:ss'),
                })
            }
        }
        return {
            ...request,
            count: count,
            dataArray: array,
        }
    }

    public async giftReceive(request: ItreasureRequest): Promise<IgiftReceiveResponse> {
        const { app } = this
        const werewolf = app.mysql.get('werewolf')
        const playerId = werewolf.escape(request.playerId)
        const start = werewolf.escape(request.start)
        const offset = werewolf.escape(request.offset)
        let count = 0
        let sql =
            'SELECT COUNT(*) AS total FROM item_consume_gift_history gg LEFT JOIN item_dic t ON gg.item_dic_id = t.id WHERE gg.to_user_id = ' +
            playerId +
            ' ORDER BY gg.id DESC'
        let result = await werewolf.query(sql)
        if (!!result && result.length > 0) {
            count = result[0].total
        }
        sql =
            'SELECT gg.*,g.name,g.price,IF (g.props_explain IS NULL OR g.props_explain="",t.remark,g.props_explain) AS props_explain FROM item_consume_gift_history gg LEFT JOIN item_dic t ON gg.item_dic_id=t.id LEFT JOIN gift g ON g.`no`=t.item_id AND ((t.item_cate_id> 4000 AND t.item_cate_id< 5000) OR t.item_cate_id=9050) WHERE gg.to_user_id = ' +
            playerId +
            ' ORDER BY gg.id DESC LIMIT ' +
            start +
            ',' +
            offset
        result = await werewolf.query(sql)
        let array: IgiftReceive[] = new Array()
        if (!!result && result.length > 0) {
            for (let i = 0, len = result.length; i < len; i++) {
                array.push({
                    name: result[i].name,
                    price: result[i].price,
                    desc: result[i].props_explain,
                    userId: result[i].user_id,
                    addDiamond: result[i].reprice_total,
                    addCharm: result[i].charm_total,
                    num: result[i].num,
                    room_id: result[i].room_id,
                    datetime: moment(result[i].create_time).format('YYYY-MM-DD HH:mm:ss'),
                })
            }
        }
        return {
            ...request,
            count: count,
            dataArray: array,
        }
    }

    public async getUserFrame(request: ItreasureRequest): Promise<IuserFrameResponse> {
        const { app } = this
        const werewolf = app.mysql.get('werewolf')
        const playerId = werewolf.escape(request.playerId)
        const start = werewolf.escape(request.start)
        const offset = werewolf.escape(request.offset)
        let count = 0
        let sql =
            'SELECT COUNT(*) AS total FROM user_avatarframe ua LEFT JOIN avatarframe a ON ua.avatarframe_id = a.id WHERE ua.user_id = ' + playerId
        let result = await werewolf.query(sql)
        if (!!result && result.length > 0) {
            count = result[0].total
        }
        sql =
            "SELECT a.id, a.`name`, CONCAT('http://" +
            app.config.imgDomain +
            "/Werewolf/Frame/',a.id,IF(ua.delsign = 0,IF(a.is_dynamic = 0,'_player.png','_player.webp')," +
            "IF(a.is_dynamic = 0,'_player_offline.png','_player_offline.webp'))) AS url, CONCAT('http://" +
            app.config.imgDomain +
            "/Werewolf/frame_note/',ua.note_id,'.png') AS noteUrl, a.type, a.channel, a.preview, a.remark, a.price, ua.uid, ua.createtime, a.is_dynamic, ua.delsign " +
            'FROM user_avatarframe ua LEFT JOIN avatarframe a ON ua.avatarframe_id = a.id WHERE ua.user_id = ' +
            playerId +
            ' ORDER BY ua.createtime DESC LIMIT ' +
            start +
            ',' +
            offset
        result = await werewolf.query(sql)
        if (!result) {
            return {
                ...request,
                count: count,
                dataArray: [],
            }
        }
        return {
            ...request,
            count: count,
            dataArray: result,
        }
    }
    public async userFrameSearch(request: ItreasureRequest): Promise<IuserFrameResponse> {
        const { app } = this

        const werewolf = app.mysql.get('werewolf')
        const playerId = werewolf.escape(request.playerId)
        const start = werewolf.escape(request.start)
        const offset = werewolf.escape(request.offset)
        let count = 0
        let startStr = '%'
        let searchWordStr = startStr + request.searchWord + startStr
        let sql = `SELECT COUNT(*) AS total  FROM user_avatarframe ua LEFT JOIN avatarframe a ON ua.avatarframe_id = a.id WHERE ua.user_id = ${playerId} AND name like ?`
        let result = await werewolf.query(sql, [searchWordStr])
        if (!!result && result.length > 0) {
            count = result[0].total
        }

        sql = `SELECT
              a.id, a.name, 
         CONCAT
              ('http://${app.config.imgDomain}/Werewolf/Frame/',a.id,IF(ua.delsign = 0,IF(a.is_dynamic = 0,'_player.png','_player.webp'),
         IF
              (a.is_dynamic = 0,'_player_offline.png','_player_offline.webp'))) AS url, 
         CONCAT
              ('http://${app.config.imgDomain}/Werewolf/frame_note/',ua.note_id,'.png') 
         AS 
              noteUrl, a.type, a.channel, a.preview, a.remark, a.price, ua.uid, ua.createtime, a.is_dynamic, ua.delsign 
         FROM 
              user_avatarframe ua 
         LEFT JOIN 
              avatarframe a ON ua.avatarframe_id = a.id 
         WHERE 
              ua.user_id = ${playerId} AND a.name like ?
         ORDER BY 
              ua.createtime DESC LIMIT ${start} , ${offset}
            `
        result = await werewolf.query(sql, [searchWordStr])

        if (!result) {
            return {
                ...request,
                count: count,
                dataArray: [],
            }
        }
        return {
            ...request,
            count: count,
            dataArray: result,
        }
    }

    /**
     * 操作用户头像框
     * @param request
     */
    public async updateFrameDelsign(request: IupdateUserFrameDelsignRequest): Promise<IupdateUserFrameDelsignResponse> {
        const { app } = this

        const werewolf = app.mysql.get('werewolf')
        const manager = app.mysql.get('manager')
        const playerId = werewolf.escape(request.playerId)
        const avatarId = werewolf.escape(request.avatarId)
        const avatarUid = werewolf.escape(request.avatarUid)
        const werewolfConn = await werewolf.beginTransaction()
        const managerConn = await manager.beginTransaction()

        try {
            let type = 2
            if (request.delsign == 1) {
                type = 3
            }
            // const wereRow = {
            //   delsign: request.delsign
            // };
            // const wereOptions = {
            //   where: {
            //     avatarframe_id: request.avatarId,
            //     user_id: request.playerId,
            //     uid:avatarUid
            //   }
            // };
            let updateSql = `UPDATE user_avatarframe
      SET
       delsign = ${request.delsign}
      WHERE
        user_id = ${request.playerId}
      AND avatarframe_id = ${request.avatarId}
      AND 
        uid = ${avatarUid}
      ;
      `
            await werewolfConn.query(updateSql)

            // await werewolfConn.update("user_avatarframe", wereRow, wereOptions);
            await managerConn.insert('wf_admin_avatar_frame', {
                admin_id: request.uid,
                user_id: request.playerId,
                avatar_frame_id: request.avatarId,
                avatar_frame_name: request.avatarId,
                type: type,
            })

            let sql =
                "SELECT a.id, a.`name`, CONCAT('http://" +
                app.config.imgDomain +
                "/Werewolf/Frame/',a.id,IF(ua.delsign = 0,IF(a.is_dynamic = 0,'_player.png','_player.webp')," +
                "IF(a.is_dynamic = 0,'_player_offline.png','_player_offline.webp'))) AS url, CONCAT('http://" +
                app.config.imgDomain +
                "/Werewolf/frame_note/',ua.note_id,'.png') AS noteUrl, a.type, a.channel, a.preview, a.remark, a.price, ua.uid, ua.createtime, a.is_dynamic, ua.delsign " +
                'FROM user_avatarframe ua LEFT JOIN avatarframe a ON ua.avatarframe_id = a.id WHERE ua.user_id = ' +
                playerId +
                ' AND a.id = ' +
                avatarId +
                ' AND ua.uid = ' +
                avatarUid
            let result = await werewolfConn.query(sql)
            await werewolfConn.commit()
            await managerConn.commit()
            return {
                dataArray: result,
            }
        } catch (error) {
            await werewolfConn.rollback()
            await managerConn.rollback()
            throw error
        }
    }

    /**
     * 添加刻字
     */
    public async uploadLettering(request: IuploadLetteringRequest): Promise<IuploadLetteringResponse> {
        const { app } = this
        const werewolf = app.mysql.get('werewolf')
        let noteId = 0
        try {
            //首先插入avatarframe表
            const noteRows = {
                url: request.name,
            }
            const result = await werewolf.insert('user_avatarframe_note', noteRows)
            noteId = result.insertId
            return {
                id: noteId,
                name: request.name,
            }
        } catch (error) {
            throw error
        }
    }
    /**
     * 取消刻字
     */
    public async delLettering(req) {
        const { app } = this
        const db = app.mysql.get('werewolf')
        const conn = await db.beginTransaction()
        const user_id = db.escape(req.user_id)
        const avatarframe_id = db.escape(req.avatarframe_id)
        const uid = db.escape(req.uid)
        try {
            let sql = `UPDATE 
      user_avatarframe
  SET 
      note_id = 0
  WHERE 
    user_id=${user_id}&&avatarframe_id=${avatarframe_id}&&uid=${uid}`
            await conn.query(sql, [req.id])
            await conn.commit() // 提交事务
        } catch (err) {
            throw err
        }
    }

    /**
     * 发刻字
     */
    public async updateUserLettering(request: IupdateUserLetteringRequest): Promise<boolean> {
        const { app } = this
        const werewolf = app.mysql.get('werewolf')
        try {
            //首先插入avatarframe表
            const wereRow = {
                note_id: request.letteringId,
            }
            const wereOptions = {
                where: {
                    avatarframe_id: request.avatarId,
                    user_id: request.playerId,
                    uid: request.uid,
                },
            }
            await werewolf.update('user_avatarframe', wereRow, wereOptions)

            //todo 查询刻字头像框名字，拼接上传图片的url，user_id,
            return true
        } catch (error) {
            throw error
        }
    }
    /**
     * php 发送
     * @param request
     */
    public async updateUserLetteringPHP(request: IupdateUserLetteringRequest) {
        const { app, ctx } = this
        // const werewolf = app.mysql.get("werewolf");
        try {
            const result: IupdateUserLetteringPHPRequest = {
                name: '',
                userNo: '',
                frameNoteUrl: '',
            }

            const avatarframe_id = request.avatarId
            const letteringId = request.letteringId
            const sql = 'SELECT a.name FROM avatarframe a  WHERE a.id=' + avatarframe_id
            const result2 = await this.selectOne(sql)
            this.logger.info(result2)
            result.name = result2.name

            result.userNo = request.playerId.toString()
            result.frameNoteUrl = `http://img.53site.com/Werewolf/frame_note/${letteringId}.png`

            const curlResp = await ctx.curl(app.config.phpAtyBaseUrl + '/Dingtalk/dtUploadNote.php', {
                method: 'POST',
                contentType: 'application/x-www-form-urlencoded',
                data: { frameName: result.name, userNo: result.userNo, frameNoteUrl: result.frameNoteUrl },
                dataType: 'json',
                timeout: 30000, // 30 秒超时
            })

            const { status, headers, data } = curlResp
            if (status == 200) {
                this.logger.debug('发送php成功', data)
            } else {
                this.logger.error('发送php失败', status, data)
            }
            return true
        } catch (e) {
            this.logger.error('发送php失败', e)
            return false
        }
    }
    //todo 查询刻字头像框名字，拼接上传图片的url，user_id,

    /**
     * 用户成就列表
     * @param request
     */
    public async achieveList(request: IuserAchieveRequest): Promise<IuserAchieveResponse> {
        const { app } = this
        const werewolf = app.mysql.get('werewolf')
        const playerId = werewolf.escape(request.playerId)
        const start = werewolf.escape(request.start)
        const offset = werewolf.escape(request.offset)
        try {
            let count = 0
            let sql =
                'SELECT COUNT(*) AS total FROM user_achievement ua LEFT JOIN achievement a ON ua.achievement_id = a.id WHERE a.mode = 1 AND a.uplevel = 0 AND ua.user_id = ' +
                playerId
            let result = await werewolf.query(sql)
            if (!!result && result.length > 0) {
                count = result[0].total
            }
            sql =
                "SELECT a.id, a.`name`, CONCAT('http://" +
                app.config.imgDomain +
                "/Werewolf/achieveNew/achieve_',id,'.png') AS url," +
                ' a.remark, a.is_complete, ua.delsign ' +
                'FROM user_achievement ua LEFT JOIN achievement a ON ua.achievement_id = a.id WHERE a.mode = 1 AND a.uplevel = 0 AND ua.user_id = ' +
                playerId +
                ' ORDER BY ua.createtime DESC LIMIT ' +
                start +
                ',' +
                offset
            result = await werewolf.query(sql)
            if (!result) {
                return {
                    ...request,
                    count: count,
                    dataArray: [],
                }
            }
            return {
                ...request,
                count: count,
                dataArray: result,
            }
        } catch (error) {
            throw error
        }
    }

    public async updateAchieveDelsign(request: IuserAchieveUpdateDelsignRequest): Promise<IuserAchieveUpdateDelsignResponse> {
        const { app } = this

        const werewolf = app.mysql.get('werewolf')
        const manager = app.mysql.get('manager')
        const playerId = werewolf.escape(request.playerId)
        const achieveId = werewolf.escape(request.achieveId)
        const werewolfConn = await werewolf.beginTransaction()
        const managerConn = await manager.beginTransaction()

        try {
            let type = 4
            if (request.delsign == 1) {
                type = 5
            }
            const wereRow = {
                delsign: request.delsign,
            }
            const wereOptions = {
                where: {
                    achievement_id: request.achieveId,
                    user_id: request.playerId,
                },
            }
            await werewolfConn.update('user_achievement', wereRow, wereOptions)
            await managerConn.insert('wf_admin_achieve', {
                admin_id: request.uid,
                user_id: request.playerId,
                achieve_id: request.achieveId,
                achieve_name: request.achieveId,
                type: type,
            })

            let sql =
                "SELECT a.id, a.`name`, CONCAT('http://" +
                app.config.imgDomain +
                "/Werewolf/achieveNew/achieve_',id,'.png') AS url," +
                ' a.remark, a.is_complete, ua.delsign ' +
                'FROM user_achievement ua LEFT JOIN achievement a ON ua.achievement_id = a.id WHERE ua.user_id = ' +
                playerId +
                ' AND a.id = ' +
                achieveId
            let result = await werewolfConn.query(sql)
            await werewolfConn.commit()
            await managerConn.commit()
            return {
                dataArray: result,
            }
        } catch (error) {
            await werewolfConn.rollback()
            await managerConn.rollback()
            throw error
        }
    }

    public async redbagDetail(request: IshutterRequest): Promise<ItreasureRedbagResponse> {
        const { app } = this
        const werewolf = app.mysql.get('werewolf')
        const playerId = werewolf.escape(request.playerId)
        try {
            let sql = `SELECT grr.group_id, grr.bag_rest_num, grr.bag_rest_size, grr.type, grr.createtime, gr.item_num AS \`bag_num\`, gr.size AS \`bag_size\`, gr.\`name\`,
        gr.\`desc\` FROM  group_redbag_record grr LEFT JOIN group_redbag gr ON grr.redbag_id = gr.id WHERE grr.user_id = ${playerId} AND grr.delsign = 0
        AND DATE_SUB(CURDATE(), INTERVAL 7 DAY) <= grr.createtime ORDER BY createtime DESC `
            let result = await werewolf.query(sql)
            return {
                dataArray: result,
            }
        } catch (error) {
            throw error
        }
    }

    public async tabnormalDescs(): Promise<ItabnormalDescItem[]> {
        const { app } = this
        const werewolf = app.mysql.get('werewolf')

        try {
            let sql = `SELECT * FROM tabnormal_describe`
            let result = await werewolf.query(sql)
            return result
        } catch (error) {
            throw error
        }
    }

    public async backgroundAuthority(request: ItreasureRequest): Promise<ItreasureBGAuthorityResponse> {
        const { app, logger } = this
        const werewolf = app.mysql.get('werewolf')
        const playerId = werewolf.escape(request.playerId)
        const start = werewolf.escape(request.start)
        const offset = werewolf.escape(request.offset)
        try {
            let count = 0
            let sql = `SELECT COUNT(*) AS total FROM type_props_buy WHERE user_id = ${playerId} AND delsign = 0`
            let result = await werewolf.query(sql)
            if (!!result && result.length > 0) {
                count = result[0].total
            }
            let dataSql = `SELECT a.user_id,a.price,a.buytime,b.duration FROM type_props_buy  a LEFT JOIN type_props_item b ON a.item_id = b.id  WHERE user_id = ${playerId} AND a.delsign = 0 ORDER BY a.buytime DESC LIMIT ${start} , ${offset}`
            let dataResult = await werewolf.query(dataSql)
            let timeSql = `SELECT FLOOR(((UNIX_TIMESTAMP(\`endtime\`)-UNIX_TIMESTAMP(NOW()))-60)/60/60/24) AS endtime FROM \`type_props_user\` WHERE \`user_id\` = ${playerId} AND \`props_id\` = 1 `
            let timeResult = await werewolf.query(timeSql)
            let remaintime = 0
            logger.info(timeResult)
            if (timeResult[0]) {
                remaintime = timeResult[0].endtime
            }
            if (remaintime < 0) {
                remaintime = 0
            }
            return {
                ...request,
                count: count,
                remaintime: String(remaintime),
                dataArray: dataResult,
            }
        } catch (error) {
            throw error
        }
    }

    public async specialEffectBuy(request: ItreasureRequest): Promise<ItreasureSpecialEffectBuyResponse> {
        const { app } = this
        const werewolf = app.mysql.get('werewolf')
        const playerId = werewolf.escape(request.playerId)
        const start = werewolf.escape(request.start)
        const offset = werewolf.escape(request.offset)
        try {
            let count = 0
            let sql = `SELECT COUNT(*) AS total FROM user_animation_get_record WHERE sender_id = ${playerId} AND receiver_id = ${playerId} AND delsign = 0`
            let result = await werewolf.query(sql)
            if (!!result && result.length > 0) {
                count = result[0].total
            }
            let dataSql = `SELECT b.\`name\`,a.animation_id,a.price,a.createtime FROM user_animation_get_record a LEFT JOIN animation b ON a.animation_id = b.id
       WHERE a.sender_id = ${playerId} AND a.sender_id = a.receiver_id AND a.delsign = 0 ORDER BY createtime DESC LIMIT ${start} , ${offset}`
            let dataResult = await werewolf.query(dataSql)
            if (!dataResult) {
                return {
                    ...request,
                    count: count,
                    dataArray: [],
                }
            }
            return {
                ...request,
                count: count,
                dataArray: dataResult,
            }
        } catch (error) {
            throw error
        }
    }

    public async specialEffectGive(request: ItreasureRequest): Promise<ItreasureSpecialEffectGiveResponse> {
        const { app } = this
        const werewolf = app.mysql.get('werewolf')
        const playerId = werewolf.escape(request.playerId)
        const start = werewolf.escape(request.start)
        const offset = werewolf.escape(request.offset)
        try {
            let count = 0
            let sql = `SELECT COUNT(*) AS total FROM user_animation_get_record WHERE sender_id = ${playerId} AND receiver_id != ${playerId} AND delsign = 0`
            let result = await werewolf.query(sql)
            if (!!result && result.length > 0) {
                count = result[0].total
            }
            let dataSql = `SELECT
      b. \`name\`,
      a.animation_id,
      a.price,
      a.createtime,
      a.receiver_id,
      c.nickname
    FROM
      user_animation_get_record a
    LEFT JOIN animation b ON a.animation_id = b.id
    LEFT JOIN tuser c ON a.receiver_id = c.\`no\`
    WHERE
      a.sender_id = ${playerId}
    AND a.receiver_id != ${playerId} AND a.delsign = 0 ORDER BY createtime DESC LIMIT ${start} , ${offset}`
            let dataResult = await werewolf.query(dataSql)
            if (!dataResult) {
                return {
                    ...request,
                    count: count,
                    dataArray: [],
                }
            }
            return {
                ...request,
                count: count,
                dataArray: dataResult,
            }
        } catch (error) {
            throw error
        }
    }

    public async specialEffectReceive(request: ItreasureRequest): Promise<ItreasureSpecialEffectReceiveResponse> {
        const { app } = this
        const werewolf = app.mysql.get('werewolf')
        const playerId = werewolf.escape(request.playerId)
        const start = werewolf.escape(request.start)
        const offset = werewolf.escape(request.offset)
        try {
            let count = 0
            let sql = `SELECT COUNT(*) AS total FROM user_animation_get_record WHERE receiver_id = ${playerId} AND  sender_id != ${playerId} AND delsign = 0 `
            let result = await werewolf.query(sql)
            if (!!result && result.length > 0) {
                count = result[0].total
            }
            let dataSql = `SELECT
      b. \`name\`,
      a.animation_id,
      a.price,
      a.createtime,
      a.sender_id,
      c.nickname
    FROM
      user_animation_get_record a
    LEFT JOIN animation b ON a.animation_id = b.id
    LEFT JOIN tuser c ON a.sender_id = c.\`no\`
    WHERE
      a.receiver_id = ${playerId}
    AND a.sender_id != ${playerId} AND a.delsign = 0 ORDER BY createtime DESC LIMIT ${start} , ${offset}`
            let dataResult = await werewolf.query(dataSql)
            if (!dataResult) {
                return {
                    ...request,
                    count: count,
                    dataArray: [],
                }
            }
            return {
                ...request,
                count: count,
                dataArray: dataResult,
            }
        } catch (error) {
            throw error
        }
    }

    public async canonRecord(request: ItreasureRequest): Promise<ItreasureCanonRecordResponse> {
        const { app } = this
        const werewolf = app.mysql.get('werewolf')
        const playerId = werewolf.escape(request.playerId)
        const start = werewolf.escape(request.start)
        const offset = werewolf.escape(request.offset)
        try {
            let count = 0
            let sql = `SELECT  COUNT(*) AS total FROM user_canon_item_record WHERE user_no = ${playerId} AND delsign = 0 `
            let result = await werewolf.query(sql)
            if (!!result && result.length > 0) {
                count = result[0].total
            }
            let dataSql = `SELECT
      b.\`name\`,
      c.title,
      c.content,
      a.price,
      a.num,
      a.createtime
    FROM
      user_canon_item_record a
    LEFT JOIN canon b ON a.canon_no = b.\`no\`
    LEFT JOIN canon_item c ON a.canon_item_no = c.\`no\`
    WHERE
      a.user_no = ${playerId}
    AND a.delsign = 0
    ORDER BY
      a.createtime DESC
    LIMIT ${start}, ${offset}`
            let dataResult = await werewolf.query(dataSql)
            if (!dataResult) {
                return {
                    ...request,
                    count: count,
                    dataArray: [],
                }
            }
            return {
                ...request,
                count: count,
                dataArray: dataResult,
            }
        } catch (error) {
            throw error
        }
    }
    //商城消费记录
    public async mallBuyRecord(request: ItreasureRequest): Promise<ItreasuremallBuyRecordResponse> {
        const { app } = this
        const werewolf = app.mysql.get('werewolf')
        const playerId = werewolf.escape(request.playerId)
        const start = werewolf.escape(request.start)
        const offset = werewolf.escape(request.offset)
        try {
            let count = 0
            let sqlStr = `SELECT * FROM item_produce_mall_history WHERE user_id =${playerId}`
            let result = await werewolf.query(sqlStr)
            if (!!result && result.length > 0) {
                count = result[0].total
            }
            let dataSql = `SELECT 
      a.user_id,
      a.to_user_id,
      a.create_time,
      a.current_price,
			a.coin_id,
			c.name as coin_name,
      b.name
      FROM item_produce_mall_history a 
			LEFT JOIN item_dic b ON a.item_dic_id = b.id 
			LEFT JOIN coin as c ON a.coin_id = c.id
      WHERE 
      user_id = ${playerId}
      AND create_time >  DATE_SUB(NOW(), INTERVAL 30 DAY) ORDER BY create_time DESC;`
            let dataResult = await werewolf.query(dataSql)
            if (!result) {
                return {
                    ...request,
                    count: count,
                    dataArray: [],
                }
            }
            return {
                ...request,
                count: count,
                dataArray: dataResult,
            }
        } catch (e) {
            throw e
        }
    }
    //礼包购买记录查询
    public async giftbagReciveRecord(request: ItreasureRequest): Promise<IgiftbagReciveRecordResponse> {
        const { app } = this
        const werewolf = app.mysql.get('werewolf')
        const playerId = werewolf.escape(request.playerId)
        const start = werewolf.escape(request.start)
        const offset = werewolf.escape(request.offset)
        try {
            let count = 0
            let sqlStr = `SELECT a.*,b.name  FROM  item_produce_gift_bag_user_record_history a LEFT JOIN  gift_bag_item b ON  a.gift_bag_no=b.no  WHERE user_id =${playerId}`
            let result = await werewolf.query(sqlStr)
            if (!!result && result.length > 0) {
                count = result[0].total
            }
            if (!result) {
                return {
                    ...request,
                    count: count,
                    dataArray: [],
                }
            }
            return {
                ...request,
                count: count,
                dataArray: result,
            }
        } catch (e) {
            throw e
        }
    }
    public async giftbagItemReciveRecord(request: ItreasureRequest): Promise<IgiftbagItemReceiveRecordResponse> {
        const { app } = this
        const werewolf = app.mysql.get('werewolf')
        const playerId = werewolf.escape(request.playerId)
        const start = werewolf.escape(request.start)
        const offset = werewolf.escape(request.offset)
        try {
            let count = 0
            let sqlStr = `SELECT a.*,b.name,c.name as item_name FROM item_produce_gift_bag_user_receive_history a LEFT JOIN  gift_bag_item b  ON  a.gift_bag_no=b.no LEFT JOIN gift_bag_content_item c ON a.gift_bag_no=c.gift_bag_no  WHERE user_id = ${playerId}`
            let result = await werewolf.query(sqlStr)
            if (!!result && result.length > 0) {
                count = result[0].total
            }

            if (!result) {
                return {
                    ...request,
                    count: count,
                    dataArray: [],
                }
            }
            return {
                ...request,
                count: count,
                dataArray: result,
            }
        } catch (e) {
            throw e
        }
    }

    public async usePropsRecord(request: ItreasureRequest): Promise<IusePropsRecordResponse> {
        const { app } = this
        const werewolf = app.mysql.get('werewolf')
        const playerId = werewolf.escape(request.playerId)
        const start = werewolf.escape(request.start)
        const offset = werewolf.escape(request.offset)
        try {
            let count = 0
            let sqlStr = `SELECT a.game_id,a.user_id,a.create_time,b.name FROM user_item_game a LEFT JOIN item_dic b ON a.item_dic_id = b.id WHERE a.user_id = ${playerId} AND create_time > DATE_SUB(CURDATE(),INTERVAL 30 DAY) ORDER BY create_time DESC LIMIT 2000`
            let result = await werewolf.query(sqlStr)
            if (!!result && result.length > 0) {
                count = result[0].total
            }
            if (!result) {
                return {
                    ...request,
                    count: count,
                    dataArray: [],
                }
            }
            return {
                ...request,
                count: count,
                dataArray: result,
            }
        } catch (e) {
            throw e
        }
    }
}
