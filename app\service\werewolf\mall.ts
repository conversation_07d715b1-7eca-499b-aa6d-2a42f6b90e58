/*
 * @Description: 首页弹窗服务类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhang<PERSON>
 * @Date: 2020-04-23 10:50:39
 * @LastEditors: zhanglu
 * @LastEditTime: 2022-09-13 11:04:27
 */
import BaseMegaService from './BaseMegaService';
import { Igift, IitemCate, IitemDic, Imaskshow, InormalItem } from './../../model/mallManager';
import { timingSafeEqual } from 'crypto';
export default class MallService extends BaseMegaService {

    /**
     * @name: select home dialog list
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async getGiftList(req) {
        const { app, ctx, logger } = this;

        // console.log("reqreq", req);

        try {
            if (req.delsign == null) {
                const sqlStr = `
                SELECT 
                        \`no\`,
                        \`name\`,
                        \`price\`,
                        \`reprice\`,
                        \`timeprice\`,
                        \`charm\`,
                        \`prive_remark\`,
                        \`props_explain\`,
                        \`MD5\`,
                        \`datatime\`,
                        \`img_name\`,
                        \`gif_name\`,
                        \`gift_type\`,
                        \`power_value\`,
                        \`gift_level_one_num\`,
                        \`gift_level_two_num\`,
                        \`gift_super\`,
                        \`gift_check_num\`,
                        \`nobleLevel\`,
                        \`show\`,
                        \`hot\`,
                        \`priority\`,
                        \`delsign\` ,
                        \`min_charm\` ,
                        \`max_charm\` ,
                        \`add_in\` ,
                        \`box_level\` ,
                        \`gift_source\` ,
                        \`g_give\` ,
                        istop,
                        team_exp,
                        user_exp,
                        drop_item_dic_id,
                        max_get_count
                    FROM 
                    gift
                `;
                let result =  await this.selectList(sqlStr, []);

                var IDList=new Array();

                for (const v of result) {
    
                    if(v.drop_item_dic_id != undefined && v.drop_item_dic_id != null){
                        IDList.push(v.drop_item_dic_id);
                    }
    
                    
                }

                var ids = "";

                for (const v of IDList) {
                    ids += v + ",";
                }
    
                ids += ids + "0";

                const sqlStr2 = `
                SELECT  * FROM  item_dic WHERE id in (${ids}) 
                `;
               
                let result2 = await this.selectList(sqlStr2, []);

                var m = new Map();
                var c = new Map();
                for (const v of result2) {
                    var id = v.id;
                    m.set(id,v.name);
                    c.set(id,v.item_cate_id);
                }

                for (const v of result) {
                    
                    v.drop_item_dic_name = m.get(v.drop_item_dic_id);
                    v.drop_item_cate_id = c.get(v.drop_item_dic_id);
                }


                return result;
            } else {
                const sqlStr = `
                SELECT 
                        \`no\`,
                        \`name\`,
                        \`price\`,
                        \`reprice\`,
                        \`timeprice\`,
                        \`charm\`,
                        \`prive_remark\`,
                        \`props_explain\`,
                        \`MD5\`,
                        \`datatime\`,
                        \`img_name\`,
                        \`gif_name\`,
                        \`gift_type\`,
                        \`power_value\`,
                        \`gift_level_one_num\`,
                        \`gift_level_two_num\`,
                        \`gift_super\`,
                        \`gift_check_num\`,
                        \`nobleLevel\`,
                        \`show\`,
                        \`hot\`,
                        \`priority\`,
                        \`delsign\` ,
                        \`min_charm\` ,
                        \`max_charm\` ,
                        \`add_in\` ,
                        \`box_level\` ,
                        \`gift_source\` ,
                        \`g_give\` ,
                        istop,
                        team_exp,
                        user_exp,
                        drop_item_dic_id,
                        max_get_count
                    FROM 
                    gift WHERE delsign = ?
                `;
                let result = await this.selectList(sqlStr, [req.delsign]);

                
                var IDList=new Array();

                for (const v of result) {
    
                    if(v.drop_item_dic_id != undefined && v.drop_item_dic_id != null){
                        IDList.push(v.drop_item_dic_id);
                    }
    
                    
                }

                var ids = "";

                for (const v of IDList) {
                    ids += v + ",";
                }
    
                ids += ids + "0";

                const sqlStr2 = `
                SELECT  * FROM  item_dic WHERE id in (${ids}) 
                `;
               
                let result2 = await this.selectList(sqlStr2, []);

                var m = new Map();
                var c = new Map();
                for (const v of result2) {
                    var id = v.id;
                    m.set(id,v.name);
                    c.set(id,v.item_cate_id);
                }

                for (const v of result) {
                    
                    v.drop_item_dic_name = m.get(v.drop_item_dic_id);
                    v.drop_item_cate_id = c.get(v.drop_item_dic_id);
                }


                return result;
            }

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateGift(req: any) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
            UPDATE \`gift\` 
            SET 
            \`name\` = ?,
            \`price\` = ?,
            \`reprice\` = ?,
            \`timeprice\` = ?,
            \`charm\` = ?,
            \`prive_remark\` = ?,
            \`props_explain\` = ?,
            \`gift_type\` = ?,
            \`power_value\` = ?,
            \`gift_level_one_num\` = ?,
            \`gift_level_two_num\` = ?,
            \`gift_super\` = ?,
            \`gift_check_num\` = ?,
            \`nobleLevel\` = ?,
            \`show\` = ?,
            \`hot\` = ?,
            \`priority\` = ?,
            \`delsign\` = ? ,
            \`img_name\` = ?,
            \`gif_name\` = ?,
            \`MD5\` = ?,
            gift_source = ?,
            box_level = ?,
            max_charm = ?,
            min_charm = ?,
            add_in = ?,
            g_give = ?,
            istop = ?,
            team_exp = ?,
            user_exp = ?,
            drop_item_dic_id =  ${req.cateId != 4040?null:req.drop_item_dic_id},
            max_get_count =  ${req.cateId != 4040?null:req.max_get_count}
            WHERE
                \`no\` = ?
            `;
            await conn.query(sql, [
                req.name,
                req.price,
                req.reprice,
                req.timeprice,
                req.charm,
                req.prive_remark,
                req.props_explain,
                req.gift_type,
                req.power_value,
                req.gift_level_one_num,
                req.gift_level_two_num,
                req.gift_super,
                req.gift_check_num,
                req.nobleLevel,
                req.show,
                req.hot,
                req.priority,
                req.delsign,
                req.img_name,
                req.gif_name,
                req.MD5,
                req.gift_source,
                req.box_level,
                req.max_charm,
                req.min_charm,
                req.add_in,
                req.g_give,
                req.istop,
                req.team_exp,
                req.user_exp,
                // req.drop_item_dic_id,
                // req.max_get_count,
                req.no
            ]);

            let sqlItemDic = `
                    UPDATE item_dic 
                    SET \`name\` = ?,
                    remark = ?
                    WHERE
                        item_id = ? 
                        AND ((item_cate_id > 4000 AND item_cate_id < 5000) OR item_cate_id = 9050)
                        
                `
            await conn.query(sqlItemDic, [req.name, req.props_explain, req.no]);

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async createGift(req: any) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
            INSERT INTO \`gift\` (
                \`name\`,
                \`price\`,
                \`reprice\`,
                \`timeprice\`,
                \`charm\`,
                \`prive_remark\`,
                \`props_explain\`,
                \`gift_type\`,
                \`power_value\`,
                \`gift_level_one_num\`,
                \`gift_level_two_num\`,
                \`gift_super\`,
                \`gift_check_num\`,
                \`nobleLevel\`,
                \`show\`,
                \`hot\`,
                \`priority\`,
                \`delsign\` ,
                \`img_name\`,
                \`gif_name\`,
                gift_source,
                box_level,
                min_charm,
                max_charm,
                add_in,
                g_give,
                istop,
                team_exp,
                user_exp,
                drop_item_dic_id,
                max_get_count

            )
            VALUES
                (
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?
                );
            `;
            const result = await conn.query(sql, [
                req.name,
                req.price,
                req.reprice,
                req.timeprice,
                req.charm,
                req.prive_remark,
                req.props_explain,
                req.gift_type,
                req.power_value,
                req.gift_level_one_num,
                req.gift_level_two_num,
                req.gift_super,
                req.gift_check_num,
                req.nobleLevel,
                req.show,
                req.hot,
                req.priority,
                req.delsign,
                req.img_name,
                req.gif_name,
                req.gift_source,
                req.box_level,
                req.min_charm,
                req.max_charm,
                req.add_in,
                req.g_give,
                req.istop,
                req.team_exp,
                req.user_exp,
                req.drop_item_dic_id,
                req.max_get_count
            ]);

            req.no = result.insertId;

            let sqlInsertItemDic = ` 
                INSERT INTO \`item_dic\` (\`item_cate_id\`, \`item_id\`, \`name\`, \`version\`, \`delsign\`,remark )
                VALUES (?,?,?,?,?,?);
            `;
            await conn.query(sqlInsertItemDic, [req.cateId, req.no, req.name, 0, 0, req.props_explain]);

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async getClothingCateList() {
        const { logger } = this;
        try {
            const sqlStr = `
            SELECT id, \`name\`, zindex_group FROM wedding_clothing_cate
            `;
            return await this.selectList(sqlStr, []);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getClothingList() {
        const { logger } = this;
        try {
            const sqlStr = `
            SELECT
                wc.id,ic.id AS itemDicId,wc.clothing_cate_id,wc.\`name\`,wc.pic,wc.sex,wc.source,wc.is_default,wc.buy_describe AS remark,wc.sort,wc.delsign,wc.create_time,GROUP_CONCAT(p.id ORDER BY p.zindex DESC) AS picIdStr
            FROM
                wedding_clothing wc
                INNER JOIN item_dic ic ON ic.item_id = wc.id 
                INNER JOIN wedding_clothing_pic p ON p.clothing_id = wc.id
                AND ic.item_cate_id = 11000 
            GROUP BY wc.id	
            ORDER BY
                id DESC
            `;
            const sqlStr2 = `
            SELECT
                wc.id,ic.id AS itemDicId,wc.clothing_cate_id,wc.name,wc.pic,wc.sex,wc.source,wc.is_default,wc.buy_describe AS remark,wc.sort,wc.delsign,wc.create_time,GROUP_CONCAT(p.id ORDER BY p.zindex DESC) AS picIdStr
            FROM
                wedding_clothing wc
                INNER JOIN item_dic ic ON ic.item_id = wc.id 
                INNER JOIN wedding_clothing_pic p ON 1
                AND ic.item_cate_id = 11000 
                AND wc.clothing_cate_id = 5
            GROUP BY wc.id	
            ORDER BY
                id DESC
            `;

            let result = await this.selectList(sqlStr, []);
            let result2 = await this.selectList(sqlStr2, []);

            result.push.apply(result, result2)


            let max = result.length - 1;
            for (let j = 0; j < max; j++) {
              // 声明一个变量，作为标志位
              let done = true;
              for (let i = 0; i < max - j; i++) {
                if (result[i].id < result[i + 1].id) {
                  let temp = result[i];
                  result[i] = result[i + 1];
                  result[i + 1] = temp;
                  done = false;
                }
              }
              if (done) {
                break;
              }
            }


            

            return result;


        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async createClothing(req: any) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
            INSERT INTO wedding_clothing
                (clothing_cate_id, \`name\`, sex, source, is_default, sort, delsign,buy_describe) VALUES 
                (?, ?, ?, ?, ?, ?, ?, ?)
            `;
            const result = await conn.query(sql, [
                req.clothing_cate_id,
                req.name,
                req.sex,
                req.source,
                req.is_default,
                req.sort,
                req.delsign,
                req.remark]);

            req.id = result.insertId;

            let sqlInsertItemDic = ` 
                INSERT INTO \`item_dic\` (\`item_cate_id\`, \`item_id\`, \`name\`, \`version\`, \`delsign\`, \`remark\` )
                VALUES (?,?,?,?,?,?);
            `;
            await conn.query(sqlInsertItemDic, [11000, req.id, req.name, 0, 0,req.remark]);

            let sqlInsertClothingPic = `
                INSERT INTO wedding_clothing_pic(clothing_id, zindex, delsign) VALUES ?
            `;
            let valueInsertClothingPic: any = [];
            for (const zindex of req.zindexList) {
                valueInsertClothingPic.push([req.id, zindex, 0]);
            }

            if(req.clothing_cate_id != 5){//不是光效
                if (valueInsertClothingPic.length > 0) {
                    await conn.query(sqlInsertClothingPic, [valueInsertClothingPic]);
                }
            }

           

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async updateClothing(req: any) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
                UPDATE wedding_clothing SET 
                    clothing_cate_id = ?, 
                    \`name\` = ?, 
                    pic = ?, 
                    sex = ?, 
                    source = ?, 
                    is_default = ?, 
                    sort = ?, 
                    delsign = ?, 
                    buy_describe = ?
                WHERE id = ?;
            `;
            await conn.query(sql, [
                req.clothing_cate_id,
                req.name,
                req.pic,
                req.sex,
                req.source,
                req.is_default,
                req.sort,
                req.delsign,
                req.remark,
                req.id]);

            let sqlItemDic = `
                    UPDATE item_dic 
                        SET \`name\` = ?,
                        \`remark\` = ?
                    WHERE
                        item_id = ? 
                        AND item_cate_id = 11000
                        
                `
            await conn.query(sqlItemDic, [req.name,req.remark, req.id]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async updateClothingIcon(req: any) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
                UPDATE wedding_clothing SET 
                    pic = ?
                WHERE id = ?;
            `;
            await conn.query(sql, [
                req.pic,
                req.id]);

            let sqlItemDic = ` 
                UPDATE item_dic 
                SET 
                pic = ?
                WHERE
                    item_id = ?
                    AND item_cate_id = 11000
            `;
            await conn.query(sqlItemDic, [
                req.pic,
                req.id]);

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    //称号
    public async getTitleList() {
        const { logger } = this;
        try {
            const sqlStr = `
            SELECT t.\`id\` AS title_id, ic.id AS item_dic_id, t.\`name\`, \`num\`, \`condition\`, \`level\`, \`tab\` AS type, \`createtime\`, \`describe\`, \`group_id\`, \`sort\`, t.\`delsign\` 
            FROM \`tag\` t INNER JOIN item_dic ic ON ic.item_id = t.id AND ic.item_cate_id = 9000
            WHERE group_id IS NULL
            ORDER BY t.sort ASC
            `;
            return await this.selectList(sqlStr, []);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async createTitle(req: any) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
            INSERT INTO \`tag\`
            (\`name\`, \`num\`, \`condition\`, \`level\`, \`tab\`, \`describe\`, \`sort\`, \`delsign\`) 
            VALUES (?,?,?,?,?,?,?,?)
            `;
            const result = await conn.query(sql, [
                req.name,
                req.num,
                req.condition,
                req.level,
                req.type,
                req.describe,
                req.sort,
                req.delsign]);

            req.id = result.insertId;

            let sqlInsertItemDic = ` 
                INSERT INTO \`item_dic\` (\`item_cate_id\`, \`item_id\`, \`name\`, \`version\`, \`delsign\` )
                VALUES (?,?,?,?,?);
            `;
            await conn.query(sqlInsertItemDic, [9000, req.id, req.name, 0, 0]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async updateTitle(req: any) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
                UPDATE tag SET 
                    \`name\` = ?, 
                    \`num\` = ?, 
                    \`condition\` = ?, 
                    \`level\` = ?, 
                    \`tab\` = ?, 
                    \`describe\` = ?, 
                    \`sort\` = ?,
                    createtime = NOW()
                WHERE \`id\` = ?;
            `;
            await conn.query(sql, [
                req.name,
                req.num,
                req.condition,
                req.level,
                req.type,
                req.describe,
                req.sort,
                req.id]);

            let sqlItemDic = `
                    UPDATE item_dic 
                        SET \`name\` = ? 
                    WHERE
                        item_id = ? 
                        AND item_cate_id = 9000
                        
                `
            await conn.query(sqlItemDic, [req.name, req.id]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async refreshTitle(req: any) {
        try {
            let sql = ` 
                UPDATE tag 
                SET createtime = NOW()
                WHERE \`id\` = ?;
            `;
            this.execSql(sql, [req.id]);
        } catch (error) {
            throw error;
        }
    }

    public async updateDelsignTitle(req: any) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
                UPDATE tag SET 
                    \`delsign\` = ?
                WHERE \`id\` = ?;
            `;
            await conn.query(sql, [
                req.delsign,
                req.id]);

            let sqlItemDic = `
                    UPDATE item_dic 
                        SET \`delsign\` = ? 
                    WHERE
                        item_id = ? 
                        AND item_cate_id = 9000
                `
            await conn.query(sqlItemDic, [req.delsign, req.id]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    //自定义称号-底图

    public async getCustomTagBgList() {
        const { logger } = this;
        try {
            const sqlStr = `
            SELECT \`id\`,name,img,remark,createtime,delsign,iconImg FROM tag_bg_img
            `;
            return await this.selectList(sqlStr, []);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async createCustomTagBg(req: any) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql1 = ` 
            INSERT INTO \`tag_bg_img\`
            (\`name\`,  \`remark\`,  \`createtime\`,\`delsign\`) 
            VALUES ('${req.name}','${req.remark}',now(),0)
            `;

            const result = await conn.query(sql1, []);

            let sqlItemDic = ` 
            INSERT INTO \`item_dic\`
            (\`name\`,  \`remark\`,  \`item_cate_id\`,\`item_id\`) 
            VALUES ('${req.name}','${req.remark}',9001,'${result.insertId}')
            `;
            await conn.query(sqlItemDic, []);

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async updateCustomTagBg(req: any) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {

            let sql = ` 
                UPDATE tag_bg_img SET 
                    \`name\` = '${req.name}', 
                    \`remark\` = '${req.remark}',
                    createtime = NOW()
                WHERE \`id\` = ${req.id};
            `;

            let sqlItemDic = ` 
                UPDATE item_dic SET 
                    \`name\` = '${req.name}', 
                    \`remark\` = '${req.remark}'
                WHERE \`item_id\` = ${req.id} AND \`item_cate_id\` = 9001;
            `;
            conn.query(sqlItemDic, []);

            conn.query(sql, []);


            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async refreshCustomTagBg(req: any) {
        try {
            let sql = "";

            if (req.img != null && req.img.length > 0) {

                sql = ` 
                   UPDATE tag_bg_img 
                   SET createtime = NOW(),img = '${req.img}'
                   WHERE \`id\` = ${req.id};
               `;
            }

            if (req.iconImg != null && req.iconImg.length > 0) {

                sql = ` 
                   UPDATE tag_bg_img 
                   SET createtime = NOW(),iconImg = '${req.iconImg}'
                   WHERE \`id\` = ${req.id};
               `;
            }

            if (req.img != null && req.img.length > 0 && req.iconImg != null && req.iconImg.length > 0) {

                sql = ` 
                UPDATE tag_bg_img 
                SET createtime = NOW(),img = '${req.img}',iconImg = '${req.iconImg}'
                WHERE \`id\` = ${req.id};
            `;
            }

            this.execSql(sql, []);
        } catch (error) {
            throw error;
        }
    }

    public async updateDelsignCustomTagBg(req: any) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {

            let sql = `UPDATE tag_bg_img  SET \`delsign\` = ?  WHERE id = ? `;

            await conn.query(sql, [req.delsign, req.id]);

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    //自定义称号-装饰1

    public async getCustomTagDecorationList1() {
        const { logger } = this;
        try {
            const sqlStr = `
            SELECT \`id\`,name,img,remark,createtime,delsign,iconImg FROM tag_decoration_left
            `;
            return await this.selectList(sqlStr, []);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async createCustomTagDecoration1(req: any) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {

            let sql = ` 
            INSERT INTO \`tag_decoration_left\`
            (\`name\`,  \`remark\`,  \`createtime\`,\`delsign\`) 
            VALUES ('${req.name}','${req.remark}',now(),0)
            `;

            const result = await conn.query(sql, []);

            let sqlItemDic = ` 
            INSERT INTO \`item_dic\`
            (\`name\`,  \`remark\`,  \`item_cate_id\`,\`item_id\`) 
            VALUES ('${req.name}','${req.remark}',9002,'${result.insertId}')
            `;
            await conn.query(sqlItemDic, []);

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async updateCustomTagDecoration1(req: any) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
                UPDATE tag_decoration_left SET 
                    \`name\` = '${req.name}', 
                    \`remark\` = '${req.remark}',
                    createtime = NOW()
                WHERE \`id\` = ${req.id};
            `;

            let sqlItemDic = ` 
            UPDATE item_dic SET 
                \`name\` = '${req.name}', 
                \`remark\` = '${req.remark}'
            WHERE \`item_id\` = ${req.id} AND \`item_cate_id\` = 9002;
        `;
            conn.query(sqlItemDic, []);

            conn.query(sql, []);

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async refreshCustomTagDecoration1(req: any) {
        try {

            let sql = "";

            if (req.img != null && req.img.length > 0) {

                sql = ` 
                    UPDATE tag_decoration_left 
                    SET createtime = NOW(),img = '${req.img}'
                    WHERE \`id\` = ${req.id};
                `;
            }

            if (req.iconImg != null && req.iconImg.length > 0) {

                sql = ` 
                    UPDATE tag_decoration_left 
                    SET createtime = NOW(),iconImg = '${req.iconImg}'
                    WHERE \`id\` = ${req.id};
                `;
            }

            if (req.img != null && req.img.length > 0 && req.iconImg != null && req.iconImg.length > 0) {

                sql = ` 
                UPDATE tag_decoration_left 
                SET createtime = NOW(),img = '${req.img}',iconImg = '${req.iconImg}'
                WHERE \`id\` = ${req.id};
            `;
            }

            this.execSql(sql, []);
        } catch (error) {
            throw error;
        }
    }

    public async updateDelsignCustomTagDecoration1(req: any) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = `UPDATE tag_decoration_left  SET \`delsign\` = ?  WHERE id = ? `;

            await conn.query(sql, [req.delsign, req.id]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    //自定义称号-装饰2
    public async getCustomTagDecorationList2() {
        const { logger } = this;
        try {
            const sqlStr = `
            SELECT \`id\`,name,img,remark,createtime,delsign,iconImg FROM tag_decoration_right
            `;
            return await this.selectList(sqlStr, []);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async createCustomTagDecoration2(req: any) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
            INSERT INTO \`tag_decoration_right\`
            (\`name\`,  \`remark\`,  \`createtime\`,\`delsign\`) 
            VALUES ('${req.name}','${req.remark}',now(),0)
            `;
            const result = await conn.query(sql, []);

            let sqlItemDic = ` 
            INSERT INTO \`item_dic\`
            (\`name\`,  \`remark\`,  \`item_cate_id\`,\`item_id\`) 
            VALUES ('${req.name}','${req.remark}',9003,'${result.insertId}')
            `;
            await conn.query(sqlItemDic, []);

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async updateCustomTagDecoration2(req: any) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
                UPDATE tag_decoration_right SET 
                    \`name\` = '${req.name}', 
                    \`remark\` = '${req.remark}',
                    createtime = NOW()
                WHERE \`id\` = ${req.id};
            `;

            let sqlItemDic = ` 
            UPDATE item_dic SET 
                \`name\` = '${req.name}', 
                \`remark\` = '${req.remark}'
            WHERE \`item_id\` = ${req.id} AND \`item_cate_id\` = 9003;
        `;
            conn.query(sqlItemDic, []);

            conn.query(sql, []);

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async refreshCustomTagDecoration2(req: any) {
        try {
            let sql = "";

            if (req.img != null && req.img.length > 0) {

                sql = ` 
                    UPDATE tag_decoration_right 
                    SET createtime = NOW(),img = '${req.img}'
                    WHERE \`id\` = ${req.id};
                `;
            }

            if (req.iconImg != null && req.iconImg.length > 0) {

                sql = ` 
                    UPDATE tag_decoration_right 
                    SET createtime = NOW(),iconImg = '${req.iconImg}'
                    WHERE \`id\` = ${req.id};
                `;
            }

            if (req.img != null && req.img.length > 0 && req.iconImg != null && req.iconImg.length > 0) {

                sql = ` 
                UPDATE tag_decoration_right 
                SET createtime = NOW(),img = '${req.img}',iconImg = '${req.iconImg}'
                WHERE \`id\` = ${req.id};
            `;
            }


            this.execSql(sql, []);
        } catch (error) {
            throw error;
        }
    }

    public async updateDelsignCustomTagDecoration2(req: any) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = `UPDATE tag_decoration_right  SET \`delsign\` = ?  WHERE id = ? `;

            await conn.query(sql, [req.delsign, req.id]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    //自定义称号-装饰3
    public async getCustomTagDecorationList3() {
        const { logger } = this;
        try {
            const sqlStr = `
            SELECT \`id\`,name,img,remark,createtime,delsign,iconImg FROM tag_decoration
            `;
            return await this.selectList(sqlStr, []);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async createCustomTagDecoration3(req: any) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
            INSERT INTO \`tag_decoration\`
            (\`name\`,  \`remark\`,  \`createtime\`,\`delsign\`) 
            VALUES ('${req.name}','${req.remark}',now(),0)
            `;

            const result = await conn.query(sql, []);

            let sqlItemDic = ` 
            INSERT INTO \`item_dic\`
            (\`name\`,  \`remark\`,  \`item_cate_id\`,\`item_id\`) 
            VALUES ('${req.name}','${req.remark}',9004,'${result.insertId}')
            `;
            await conn.query(sqlItemDic, []);


            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async updateCustomTagDecoration3(req: any) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
                UPDATE tag_decoration SET 
                    \`name\` = '${req.name}', 
                    \`remark\` = '${req.remark}',
                    createtime = NOW()
                WHERE \`id\` = ${req.id};
            `;

            let sqlItemDic = ` 
            UPDATE item_dic SET 
                \`name\` = '${req.name}', 
                \`remark\` = '${req.remark}'
            WHERE \`item_id\` = ${req.id} AND \`item_cate_id\` = 9004;
        `;
            conn.query(sqlItemDic, []);

            conn.query(sql, []);

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async refreshCustomTagDecoration3(req: any) {
        try {
            let sql = "";

            if (req.img != null && req.img.length > 0) {

                sql = ` 
                    UPDATE tag_decoration 
                    SET createtime = NOW(),img = '${req.img}'
                    WHERE \`id\` = ${req.id};
                `;
            }

            if (req.iconImg != null && req.iconImg.length > 0) {

                sql = ` 
                    UPDATE tag_decoration 
                    SET createtime = NOW(),iconImg = '${req.iconImg}'
                    WHERE \`id\` = ${req.id};
                `;
            }

            if (req.img != null && req.img.length > 0 && req.iconImg != null && req.iconImg.length > 0) {

                sql = ` 
                UPDATE tag_decoration 
                SET createtime = NOW(),img = '${req.img}',iconImg = '${req.iconImg}'
                WHERE \`id\` = ${req.id};
            `;
            }


            this.execSql(sql, [req.id]);
        } catch (error) {
            throw error;
        }
    }

    public async updateDelsignCustomTagDecoration3(req: any) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = `UPDATE tag_decoration  SET \`delsign\` = ?  WHERE id = ? `;

            await conn.query(sql, [req.delsign, req.id]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }
    //自定义称号-文字
    public async getCustomTagTextList() {
        const { logger } = this;
        try {
            const sqlStr = `
            SELECT t.\`id\`,f.\`id\` AS fontId,t.name,t.font_name,f.type AS font_type,t.color_a,t.color_b,t.color_border,t.remark,t.createtime,t.delsign,t.iconImg,f.remark AS fontFileRemark FROM tag_text t
            LEFT JOIN tag_font f ON f.name = t.font_name
            ORDER BY t.id ASC
            `;
            let list = await this.selectList(sqlStr, []);

            for (const item of list) {
                if (item.font_name === "") {
                    item.fontId = "";
                    item.fontFileRemark = "";
                }
            }
            return list;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async createCustomTagText(req: any) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
            INSERT INTO \`tag_text\`
            (\`name\`,  \`remark\`,\`font_name\`,\`font_id\`,  \`color_a\`,  \`color_b\`,  \`color_border\`, \`createtime\`,\`delsign\`) 
            VALUES ('${req.name}','${req.remark}','${req.fontName}','${req.fontId}','${req.color_a}','${req.color_b}','${req.color_border}',now(),0)
            `;

            const result = await conn.query(sql, []);

            let sqlItemDic = ` 
            INSERT INTO \`item_dic\`
            (\`name\`,  \`remark\`,  \`item_cate_id\`,\`item_id\`) 
            VALUES ('${req.name}','${req.remark}',9005,'${result.insertId}')
            `;
            await conn.query(sqlItemDic, []);

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async updateCustomTagText(req: any) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
                UPDATE tag_text SET 
                    \`name\` = '${req.name}', 
                    \`color_a\` = '${req.color_a}', 
                    \`color_b\` = '${req.color_b}', 
                    \`color_border\` = '${req.color_border}', 
                    \`remark\` = '${req.remark}',
                    \`font_name\` = '${req.fontName}',
                    \`font_id\` = '${req.fontId}',
                    createtime = NOW()
                WHERE \`id\` = ${req.id};
            `;

            let sqlItemDic = ` 
            UPDATE item_dic SET 
                \`name\` = '${req.name}', 
                \`remark\` = '${req.remark}'
            WHERE \`item_id\` = ${req.id} AND \`item_cate_id\` = 9005;
        `;
            conn.query(sqlItemDic, []);

            conn.query(sql, []);

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async refreshCustomTagText(req: any) {
        try {
            // let sql = ` 
            //     UPDATE tag_text 
            //     SET createtime = NOW(),font_name = '${req.fontName}',iconImg = '${req.iconImg}
            //     WHERE \`id\` = ${req.id};
            // `;

            let sql = '';

            if (req.fontName != null && req.fontName.length > 0) {

                sql = ` 
                    UPDATE tag_text 
                    SET createtime = NOW(),font_name = '${req.fontName}'
                    WHERE \`id\` = ${req.id};
                `;
            }

            if (req.iconImg != null && req.iconImg.length > 0) {

                sql = ` 
                    UPDATE tag_text 
                    SET createtime = NOW(),iconImg = '${req.iconImg}'
                    WHERE \`id\` = ${req.id};
                `;
            }

            if (req.fontName != null && req.fontName.length > 0 && req.iconImg != null && req.iconImg.length > 0) {

                sql = ` 
                UPDATE tag_text 
                SET createtime = NOW(),font_name = '${req.fontName}',iconImg = '${req.iconImg}'
                WHERE \`id\` = ${req.id};
            `;
            }

            this.execSql(sql, []);
        } catch (error) {
            throw error;
        }
    }

    public async updateDelsignCustomTagText(req: any) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = `UPDATE tag_text  SET \`delsign\` = ?  WHERE id = ? `;

            await conn.query(sql, [req.delsign, req.id]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    //自定义称号-字体 
    public async getCustomTagFontList(req: any) {
        const { logger } = this;
        try {
            let sqlStr = `
            SELECT \`id\`,name,remark,type,createtime FROM tag_font;
            `;

            if (req.isHaveFont == "1") {
                sqlStr = `
            SELECT \`id\`,name,remark,createtime,type FROM tag_font WHERE name <> "";
            `;
            }
            return await this.selectList(sqlStr, []);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getCustomTagHaveFontList(req: any) {
        const { logger } = this;
        try {
            let sqlStr = `
            SELECT \`id\`,name,remark FROM tag_font WHERE name <> "";
            `;

            return await this.selectList(sqlStr, []);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async createCustomTagFont(req: any) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
            INSERT INTO \`tag_font\`
            ( \`remark\`,\`createtime\`,\`type\`) 
            VALUES ('${req.remark}',NOW(),'${req.type}')
            `;
            conn.query(sql, []);

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async updateCustomTagFont(req: any) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
                UPDATE tag_font SET 
                    createtime = NOW(),
                     \`remark\` = '${req.remark}',
                     \`type\` = '${req.type}'
                WHERE \`id\` = ${req.id};
            `;
            this.logger.error("sql "+sql);
            conn.query(sql, []);

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async refreshCustomTagFont(req: any) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            // let sql = ` 
            //     UPDATE tag_text 
            //     SET createtime = NOW(),font_name = '${req.fontName}',iconImg = '${req.iconImg}
            //     WHERE \`id\` = ${req.id};
            // `;

            let sql = ` 
                    UPDATE tag_font
                    SET createtime = NOW(),
                     \`name\` = '${req.fontName}'
                    WHERE \`id\` = ${req.id};
                `;

            conn.query(sql, []);

            await conn.commit(); // 提交事务
        } catch (error) {
            throw error;
        }
    }


    //信物
    public async getKeepSakeList(req: any) {
        const { logger } = this;
        try {
            let sqlStr = `
            SELECT d.id,d.item_id,d.item_cate_id,d.name,d.pic,d.icon,d.remark,
                    a.id AS animation_id,a.type,a.isLight,a.bg,a.timer,a.price,a.show,a.buy,a.sort,a.createtime,a.alert,a.hot,a.channel,a.buff,a.ss_buff,a.delsign,a.createtime,a.show
                    FROM item_dic d,animation a 
                    WHERE d.item_cate_id = 1094 AND a.role = 2004 AND d.item_id = a.id;
            `;


            return await this.selectList(sqlStr, []);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async createKeepSake(req: any) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            
            let animation_sql = ` 
                INSERT INTO animation 
                    (
                    \`name\`,
                    \`role\`,
                    \`type\`,
                    \`isLight\`, 
                    \`bg\`,

                    \`timer\`, 
                    \`price\`, 
                    \`show\`, 
                    \`buy\`,
                    \`sort\`,

                    \`createtime\`,
                    \`hot\`, 
                    \`remark\`,
                    \`channel\`, 
                    \`ss_buff\`, 
                    \`buff\`)
                    VALUES ('${req.name}','2004','${req.type}','${req.isLight}','0',
                    '${req.timer}','0','${req.show}','0','0',
                    NOW(),'0','${req.remark}','${req.channel}',${Number(req.ss_buff)},'${req.buff}');
                `;

                if(req.isLight != 5){
                    animation_sql = ` 
                INSERT INTO animation 
                    (
                    \`name\`,
                    \`role\`,
                    \`type\`,
                    \`isLight\`, 
                    \`bg\`,

                    \`timer\`, 
                    \`price\`, 
                    \`show\`, 
                    \`buy\`,
                    \`sort\`,

                    \`createtime\`,
                    \`hot\`, 
                    \`remark\`,
                    \`channel\`, 
                    \`buff\`)
                    VALUES ('${req.name}','2004','${req.type}','${req.isLight}','0',
                    '${req.timer}','0','${req.show}','0','0',
                    NOW(),'0','${req.remark}','${req.channel}','${req.buff}');
                `;
                }

                    // \`type\` = '1',
                    // \`isLight\` = '0',
                    // \`bg\` = '0',
                    // \`price\` = '0',
                    // \`buy\` = '0',
                    // \`sort\` = '126',
                    // \`hot\` = '0',
                    // \`channel\` = '0'

                 const result = await conn.query(animation_sql, []);
                //  const result = await db.query(animation_sql);


                let item_dic_sql = ` 
                INSERT INTO item_dic 
                    (\`item_cate_id\`, 
                    \`item_id\`, 
                    \`name\`,
                        \`remark\`)
                VALUES ('1094','${result.insertId}','${req.name}','${req.remark}')
                `;

                this.logger.error(result);
                this.logger.error(item_dic_sql);

                await conn.query(item_dic_sql, []);
 


                await conn.commit(); // 提交事务
                
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async updateKeepSake(req: any) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {

            let animation_sql = ` 
            UPDATE animation SET
                \`name\` = '${req.name}',
                \`timer\` = '${req.timer}',
                \`createtime\` = NOW(),
                \`remark\` = '${req.remark}',
                \`type\` = '${req.type}',
                \`isLight\` = '${req.isLight}',
                \`channel\` = '${req.channel}',
                \`show\` = '${req.show}',
                \`ss_buff\` = ${Number(req.ss_buff)},
                \`buff\` = '${req.buff}'
            WHERE \`id\` = ${req.animation_id};
        `;

        if(req.isLight != 5){
            animation_sql = ` 
            UPDATE animation SET
                \`name\` = '${req.name}',
                \`timer\` = '${req.timer}',
                \`createtime\` = NOW(),
                \`remark\` = '${req.remark}',
                \`type\` = '${req.type}',
                \`isLight\` = '${req.isLight}',
                \`channel\` = '${req.channel}',
                \`show\` = '${req.show}',
                \`buff\` = '${req.buff}'
            WHERE \`id\` = ${req.animation_id};
        `;
        }
            let sql = ` 
                UPDATE item_dic SET 
                    \`name\` = '${req.name}',
                     \`remark\` = '${req.remark}'
                WHERE \`id\` = ${req.id};
            `;
            conn.query(animation_sql, []);
            conn.query(sql, []);

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async refreshKeepSake(req: any) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            // let sql = ` 
            //     UPDATE tag_text 
            //     SET createtime = NOW(),font_name = '${req.fontName}',iconImg = '${req.iconImg}
            //     WHERE \`id\` = ${req.id};
            // `;

            
            let time_sql = ` 
                UPDATE animation
                SET createtime = NOW()
                WHERE \`id\` = ${req.id};
                 `;

            let sql = ` 
                    UPDATE item_dic
                    SET \`pic\` = '${req.pic}',
                     \`icon\` = '${req.icon}'
                    WHERE \`item_id\` = ${req.animation_id};
                    `;
            if(req.pic && (req.icon == null || req.icon == '')){
                    sql = ` 
                    UPDATE item_dic
                    SET \`pic\` = '${req.pic}'
                    WHERE \`item_id\` = ${req.animation_id};
                   `;
            }else if((req.pic == null || req.pic == '') && req.icon){
                    sql = ` 
                    UPDATE item_dic
                    SET \`icon\` = '${req.icon}'
                    WHERE \`item_id\` = ${req.animation_id};
                   `;
            }

            this.logger.error(time_sql);
            // this.logger.error(sql);

            conn.query(time_sql, []);
            conn.query(sql, []);

            await conn.commit(); // 提交事务
        } catch (error) {
            throw error;
        }
    }

    public async updateDelsignKeepSake(req: any) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {

            let sqlItemDic = `
                    UPDATE animation 
                        SET \`delsign\` = ? 
                    WHERE
                        id = ? 
                        AND role = 2004
                `
            await conn.query(sqlItemDic, [req.delsign, req.animation_id]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async getKeepSakeInfoList(req: any) {
        const { logger } = this;
        try {
            let sqlStr = `
                SELECT a.*,d.name,d.id AS item_dic_id,d.item_id,d.pic,d.icon FROM user_animation a,item_dic d WHERE a.animation_id IN
                (SELECT id FROM animation WHERE role = '2004') AND a.user_id = '${req.user_id}'
                AND d.item_id = a.animation_id
                ;
            `;
            logger.error("---"+sqlStr);


            let list = await this.selectList(sqlStr, []);

            if(list != null && list.length > 0){
                for (const item of list) {
                    let sqlStr2 = `
                    SELECT create_time AS destroytime FROM item_consume_cp_keepsake_history WHERE user_id = '${req.user_id}' AND item_dic_id = '${item.item_dic_id}' ORDER BY id DESC;
                    `;
                    logger.error("---"+sqlStr2);
                    let list2 = await this.selectList(sqlStr2, []);
                    if(list2 != null && list2.length > 0){
                        item.destroytime = list2[0].destroytime;
                    }
    
    
                    let sqlStr3 = `
                    SELECT * FROM user_animation_keepsake WHERE user_id = '${req.user_id}' AND animation_id = '${item.item_id}';
                    `;
                    logger.error("---"+sqlStr3);
                    let list3 = await this.selectList(sqlStr3, []);
                    if(list3 != null && list3.length > 0){
                        item.desc = list3[0].desc;
                    }
    
                }
            }

            


            return list;

        } catch (error) {
            logger.error(error);
            throw error
        }
    }
    
    public async updateShowInfoKeepSake(req: any) {
         // const { newFlag, gameConfigId, hot, desc } = req;
         const { app } = this;
         const db = app.mysql.get('werewolf');
         //开启事物
         const conn = await db.beginTransaction();
         try {
 
             let user_animation_sql = `
                     UPDATE user_animation
                         SET \`delsign\` = ? 
                     WHERE
                         user_id = ? 
                         AND animation_id = ?
                 `

                 let user_animation_keepsake_sql = `
                     UPDATE user_animation_keepsake 
                         SET \`delsign\` = ? 
                     WHERE
                         user_id = ? 
                         AND animation_id = ?
                 `

             await conn.query(user_animation_sql, [req.delsign, req.user_id,req.animation_id]);
             await conn.query(user_animation_keepsake_sql, [req.delsign, req.user_id,req.animation_id]);
             await conn.commit(); // 提交事务
         } catch (error) {
             await conn.rollback(); // 一定记得捕获异常后回滚事务！
             throw error;
         }
    }


    //
    public async getAchievementList(req: any) {
        const { logger } = this;
        try {
            const sqlStr = `
            SELECT GROUP_CONCAT(l.\`level\`,'-',l.\`unlock_num\`,'-',l.\`desc\`,'-',l.delsign ORDER BY l.\`level\`) AS content, t.\`id\`, ic.id AS item_dic_id, t.\`name\`, \`type\`, \`role\`, \`camp\`, t.\`remark\`, \`mode\`, \`unit\`, \`easter_egg\`, \`game\`, \`uplevel\`, t.\`desc\`, \`is_complete\`, t.\`delsign\`
            FROM 
            \`achievement\` t 
            LEFT JOIN achievement_level l ON l.achievement_id = t.id
            INNER JOIN item_dic ic ON ic.item_id = t.id AND ic.item_cate_id = 9010
            WHERE t.is_complete = ?
            GROUP BY t.id
            ORDER BY t.id DESC
            LIMIT ?, ?
            `;
            return await this.selectList(sqlStr, [req.isComplete, (req.current - 1) * req.pageCount, req.pageCount]);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getAchievementListCount(req: any) {
        const { logger } = this;
        try {
            const sqlStr = `
            SELECT COUNT(*) AS num
            FROM 
            achievement t 
            INNER JOIN item_dic ic ON ic.item_id = t.id AND ic.item_cate_id = 9010
            ORDER BY t.id DESC
            `;
            return await this.selectOne(sqlStr, []);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async createAchievement(req: any) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
            INSERT INTO achievement(game_type, \`name\`, type, role, camp, remark, \`mode\`, unit, easter_egg, game, uplevel, \`desc\`, delsign) 
            VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)
            `;
            const result = await conn.query(sql, [
                0,
                req.name,
                req.type,
                req.role,
                req.camp,
                req.remark,
                req.mode,
                req.unit,
                req.easter_egg,
                req.game,
                req.uplevel,
                req.desc,
                req.delsign]);

            req.id = result.insertId;

            let sqlInsertItemDic = ` 
                INSERT INTO \`item_dic\` (\`item_cate_id\`, \`item_id\`, \`name\`, \`version\`, \`delsign\` )
                VALUES (?,?,?,?,?);
            `;
            await conn.query(sqlInsertItemDic, [9010, req.id, req.name, 0, 0]);
            this.handleAchievementLevelList(req, conn, true);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async updateAchievement(req: any) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
            UPDATE \`achievement\` 
            SET 
            \`name\` = ?,
            \`type\` = ?,
            \`role\` = ?,
            \`camp\` = ?,
            \`remark\` = ?,
            \`mode\` = ?,
            \`unit\` = ?,
            \`easter_egg\` = ?,
            \`game\` = ?,
            \`uplevel\` = ?,
            \`desc\` = ?
            WHERE
            \`id\` = ?
            `;
            await conn.query(sql, [
                req.name,
                req.type,
                req.role,
                req.camp,
                req.remark,
                req.mode,
                req.unit,
                req.easter_egg,
                req.game,
                req.uplevel,
                req.desc,
                req.id]);

            let sqlItemDic = `
                UPDATE item_dic 
                    SET \`name\` = ? 
                WHERE
                    item_id = ? 
                    AND item_cate_id = 9010
                `
            await conn.query(sqlItemDic, [req.name, req.id]);
            await this.handleAchievementLevelList(req, conn, false);

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async handleAchievementLevelList(request: any, conn: any, isInsert: any) {
        if (!!!isInsert) {
            let sqlLevleCount = `
                SELECT COUNT(*) AS num FROM achievement_level WHERE achievement_id = ?
            `;
            let result: any = await conn.query(sqlLevleCount, [request.id]);
            if (result[0].num < request.levelList.length) {
                let sql = ` 
                UPDATE achievement SET 
                    \`is_complete\` = ?
                WHERE \`id\` = ?
            `;
                await conn.query(sql, [
                    0,
                    request.id]);
            }

            let deleteSql = `
                DELETE FROM achievement_level WHERE achievement_id = ?
            `;
            await conn.query(deleteSql, [request.id]);
        }

        if (request.levelList.length > 0) {
            let insertSql = `
            INSERT INTO achievement_level(achievement_id, unlock_num, \`level\`, \`desc\`, delsign) VALUES`;
            let value = Array();
            for (const item of request.levelList) {
                value.push(`(${request.id}, ${item.unlock_num} ,${item.level} ,${item.desc} , 0)`);
            }
            insertSql += value.toString();
            await conn.query(insertSql);
        }
    }

    public async updateAchievementDelsign(req: any) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
                UPDATE achievement SET 
                    \`delsign\` = ?
                WHERE \`id\` = ?
            `;
            await conn.query(sql, [
                req.delsign,
                req.id]);

            let sqlLevel = `
                UPDATE achievement_level SET delsign = ? WHERE achievement_id = ?
            `
            await conn.query(sqlLevel, [req.delsign, req.id]);

            let sqlItemDic = `
                    UPDATE item_dic 
                        SET \`delsign\` = ? 
                    WHERE
                        item_id = ? 
                        AND item_cate_id = 9010
                `
            await conn.query(sqlItemDic, [req.delsign, req.id]);

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async updateAchievementComplete(req: any) {
        const { app } = this;
        try {
            let sql = ` 
                UPDATE achievement SET 
                    \`is_complete\` = ?
                WHERE \`id\` = ?
            `;
            this.execSql(sql, [
                req.is_complete,
                req.id]);
        } catch (error) {
            throw error;
        }
    }

    public async getItemDicList(req) {
        const { app, ctx, logger } = this;

        try {
            if (req == null || req == "" || req.cateId == null || req.cateId == "" || req.cateId == "-1") {
                const sqlStr = `
                SELECT 
                ic.\`id\`,
                ic.\`item_cate_id\`,
                ic.\`item_id\`,
                ic.\`name\`,
                ic.\`pic\`,
                ic.\`icon\`,
                ic.\`remark\`,
                ic.\`version\`,
                ic.\`delsign\`,
				ica.\`name\` AS categoryName,
                cou.time,
                wc.sex
                FROM \`item_dic\` ic
				INNER JOIN item_cate ict ON ict.id = ic.item_cate_id
				LEFT JOIN item_catalog ica ON ica.id = ict.item_catalog_id
                LEFT JOIN wedding_clothing wc ON wc.id = ic.item_id AND ic.item_cate_id = 11000
                LEFT JOIN coupon cou ON cou.id = ic.item_id AND ic.item_cate_id = 12000
                ORDER BY id DESC
                `;
                return await this.selectList(sqlStr, []);
            } else {
                const sqlStr = `
                SELECT 
                ic.\`id\`,
                ic.\`item_cate_id\`,
                ic.\`item_id\`,
                ic.\`name\`,
                ic.\`pic\`,
                ic.\`icon\`,
                ic.\`remark\`,
                ic.\`version\`,
                ic.\`delsign\`,
				ica.\`name\` AS categoryName,
                cou.time,
                wc.sex
                FROM \`item_dic\` ic
				INNER JOIN item_cate ict ON ict.id = ic.item_cate_id
                LEFT JOIN item_catalog ica ON ica.id = ict.item_catalog_id
                LEFT JOIN wedding_clothing wc ON wc.id = ic.item_id AND ic.item_cate_id = 11000
                LEFT JOIN coupon cou ON cou.id = ic.item_id AND ic.item_cate_id = 12000
                WHERE ic.item_cate_id = ?
                ORDER BY id DESC
                `;
                return await this.selectList(sqlStr, [req.cateId]);
            }

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getItemCateList() {
        const { logger } = this;

        try {
            const sqlStr = `
            SELECT 
            null AS \`id\`,
            null AS \`item_catalog_id\`,
            null AS \`cls\`,
            null AS \`type\`,
            '全部' AS \`name\`,
            null AS \`remark\`,
            null AS \`once\`,
            null AS \`time_limit\`,
            null AS \`consume\`,
            null AS \`item_table\`,
            null AS \`item_user_table\` 
            UNION ALL 
            SELECT 
                \`id\`,
                \`item_catalog_id\`,
                \`cls\`,
                \`type\`,
                \`name\`,
                \`remark\`,
                \`once\`,
                \`time_limit\`,
                \`consume\`,
                \`item_table\`,
                \`item_user_table\` 
                FROM \`item_cate\` 
                WHERE id != 1032
            `;
            return await this.selectList(sqlStr, []);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async createItemDic(req: IitemDic) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
            INSERT INTO \`item_dic\` (
                \`item_cate_id\`,
                \`item_id\`,
                \`name\`,
                \`pic\`,
                \`icon\`,
                \`remark\`,
                \`version\`,
                \`delsign\` 
            )
            VALUES
                (
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ? 
                );
            `;
            await conn.query(sql, [
                req.item_cate_id,
                req.item_id,
                req.name,
                req.pic,
                req.icon,
                req.remark,
                req.version,
                req.delsign]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async updateItemDic(req: IitemDic) {

        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
            UPDATE \`item_dic\` 
                    SET \`item_cate_id\` = ?,
                    \`item_id\` = ?,
                    \`name\` = ?,
                    \`pic\` = ?,
                    \`icon\` = ?,
                    \`remark\` = ?,
                    \`version\` = ?,
                    \`delsign\` = ? 
                    WHERE
                        \`id\` = ?
            `;
            await conn.query(sql, [
                req.item_cate_id,
                req.item_id,
                req.name,
                req.pic,
                req.icon,
                req.remark,
                req.version,
                req.delsign,
                req.id]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async getAnimationList() {
        const { logger } = this;

        try {
            const sqlStr = `
            SELECT 
                \`id\`,
                \`name\`,
                \`role\`,
                \`type\`,
                \`isLight\`,
                \`bg\`,
                \`timer\`,
                \`price\`,
                \`show\`,
                \`buy\`,
                \`sort\`,
                \`createtime\`,
                \`datePath\`,
                \`serialNumber\`,
                \`alert\`,
                \`hot\`,
                \`remark\`,
                \`channel\`,
                \`delsign\` 
            FROM \`animation\`
            WHERE \`delsign\` = 0 AND \`bg\` != 2
            `;
            return await this.selectList(sqlStr, []);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getMaskshowList() {
        const { logger } = this;

        try {
            const sqlStr = `
            SELECT \`no\`, \`name\` FROM maskshow WHERE delsign = 0
            `;
            return await this.selectList(sqlStr, []);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getGroupBadgeList() {
        const { logger } = this;

        try {
            const sqlStr = `
            SELECT \`id\`, \`name\` FROM group_badge WHERE delsign = 0
            `;
            return await this.selectList(sqlStr, []);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getGroupFrameList() {
        const { logger } = this;

        try {
            const sqlStr = `
            SELECT \`id\`, \`name\` FROM group_frame WHERE delsign = 0
            `;
            return await this.selectList(sqlStr, []);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getGroupBannerList() {
        const { logger } = this;

        try {
            const sqlStr = `
            SELECT \`id\`, \`name\` FROM group_banner WHERE delsign = 0
            `;
            return await this.selectList(sqlStr, []);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getAvatarFrameList() {
        const { logger } = this;

        try {
            const sqlStr = `
            SELECT \`id\`, \`name\` FROM avatarframe WHERE delsign = 0
            `;
            return await this.selectList(sqlStr, []);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getNormalItemList() {
        const { logger } = this;

        try {
            const sqlStr = `
            SELECT \`no\`, \`name\`, \`role\`, \`unit\`, \`unit_num\`, \`price\`, \`datatime\`, \`img_name\`, \`nobleLevel\`, \`show\`, \`props_explain\`, \`priority\`, \`delsign\` FROM normal_item
            `;
            return await this.selectList(sqlStr, []);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getGroupPropsList() {
        const { logger } = this;

        try {
            const sqlStr = `
            SELECT \`id\`, \`name\` \`type\` FROM group_props WHERE delsign = 0
            `;
            return await this.selectList(sqlStr, []);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getGiftBagList() {
        const { logger } = this;

        try {
            const sqlStr = `
            SELECT \`no\`, \`name\` FROM gift_bag WHERE delsign = 0
            `;
            return await this.selectList(sqlStr, []);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getAllMaskshowList() {
        const { logger } = this;

        try {
            const sqlStr = `
            SELECT \`no\`, \`name\`, \`md5\`, \`prive_remark\`, \`props_explain\`, \`datatime\`, \`priority\`, \`buy\`, \`delsign\`, \`show\`, \`hot\` FROM maskshow ORDER BY priority DESC, no ASC 
            `;
            return await this.selectList(sqlStr, []);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async insertMaskshow(req: Imaskshow) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
            INSERT INTO \`maskshow\` (
                \`name\`,
                \`props_explain\`,
                \`priority\`,
                \`buy\`,
                \`delsign\`,
                \`show\`,
                \`hot\` 
            )
            VALUES
                (?,?,?,?,?,?,?);
            `;
            const result = await conn.query(sql, [
                req.name,
                req.props_explain,
                req.priority,
                req.buy,
                req.delsign,
                req.show,
                req.hot]);

            req.no = result.insertId;

            let sqlInsertItemDic = ` 
                INSERT INTO \`item_dic\` (\`item_cate_id\`, \`item_id\`, \`name\`, \`version\`, \`delsign\`, remark )
                VALUES (?,?,?,?,?,?);
            `;
            await conn.query(sqlInsertItemDic, [1040, req.no, req.name, 0, 0, req.props_explain]);

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async updateMaskshow(req: Imaskshow) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
            UPDATE \`maskshow\` 
            SET 
            \`name\` = ?,
            \`prive_remark\` = ?,
            \`props_explain\` = ?,
            \`priority\` = ?,
            \`buy\` = ?,
            \`show\` = ?,
            \`hot\` = ?,
            \`delsign\` = ? ,
            \`md5\` = ? 
            WHERE
                \`no\` = ?
            `;
            await conn.query(sql, [
                req.name,
                req.prive_remark,
                req.props_explain,
                req.priority,
                req.buy,
                req.show,
                req.hot,
                req.delsign,
                req.md5,
                req.no]);

            let sqlItemDic = `
                    UPDATE item_dic 
                    SET \`name\` = ? 
                    WHERE
                        item_id = ? 
                        AND item_cate_id = 1040 
                `
            await conn.query(sqlItemDic, [req.name, req.no]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async getRoleList() {
        const { logger } = this;

        try {
            const sqlStr = `
            SELECT * FROM role
            `;
            return await this.selectList(sqlStr, []);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async insertNormalItem(req: InormalItem) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
            INSERT INTO \`normal_item\` (\`name\`, \`role\`, \`unit\`, \`unit_num\`, \`price\`, \`nobleLevel\`, \`show\`, \`props_explain\`, \`priority\`, \`delsign\`)
            VALUES
                (?,?,?,?,?,?,?,?,?,?);
            `;
            const result = await conn.query(sql, [
                req.name,
                req.role,
                req.unit,
                req.unit_num,
                req.price,
                req.nobleLevel,
                req.show,
                req.props_explain,
                req.priority,
                req.delsign]);

            req.no = result.insertId;

            let sqlInsertItemDic = ` 
                    INSERT INTO \`item_dic\` (\`item_cate_id\`, \`item_id\`, \`name\`, \`version\`, \`delsign\`, remark )
                    VALUES (?,?,?,?,?,?);
                `;
            await conn.query(sqlInsertItemDic, [8000, req.no, req.name, 0, 0, req.props_explain]);

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async updateNormalItem(req: InormalItem) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
            UPDATE \`normal_item\` 
            SET 
            \`name\` = ?,
            \`role\` = ?,
            \`img_name\` = ?,
            \`unit\` = ?,
            \`unit_num\` = ?,
            \`price\` = ?,
            \`nobleLevel\` = ?,
            \`show\` = ?,
            \`props_explain\` = ?,
            \`priority\` = ?,
            \`delsign\` = ? 
            WHERE
                \`no\` = ?
            `;
            await conn.query(sql, [
                req.name,
                req.role,
                req.img_name,
                req.unit,
                req.unit_num,
                req.price,
                req.nobleLevel,
                req.show,
                req.props_explain,
                req.priority,
                req.delsign,
                req.no]);

            let sqlItemDic = `
                UPDATE item_dic 
                SET \`name\` = ?,
                remark = ?
                WHERE
                    item_id = ? 
                    AND item_cate_id = 8000 
            `
            await conn.query(sqlItemDic, [req.name, req.props_explain, req.no]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }


    public async getCouponList() {
        const { logger } = this;
        try {
            const sqlStr = `
            SELECT c.*, i.remark FROM 
            coupon c INNER JOIN item_dic i ON i.item_id = c.id AND i.item_cate_id = 12000
            ORDER BY id DESC
            `;
            return await this.selectList(sqlStr, []);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async createCoupon(req: any) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
            INSERT INTO coupon 
            (item_dic_id,item_id,sex,type,name,time,discount_coin_id,discount_coin_item_dic_id,discount_coin_num,delsign)
            VALUES
                (?,?,?,?,?,?,?,?,?,1)
            `;
            const result = await conn.query(sql, [
                req.item_dic_id,
                req.item_id,
                req.sex,
                req.type,
                req.name,
                req.time,
                req.discount_coin_id,
                req.discount_coin_item_dic_id,
                req.discount_coin_num]);

            req.no = result.insertId;

            let sqlInsertItemDic = ` 
                INSERT INTO \`item_dic\` (\`item_cate_id\`, \`item_id\`, \`name\`, remark,  \`version\`, \`delsign\` )
                VALUES (?,?,?,?,?,?);
            `;
            await conn.query(sqlInsertItemDic, [req.cateId, req.no, req.name, req.remark, 0, 0]);

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async updateCoupon(req: any) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
            UPDATE coupon 
            SET item_dic_id = ?,
            item_id = ?,
            sex = ?,
            name = ?,
            time = ?,
            discount_coin_id = 12,
            discount_coin_item_dic_id = 3454,
            discount_coin_num = ?
            WHERE
                id = ?
            `;
            await conn.query(sql, [
                req.item_dic_id,
                req.item_id,
                req.sex,
                req.name,
                req.time,
                req.discount_coin_num,
                req.id]);

            let sqlItemDic = `
                UPDATE item_dic 
                SET \`name\` = ? , \`remark\` = ? 
                WHERE
                    item_id = ? 
                    AND item_cate_id = 12000 
            `
            await conn.query(sqlItemDic, [req.name, req.remark, req.id]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async updateCouponImage(req: any) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
            UPDATE coupon 
            SET pic = ?
            WHERE
                id = ?
            `;
            await conn.query(sql, [
                req.pic,
                req.id]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async updateCouponDelsign(req: any) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
            UPDATE coupon 
            SET delsign = ?
            WHERE
                id = ?
            `;
            await conn.query(sql, [
                req.delsign,
                req.id]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async getFrameConditionList() {
        const { logger } = this;

        try {
            const sqlStr = `
            SELECT 
                m.*, a.\`name\`, 2010 AS cate_id, a.is_dynamic, a.level
            FROM 
                market_condition m 
            INNER JOIN avatarframe a ON m.item_id = a.id        
            ORDER BY a.id DESC
            `;
            return await this.selectList(sqlStr, []);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getAllFrameList() {
        const { logger } = this;

        try {
            const sqlStr = `
                SELECT id, \`name\` FROM avatarframe WHERE delsign = 0 ORDER BY id DESC
            `;
            return await this.selectList(sqlStr, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateFrameCondition(req: any) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            if (req.type == 0) {
                let sql = ` 
                UPDATE market_condition 
                SET 
                type = 0,
                start_time = ?,
                end_time = ?,
                time = NULL
                WHERE
                    id = ?
            `;
                await conn.query(sql, [
                    req.start_time,
                    req.end_time,
                    req.id]);
            } else {
                let sql = ` 
                UPDATE market_condition 
                SET 
                type = 1,
                start_time = NULL,
                end_time = NULL,
                time = ?
                WHERE
                    id = ?
            `;
                await conn.query(sql, [
                    req.time,
                    req.id]);
            }
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async insertFrameCondition(req: any) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            if (req.type == 0) {
                let sql = ` 
                INSERT INTO werewolf.market_condition (
                    item_id,
                    type,
                    start_time,
                    end_time,
                    time,
                    delsign 
                )
                VALUES
                    (
                        ?,
                        0,
                        ?,
                        ?,
                        NULL,
                        0
                    );
            `;
                await conn.query(sql, [
                    req.item_id,
                    req.start_time,
                    req.end_time]);
            } else {
                let sql = ` 
                INSERT INTO werewolf.market_condition (
                    item_id,
                    type,
                    start_time,
                    end_time,
                    time,
                    delsign 
                )
                VALUES
                    (
                        ?,
                        1,
                        NULL,
                        NULL,
                        ?,
                        0
                    );
            `;
                await conn.query(sql, [
                    req.item_id,
                    req.time]);
            }
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async updateFrameConditionDelsign(req: any) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
            UPDATE market_condition 
            SET delsign = ?
            WHERE
                id = ?
            `;
            await conn.query(sql, [
                req.delsign,
                req.id]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }
}
