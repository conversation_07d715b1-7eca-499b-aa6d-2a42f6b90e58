/*
 * @Description: 公会战服务类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: z<PERSON>yi
 * @Date: 2020-10-28 11:35:39
 */
import BaseMegaService from './BaseMegaService';

export default class GroupVersusService extends BaseMegaService {

    public async getSeasonList() {
        const {logger} = this;
        try {
            let sql = `SELECT DISTINCT season 
                        FROM group_versus_schedule
                        ORDER BY season`;
            let result = await this.selectList(sql, []);
            let ret: any = [];
            for (let item of result) {
                ret.push(item['season']);
            }
            return ret;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getGroupList(req) {
        const {app, ctx, logger} = this;
        try {
            let sql = `SELECT type,rounds,DATE_FORMAT(start_time,'%Y-%m-%d') AS start_time,DATE_FORMAT(start_time, '%m月%e日') AS date
                        FROM group_versus_schedule
                        WHERE season = ? AND type IN (1,3)
                        ORDER BY rounds`;
            return await this.selectList(sql, [req.season]);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getVersusList(req) {
        const {app, ctx, logger} = this;
        try {
            if (req.type != 2) {
                let sql = `SELECT gvsr.group_id AS id,g.group_name AS name ,gvsr.this_rank AS rank,
                        gvsr.this_score AS score,IFNULL(SUM(gvph.power),0) AS power
                        FROM group_versus_score_record gvsr
                        LEFT JOIN \`group\` g ON gvsr.group_id = g.id
                        LEFT JOIN group_versus_power_history gvph 
                            ON gvsr.group_id = gvph.group_id AND gvsr.season = gvph.season AND gvsr.create_date = gvph.create_date
                        WHERE gvsr.season = ? AND gvsr.create_date = ? AND gvsr.name = ?
                        GROUP BY gvsr.group_id
                        ORDER BY gvsr.id`;
                logger.info(sql);
                return await this.selectList(sql, [req.season, req.createDate, req.name]);
            } else {
                let sql = `SELECT gvsr.group_id AS id,g.group_name AS name ,gvsr.this_rank AS rank,
                        gvsr.this_score AS score,IFNULL(SUM(gvph.power),0) AS power
                        FROM group_versus_score_record gvsr
                        LEFT JOIN \`group\` g ON gvsr.group_id = g.id
                        LEFT JOIN group_versus_power_history gvph 
                            ON gvsr.group_id = gvph.group_id AND gvsr.season = gvph.season AND gvsr.create_date = gvph.create_date
                        WHERE gvsr.season = ? AND gvsr.type = ?
                        GROUP BY gvsr.group_id
                        ORDER BY gvsr.id`;
                return await this.selectList(sql, [req.season, req.type]);
            }
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getVersusDetail(req) {
        const {app, ctx, logger} = this;
        try {
            const arr1: any[] = [];
            const arr2: any[] = [];
            const arr3: any[] = [];
            const arr4: any[] = [];
            let result = {
                id: req.group_id,
                taskContent: arr1,
                taskCount: arr2,
                taskNum: arr3,
                buffContent: arr4,
                buffCount4: 0,
            };
            let sql = `SELECT td.content AS taskContent, IFNUll(tcmgac.num,0)AS taskNum, IFNUll(trmgac.num,0) AS taskCount
                        FROM task_config_group_against tcga
                        LEFT JOIN task_config_mission_group_against_challenge tcmgac ON tcga.mission_id = tcmgac.id
                        LEFT JOIN task_detail td ON td.id = tcmgac.task_id
                        LEFT JOIN task_record_mission_group_against_challenge trmgac 
                        ON tcga.group_id = trmgac.group_id AND tcga.group_versus_date = trmgac.group_versus_date AND trmgac.mission_id = tcmgac.id
                        WHERE tcga.group_id = ? AND tcga.group_versus_date = ?
                        ORDER BY tcga.is_delsign DESC,tcmgac.class`;
            let task = await this.selectList(sql, [req.group_id, req.date]);
            if (task != undefined) {
                for (let item of task) {
                    result['taskContent'].push(item['taskContent']);
                    result['taskCount'].push(item['taskCount']);
                    result['taskNum'].push(item['taskNum']);
                }
            } else {
                result['taskContent'].push('无任务');
                result['taskContent'].push('无任务');
                result['taskContent'].push('无任务');
            }
            sql = `SELECT gvb.desc AS content
                    FROM group_versus_buff_record gvbr
                    LEFT JOIN group_versus_buff gvb ON gvb.id = gvbr.buff_id
                    WHERE gvbr.group_id = ? AND gvbr.create_date = ?
                    ORDER BY gvb.type`;
            let buffContent = await this.selectList(sql, [req.group_id, req.date]);
            if (buffContent != undefined) {
                for (let i = 0; i < 3; i++) {
                    if (buffContent[i] != undefined) {
                        result['buffContent'].push(buffContent[i]['content']);
                    } else {
                        result['buffContent'].push('无buff');
                    }
                }
            } else {
                result['buffContent'].push('无buff');
                result['buffContent'].push('无buff');
                result['buffContent'].push('无buff');
            }

            sql = `SELECT COUNT(DISTINCT icoh.id) AS count
                    FROM group_user gu 
                    LEFT JOIN group_message gm ON gu.group_id = gm.group_id AND gm.type IN (1,9)
                    LEFT JOIN item_consume_other_history icoh ON gu.user_id = icoh.user_id AND icoh.item_dic_id = 1251
                    WHERE gu.group_id = ? AND  icoh.create_date = ? 
                    AND (gu.delsign = 0 OR (gu.delsign = 1 AND gm.time > ?))`;
            let bufferCount4 = await this.selectList(sql, [req.group_id, req.date, req.date]);
            if (bufferCount4 != undefined && bufferCount4['count'] != undefined) {
                result['buffCount4'] = bufferCount4['count'];
            }
            logger.info(result);
            return result;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }
}
