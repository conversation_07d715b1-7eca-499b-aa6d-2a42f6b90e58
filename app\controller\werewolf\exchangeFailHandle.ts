import BaseMegaController from "./BaseMegaController";

export default class ExchangeFailHandleController extends BaseMegaController {

    /**
     * 查询列表
     */
    public async selectFailList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.exchangeFailHandle.selectFailList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async reSend() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.exchangeFailHandle.reSend(requestBody.failId);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }


}