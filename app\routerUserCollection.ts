/**
 @name:
 @description: 收藏室
 @author: <PERSON><PERSON><PERSON><PERSON>
 @time: 2021-08-07 16:36:02
 **/
import { Router, Application } from "egg";
import { AccessRouteId, AccessRouteName } from "./model/accessRouteCof";
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

    const { controller, router } = app;

    //收藏室 查询
    router.post(`${API_VERSION}/werewolf/userCollection/getUserCollectionList`, accCtr(AccessRouteId.wolf_collect_room), controller.werewolf.userCollection.getUserCollectionList);
    //收藏室 更新
    router.post(`${API_VERSION}/werewolf/userCollection/UpDateCollectionList`, accCtr(AccessRouteId.wolf_collect_room), controller.werewolf.userCollection.updateCollectionList);

    router.post(`${API_VERSION}/werewolf/userCollection/getUserCollectionCleanRecords`, accCtr(AccessRouteId.wolf_collect_room), controller.werewolf.userCollection.getUserCollectionCleanRecords);
    router.post(`${API_VERSION}/werewolf/userCollection/insertUserCollectionCleanRecords`, accCtr(AccessRouteId.wolf_collect_room), controller.werewolf.userCollection.insertUserCollectionCleanRecords);

}

export default load
