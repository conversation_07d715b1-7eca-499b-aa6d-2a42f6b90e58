/*
 * @Description: werewolf dto 第二部分
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2019-10-11 13:49:04
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2021-10-11 17:01:10
 */

export interface IpropsConfigItem {
  no: number;
  name: string;
  type: number;
  unit: number;
  unit_num: number;
  price: number;
  reprice: number;
  timeprice: number;
  charm: number;
  prive_remark: string;
  special_content: string;
  bundle_info: string;
  props_explain: string;
  datatime: string;
  img_name: string;
  gif_name: string;
  can_use: number;
  priority: number;
  props_text: string;
  buy: number;
  single_purchase: number;
  box: number;
  gift_source: number;
  gift_type: number;
  power_value: number;
  delsign: number;
  gift_level_one_num: number;
  gift_level_two_num: number;
  gift_super: number;
  mask_buy: number;
  gift_check_num: number;
  nobleLevel: number;
  show: number;
  hot: number;
  diamond: number;
}

//更新gameConfigOpen
export interface IupdateGameConfOpenReq {
  no: number;
  name: string;
  visual_no: number;
  item_dic_id: number;
  visual_type: number;
  level: number;
  price: number;
  coin: number;
  coin_e: number;
  coin_min: number;
  coin_max: number;
  weight: number;
  buy: number;
  exchange: number;
  give: number;
  achievement: number;
  img_name: string;
  type: number;
  num: number;
  num_surplus: number;
  num_user_limit: number;
  num_user_once_limit: number;
  num_daily_limit: string;
  putaway_time: string;
  slotout_time: string;
  desc: string;
  wish: string;
  delsign: string;
  sql_text: string;
}

export interface IanimationReq {
  id: number;
  name: string;
  role: number;//1000麦克风 999 警徽 otherrole 角色
  type: number;//类别气泡字体颜色10进制
  bg: number;//0非背景版 1系统背景板 2自定义背景（role 2000时候有用）
  timer: number;//角色动画时间
  price: number;
  show: number;//0 不展示 1展示
  buy: number;//0 不可购买 1可购买
  sort: number;//排序
  createtime: string;
  datePath: string;//自定义背景板上传的日期
  serialNumber: string;//自定义背景板上传的流水号
  alert: string;//不让购买时候的提示文案
  hot: number;//热销
  remark: string;//描述
  channel: number;//渠道 0道具商城 1公会  2cp  3兑换
  isLight: number;//开场特效 0上层 1下层
  delsign: number;
  ss_buff: string;
}

export interface IframeReq {
  id: number;
  name: string;// 头像框名称
  type: number;//头像框类型,1:认证标识  2:成就类头像,3:消费类头像,4:活动类头像
  channel: number;//渠道，0：普通商城，1：兑换商城，2：公会商城，3：cp商城，4：活动
  preview: number;//是否在试衣间中展示 0否1是
  remark: string;//描述
  price: number;//价格 大于0可显示在道具商城
  createtime: string;//资源上传时间
  is_dynamic: number;//是不是动态资源
  alert: string;//不让购买时候的提示文案
  hot: number;//热销
  delsign: number;
}

export interface IgiftBagInfo {
  no: number;
  name: string;
  price: number;
  desc: string;
  img_name: string;
  num_user_limit: number;
  putaway_time: string;
  slotout_time: string;
  duration: number;
  sort: number;
  delsign: number;
}

export interface IgiftBagContentInfo {
  no: number;
  item_dic_id: number;
  name: string;
  img_name: string;
  num: number;
  direct: number;
  sql_text: string;
  describe: string;
  delsign: number;
  gift_bag_no: number;
}

export interface IdocumentResourceReq {
  document: string;
  name: string;
  extension: string;
  type: number;
  fontMd5: string;
}

export interface ItboxSeasonOpenReq {
  season: string;
  starttime: string;
  endtime: string;
}

export interface IdeductionRecoreRequest {
  playerId: string;
}
export interface IdeductionGameMatchRequest {
  gamelist: number;
}

export interface IaccountBlacklistRequest {
  playerId: string;
}
export interface AccountAbnormalRequest {
  userNo: number;
  gameNo: number;
}
export interface ScoreGroupRequest {
  userNo: string;
}

export interface ScoreGroupRequest1{
  gameId: number;
  offset: number;
  start: number;
  count: number;
}
export interface UdidRequest {
  udid: string;
}

export interface LittleOpenBlackRequest {
  gameIds: string;
  userId: number;
}

export interface DelLittleRequest{
  operator_id: number;  //操作员id
  user_id: number;  //玩家id
  del_reason: string; //删除原因

}

export interface ItboxUpdateEndTimeReq {
  seasonOri: ItboxSeasonOpenReq;
  seasonNew: ItboxSeasonOpenReq;
}

//更新排序
export interface IupdateGameConfOpenSortReq {
  newSort: GameConfSortItem[]
}

export interface GameConfSortItem {
  no: number;
  sort: number;
}

//创建gameConfigOpen
export interface IcreateGameConfOpenReq {
  no: number;
  name: string;
  visual_no: number;
  visual_type: number;
  item_dic_id: number;
  level: number;
  price: number;
  coin: number;
  coin_e: number;
  coin_min: number;
  coin_max: number;
  weight: number;
  buy: number;
  exchange: number;
  give: number;
  achievement: number;
  img_name: string;
  type: number;
  num: number;
  num_surplus: number;
  num_user_limit: number;
  num_user_once_limit: number;
  num_daily_limit: string;
  putaway_time: string;
  slotout_time: string;
  desc: string;
  wish: string;
  delsign: string;
  sql_text: string;
}

export interface IuserScoreReq {
  userList: string;
}

export interface IavatarFramePeriod {
  id: number;
  name: string;
  pic_mob: string;
  pic_pc: string;
  pic_word: string;
  num: number;
  period: number;
  price: number;
  starttime: string;
  endtime: string;
  delsign: number;
  selled: number;
}
export interface IavatarFramePeriodv2 {
  id?: number;
  show_period?: number;
  name?: string;
  pic_mob?: string;
  pic_pc?: string;
  pic_word?: string;
  price?: number;
  starttime?: string;
  endtime?: string;
  sell_num?: number;
  selled_num?: number;
  selled_show_num?: number;
  period_status?: number;
  delsign?: number;
  author?: string;
  current?: boolean;
  avatar_frame_id?: number;
  uid?: string;
  daymic_avaframe_id: number;
  show_place?: string
  real_starttime?: string
  real_endtime?: string
  puton_sort?: number
}
export interface FrameRequestPeriod {
  frameName: string;
}
export interface FrameRequestPeriodid {
  id: number;
}
export interface FrameRequestPeriodSort {
  id: number;
  show_place: string
}