/*
 * @Author: chen peng 
 * @Date: 2021-08-18 14:37:26
 * @LastEditTime: 2021-08-18 16:50:02
 * @LastEditors: Please set LastEditors
 * @Description: 集市
 * @FilePath: /MGKFHTServer/app/controller/werewolf/market.ts
 */
import { Controller } from 'egg';
import { IerrorMsg, HttpErr } from '../../model/common';
import {
    ImarketCirculateReq,
    ImarketCirculateResp,
    ImarketResp,
    ImarketDetailsResp,
    ImarketStateReq,
    IgetAvatarMapContentList,
    IupdateAvatarMapReq,
    IinsertAvatarMapReq,
    IdeleteAvatarMapReq,
    IinsertAvatarMapContentReq,
    IdeleteAvatarMapContentReq, IupdateAvatarMapContentReq, IgetGemRecordsReq
} from "../../model/wfMarket";
import BaseMegaController from './BaseMegaController';

export default class MarketContorller extends BaseMegaController {

    public async getMarketCirculateList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const resp: ImarketCirculateResp = await ctx.service.werewolf.market.getMarketCirculateList(requestBody);
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async getDailyMarketList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const resp: ImarketResp = await ctx.service.werewolf.market.getDailyMarketList(requestBody);
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    
    public async updateMarketState() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const resp: any = await ctx.service.werewolf.market.updateMarketState(requestBody);
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async getAvatarMapList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const resp: any = await ctx.service.werewolf.market.getAvatarMapList(requestBody);
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async insertAvatarMap() {
        const { ctx, logger } = this;
        const rules = {
            name: { type: 'string' },
            sort: { type: 'number' },
            delsign: { type: 'number' },
            type: { type: 'number' },
        };
        try {
            ctx.validate(rules);
            const requestBody: IinsertAvatarMapReq = ctx.request.body;
            const resp: any = await ctx.service.werewolf.market.insertAvatarMap(requestBody);
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async deleteAvatarMap() {
        const { ctx, logger } = this;
        const rules = {
            id: { type: 'number' },
        };
        try {
            ctx.validate(rules);
            const requestBody: IdeleteAvatarMapReq = ctx.request.body;
            const resp: any = await ctx.service.werewolf.market.deleteAvatarMap(requestBody);
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    public async updateAvatarMap(req: IupdateAvatarMapReq) {
        const { ctx, logger } = this;
        const rules = {
            id: { type: 'number' },
            name: { type: 'string' },
            sort: { type: 'number' },
            delsign: { type: 'number' },
            type: { type: 'number' },
        };
        try {
            ctx.validate(rules);
            const requestBody: IupdateAvatarMapReq = ctx.request.body;
            const resp: any = await ctx.service.werewolf.market.updateAvatarMap(requestBody);
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }



    public async getAvatarMapContentList() {
        const { ctx, logger } = this;
        const rules = {
            avatarframe_map_id: { type: 'number' },
        };
        try {
            ctx.validate(rules);
            const requestBody: IgetAvatarMapContentList = ctx.request.body;
            const resp: any = await ctx.service.werewolf.market.getAvatarMapContentList(requestBody);
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async insertAvatarMapContent() {
        const { ctx, logger } = this;
        const rules = {
            avatarframe_id: { type: 'number' },
            avatarframe_map_id: { type: 'number' },
            sort: { type: 'number' },
            source: { type: 'string' },
        };
        try {
            ctx.validate(rules);
            const requestBody: IinsertAvatarMapContentReq = ctx.request.body;
            const resp: any = await ctx.service.werewolf.market.insertAvatarMapContent(requestBody);
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async deleteAvatarMapContent() {
        const { ctx, logger } = this;
        const rules = {
            id: { type: 'number' },
        };
        try {
            ctx.validate(rules);
            const requestBody: IdeleteAvatarMapContentReq = ctx.request.body;
            const resp: any = await ctx.service.werewolf.market.deleteAvatarMapContent(requestBody);
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateAvatarMapContent() {
        const { ctx, logger } = this;
        const rules = {
            id: { type: 'number' },
            avatarframe_id: { type: 'number' },
            sort: { type: 'number' },
            source: { type: 'string' },
        };
        try {
            ctx.validate(rules);
            const requestBody: IupdateAvatarMapContentReq = ctx.request.body;
            const resp: any = await ctx.service.werewolf.market.updateAvatarMapContent(requestBody);
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }


    public async getGemRecords() {
        const { ctx, logger } = this;
        const rules = {
            userId: { type: 'number' },
        };
        try {
            ctx.validate(rules);
            const requestBody: IgetGemRecordsReq = ctx.request.body;
            const resp: any = await ctx.service.werewolf.market.getGemRecords(requestBody);
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
}