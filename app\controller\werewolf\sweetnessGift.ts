import BaseMegaController from "./BaseMegaController";
import {IsweetnessGiftListReq, IsweetnessGiftListResp} from "../../model/werewolf";
import {HttpErr, IerrorMsg} from "../../model/common";
import {
    ISweetnessGiftInsertParams,
    ISweetnessGiftUpdateParams,
    ISweetnessGiftDelParams
} from "../../model/sweetnessGiftDto";

export default class SweetnessGiftController extends BaseMegaController{

    public async getSweetnesskGiftList(){
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: IsweetnessGiftListReq = ctx.request.body;
            const responseBody: IsweetnessGiftListResp = await ctx.service.werewolf.sweetnessGift.getSweetnesskGiftList();
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async insert(){
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: ISweetnessGiftInsertParams = ctx.request.body;
            const responseBody = await ctx.service.werewolf.sweetnessGift.insert(requestBody);
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async update(){
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: ISweetnessGiftUpdateParams = ctx.request.body;
            const responseBody = await ctx.service.werewolf.sweetnessGift.update(requestBody);
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateAwardBg(){
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: ISweetnessGiftUpdateParams = ctx.request.body;
            const responseBody = await ctx.service.werewolf.sweetnessGift.updateAwardBg(requestBody);
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateItemDelSign(){
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: ISweetnessGiftDelParams = ctx.request.body;
            const responseBody = await ctx.service.werewolf.sweetnessGift.updateItemDelSign(requestBody);
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
}