/*
 * @Description: 了了提现外部暴露接口
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: leeou
 * @Date: 2019-02-02 11:47:41
 * @LastEditors: zhanglu
 * @LastEditTime: 2022-10-20 10:09:33
 */
import { Controller } from "egg";
import { HttpErr, IerrorMsg } from "../../model/common";
import {
  IwithDrawListRequest,
  IwithDrawListResponse,
  IwithDrawOperateRequest,
  IwithDrawOperateResponse,
} from "../../model/aiim";
import { IapiDocItem } from "../../model/werewolf";

export default class WithDrawController extends Controller {
  /**
   * 接口文档列表
   */
  public async list() {
    const { ctx, logger } = this;
    // 校验规则
    const rule = {
      uid: { type: "number" },
      start: { type: "number" },
      offset: { type: "number" },
      type: { type: "number" },
    };
    try {
      const responseBody: IapiDocItem[] = await ctx.service.dev.apiDoc.list();
      ctx.body = responseBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async getServerMonitorList() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      const responseBody = await ctx.service.dev.apiDoc.getServerMonitorList(requestBody);
      ctx.body = responseBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async getServerMonitorTypeList() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      const responseBody = await ctx.service.dev.apiDoc.getServerMonitorTypeList(requestBody);
      ctx.body = responseBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async getServerMonitorUserList() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      const responseBody = await ctx.service.dev.apiDoc.getServerMonitorUserList(requestBody);
      ctx.body = responseBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async getServerMonitorBusinessList() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      const responseBody = await ctx.service.dev.apiDoc.getServerMonitorBusinessList(requestBody);
      ctx.body = responseBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async getServerMonitorServerList() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      const responseBody = await ctx.service.dev.apiDoc.getServerMonitorServerList(requestBody);
      ctx.body = responseBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async getServerMonitorEnvList() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      const responseBody = await ctx.service.dev.apiDoc.getServerMonitorEnvList(requestBody);
      ctx.body = responseBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }
  
  public async getServerMonitorFormatList() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      const responseBody = await ctx.service.dev.apiDoc.getServerMonitorFormatList(requestBody);
      ctx.body = responseBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async getServerMonitorSystemList() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      const responseBody = await ctx.service.dev.apiDoc.getServerMonitorSystemList(requestBody);
      ctx.body = responseBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async updateServerMonitor() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      await ctx.service.dev.apiDoc.updateServerMonitor(requestBody);
      ctx.body = { err_code: HttpErr.Success, err_msg: "ok" };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async updateServerMonitorSingleData() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      await ctx.service.dev.apiDoc.updateServerMonitorSingleData(requestBody);
      ctx.body = { err_code: HttpErr.Success, err_msg: "ok" };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async updateServerMonitorDelsign() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      await ctx.service.dev.apiDoc.updateServerMonitorDelsign(requestBody);
      ctx.body = { err_code: HttpErr.Success, err_msg: "ok" };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async updateServerMonitorLogDelsign() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      await ctx.service.dev.apiDoc.updateServerMonitorLogDelsign(requestBody);
      ctx.body = { err_code: HttpErr.Success, err_msg: "ok" };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async insertServerMonitor() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      await ctx.service.dev.apiDoc.insertServerMonitor(requestBody);
      ctx.body = { err_code: HttpErr.Success, err_msg: "ok" };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async insertBusiness() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      await ctx.service.dev.apiDoc.insertBusiness(requestBody);
      ctx.body = { err_code: HttpErr.Success, err_msg: "ok" };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async insertEnv() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      await ctx.service.dev.apiDoc.insertEnv(requestBody);
      ctx.body = { err_code: HttpErr.Success, err_msg: "ok" };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async insertType() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      await ctx.service.dev.apiDoc.insertType(requestBody);
      ctx.body = { err_code: HttpErr.Success, err_msg: "ok" };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async insertUser() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      await ctx.service.dev.apiDoc.insertUser(requestBody);
      ctx.body = { err_code: HttpErr.Success, err_msg: "ok" };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async insertFormat() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      await ctx.service.dev.apiDoc.insertFormat(requestBody);
      ctx.body = { err_code: HttpErr.Success, err_msg: "ok" };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async insertServer() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      await ctx.service.dev.apiDoc.insertServer(requestBody);
      ctx.body = { err_code: HttpErr.Success, err_msg: "ok" };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async insertSystem() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      await ctx.service.dev.apiDoc.insertSystem(requestBody);
      ctx.body = { err_code: HttpErr.Success, err_msg: "ok" };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async updateBusiness() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      await ctx.service.dev.apiDoc.updateBusiness(requestBody);
      ctx.body = { err_code: HttpErr.Success, err_msg: "ok" };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async updateEnv() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      await ctx.service.dev.apiDoc.updateEnv(requestBody);
      ctx.body = { err_code: HttpErr.Success, err_msg: "ok" };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async updateType() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      await ctx.service.dev.apiDoc.updateType(requestBody);
      ctx.body = { err_code: HttpErr.Success, err_msg: "ok" };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async updateUser() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      await ctx.service.dev.apiDoc.updateUser(requestBody);
      ctx.body = { err_code: HttpErr.Success, err_msg: "ok" };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async updateFormat() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      await ctx.service.dev.apiDoc.updateFormat(requestBody);
      ctx.body = { err_code: HttpErr.Success, err_msg: "ok" };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async updateServer() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      await ctx.service.dev.apiDoc.updateServer(requestBody);
      ctx.body = { err_code: HttpErr.Success, err_msg: "ok" };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async updateSystem() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      await ctx.service.dev.apiDoc.updateSystem(requestBody);
      ctx.body = { err_code: HttpErr.Success, err_msg: "ok" };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }


  public async deleteBusiness() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      await ctx.service.dev.apiDoc.deleteBusiness(requestBody);
      ctx.body = { err_code: HttpErr.Success, err_msg: "ok" };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async deleteEnv() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      await ctx.service.dev.apiDoc.deleteEnv(requestBody);
      ctx.body = { err_code: HttpErr.Success, err_msg: "ok" };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async deleteType() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      await ctx.service.dev.apiDoc.deleteType(requestBody);
      ctx.body = { err_code: HttpErr.Success, err_msg: "ok" };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async deleteUser() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      await ctx.service.dev.apiDoc.deleteUser(requestBody);
      ctx.body = { err_code: HttpErr.Success, err_msg: "ok" };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async deleteFormat() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      await ctx.service.dev.apiDoc.deleteFormat(requestBody);
      ctx.body = { err_code: HttpErr.Success, err_msg: "ok" };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async deleteServer() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      await ctx.service.dev.apiDoc.deleteServer(requestBody);
      ctx.body = { err_code: HttpErr.Success, err_msg: "ok" };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  public async deleteSystem() {
    const { ctx, logger } = this;
    try {
      const requestBody: any = ctx.request.body;
      await ctx.service.dev.apiDoc.deleteSystem(requestBody);
      ctx.body = { err_code: HttpErr.Success, err_msg: "ok" };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }
  
}
