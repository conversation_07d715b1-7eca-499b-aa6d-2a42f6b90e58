import { Router, Application } from 'egg'
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {
    const { controller, router } = app
    router.post(`${API_VERSION}/werewolf/miningSeason/getMiningSeasonList`, controller.werewolf.miningSeason.getMiningSeasonList)
    router.post(`${API_VERSION}/werewolf/miningSeason/createMiningSeasonList`, controller.werewolf.miningSeason.createMiningSeasonList)
    router.post(`${API_VERSION}/werewolf/miningSeason/updateMiningSeasonList`, controller.werewolf.miningSeason.updateMiningSeasonList)

    router.post(`${API_VERSION}/werewolf/miningSeason/getMiningIllustratedList`, controller.werewolf.miningSeason.getMiningIllustratedList)
    router.post(`${API_VERSION}/werewolf/miningSeason/createMiningIllustratedList`, controller.werewolf.miningSeason.createMiningIllustratedList)
    router.post(`${API_VERSION}/werewolf/miningSeason/updateMiningIllustratedList`, controller.werewolf.miningSeason.updateMiningIllustratedList)


    
    
    
}

export default load
