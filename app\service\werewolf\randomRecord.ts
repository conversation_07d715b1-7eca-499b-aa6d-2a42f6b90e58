import { Service } from 'egg';
import { IrecordList } from '../../model/randomRecordCof';
import * as moment from 'moment';
import { time } from 'console';

export default class RandomRecordService extends Service {
    public async getRecordList() {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        try {
            //查询板子列表
            let sql = `SELECT
            ag.id,
            ag.game_config_id,
            ag.record_date,
            tg.\`desc\` AS gname
    
        FROM
            award_game_config_random_record AS ag
            LEFT JOIN tgameconfig_open AS tg ON tg.gameconfig_id  = ag.game_config_id 
        WHERE
            ag.is_delsign = 0
        ORDER BY
            ag.record_date Desc
        
        `;
            const recordList = await db.query(sql);
            return recordList;
        } catch (error) {
            throw error;
        }
    }
    public async getRecordBoard(req) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        try {
            let sql = `SELECT
            tgo.gameconfig_id,
            tgo.\`desc\` AS gname,
            tgo.open_time,
            tgo.close_time
        FROM
            tgameconfig_open AS tgo
        WHERE
            tgo.open_time = "0000-00-00 00:00:00"
            tgo.is_delsign = 0
        `;
            const RecordBoard = await db.query(sql);

            sql = `SELECT
            tgo.gameconfig_id,
            tgo.\`desc\` AS gname,
            tgo.open_time,
            tgo.close_time
        FROM
            tgameconfig_open AS tgo
        WHERE
            tgo.open_time != "0000-00-00 00:00:00"`;
            const RecordBoard1 = await db.query(sql);

            let newRecordBoard = RecordBoard1.filter((item, index) => {
                return moment(item.open_time).valueOf() < moment(req.weekTime).valueOf() && moment(req.weekTime).valueOf() < moment(item.close_time).valueOf()
            })
            let RecordBoardList = [...RecordBoard, ...newRecordBoard]
            return { RecordBoardList };
        } catch (error) {
            throw error;
        }
    }
    public async createRecordBoard(req) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        const gameConfigId1 = db.escape(req.board1);
        const gameConfigId2 = db.escape(req.board2);
        const create_time = db.escape(req.createTime);
        const weekTime = db.escape(req.weekTime);
        const werewolfConn = await db.beginTransaction();
        try {
            let sqlStr = `INSERT INTO 
            award_game_config_random_record (record_date, game_config_id,create_time)
        VALUES 
            (${weekTime},${gameConfigId1},${create_time}),(${weekTime},${gameConfigId2},${create_time})`
            await werewolfConn.query(sqlStr, [req.gameBoardId1]);
            await werewolfConn.commit();
        } catch (error) {
            await werewolfConn.rollback();
            throw error;
        }
    }
    public async updateRecordBoard(req) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        const conn = await db.beginTransaction();
        const id1 = db.escape(req.id1);
        const id2 = db.escape(req.id2);
        const gameBoardId1 = db.escape(req.gameBoardId1);
        const gameBoardId2 = db.escape(req.gameBoardId2)
        try {
            let sql = `UPDATE 
                    award_game_config_random_record
                SET 
                    game_config_id = ${gameBoardId1} 
                WHERE 
                    id=${id1} `
            await conn.query(sql, [req.id]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }
    public async deleteRecordBoard(req) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        const weekTime = db.escape(req.weekTime);
        try {
            let sql = `UPDATE award_game_config_random_record 
            SET 
                    is_delsign = 1
            WHERE 
                record_date = ${weekTime}`
            await db.query(sql, [weekTime]);
        } catch (error) {

            throw error;
        }
    }
}