/*
 * @Description: 无
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-06-02 16:03:58
 * @LastEditors: hammercui
 * @LastEditTime: 2020-09-01 13:12:34
 */
import { Subscription, Context } from "egg";
import moment = require('moment');
import { IoldPopularItem, InewPopularItem, IwfKingBaseItem } from '../model/wfKing';

module.exports = (app) => {

    return {
        schedule: {
            // interval: app.config.loopCoin2wInterval, // 10s
            interval: "20s", // 10s
            type: 'worker', // 指定所有的 worker 都需要执行
        },

        async task(ctx: Context) {
            await ctx.service.werewolf.coin2wTrade.checkPeriodPutOn();
        }
    }
};
