import {Service} from 'egg'
import BaseMegaService from './BaseMegaService'
import {
    ImarketCirculateReq,
    ImarketResp,
    ImarketDetailsResp,
    ImarketCirculateResp,
    ImarketStateReq,
    IgetAvatarMapContentList,
    IupdateAvatarMapReq,
    IinsertAvatarMapReq,
    IdeleteAvatarMapReq,
    IinsertAvatarMapContentReq,
    IdeleteAvatarMapContentReq, IupdateAvatarMapContentReq, IgetGemRecordsReq
} from '../../model/wfMarket'

export default class MarketService extends BaseMegaService {
    // 集市 流通数据查询
    public async getMarketCirculateList(req: ImarketCirculateReq): Promise<ImarketCirculateResp> {
        const {app, logger} = this
        try {
            // 框信息
            let sql = `SELECT item_id, remark
                       FROM item_dic
                       WHERE \`name\` = ?
            `
            const resultItem = await this.selectOne(sql, [req.name])
            // 全服总数
            // sql = `
            // SELECT COUNT(*) AS total_num FROM user_avatarframe WHERE avatarframe_id = ?
            // `;
            sql = `SELECT COUNT(*) AS total_num
                   FROM user_avatarframe
                   WHERE avatarframe_id = ?
                     AND user_id NOT IN (
                       SELECT user_id
                       FROM tuser_inner_total
                   )`
            const totalResult1 = await this.selectOne(sql, [resultItem.item_id])
            const total_num1 = totalResult1.total_num
            sql = `
                SELECT COUNT(*) AS total_num
                FROM official_avatarframe
                WHERE avatarframe_id = ?
            `
            const totalResult2 = await this.selectOne(sql, [resultItem.item_id])
            const total_num2 = totalResult2.total_num
            const total_num = total_num1 + total_num2
            // 在售总数
            sql = `
                SELECT COUNT(*) AS selling_num
                FROM market
                WHERE item_id = ?
                  AND ISNULL(buy_time)
                  AND display_start_time < NOW()
                  AND display_end_time > NOW()
            `
            const sellingResult = await this.selectOne(sql, [resultItem.item_id])
            const selling_num = sellingResult.selling_num
            // 交易成功数
            sql = `
                SELECT COUNT(*) AS deal_num
                FROM item_produce_market_history
                WHERE item_id = ?
            `
            const dealResult = await this.selectOne(sql, [resultItem.item_id])
            const deal_num = dealResult.deal_num
            // 交易成功 最高 最低 平均价
            sql = `
                SELECT MAX(coin_num) AS max_coin_num, MIN(coin_num) AS min_coin_num, AVG(coin_num) AS avg_coin_num
                FROM item_produce_market_history
                WHERE item_id = ?            `
            const dataAll = await this.selectOne(sql, [resultItem.item_id])

            const result: ImarketCirculateResp = {
                item_id: resultItem.item_id,
                remark: resultItem.remark,
                total_num: total_num,
                selling_num: selling_num,
                deal_num: deal_num,
                max: dataAll.max_coin_num,
                min: dataAll.min_coin_num,
                avg: dataAll.avg_coin_num,
                url: `http://img.53site.com/Werewolf/Frame/${resultItem.item_id}_player.png`,
            }
            return result
        } catch (error) {
            throw error
        }
    }

    // 集市 每日大盘
    public async getDailyMarketList(req: any): Promise<ImarketResp> {
        const {app, logger} = this
        try {
            let sql = `
                SELECT b.item_type,
                       a.create_date,
                       SUM(a.coin_num) AS day_diamonds,
                       COUNT(a.id)     AS day_counts
                FROM item_produce_market_history a
                         LEFT JOIN market b ON a.market_id = b.id
                WHERE a.create_date > DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                  AND a.to_user_id > 0
                GROUP BY a.create_date, b.item_type
            `
            const resultItem = await this.selectList(sql, [])
            let tranList: ImarketDetailsResp[] = new Array()
            let diamondList: ImarketDetailsResp[] = new Array()

            if (!!resultItem && resultItem.length > 0) {
                for (let i = 0, len = resultItem.length; i < len; i++) {
                    if (req.item_type == resultItem[i].item_type) {
                        tranList.push({
                            date: resultItem[i].create_date,
                            scales: resultItem[i].day_counts,
                        })
                        diamondList.push({
                            date: resultItem[i].create_date,
                            scales: resultItem[i].day_diamonds,
                        })
                    }
                }
            }

            const result = {
                tranList: tranList,
                diamondList: diamondList,
            }
            return result
        } catch (error) {
            throw error
        }
    }

    // 更新集市列表隐藏显示接口
    public async updateMarketState(req: ImarketStateReq) {
        const {app} = this
        try {
            let sql = `UPDATE \`market\`
                       SET \`delsign\`=?
                       WHERE \`id\` = ? `
            const result = await this.execSql(sql, [req.delsign, req.id])
            return result
        } catch (error) {
            throw error
        }
    }

    // 更新集市列表隐藏显示接口
    public async getAvatarMapList(req: any) {
        const {app} = this
        try {
            let sql = `
                SELECT id,
                       sort,
                       \`name\`,
                       type,
                       delsign
                FROM avatarframe_map
                ORDER BY sort ASC, id DESC
            `;
            const result = await this.execSql(sql, [])
            return result
        } catch (error) {
            throw error
        }
    }

    public async insertAvatarMap(req: IinsertAvatarMapReq) {
        const {app} = this
        try {
            let sql = `
                INSERT INTO avatarframe_map (sort, \`name\`, type, delsign)
                VALUES (?, ?, ?, ?)
            `;
            const result = await this.execSql(sql, [req.sort, req.name, req.type, req.delsign])
            return result
        } catch (error) {
            throw error
        }
    }

    public async deleteAvatarMap(req: IdeleteAvatarMapReq) {
        const {app} = this
        try {
            let sql = `
                DELETE
                FROM avatarframe_map
                WHERE id = ?
            `;
            const result = await this.execSql(sql, [req.id])
            return result
        } catch (error) {
            throw error
        }
    }

    public async updateAvatarMap(req: IupdateAvatarMapReq) {
        const {app} = this
        try {
            let sql = `
                UPDATE avatarframe_map
                SET sort    =?,
                    \`name\`=?,
                    delsign =?,
                    type=?
                WHERE id = ?
            `;
            const result = await this.execSql(sql, [req.sort, req.name, req.delsign, req.type, req.id])
            return result
        } catch (error) {
            throw error
        }
    }

    public async getAvatarMapContentList(req: IgetAvatarMapContentList) {
        const {app} = this
        try {
            let sql = `
                SELECT x.id,
                       x.avatarframe_id,
                       x.avatarframe_map_id,
                       x.sort,
                       x.source,
                       y.\`name\` avatarframe_name
                FROM avatarframe_map_content x
                         LEFT JOIN avatarframe y ON x.avatarframe_id = y.id
                WHERE x.avatarframe_map_id = ?
                ORDER BY x.sort ASC, x.id DESC
            `;
            const result = await this.execSql(sql, [req.avatarframe_map_id])
            return result
        } catch (error) {
            throw error
        }
    }

    public async insertAvatarMapContent(req: IinsertAvatarMapContentReq) {
        const {app} = this
        try {
            let sql = `
                INSERT INTO avatarframe_map_content (avatarframe_id, avatarframe_map_id, sort, source)
                VALUES (?, ?, ?, ?)
            `;
            const result = await this.execSql(sql, [req.avatarframe_id, req.avatarframe_map_id, req.sort, req.source])
            return result
        } catch (error) {
            throw error
        }
    }

    public async deleteAvatarMapContent(req: IdeleteAvatarMapContentReq) {
        const {app} = this
        try {
            let sql = `
                DELETE
                FROM avatarframe_map_content
                WHERE id = ?
            `;
            const result = await this.execSql(sql, [req.id])
            return result
        } catch (error) {
            throw error
        }
    }

    public async updateAvatarMapContent(req: IupdateAvatarMapContentReq) {
        const {app} = this
        try {
            let sql = `
                UPDATE avatarframe_map_content
                SET avatarframe_id = ?,
                    sort           = ?,
                    source         =?
                WHERE id = ?
            `;
            const result = await this.execSql(sql, [req.avatarframe_id,req.sort,req.source,req.id])
            return result
        } catch (error) {
            throw error
        }
    }

    public async getGemRecords(req: IgetGemRecordsReq) {
        const {app,ctx} = this
        try {

            const result = await ctx.service.werewolf.exchangeRpc.post('mining/gem/log', {userId: req.userId})
            return result
        } catch (error) {
            throw error
        }
    }


}
