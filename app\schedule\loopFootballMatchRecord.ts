import { Subscription, Context } from 'egg'
import moment = require('moment')

module.exports = app => {
    return {
        schedule: {
            // interval: app.config.broadcastInterval, // 10s
            interval: '10s', // 10s

            type: 'worker', // 指定所有的 worker 都需要执行
            // cron: '0 0 0 * * ?',
        },
        async task(ctx: Context) {
            // await ctx.service.werewolf.footballMatchRecord.checkFootballMatchRecord()
        },
    }
}
