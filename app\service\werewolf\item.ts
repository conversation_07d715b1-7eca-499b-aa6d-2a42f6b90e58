/*
 * @Description: 道具管理
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhangyi
 * @Date: 2020-11-03 11:35:39
 */
import BaseMegaService from './BaseMegaService';

export default class GroupVersusService extends BaseMegaService {

    public async getItemTypeList(req) {
        const {app, ctx, logger} = this;
        try {
            let sql = `SELECT id AS item_cate_id,name AS name
                    FROM item_cate
                    WHERE item_user_table like '%user%' OR item_user_table like '%cp%' 
                     AND item_user_table NOT IN ('type_props_user')
                    ORDER BY id`;
            return await this.selectList(sql, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getItemList(req) {
        const {app, ctx, logger} = this;
        try {
            let sql = `SELECT id AS item_dic_id,item_cate_id AS item_cate_id,item_id AS item_id,name AS name
                    FROM item_dic
                    ORDER BY id`;
            return await this.selectList(sql, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async createItemRecord(req) {
        const {app, ctx, logger} = this;
        const db = app.mysql.get('werewolf');
        let title = req.emailTitle;
        let content = req.emailContent;
        let mall = req.mall;
        let num = req.num;
        let mallList = req.mallItemList;
        let userList = req.userList;
        let reason = req.reason;
        let sql;
        sql = `SELECT IFNULL(max(type) + 1,1) AS type
                   FROM item_distribution_award`;
        let type = await this.selectList(sql, []);
        //开启事物
        const conn = await db.beginTransaction();
        try {
            // 奖池表
            if (mallList != null && mallList.length > 0) {
                sql = `INSERT INTO item_distribution_award(type,item_dic_id,num)
                        VALUES (${type[0]['type']},${mall},${num}),`;
                for (let item of mallList) {
                    sql += `(${type[0]['type']},${item['mall'].substring(4, item['mall'].indexOf(' 名称'))},${item['num']}),`;
                }
                sql = sql.substr(0, sql.length - 1);
                await conn.query(sql, []);
            } else {
                sql = `INSERT INTO item_distribution_award(type,item_dic_id,num)
                        VALUES (?,?,?)`;
                await conn.query(sql, [type[0]['type'],mall,num]);
            }
            // 预发送记录表
            sql = `INSERT INTO item_distribution(title,content,user_list,item_distribution_award_type,reason)
                    VALUE (?,?,?,?,?)`;
            await conn.query(sql, [title,content,userList,type[0]['type'],reason]);
            // 插入操作流水
            sql = `INSERT INTO item_distribution_log(user_id,item_distribution_id,msg)
                    VALUE (?,(SELECT LAST_INSERT_ID()),'创建一条预发送记录')`;
            await conn.query(sql, [req.uid]);
            await conn.commit();
            return true;
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            logger.error(error);
            throw error
        }
    }

    public async getRecordList(req) {
        const {app, ctx, logger} = this;
        try {
            let sql = `SELECT id.id AS id,id.title AS title,id.content AS content,id.reason AS reason,
                        id.status AS status,id.create_time AS createTime,id.user_list AS userList,
                        idc.name AS itemName,ida.num AS itemNum
                        FROM item_distribution id
                        LEFT JOIN item_distribution_award ida ON id.item_distribution_award_type = ida.type
                        LEFT JOIN item_dic idc ON ida.item_dic_id = idc.id
                        WHERE id.delsign = 0
                        AND DATE_SUB(CURDATE(), INTERVAL 30 DAY) <= DATE(id.create_time )
                        ORDER BY id DESC`;
            return await this.selectList(sql, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getItem(req) {
        const {app, ctx, logger} = this;
        try {
            let sql = `SELECT id.id AS id,id.title AS emailTitle,id.content AS emailContent,
                        id.status AS status,id.user_list AS userList,id.reason AS reason,
                        idc.id AS mall,idc.item_cate_id AS cate,ida.num AS num,idc.name AS name
                        FROM item_distribution id
                        LEFT JOIN item_distribution_award ida ON id.item_distribution_award_type = ida.type
                        LEFT JOIN item_dic idc ON ida.item_dic_id = idc.id
                        WHERE id.id = ${req.id} AND id.delsign = 0
                        ORDER BY id`;
            let result = await this.selectList(sql, []);
            let item = {};
            let mallItemList: any = [];
            for (let i = 1; i < result.length; i++) {
                let mallItem = {};
                mallItem['fieldKey'] = i - 1;
                mallItem['key'] = i - 1;
                mallItem['name'] = i - 1;
                mallItem['isListField'] = true;
                mallItem['cate'] = result[i]['cate'];
                mallItem['mall'] = 'ID: ' + result[i]['mall'] + ' 名称: ' + result[i]['name'];
                mallItem['num'] = result[i]['num'];
                mallItemList[i - 1] = mallItem;
            }
            item['id'] = result[0]['id'];
            item['emailTitle'] = result[0]['emailTitle'];
            item['emailContent'] = result[0]['emailContent'];
            item['emailContent'] = result[0]['emailContent'];
            item['cate'] = result[0]['cate'];
            item['mall'] = 'ID: ' + result[0]['mall'] + ' 名称: ' + result[0]['name'];
            item['num'] = result[0]['num'];
            item['userList'] = result[0]['userList'];
            item['reason'] = result[0]['reason'];
            item['mallItemList'] = mallItemList;
            return item;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async deleteItemRecord(req) {
        const {app, ctx, logger} = this;
        const db = app.mysql.get('werewolf');
        let sql = `SELECT item_distribution_award_type AS type
                        FROM item_distribution
                        WHERE id = ${req.id} AND delsign = 0`;
        let type = await db.query(sql, []);
        //开启事物
        const conn = await db.beginTransaction();
        try {
            sql = `UPDATE item_distribution SET delsign = 1 WHERE id = ?`;
            await conn.query(sql, [req.id]);
            sql = `UPDATE item_distribution_award SET delsign = 1 WHERE type = ?`;
            await conn.query(sql, [type[0]['type']]);
            // 插入操作流水
            sql = `INSERT INTO item_distribution_log(user_id,item_distribution_id,msg)
                    VALUE (?,?,'删除一条预发送记录')`;
            await conn.query(sql, [req.uid,req.id]);
            await conn.commit();
            return true;
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            logger.error(error);
            throw error
        }
    }


    public async sendReward(req) {
        const {app, ctx, logger} = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        let sql = `SELECT  id.title AS emailTitle,id.content AS emailContent,
                        id.user_list AS userList, ida.item_dic_id AS item_dic_id,ida.num AS num
                        FROM item_distribution id
                        LEFT JOIN item_distribution_award ida ON id.item_distribution_award_type = ida.type
                        WHERE id.id = ? AND id.delsign = 0
                        ORDER BY id.id`;
        let result = await this.selectList(sql, [req.id]);
        let userList = result[0]['userList'].split(",");
        let itemDicList: any = [];
        let type = 0;
        for (let item of result) {
            if (item.num < 0) {
                type = 1;
                item.num = -item.num;
            }
            itemDicList.push({itemDicId: item.item_dic_id, num: item.num});
        }
        logger.info(itemDicList);
        let requestBody: any = [];
        for (let item of userList) {
            let val = {};
            val['userId'] = item;
            val['itemDicList'] = itemDicList;
            requestBody.push(val);
        }
        let jsonStr = JSON.stringify({
            negative: 1, // 允许扣成负数
            type: type,
            userList: requestBody
        });
        logger.info("-----"+jsonStr+"-----");
        let res = await ctx.curl(app.config.WerewolfExchange_BETA + 'item/obtain', {
            method: 'POST',
            // 通过 contentType 告诉 HttpClient 以 JSON 格式发送
            headers: {
                "content-type": "application/json",
            },
            data: jsonStr,
            timeout: 5000, // 3 秒超时
        });
        const conn = await db.beginTransaction();
        try {
            if (result[0]['emailTitle'] != null) {
                sql = `INSERT INTO tnotcie(userNo,u_state,n_time,u_time,title,content,prop_num,note,isnew)
                        VALUES`;
                for (let item of userList) {
                    sql += `(${item},1,NOW(),NOW(),'${result[0]['emailTitle']}','${result[0]['emailContent']}',0,1,1),`;
                }
                sql = sql.substr(0, sql.length - 1);
                await conn.query(sql, []);
            }
            sql = `UPDATE item_distribution SET status = 1 WHERE id = ${req.id}`;
            await conn.query(sql, []);
            // 插入操作流水
            sql = `INSERT INTO item_distribution_log(user_id,item_distribution_id,msg)
                    VALUE (?,?,'发送一条记录')`;
            await conn.query(sql, [req.uid,req.id]);
            await conn.commit();
            return true;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getUserList(req) {
        const {app, ctx, logger} = this;
        try {
            let sql = `SELECT no AS id ,nickname AS name
                        FROM tuser
                        WHERE no IN (?)`;
            return await this.selectList(sql, [req]);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async checkUserList(req) {
        const {app, ctx, logger} = this;
        try {
            let userList = req['userList'].split(",");
            let result: any = [];
            for (const userId of userList) {
                let sql = `SELECT no AS id
                        FROM tuser
                        WHERE no = ?`;
                let no =  await this.selectOne(sql, [userId]);
                if (no == undefined) {
                    result.push(userId);
                }
            }
            let response: {};
            if (result.length == 0) {
                response = {
                    status: 0
                }
            }else {
                response = {
                    status: 1,
                    data: result.toString() + '不存在'
                };
            }
            return response;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

}
