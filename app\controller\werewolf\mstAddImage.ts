import BaseMegaController from "./BaseMegaController";
import {InewPlayerRobotListReq, InewPlayerRobotListResp} from "../../model/werewolf";
import {HttpErr, IerrorMsg} from "../../model/common";
import {
    MstAddImageSearchLikeNameParams,
    MstAddImageSearchParams,
    MstAddImageInsertInfoParams
} from "../../model/mstAddImageDto";

export default class MstAddImageController extends BaseMegaController{

    public async searchFromStableList(){
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: MstAddImageSearchParams = ctx.request.body;
            const responseBody = await ctx.service.werewolf.mstAddImage.searchFromStableList(requestBody);
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async getLikeModelName(){
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: MstAddImageSearchLikeNameParams = ctx.request.body;
            const responseBody = await ctx.service.werewolf.mstAddImage.getLikeModelName(requestBody);
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async getVaeList(){
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
        };
        try {
            // 校验
            ctx.validate(rule);
            const responseBody = await ctx.service.werewolf.mstAddImage.getVaeList();
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async getSamplerList(){
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
        };
        try {
            // 校验
            ctx.validate(rule);
            const responseBody = await ctx.service.werewolf.mstAddImage.getSamplerList();
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async insertMstImageInfo(){
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: MstAddImageInsertInfoParams = ctx.request.body;
            const responseBody = await ctx.service.werewolf.mstAddImage.insertMstImageInfo(requestBody);
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    // public async updateMstImageInfo(){
    //     const { ctx, logger, app } = this;
    //     // 校验规则
    //     const rule = {
    //     };
    //     try {
    //         // 校验
    //         ctx.validate(rule);
    //         const requestBody: MstAddImageInsertInfoParams = ctx.request.body;
    //         const responseBody = await ctx.service.werewolf.mstAddImage.updateMstImageInfo(requestBody);
    //         ctx.body = responseBody;
    //         ctx.status = HttpErr.Success;
    //     } catch (e) {
    //         logger.error(e);
    //         const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
    //         ctx.body = err;
    //         ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    //     }
    // }

}