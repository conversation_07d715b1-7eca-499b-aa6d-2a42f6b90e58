import BaseMegaController from "./BaseMegaController";
import {HttpErr, IerrorMsg} from "../../model/common";

export default class UserController extends BaseMegaController {
    public async removePwdLimit() {
        const {ctx, logger, app} = this;
        // 校验规则
        const rule = {userId: {type: 'string'}};
        try {
            // 校验
            ctx.validate(rule);
            const requestBody = ctx.request.body
            const responseBody = await ctx.service.werewolf.user.removePwdLimit(requestBody);
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = {err_code: HttpErr.BadRequest, err_msg: '参数有误'};
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
}