/*
 * @Description: 玩家资产
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2018-11-26 16:30:09
 * @LastEditors: hammercui
 * @LastEditTime: 2020-04-13 14:58:27
 */

import { Service } from 'egg';
export default class PropsService extends Service {
	/**
     * @name: 
     * @msg:查询玩家全部资产 
     * @param playerId{number} 
     * @return: 
     */
	public async all(playerId: number) {
		const { app,logger } = this;
		try {
			const werewolf = app.mysql.get('werewolf');
			const results = await werewolf.query(
				'SELECT tuser_props.*, tuser.nickname, tuser.headicon, tuser.username,tuser.binding_phone, tuser.password FROM tuser_props ' +
				'LEFT JOIN tuser ON tuser.`no` = tuser_props.userno WHERE userno = ' +
				werewolf.escape(playerId)
			);
			if (!results || results.length < 1) {
				return {};
			}
			const pattern = /0?(13|14|15|18|17)[0-9]{9}/;
			logger.debug("username",results[0].username);
			
			if (pattern.test(results[0].username)) {
				results[0].binding_phone = results[0].username;
			}
			if (!!results && results.length > 0) {
				return results[0];
			} else {
				return {};
			}
		} catch (err) {
			throw err;
		}
	}
}
