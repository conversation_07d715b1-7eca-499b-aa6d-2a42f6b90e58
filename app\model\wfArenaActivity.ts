/*
 * @Author: your name
 * @Date: 2021-07-29 13:48:49
 * @LastEditTime: 2021-07-29 16:40:47
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /MGKFHTServer/app/model/arenaActivity.ts
 */
// 竞技场活动模型

export interface IarenaActivityList {
    id: number;
    user_no: number;//
    create_time: string;//对局时间
    win: string;//胜负
    escape: number;//逃跑
    score: number;//分数
    valid: number; //有效局
    role_img: string;//图片
}

export interface IarenaUserInfo {
    id: number;
    user_id: number;//
    current_level: number;//当前等级
    win: number;//胜
    lose: number;//负
    score: number;//分数
    mvp: number; //mvp数
    nickname: string;//名字
}

export interface IarenaUserInfoReq {
    user_id: number;//
}

export interface IarenaResp {
    info: IarenaUserInfo;
    list: IarenaActivityList[];
}