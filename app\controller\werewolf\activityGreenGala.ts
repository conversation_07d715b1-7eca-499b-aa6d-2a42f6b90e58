import BaseMegaController from './BaseMegaController'
import { ActivityStateOperRes } from '../../model/werewolf'
import { HttpErr, IerrorMsg } from '../../model/common'

export default class ActivityGreenGalaController extends BaseMegaController {

    public async findTeamConfig() {
        const { ctx, logger } = this
        const rules = {

        }
        try {
            ctx.validate(rules)
            const requestBody = ctx.request.body
            const body = await ctx.service.werewolf.activityGreenGala.findTeamConfig(requestBody)
            ctx.body = body
            ctx.status = HttpErr.Success
        } catch (e) {
            logger.error(e)
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' }
            ctx.body = err
            ctx.status = HttpErr.BadRequest //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async findItems() {
        const { ctx, logger } = this
        const rules = {
            columnId: { type: 'number' },
            date: { type: 'string', required: false },
        }
        try {
            ctx.validate(rules)
            const requestBody = ctx.request.body
            const body = await ctx.service.werewolf.activityGreenGala.findItems(requestBody)
            ctx.body = body
            ctx.status = HttpErr.Success
        } catch (e) {
            logger.error(e)
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' }
            ctx.body = err
            ctx.status = HttpErr.BadRequest //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateItem() {
        const { ctx, logger } = this
        const rules = {
            score_a: { type: 'number' },
            score_b: { type: 'number' },
            team_id_a: { type: 'number' },
            team_id_b: { type: 'number' },
            game_state: {type:'number'},
            mid: { type: 'string' },
        }
        try {
            ctx.validate(rules)
            const requestBody = ctx.request.body
            const body = await ctx.service.werewolf.activityGreenGala.updateItem(requestBody)
            ctx.body = body
            ctx.status = HttpErr.Success
        } catch (e) {
            logger.error(e)
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' }
            ctx.body = err
            ctx.status = HttpErr.BadRequest //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateResultState() {
        const { ctx, logger } = this
        const rules = {
            result_state: { type: 'number' },
            id: { type: 'number' },
        }
        try {
            ctx.validate(rules)
            const requestBody = ctx.request.body
            const body = await ctx.service.werewolf.activityGreenGala.updateResultState(requestBody)
            ctx.body = body
            ctx.status = HttpErr.Success
        } catch (e) {
            logger.error(e)
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' }
            ctx.body = err
            ctx.status = HttpErr.BadRequest //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async findDateList() {
        const { ctx, logger } = this
        const rules = {
            columnId: { type: 'number' },
        }
        try {
            ctx.validate(rules)
            const requestBody = ctx.request.body
            const body = await ctx.service.werewolf.activityGreenGala.findDateList(requestBody)
            ctx.body = body
            ctx.status = HttpErr.Success
        } catch (e) {
            logger.error(e)
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' }
            ctx.body = err
            ctx.status = HttpErr.BadRequest //请求参数错误 401Unauthorized未鉴权
        }
    }
}
