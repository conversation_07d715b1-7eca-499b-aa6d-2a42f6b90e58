/*
 * @Description: 商城道具管理-控制类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: z<PERSON><PERSON>
 * @Date: 2020-04-24 13:00:00
 * @LastEditors: zhang<PERSON>
 * @LastEditTime: 2021-04-19 17:29:45
 */
import { Controller } from 'egg';
import BaseMegaController from './BaseMegaController';
import { IerrorMsg, HttpErr } from '../../model/common';

export default class LotteryBoxController extends BaseMegaController {

    public async getActivityList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.lotteryBox.getActivityList();
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getAwardList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.lotteryBox.getAwardList();
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }
    
    public async updateActivity() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.lotteryBox.updateActivity(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async insertActivity() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.lotteryBox.insertActivity(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async insertLotteryBoxAward() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.lotteryBox.insertLotteryBoxAward(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async updateLotteryBoxAward() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.lotteryBox.updateLotteryBoxAward(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async getBoxList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.lotteryBox.getBoxList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err); }
    }

    public async insertLotteryBox() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.lotteryBox.insertLotteryBox(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async updateLotteryBox() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.lotteryBox.updateLotteryBox(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async updateLotteryBoxDelsign() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.lotteryBox.updateLotteryBoxDelsign(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async updateLotteryBoxImg() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.lotteryBox.updateLotteryBoxImg(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async getCoinList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.lotteryBox.getCoinList();
            this.respSuccData(list)
        } catch (err) { this.respFail(err); }
    }

    public async getRuleList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.lotteryBox.getRuleList();
            this.respSuccData(list)
        } catch (err) { this.respFail(err); }
    }

    public async updateLotteryBoxRule() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.lotteryBox.updateLotteryBoxRule(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }
    
    public async insertLotteryBoxRule() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.lotteryBox.insertLotteryBoxRule(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async getActivityRuleList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.lotteryBox.getActivityRuleList();
            this.respSuccData(list)
        } catch (err) { this.respFail(err); }
    }

    public async insertActivityRule() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.lotteryBox.insertActivityRule(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async updateActivityRule() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.lotteryBox.updateActivityRule(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async getBoxRuleList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.lotteryBox.getBoxRuleList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err); }
    }

    public async insertBoxRule() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.lotteryBox.insertBoxRule(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async updateBoxRule() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.lotteryBox.updateBoxRule(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async updateBoxRuleDelsign() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.lotteryBox.updateBoxRuleDelsign(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async getBoxActivityList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.lotteryBox.getBoxActivityList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err); }
    }

    public async getBoxDiscountRuleList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.lotteryBox.getBoxDiscountRuleList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err); }
    }

    public async updateBoxDiscountRule() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.lotteryBox.updateBoxDiscountRule(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async updateBoxDiscountRuleDelsign() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.lotteryBox.updateBoxDiscountRuleDelsign(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async updateActivityRuleDelsign() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.lotteryBox.updateActivityRuleDelsign(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async getLeadBoxList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.lotteryBox.getLeadBoxList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err); }
    }
    
    public async insertLeadBoxList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.lotteryBox.insertLeadBoxList(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }
    
    public async getBoxSelectImgList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.lotteryBox.getBoxSelectImgList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err); }
    }
}
