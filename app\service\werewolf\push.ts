/*
 * @Description: 统一推送模块
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-03-16 11:56:23
 * @LastEditors: hammercui
 * @LastEditTime: 2020-09-29 11:33:20
 */
import { Service } from 'egg';
import { PushMessageResquest } from '../../model/werewolf';
import { Ipayload } from '../../model/common';
import { IpushRecord, PushReceiveType, IpushShortInfo, PushState } from '../../model/werewolfPush';

export default class PushService extends Service {

    private pushHashKey = "console_push_list"; //推送消息hashtable key

    public async checkPush() {
        const { app, ctx, logger } = this;
        try {
            //查询hask所有字段和值
            const result = await app.redis.get("tokenRedis").hgetall(this.pushHashKey);
            if (result && result.length > 0) {
                let map = JSON.parse(result);
                logger.debug(result);
                //遍历hash表，查询可发送push
                // tslint:disable-next-line:forin
                for (let key in map) {
                    let pushId = parseInt(key, 10);//推送id
                    let infoJson = map[key];//时间戳结构体
                    let newPushShortInfo: IpushShortInfo = JSON.parse(infoJson)
                    if (newPushShortInfo.state == 0 && Date.now() >= newPushShortInfo.timestamp) {
                        //更新redis状态
                        newPushShortInfo.state = PushState.Pusing;
                        await this.updateHashVale(pushId, newPushShortInfo)
                        // 发送推送 
                        await this.sendOnePush(pushId);
                    }
                }
            }
        } catch (error) {
            throw error;
        }
    }

    //删除指定字段
    public async delHashVale(valueKey) {
        const { app, ctx, logger } = this;
        try {
            await app.redis.get("tokenRedis").hdel(this.pushHashKey, valueKey);
        } catch (error) {
            throw error;
        }
    }
    /**
     * @name: 更新redis中推送状态
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async updateHashVale(valueKey, newPushShortInfo: IpushShortInfo) {
        const { app, ctx, logger } = this;
        try {
            await app.redis.get("tokenRedis").hset(this.pushHashKey, valueKey, JSON.stringify(newPushShortInfo));
        } catch (error) {
            throw error;
        }
    }

    //发送一个推送
    public async sendOnePush(pushId: number) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get('werewolf');
        try {
            //todo 查询push详情
            const sqlStr = "SELECT * FROM `push_record` WHERE id = 1 AND push_state = 0;"
            const result: IpushRecord[] = await db.query(sqlStr);
            if (result.length > 0) {
                for (const pushRecord of result) {
                    if (pushRecord) {
                        switch (pushRecord.receive_type) {
                            // 发送3个月登陆过的用户(默认)
                            case PushReceiveType.Old3MLogin:
                                await this.sendPushForOld3M(pushId);
                                break;
                        }
                    }
                }
            }
        } catch (error) {
            throw error;
        }
    }

    // 3个月登陆过的用户(默认)
    public async sendPushForOld3M(pushId: number) {
        const { app, ctx } = this;
        const db = app.mysql.get('werewolf');
        try {
            // todo 查询用户总数
            // 每次查询1000人
        } catch (error) {
            throw error;
        }
    }

    /**
     * @name: 查询三天前新注册用户一直没开局的
     */
    public async searchNewUserList(request: PushMessageResquest) {
        const { app, ctx } = this;
        const db = app.mysql.get('werewolf');
        const content = db.escape(request.content);
        const sql = "SELECT a.deviceToken, a.isAndroid FROM `tapns` a LEFT JOIN tuser b ON a.`user` = b.`no` WHERE b.createtime > DATE_SUB(NOW(),INTERVAL 3 DAY)" +
            "AND (b.win + b.lose) = 0 AND a.deviceToken != '' AND b.delsign = 0 GROUP BY a.deviceToken"

        try {
            const result = await db.query(sql);
            if (result.length > 0) {
                let res = await ctx.curl('http://coder.53site.com/Werewolf/DailyMission/PHPCode/api/Push/writeFile.php', {
                    method: 'POST',
                    contentType: 'json',
                    data: { token: result, content: content },
                    dataType: 'text', // 自动解析 JSON response
                    timeout: 3000, // 3 秒超时
                });
                const data = JSON.parse(res.data)
                if (data.code == 1) {
                    const payload: Ipayload = ctx.helper.exPayload;
                    await this.insertPushMsgRecord({
                        admin_id: payload.uid,
                        content: content,
                        receive_type: 2
                    });
                } else {
                    throw new Error('php执行失败')
                }
            } else {
                throw new Error('用户数量为0')
            }
        } catch (error) {
            throw error;
        }
    }

    /**
     * @name: 老用户沉默15天未登录的（老用户标准：开局10局以上）
     */
    public async searchOldUserFifthDays(request: PushMessageResquest) {
        const { app, ctx } = this;
        const db = app.mysql.get('werewolf');
        const content = db.escape(request.content);
        const sql = "SELECT a.deviceToken, a.isAndroid FROM `tapns` a LEFT JOIN tuser b ON a.`user` = b.`no`  WHERE b.logintime > DATE_SUB(NOW(),INTERVAL 3 MONTH)" +
            "AND b.logintime < DATE_SUB(NOW(),INTERVAL 15 DAY) AND (b.win + b.lose) > 10 AND a.deviceToken != '' AND b.delsign = 0 GROUP BY a.deviceToken ";

        try {
            const result = await db.query(sql);
            if (result.length > 0) {
                let res = await ctx.curl('http://coder.53site.com/Werewolf/DailyMission/PHPCode/api/Push/writeFile.php', {
                    method: 'POST',
                    // 通过 contentType 告诉 HttpClient 以 JSON 格式发送
                    contentType: 'json',
                    data: { token: result, content: content },
                    dataType: 'text', // 自动解析 JSON response
                    timeout: 3000, // 3 秒超时
                });
                const data = JSON.parse(res.data)
                if (data.code == 1) {
                    const payload: Ipayload = ctx.helper.exPayload;
                    await this.insertPushMsgRecord({
                        admin_id: payload.uid,
                        content: content,
                        receive_type: 3
                    });
                } else {
                    throw new Error('php执行失败')
                }
            } else {
                throw new Error('用户数量为0')
            }
        } catch (error) {
            throw error;
        }
    }
    /**
     * @name: 老用户沉默30天未登录的（老用户标准：开局10局以上）
     */
    public async searchOldUserThirtyDays(request: PushMessageResquest) {
        const { app, ctx } = this;
        const db = app.mysql.get('werewolf');
        const content = db.escape(request.content);
        const sql = "SELECT a.deviceToken, a.isAndroid FROM `tapns` a LEFT JOIN tuser b ON a.`user` = b.`no`  WHERE b.logintime > DATE_SUB(NOW(),INTERVAL 3 MONTH)" +
            "AND b.logintime < DATE_SUB(NOW(),INTERVAL 30 DAY) AND (b.win + b.lose) > 10 AND a.deviceToken != '' AND b.delsign = 0 GROUP BY a.deviceToken";

        try {
            const result = await db.query(sql);
            if (result.length > 0) {
                let res = await ctx.curl('http://coder.53site.com/Werewolf/DailyMission/PHPCode/api/Push/writeFile.php', {
                    method: 'POST',
                    // 通过 contentType 告诉 HttpClient 以 JSON 格式发送
                    contentType: 'json',
                    data: { token: result, content: content },
                    dataType: 'text', // 自动解析 JSON response
                    timeout: 3000, // 3 秒超时
                });
                const data = JSON.parse(res.data)
                if (data.code == 1) {
                    const payload: Ipayload = ctx.helper.exPayload;
                    await this.insertPushMsgRecord({
                        admin_id: payload.uid,
                        content: content,
                        receive_type: 4
                    });
                } else {
                    throw new Error('php执行失败')
                }
            } else {
                throw new Error('用户数量为0')
            }
        } catch (error) {
            throw error;
        }
    }

    /**
     * @name: 给某个用户发送推送
     */
    public async sendMsgToSomebody(request: PushMessageResquest) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get('werewolf');
        const userIdList = request.userId.toString();
        const content = db.escape(request.content);
        const sql = "SELECT a.deviceToken, a.isAndroid FROM `tapns` a LEFT JOIN tuser b ON a.`user` = b.`no` WHERE  a.`user` IN( " + userIdList + ") AND a.deviceToken != '' AND b.delsign = 0 GROUP BY a.deviceToken";
        try {
            const result = await db.query(sql);
            if (result.length > 0) {
                let res = await ctx.curl('http://coder.53site.com/Werewolf/DailyMission/PHPCode/api/Push/writeFile.php', {
                    method: 'POST',
                    // 通过 contentType 告诉 HttpClient 以 JSON 格式发送
                    contentType: 'json',
                    data: { token: result, content: content },
                    dataType: 'text', // 自动解析 JSON response
                    timeout: 3000, // 3 秒超时
                });
                logger.info(result, content, res.data)
                const data = JSON.parse(res.data)
                if (data.code == 1) {
                    const payload: Ipayload = ctx.helper.exPayload;
                    await this.insertPushMsgRecord({
                        admin_id: payload.uid,
                        content: content,
                        receive: userIdList,
                        receive_type: 1
                    });
                } else {
                    throw new Error('php执行失败')
                }

            } else {
                throw new Error('用户数量为0')
            }
        } catch (error) {
            throw error;
        }
    }

    /**
     * @name: 插入操作记录 
     * */
    private async insertPushMsgRecord(row: any) {
        const { app, ctx, logger } = this;
        const manager = app.mysql.get("manager");
        try {
            await manager.insert("wf_admin_push", row);
        } catch (error) {
            throw new Error('推送成功,插入操作表失败，此条推送无记录');
        }
    }
}
