
import BaseMegaService from "../../service/werewolf/BaseMegaService";
import {createResponseBody} from "../../util/responseUtils";
import {HttpErr} from "../../model/common";

export default class UserService extends BaseMegaService {
    public async removePwdLimit(req) {
        const {app, ctx, logger} = this;
        const db = app.mysql.get('werewolf')

        try {

            let sql0 = `
                SELECT 1
                FROM tuser
                WHERE \`no\` = ? LIMIT 1
            `
            const arr = await db.query(sql0, [req.userId])

            if (arr.length < 1) {
                return createResponseBody(HttpErr.PlayerStatusErr, '用户不存在！')
            }

            const sql = `INSERT INTO \`user_need_update_pwd\` (\`user_id\`, \`state\`)
                         VALUES (?, '99') ON DUPLICATE KEY
            UPDATE \`state\`='99'`
            const res = await db.query(sql, [req.userId])
            return createResponseBody(HttpErr.Success, '操作成功！')

        } catch (error) {
            logger.error(error);
            throw error
        }
    }
}