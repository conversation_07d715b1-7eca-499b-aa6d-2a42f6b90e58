// 飞书控制器单元测试
import 'mocha';
import * as assert from 'assert';
import { app } from 'egg-mock/bootstrap';
import { HttpErr } from '../../../app/model/common';

describe('test/app/controller/feiShu.test.ts', () => {

  before(async () => {
    await app.ready();
  });

  describe('POST /api/werewolf/feiShu/sendMessage', () => {

    it('should send message successfully', async () => {

      const requestData = {
        title: '运营活动-世界红包警告',
        content: '【运营活动-世界红包警告】test'
      };

      const result = await app.httpRequest()
        .post('/api/feiShu/sendMessage')
        .send(requestData)
        .expect(200);

      assert.strictEqual(result.body.success, true);
      assert.strictEqual(result.body.message, '消息发送成功');
    });
    
  });
});