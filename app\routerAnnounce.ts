/*
 * @Description: 公告
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-03-24 11:50:11
 * @LastEditors: hammercui
 * @LastEditTime: 2020-08-08 09:54:41
 */
import { Router, Application } from "egg";
import { AccessRouteId } from './model/accessRouteCof';
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

    const { controller, router } = app;
    //【应用管理】首页弹窗
    //列表
    router.post(`${API_VERSION}/werewolf/announce/list`, accCtr(AccessRouteId.wolf_announce), controller.werewolf.announce.list)
    //新建
    router.post(`${API_VERSION}/werewolf/announce/create`, accCtr(AccessRouteId.wolf_announce), controller.werewolf.announce.create)
    //更新上下架状态
    router.post(`${API_VERSION}/werewolf/announce/updateState`, accCtr(AccessRouteId.wolf_announce), controller.werewolf.announce.updateState)
    //编辑
    router.post(`${API_VERSION}/werewolf/announce/edit`, accCtr(AccessRouteId.wolf_announce), controller.werewolf.announce.edit)
    //删除
    router.post(`${API_VERSION}/werewolf/announce/del`, accCtr(AccessRouteId.wolf_announce), controller.werewolf.announce.del)
    //上移排序
    router.post(`${API_VERSION}/werewolf/announce/upSort`, accCtr(AccessRouteId.wolf_announce), controller.werewolf.announce.upSort)
    //下移排序
    router.post(`${API_VERSION}/werewolf/announce/downSort`, accCtr(AccessRouteId.wolf_announce), controller.werewolf.announce.downSort)
}

export default load
