import {Controller} from "egg";
import {HttpErr, IerrorMsg} from "../../model/common";

/**
 @name:
 @description:
 @author: <PERSON><PERSON><PERSON><PERSON>
 @time: 2021-08-18 11:12:47
 **/

export default class OfficerAccoutContorller extends Controller {

    public async getOfficerAccoutDesc() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const resp = await ctx.service.werewolf.officerAccoutDes.getOfficerAccoutDes(requestBody);
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
}
