import BaseMegaController from "./BaseMegaController";
import {InewPlayerRobotListReq, InewPlayerRobotListResp} from "../../model/werewolf";
import {HttpErr, IerrorMsg} from "../../model/common";
import {
    InewPlayerRobotInsertParams,
    InewPlayerRobotUpdateParams,
    InewPlayerRobotUpdateTypeParams,
    InewPlayerRobotSearchParams
} from "../../model/newPlayerRobotDto";

export default class IllustratedBookController extends BaseMegaController{
    public async getList(){
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: InewPlayerRobotListReq = ctx.request.body;
            const responseBody: InewPlayerRobotListResp = await ctx.service.werewolf.illustratedBook.getList();
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    public async fetchFilterItems(){
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: any = ctx.request.body;
            const responseBody: InewPlayerRobotListResp = await ctx.service.werewolf.illustratedBook.fetchFilterItems();
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async searchFromList(){
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: InewPlayerRobotSearchParams = ctx.request.body;
            const responseBody = await ctx.service.werewolf.illustratedBook.searchFromList(requestBody);
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async insert(){
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: InewPlayerRobotInsertParams = ctx.request.body;
            const responseBody = await ctx.service.werewolf.illustratedBook.insert(requestBody);
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async update(){
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: InewPlayerRobotUpdateParams = ctx.request.body;
            const responseBody = await ctx.service.werewolf.illustratedBook.update(requestBody);
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateType(){
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: InewPlayerRobotUpdateTypeParams = ctx.request.body;
            const responseBody = await ctx.service.werewolf.illustratedBook.updateType(requestBody);
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    

    public async updateItemDelSign(){
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: any = ctx.request.body;
            const responseBody = await ctx.service.werewolf.illustratedBook.updateItemDelSign(requestBody);
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async insertPopItem(){
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: any = ctx.request.body;
            const responseBody = await ctx.service.werewolf.illustratedBook.insertPopItem(requestBody);
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    public async updateItemPop(){
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: any = ctx.request.body;
            const responseBody = await ctx.service.werewolf.illustratedBook.updateItemPop(requestBody);
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
}