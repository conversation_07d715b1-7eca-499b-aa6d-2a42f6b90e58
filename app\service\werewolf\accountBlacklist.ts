/*
 * @Description: 小号黑名单查询
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: 赵宝强
 * @Date: 2020-11-09 17:52
 * @LastEditors: 赵宝强
 * @LastEditTime:
 */
import {DelLittleRequest, IaccountBlacklistRequest} from "../../model/werewolf2";
import BaseMegaService from './BaseMegaService';
import {LittleBlacklist} from "../../model/accountBlacklist";
export default class AccountBlacklistService extends BaseMegaService {

    // 1 小号黑名单
    public async getLittleBlacklist(req: IaccountBlacklistRequest): Promise<LittleBlacklist[]> {
        const { app, ctx, logger } = this;
            let ids: any = req.playerId.split(",");
        try {
            const sqlStr = `SELECT ucl.user_no, ucl.createtime,ucl.remark,ucl.is_delsign,ucl.u_is_delsign_num,t.udid
                            FROM user_cheat_little ucl
                            LEFT JOIN tuser t ON ucl.user_no = t.no
                            WHERE ucl.user_no in (?); `
            const array: LittleBlacklist[] = await this.selectList(sqlStr, [ids]);
            return array;
        } catch (err) {
            logger.error(err);
            throw err;
        }
    }

    //删除小号
    public async delLittleBlacklist(req: DelLittleRequest): Promise<number> {

        const { app, ctx, logger } = this;
        const db = app.mysql.get('werewolf');
        const werewolfConn = await db.beginTransaction();
        try {
            let sqlStr = `DELETE FROM user_cheat_little WHERE user_no = ${req.user_id}; `
            await werewolfConn.query(sqlStr);
            let inSqlStr = `INSERT INTO little_operate_record(operator_id, user_id, del_reason, create_time) values(${req.operator_id},${req.user_id},'${req.del_reason}',now()); `
            await werewolfConn.query(inSqlStr);
            await werewolfConn.commit();
            return 1;
        } catch (err) {
            logger.error(err);
            await werewolfConn.rollback();
            throw err;
        }
    }



}