import { Router, Application } from "egg";

import { AccessRouteId } from './model/accessRouteCof';
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {
    const { controller, router } = app;
    router.get(`${API_VERSION}/werewolf/randomRecord/getRecordList`, accCtr(AccessRouteId.wolf_game_config), controller.werewolf.randomRecord.getRecordList);
    router.post(`${API_VERSION}/werewolf/randomRecord/getRecordBoard`, accCtr(AccessRouteId.wolf_game_config), controller.werewolf.randomRecord.getRecordBoard);
    router.post(`${API_VERSION}/werewolf/randomRecord/createRecordBoard`, accCtr(AccessRouteId.wolf_game_config), controller.werewolf.randomRecord.createRecordBoard);
    router.post(`${API_VERSION}/werewolf/randomRecord/updateRecordBoard`, accCtr(AccessRouteId.wolf_game_config), controller.werewolf.randomRecord.updateRecordBoard);
    router.post(`${API_VERSION}/werewolf/randomRecord/deleteRecordBoard`, accCtr(AccessRouteId.wolf_game_config), controller.werewolf.randomRecord.deleteRecordBoard);
}
export default load