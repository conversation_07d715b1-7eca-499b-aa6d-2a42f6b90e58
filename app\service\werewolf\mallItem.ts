/*
 * @Description: 商城道具服务类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: z<PERSON><PERSON>
 * @Date: 2020-04-24 11:35:39
 * @LastEditors: zhanglu
 * @LastEditTime: 2022-09-15 13:21:06
 */
import BaseMegaService from './BaseMegaService';
import { MallItemBaseType, MallItemBaseInfo, CoinOperation, UploadTagInfoReq } from './../../model/mallItemManager';
import {IaddRealityLotteryBoxParams, IdeleteRealityLotteryBoxParams, IupdateRealityLotteryBoxParams} from "../../model/mallItemDto";
export default class MallItemService extends BaseMegaService {

    /**
     * @name: select  getBaseTypeList
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async getBaseTypeList() {
        const { app, ctx, logger } = this;
        try {
            let BaseTypeList = [] as any[];
            let sqlStr1 = `SELECT * FROM item_catalog WHERE \`level\` = 1 AND id != 60 AND id != 80 AND id != 10000 ORDER BY sort, id `;
            BaseTypeList.push(await this.selectList(sqlStr1, []));
            let sqlStr2 = `SELECT * FROM item_catalog WHERE \`level\` = 2 ORDER BY sort, id `;
            BaseTypeList.push(await this.selectList(sqlStr2, []));
            let sqlStr3 = `SELECT * FROM item_catalog WHERE \`level\` = 3 ORDER BY sort, id `;
            BaseTypeList.push(await this.selectList(sqlStr3, []));
            return BaseTypeList;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getRealityLotteryBoxRecord(req) {
        const {logger} = this;

        try {
            const sql = `SELECT a.user_id, b.nickname, c.name, date_format(a.create_time, '%Y/%m/%d %H:%i:%s') create_time
                         FROM lottery_box_user_record a
                                  LEFT JOIN tuser b ON a.user_id = b.\`no\`
                                  LEFT JOIN lottery_box c ON a.box_id = c.\`id\`
                         WHERE a.box_id in (?)
                         ORDER BY a.create_time DESC`
            const data = await this.selectList(sql, [req.ids])
            return data ?? []
        } catch (e) {
            logger.error(e)
            return false

        }
    }

    public async getLotteryBoxLevel(req) {
        const {logger} = this;
        try {
            const sql = `SELECT * FROM lottery_box_level`
            return await this.selectList(sql,[])
        } catch (error) {
            logger.error(error)
            return false
        }
    }

    async getRealityLotteryBox(req) {
        const {logger} = this;

        try {
            const sql = `SELECT a.\`id\`,
                                a.sort,
                                a.\`name\`,
                                a.\`level\`,
                                a.\`show_level\`,
                                a.img,
                                a.delsign,
                                a.num,
                                a.num_surplus,
                                a.remark,
                                date_format(a.activity_start_time, '%Y/%m/%d %H:%i:%s') activity_start_time,
                                date_format(a.activity_end_time, '%Y/%m/%d %H:%i:%s')   activity_end_time,
                                b.price,
                                c.mall_tag_id,
                                date_format(c.start_time, '%Y/%m/%d %H:%i:%s') mall_tag_start_time,
                                date_format(c.end_time, '%Y/%m/%d %H:%i:%s') mall_tag_end_time,
                                d.pic mall_tag_pic,
                                d.remark mall_tag_remark
                         FROM lottery_box a
                                  LEFT JOIN lottery_box_charge b ON a.\`id\` = b.box_id
                                  LEFT JOIN lottery_box_tag c ON a.\`id\` = c.box_id
                                  LEFT JOIN mall_tag d ON c.mall_tag_id = d.\`id\`
                         WHERE ISNULL(a.item_dic_id)
                           AND a.box_award_id = 0
                         ORDER BY a.sort ASC`
            const data = await this.selectList(sql, [])
            return data ?? []
        } catch (e) {
            logger.error(e)
            return false

        }
    }

    public async addRealityLotteryBox(params: IaddRealityLotteryBoxParams) {
        const { app, logger } = this;

        const db = app.mysql.get('werewolf');
        const transaction = await db.beginTransaction();

        try {

            const sql = `INSERT INTO lottery_box (\`name\`, box_award_id, \`level\`,show_level, img, num, num_surplus, remark,
                                                  delsign, sort, activity_start_time, activity_end_time)
                         VALUES (?,?,?,?,?,?,?,?,?,?,?,?)`
            const data = await transaction.query(sql, [params.name, 0, params.level,params.show_level, params.img, params.num, params.num_surplus, params.remark, params.delsign, params.sort, params.activity_start_time, params.activity_end_time])
            const insertId = data.insertId

            const priceSql = `INSERT INTO lottery_box_charge (box_id,coin_id,price,delsign)
                         VALUES (?,13,?,0)`
            const priceData = await transaction.query(priceSql,[insertId,params.price])

            if (params.mall_tag_id) {
                const tagSql = `INSERT INTO lottery_box_tag (box_id, mall_tag_id, start_time, end_time)
                VALUES (?, ?, ?, ?)`
                const tagData = await transaction.query(tagSql, [insertId, params.mall_tag_id, params.mall_tag_start_time, params.mall_tag_end_time])
            }


            await transaction.commit()

            return priceData
        }catch (e) {
            await transaction.rollback()
            logger.error(e)
            return false

        }
    }

    public async deleteRealityLotteryBox(params: IdeleteRealityLotteryBoxParams) {
        const {app,logger} = this;

        const db = app.mysql.get('werewolf');
        const transaction = await db.beginTransaction();

        try {
            const sql = `DELETE FROM lottery_box WHERE id = ?`
            const data = await transaction.query(sql, [params.id])

            const priceSql = `DELETE FROM lottery_box_charge WHERE box_id =? AND coin_id = ?`
            const priceData = await transaction.query(priceSql, [params.id, 13])

            const tagSql = `DELETE FROM lottery_box_tag WHERE box_id = ?`
            const tagData = await transaction.query(tagSql, [params.id])

            await transaction.commit()
            return true
        } catch (error) {
            await transaction.rollback()
            logger.error(error)
            throw error
        }

    }

    public async updateRealityLotteryBox(params: IupdateRealityLotteryBoxParams) {
        const {app,logger} = this;

        const db = app.mysql.get('werewolf');
        const transaction = await db.beginTransaction();

        try {
            const sql = `UPDATE lottery_box
                         SET \`name\`            = ?,
                             box_award_id        = ?,
                             \`level\`           = ?,
                             show_level          = ?,
                             img                 = ?,
                             num                 = ?,
                             num_surplus         = ?,
                             remark              = ?,
                             delsign             = ?,
                             sort                = ?,
                             activity_start_time = ?,
                             activity_end_time   = ?
                         WHERE id = ?
            `
            const data = await transaction.query(sql, [params.name, 0, params.level,params.show_level, params.img, params.num, params.num_surplus, params.remark, params.delsign, params.sort, params.activity_start_time, params.activity_end_time, params.id])

            const priceSql = `INSERT INTO lottery_box_charge (box_id,coin_id,price) VALUES (?,?,?) ON DUPLICATE KEY
                              UPDATE price=?`
            const priceData = await transaction.query(priceSql, [params.id, 13, params.price, params.price])

            if (params.mall_tag_id){
                const tagSql = `INSERT INTO lottery_box_tag (box_id, mall_tag_id, start_time, end_time)
                            VALUES (?, ?, ?, ?) ON DUPLICATE KEY
                            UPDATE mall_tag_id = ?,start_time=?,end_time=?`
                const tagData = await transaction.query(tagSql, [params.id, params.mall_tag_id, params.mall_tag_start_time, params.mall_tag_end_time, params.mall_tag_id, params.mall_tag_start_time, params.mall_tag_end_time])
            }else{
                const tagSql = `DELETE FROM lottery_box_tag WHERE box_id = ?`
                const tagData = await transaction.query(tagSql,[params.id])
            }

            await transaction.commit()

            return priceData
        } catch (e) {
            await transaction.rollback()
            logger.error(e)
            return false
        }
    }

    //查询分类道具信息
    public async getTypeList(req) {
        const { app, ctx, logger } = this;
        try {

            // -200 实物
            if (req.typeId == -200) {
                return await this.getRealityLotteryBox(req)
            }

            let consoleDelsign = 0;
            if (req.delsign_console != undefined && req.delsign_console != null) {
                consoleDelsign = req.delsign_console;
            }
            let sqlStr = `SELECT
                mi.id,
                mi.sort,
                mi.box_mall_sort,
                mi.item_dic_id,
                mi.mall_condition_id,
                mi.type,
                i.\`name\`,
                wc.name AS clothName,
                i.pic,
                i.version,
                i.icon,
                i.remark AS itemDicRemark,
                ic.id AS cate_id,
                IF(NOW() >= mi.start_time AND NOW() <= mi.end_time,mi.delsign,1) AS delsign,
                IF(NOW() >= mi.start_time AND NOW() <= mi.end_time,0,1) AS expire,
                mi.give,
                mi.buy,
                GROUP_CONCAT(mic.coin_id) AS coin_id,
                GROUP_CONCAT(mic.original_price) AS original_price,
                mi.remark,
                mi.buy_remark,
                mi.buy_jump,
                mi.buy_jump_type,
                mt.pic AS tag_pic,
                mt.remark AS tag_remark,
                mit.start_time AS tag_start_time,
                mit.end_time AS tag_end_time,
                mit.mall_tag_id,
                mic.start_time AS discount_start_time,
                mic.end_time AS discount_end_time,
                mic.current_price,
                mi.start_time,
                mi.end_time,
                c.\`name\` AS coinName,
                i.item_cate_id,
                i.item_id,
                ni.img_name AS normalImg,
                ma.prive_remark AS maskshowImg,
                mcb.\`level\` AS conditionLevel,
                mcb.type AS conditionType,
                mcb.id AS conditionId,
                mi.item_catalog_id,
                mi.delsign_console,
                wc.pic AS clothPic,
                wc.buy_describe AS clothBuyDescribe,
                wc.sex,
                mi.coin_not_show,
                mi.lottery_box_level AS level
            FROM
                mall_item mi
            INNER JOIN item_dic i ON mi.item_dic_id = i.id
            LEFT JOIN mall_item_charge mic ON mic.mall_item_id = mi.id AND mic.delsign = 0
            LEFT JOIN coin c ON mic.coin_id = c.id
            INNER JOIN item_cate ic ON i.item_cate_id = ic.id
            INNER JOIN item_catalog mc ON mc.id = mi.item_catalog_id
            LEFT JOIN mall_condition mcb ON mcb.id = mi.mall_condition_id
            LEFT JOIN mall_item_tag mit ON mit.mall_item_id = mi.id 
            LEFT JOIN mall_tag mt ON mt.id = mit.mall_tag_id AND mt.delsign = 0
            LEFT JOIN normal_item ni ON ni.\`no\` = i.item_id
            LEFT JOIN maskshow ma ON ma.\`no\` = i.item_id
            LEFT JOIN wedding_clothing wc ON wc.\`id\` = i.item_id
            WHERE
                mi.item_catalog_id IN (${req.typeId})
                AND mi.delsign_console = (${consoleDelsign})
                `;

            if (req.isBoxMall && req.isBoxMall == 1) {
                sqlStr += `
                    AND mi.type = 1
                `;
            }

            sqlStr += `
                GROUP BY item_dic_id    
            `

            if (req.isBoxMall && req.isBoxMall == 1) {
                sqlStr += `
                ORDER BY
                    mi.box_mall_sort ASC
            `
            }else{
                sqlStr += `
                ORDER BY
                    mi.sort ASC
            `
            }
            
            let data = await this.selectList(sqlStr, [])
            if (data === null) {
                return [];
            }
            return data;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getMallTagList() {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
            SELECT * FROM mall_tag WHERE delsign = 0
            `;
            let data = await this.selectList(sqlStr, [])

            return data;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getMallConditionList() {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
            SELECT * FROM mall_condition
            `;
            let data = await this.selectList(sqlStr, [])

            return data;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async insertMallItem(req: any) {
        const { app, logger } = this;
        const db = app.mysql.get('werewolf');
        const manager = app.mysql.get("manager");

        //开启事物
        const conn = await db.beginTransaction();
        const managerConn = await manager.beginTransaction();

        try {
            let sqlUpsertMallItem = ` 
            INSERT INTO \`mall_item\` (\`type\`,\`item_catalog_id\`,\`item_dic_id\`,\`mall_condition_id\`,\`remark\`,\`give\`,\`buy\`,\`buy_remark\`,\`buy_jump\`,\`start_time\`,\`end_time\`,\`sort\`,\`delsign\`,\`buy_jump_type\`, coin_not_show,box_mall_sort) VALUES
                    (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) 
            `;

            const result = await conn.query(sqlUpsertMallItem, [
                req.type,
                req.item_catalog_id,
                req.item_dic_id,
                req.mall_condition_id,
                req.remark,
                req.give,
                req.buy,
                req.buy_remark,
                req.buy_jump,
                req.start_time,
                req.end_time,
                req.sort,
                1,
                req.buy_jump_type,
                req.coin_not_show,
                req.box_mall_sort
            ]);

            req.id = result.insertId

            if (req.isBoxMall && req.isBoxMall == 1) {
                let sqlUpsertMallItemLevel = ` 
                    UPDATE \`mall_item\` 
                    SET 
                    lottery_box_level = ?
                    WHERE
                        \`id\` = ?;       
                    `;

                    await conn.query(sqlUpsertMallItemLevel, [
                        req.level,
                        req.id]);
                        
                let sqlUpdateItemDicRemark = ` 
                UPDATE item_dic 
                SET 
                remark = ?
                WHERE
                    id = ?
                `;

                await conn.query(sqlUpdateItemDicRemark, [
                    req.itemDicRemark,
                    req.item_dic_id]);
            }


            if (req.coinState.length > 0) {
                let sqlUpsertMallItemCharge = ` 
                    INSERT INTO \`mall_item_charge\`(\`mall_item_id\`, \`coin_id\`, \`original_price\`, \`current_price\`, \`start_time\`, \`end_time\`, \`delsign\`) VALUES
                        ?
                        ON DUPLICATE KEY UPDATE mall_item_id = VALUES(mall_item_id), \`coin_id\` = VALUES(\`coin_id\`), \`original_price\` = VALUES(\`original_price\`), \`current_price\` = VALUES(\`current_price\`), \`start_time\` = VALUES(\`start_time\`), \`end_time\` = VALUES(\`end_time\`)
                    `;

                let valueList: any = [];
                for (let index = 0; index < req.coinState.length && index < 2; index++) {
                    const element: any = req.coinState[index];
                    if (element.coinId == 1) {
                        valueList.push([
                            req.id,
                            element.coinId,
                            element.num,
                            req.current_price,
                            req.discount_start_time,
                            req.discount_end_time,
                            0]);
                    } else {
                        valueList.push([
                            req.id,
                            element.coinId,
                            element.num,
                            null,
                            null,
                            null,
                            0]);
                    }
                }
                await conn.query(sqlUpsertMallItemCharge, [valueList]);
            }

            if (req.mall_tag_id != null && req.mall_tag_id != undefined && req.mall_tag_id > 0) {
                let sqlUpdateMallTag = `
                    INSERT INTO \`mall_item_tag\`(\`mall_item_id\`, \`mall_tag_id\`, \`start_time\`, \`end_time\`) VALUES
                     (?, ?, ?, ?)
                    ON DUPLICATE KEY UPDATE mall_item_id = VALUES(mall_item_id), \`mall_tag_id\` = VALUES(\`mall_tag_id\`), \`start_time\` = VALUES(\`start_time\`), \`end_time\` = VALUES(\`end_time\`)
                `;

                await conn.query(sqlUpdateMallTag, [
                    req.id,
                    req.mall_tag_id,
                    req.tag_start_time,
                    req.tag_end_time]);
            }

            this.updateClothingInMall(req, conn);

            let sqlOperate = `
            INSERT INTO \`wf_mall_item_operate_record\`(\`type\`,\`name\`,\`operate_type\`, \`operate_user_id\`, \`item_catalog_id\`,
             \`item_dic_id\`, \`mall_condition_id\`, \`remark\`, \`give\`, \`buy\`, \`buy_jump_type\`, \`buy_jump\`, 
             \`buy_remark\`, \`start_time\`, \`end_time\`, \`sort\`, \`delsign\`, \`dimaond\`, 
             \`current_price\`, \`discount_start_time\`, \`discount_end_time\`, \`box_coin\`, \`box_key\`, 
             \`activity_coin\`, \`top_coin\`, \`group_coin\`, \`mall_tag_id\`, 
             \`tag_start_time\`, \`tag_end_time\`, coin_not_show) VALUES
            (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
            `;
            await managerConn.query(sqlOperate, [
                req.operateInfo.type,
                req.operateInfo.name,
                req.operateInfo.operate_type,
                req.operateInfo.operate_user_id,
                req.operateInfo.item_catalog_id,
                req.operateInfo.item_dic_id,
                req.operateInfo.mall_condition_id,
                req.operateInfo.remark,
                req.operateInfo.give,
                req.operateInfo.buy,
                req.operateInfo.buy_jump_type,
                req.operateInfo.buy_jump,
                req.operateInfo.buy_remark,
                req.operateInfo.start_time,
                req.operateInfo.end_time,
                req.operateInfo.sort,
                req.operateInfo.delsign,
                req.operateInfo.dimaond,
                req.operateInfo.current_price,
                req.operateInfo.discount_start_time,
                req.operateInfo.discount_end_time,
                req.operateInfo.box_coin,
                req.operateInfo.box_key,
                req.operateInfo.activity_coin,
                req.operateInfo.top_coin,
                req.operateInfo.group_coin,
                req.operateInfo.mall_tag_id,
                req.operateInfo.tag_start_time,
                req.operateInfo.tag_end_time,
                req.operateInfo.coin_not_show
            ]);

            await conn.commit(); // 提交事务
            await managerConn.commit();
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            await managerConn.rollback();
            logger.error(error);

            throw error;
        }
    }

    public async updateMallItem(req: any) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        const manager = app.mysql.get("manager");

        //开启事物
        const conn = await db.beginTransaction();
        const managerConn = await manager.beginTransaction();

        try {
            let sqlUpsertMallItem = ` 
                    UPDATE \`mall_item\` 
                    SET \`item_catalog_id\` = ?,
                    \`item_dic_id\` = ?,
                    \`mall_condition_id\` = ?,
                    \`remark\` = ?,
                    \`give\` = ?,
                    \`buy\` = ?,
                    \`buy_remark\` = ?,
                    \`buy_jump\` = ?,
                    \`start_time\` = ?,
                    \`end_time\` = ?,
                    \`sort\` = ?,
                    \`box_mall_sort\` = ?,
                    \`delsign\` = ? ,
                    \`buy_jump_type\` = ?,
                    \`type\` = ?,
                    coin_not_show = ?
                    WHERE
                        \`id\` = ?;       
            `;

            await conn.query(sqlUpsertMallItem, [
                req.item_catalog_id,
                req.item_dic_id,
                req.mall_condition_id,
                req.remark,
                req.give,
                req.buy,
                req.buy_remark,
                req.buy_jump,
                req.start_time,
                req.end_time,
                req.sort,
                req.box_mall_sort,
                req.delsign,
                req.buy_jump_type,
                req.type,
                req.coin_not_show,
                req.id]);

                if (req.isBoxMall && req.isBoxMall == 1) {

                    let sqlUpsertMallItemLevel = ` 
                    UPDATE \`mall_item\` 
                    SET 
                    lottery_box_level = ?
                    WHERE
                        \`id\` = ?;       
                    `;

                    await conn.query(sqlUpsertMallItemLevel, [
                        req.level,
                        req.id]);

                    let sqlUpdateItemDicRemark = ` 
                        UPDATE item_dic 
                        SET 
                        remark = ?
                        WHERE
                            id = ?
                    `;

                    await conn.query(sqlUpdateItemDicRemark, [
                        req.itemDicRemark,
                        req.item_dic_id]);
                }


            let sqlUpsertMallItemCharge = ` 
                INSERT INTO \`mall_item_charge\`(\`mall_item_id\`, \`coin_id\`, \`original_price\`, \`current_price\`, \`start_time\`, \`end_time\` , \`delsign\`) VALUES
                    ?
                    ON DUPLICATE KEY UPDATE mall_item_id = VALUES(mall_item_id), \`coin_id\` = VALUES(\`coin_id\`), \`original_price\` = VALUES(\`original_price\`), \`current_price\` = VALUES(\`current_price\`), \`start_time\` = VALUES(\`start_time\`), \`end_time\` = VALUES(\`end_time\`), \`delsign\` = VALUES(\`delsign\`)
                `;
            let valueUpsertList: any = [];

            // tslint:disable-next-line:prefer-for-of
            for (let index = 0; index < req.coinState.length; index++) {
                const element: any = req.coinState[index];
                if (element.ope == CoinOperation.insert || element.ope == CoinOperation.update) {
                    if (element.coinId == 1) {
                        valueUpsertList.push([
                            req.id,
                            element.coinId,
                            element.num,
                            req.current_price,
                            req.discount_start_time,
                            req.discount_end_time,
                            0]);
                    } else {
                        valueUpsertList.push([
                            req.id,
                            element.coinId,
                            element.num,
                            null,
                            null,
                            null,
                            0]);
                    }
                } else if (element.ope == CoinOperation.delete) {
                    valueUpsertList.push([
                        req.id,
                        element.coinId,
                        element.num,
                        null,
                        null,
                        null,
                        1]);
                }
            }

            if (valueUpsertList.length > 0) {
                await conn.query(sqlUpsertMallItemCharge, [valueUpsertList]);
            }

            for (const element of req.coinState) {
                if (element.ope == CoinOperation.delete) {
                    let sqlDeleteMallItemCharge = `
                        DELETE FROM mall_item_charge WHERE mall_item_id = ? AND coin_id = ?
                    `;
                    await conn.query(sqlDeleteMallItemCharge, [req.id, element.coinId]);
                }
            }

            if (req.mall_tag_id != null && req.mall_tag_id != undefined && req.mall_tag_id > 0) {
                let sqlUpdateMallTag = `
                    INSERT INTO \`mall_item_tag\`(\`mall_item_id\`, \`mall_tag_id\`, \`start_time\`, \`end_time\`) VALUES
                     (?, ?, ?, ?)
                    ON DUPLICATE KEY UPDATE mall_item_id = VALUES(mall_item_id), \`mall_tag_id\` = VALUES(\`mall_tag_id\`), \`start_time\` = VALUES(\`start_time\`), \`end_time\` = VALUES(\`end_time\`)
                `;

                await conn.query(sqlUpdateMallTag, [
                    req.id,
                    req.mall_tag_id,
                    req.tag_start_time,
                    req.tag_end_time]);
            }

            if (req.oriItem.mall_tag_id > 0 && req.mall_tag_id == null) {
                let sqlDeleteMallTag = `
                DELETE FROM mall_item_tag WHERE mall_item_id = ? AND mall_tag_id = ?
                `;
                await conn.query(sqlDeleteMallTag, [
                    req.id,
                    req.oriItem.mall_tag_id]);
            }

            this.updateClothingInMall(req, conn);

            let sqlOperate = `
            INSERT INTO \`wf_mall_item_operate_record\`(\`type\`,\`name\`,\`operate_type\`, \`operate_user_id\`, \`item_catalog_id\`,
             \`item_dic_id\`, \`mall_condition_id\`, \`remark\`, \`give\`, \`buy\`, \`buy_jump_type\`, \`buy_jump\`, 
             \`buy_remark\`, \`start_time\`, \`end_time\`, \`sort\`, \`delsign\`, \`dimaond\`, 
             \`current_price\`, \`discount_start_time\`, \`discount_end_time\`, \`box_coin\`, \`box_key\`, 
             \`activity_coin\`, \`top_coin\`, \`group_coin\`, \`mall_tag_id\`, 
             \`tag_start_time\`, \`tag_end_time\`, coin_not_show, cloth_coin) VALUES
            (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
            `;
            await managerConn.query(sqlOperate, [
                req.operateInfo.type,
                req.operateInfo.name,
                req.operateInfo.operate_type,
                req.operateInfo.operate_user_id,
                req.operateInfo.item_catalog_id,
                req.operateInfo.item_dic_id,
                req.operateInfo.mall_condition_id,
                req.operateInfo.remark,
                req.operateInfo.give,
                req.operateInfo.buy,
                req.operateInfo.buy_jump_type,
                req.operateInfo.buy_jump,
                req.operateInfo.buy_remark,
                req.operateInfo.start_time,
                req.operateInfo.end_time,
                req.operateInfo.sort,
                req.operateInfo.delsign,
                req.operateInfo.dimaond,
                req.operateInfo.current_price,
                req.operateInfo.discount_start_time,
                req.operateInfo.discount_end_time,
                req.operateInfo.box_coin,
                req.operateInfo.box_key,
                req.operateInfo.activity_coin,
                req.operateInfo.top_coin,
                req.operateInfo.group_coin,
                req.operateInfo.mall_tag_id,
                req.operateInfo.tag_start_time,
                req.operateInfo.tag_end_time,
                req.operateInfo.coin_not_show,
                req.operateInfo.cloth_coin_update_str,
            ]);

            await conn.commit(); // 提交事务
            await managerConn.commit();
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            await managerConn.rollback();
            throw error;
        }
    }

    public async updateClothingInMall(req, conn) {
        if (req.cate_id == 11000) {
            let isLimited = 0;
            if (req.secondPid == 11010) {
                isLimited = 1;
            }
            let sqlCloth = `
                UPDATE wedding_clothing
                SET
                sort = ?,
                delsign = ?,
                buy_describe = ?,
                is_limited = ?
                WHERE
                    id = ?;
            `
            await conn.query(sqlCloth, [
                req.sort,
                req.delsign,
                req.clothBuyDescribe,
                isLimited,
                req.item_id]);
        }
    }

    public async updateClothingDelsignInMall(req, conn) {

        if (req.cate_id == 11000) {
            let sqlCloth = `
                UPDATE wedding_clothing
                SET
                delsign = ?
                WHERE
                    id = ?;
            `
            await conn.query(sqlCloth, [
                req.delsign,
                req.item_id]);
        }
    }

    public async getJumpTypeList() {
        const { app, ctx, logger } = this;

        try {
            let sqlStr = ` 
            SELECT dialog_type AS \`id\`, dialog_type_name AS \`name\` FROM home_dialog_type WHERE del_sign = 0
            `;
            let data = await this.selectList(sqlStr, [])
            return data;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getOpreateList(req: any) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
            SELECT
            r.\`id\`,
            r.createtime,
            u.nickname,
            name,
            type,
            \`operate_type\`,
            \`operate_user_id\`,
            \`item_catalog_id\`,
            \`item_dic_id\`,
            \`mall_condition_id\`,
            \`remark\`,
            \`give\`,
            \`buy\`,
            \`buy_jump_type\`,
            \`buy_jump\`,
            \`buy_remark\`,
            \`start_time\`,
            \`end_time\`,
            \`sort\`,
            r.\`delsign\`,
            \`dimaond\`,
            \`current_price\`,
            \`discount_start_time\`,
            \`discount_end_time\`,
            \`box_coin\`,
            \`box_key\`,
            \`activity_coin\`,
            \`top_coin\`,
            \`group_coin\`,
            \`mall_tag_id\`,
            \`tag_start_time\`,
            \`tag_end_time\` ,
            coin_not_show,
            cloth_coin
        FROM
            wf_mall_item_operate_record r,
            wf_admin_user u 
        WHERE
            r.operate_user_id = u.id 
        ORDER BY
            createtime DESC
            Limit ?, ?
            `;
            let data = await this.selectList(sqlStr, [(req.current - 1) * req.pageCount, req.pageCount], 'manager');
            let totalCountSql = `
                SELECT
                COUNT(*) AS num
            FROM
                wf_mall_item_operate_record r,
                wf_admin_user u 
            WHERE
                r.operate_user_id = u.id 
            `
            let count = await this.selectOne(totalCountSql, [], 'manager');
            return { operateList: data, operateCount: count.num };
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async uploadTagInfo(req: UploadTagInfoReq) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `SELECT COUNT(*) AS total FROM mall_tag WHERE pic = ?`;
            let data = await this.selectOne(sqlStr, [req.pic])
            logger.error('data=>', data)
            if (data['total'] > 0) {
                return false;
            }
            let insertSqlStr = `INSERT INTO \`mall_tag\` (\`pic\`, \`remark\`) VALUES ( ?, ?);`
            let result = await this.execSql(insertSqlStr, [req.pic, req.remark]);
            return result;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }
    public async delMallTag(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `UPDATE mall_tag SET  delsign = 1 WHERE id = ?;`;
            await this.selectOne(sqlStr, [req.id])
        } catch (error) {
            logger.error(error);
            throw error
        }
    }
    public async changeMallItemDelsign(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get('werewolf');
        const manager = app.mysql.get("manager");

        //开启事物
        const conn = await db.beginTransaction();
        const managerConn = await manager.beginTransaction();

        try {
            let sqlStr = `UPDATE mall_item SET delsign = ? WHERE id = ?;`;
            await conn.query(sqlStr, [req.delsign, req.id]);

            this.updateClothingDelsignInMall(req, conn);

            let sqlOperate = `
            INSERT INTO \`wf_mall_item_operate_record\`(\`name\`,\`operate_type\`, \`operate_user_id\`, \`item_catalog_id\`,\`item_dic_id\`, delsign) VALUES
            (?, ?, ?, ?, ?, ?);
            `;
            await managerConn.query(sqlOperate, [
                req.operateInfo.name,
                req.operateInfo.operate_type,
                req.operateInfo.operate_user_id,
                req.operateInfo.item_catalog_id,
                req.operateInfo.item_dic_id,
                req.operateInfo.delsign,
            ]);

            await conn.commit(); // 提交事务
            await managerConn.commit();

        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            await managerConn.rollback();

            logger.error(error);
            throw error
        }
    }

    public async changeMallItemDelsignConsole(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get('werewolf');
        const manager = app.mysql.get("manager");

        //开启事物
        const conn = await db.beginTransaction();
        const managerConn = await manager.beginTransaction();

        try {

            let sqlStr = `UPDATE mall_item SET delsign_console = ? WHERE id = ?;`;
            await conn.query(sqlStr, [req.delsignConsole, req.id]);

            let sqlOperate = `
            INSERT INTO \`wf_mall_item_operate_record\`(\`name\`,\`operate_type\`, \`operate_user_id\`, \`item_catalog_id\`,\`item_dic_id\`) VALUES
            (?, ?, ?, ?, ?);
            `;
            await managerConn.query(sqlOperate, [
                req.operateInfo.name,
                req.operateInfo.operate_type,
                req.operateInfo.operate_user_id,
                req.operateInfo.item_catalog_id,
                req.operateInfo.item_dic_id
            ]);

            await conn.commit(); // 提交事务
            await managerConn.commit();

        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            await managerConn.rollback();

            logger.error(error);
            throw error
        }
    }

    public async changeEditManagerNum(req) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        try {
            let sql = `UPDATE base20000_avatar_frame_period SET error_num = ? WHERE id = ?;`;
            await db.query(sql, [req.editNum, req.id]);
        } catch (error) {
            throw error;
        }
    }

    public async getCoinList(req: any) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
            SELECT
                id, name
            FROM
                coin
            WHERE
                type = ?
            `;
            let data = await this.selectList(sqlStr, [req.type]);
            return data;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }
}
