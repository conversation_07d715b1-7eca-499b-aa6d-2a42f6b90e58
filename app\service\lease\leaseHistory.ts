/*
 * @Description: 新手辅助
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: wb
 * @Date: 2020-04-24 11:35:39
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-07-21 10:54:23
 */
import BaseMegaService from '../werewolf/BaseMegaService';
import {
    InewPlayerRobotInsertParams,
    InewPlayerRobotUpdateParams,
    InewPlayerRobotUpdateTypeParams,
    InewPlayerRobotSearchParams
} from "../../model/newPlayerRobotDto";

const managerDb = "manager";
const werewolfDB = "werewolf";
const groupCount = 900;
const time = 20;
const isAll = 0;

export default class LeaseHistoryService extends BaseMegaService {

    public async getList() {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
                SELECT b.id,
                       b.admin_id,
                       u.nickname AS admin_nickname,
                       b.msg_type,
                       b.sum,
                       b.desc,
                       b.tend_type,
                       b.tend_page,
                       b.acTendUrl,
                       DATE_FORMAT(b.create_time, '%Y-%m-%d %H:%i:%s') create_time,
                       DATE_FORMAT(b.send_time, '%Y-%m-%d %H:%i:%s')   send_time,
                       b.type,
                       b.progress,
                       b.domainName,
                       b.residueTime,
                       d.msg_user,
                       b.tend_shop AS tendShop,
                       b.tab1,
                       b.tab2
                FROM wf_admin_user_msg b
                         INNER JOIN wf_admin_user u ON u.id = b.admin_id
                         INNER JOIN wd_admin_user_msg_details d  ON d.admin_user_msg_id = b.id
                WHERE d.isFirst = 1 AND b.id = 1166
                ORDER BY  b.send_time DESC
            `;
            // ORDER BY  b.type ASC, b.send_time ASC

            return await this.selectList(sqlStr, [], managerDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async searchFromList(req: InewPlayerRobotSearchParams) {
        const { app, ctx, logger } = this;
        const manager = app.mysql.get(managerDb);

        try {

            let sql = `
            SELECT DISTINCT
                       b.id,
                       b.admin_id,
                       u.nickname AS admin_nickname,
                       b.msg_type,
                       b.sum,
                       b.desc,
                       b.tend_type,
                       b.tend_page,
                       b.acTendUrl,
                       DATE_FORMAT(b.create_time, '%Y-%m-%d %H:%i:%s') create_time,
                       DATE_FORMAT(b.send_time, '%Y-%m-%d %H:%i:%s')   send_time,
                       b.type,
                       b.progress,
                       b.domainName,
                       b.residueTime,
                       d.msg_user,
                       b.tend_shop AS tendShop,
                       b.tab1,
                       b.tab2
                FROM wf_admin_user_msg b
                         INNER JOIN wf_admin_user u ON u.id = b.admin_id
                         INNER JOIN wd_admin_user_msg_details d  ON d.admin_user_msg_id = b.id 
                         WHERE (b.sum LIKE '%${req.sum}%' OR u.nickname LIKE '%${req.sum}%' )
                ORDER BY b.send_time DESC
            `;
            // AND d.isFirst = 1

            if (req.isUserId == "1") {
                sql = `
                SELECT  DISTINCT
                       b.id,
                       b.admin_id,
                       u.nickname AS admin_nickname,
                       b.msg_type,
                       b.sum,
                       b.desc,
                       b.tend_type,
                       b.tend_page,
                       b.acTendUrl,
                       DATE_FORMAT(b.create_time, '%Y-%m-%d %H:%i:%s') create_time,
                       DATE_FORMAT(b.send_time, '%Y-%m-%d %H:%i:%s')   send_time,
                       b.type,
                       b.progress,
                       b.domainName,
                       d.msg_user,
                       b.tend_shop AS tendShop,
                       b.tab1,
                       b.tab2
                FROM wf_admin_user_msg b
                         INNER JOIN wf_admin_user u ON u.id = b.admin_id
                         INNER JOIN wd_admin_user_msg_details d  ON d.admin_user_msg_id = b.id 
                         WHERE (d.all_msg_user LIKE '${req.sum},%' OR d.all_msg_user LIKE '%,${req.sum}' OR d.all_msg_user LIKE '%,${req.sum},%' OR d.msg_user = ${req.sum})
                         ORDER BY b.send_time DESC
            `;

            // AND d.isFirst = 1

            }
            

            return await this.selectList(sql, [], managerDb);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateType(req: InewPlayerRobotUpdateTypeParams) {
        const { app, ctx, logger } = this;
        const manager = app.mysql.get(managerDb);
        const managerConn = await manager.beginTransaction();

        //'0：等待广播  1：已经取消 2：已经广播 3：发送中'
        let sqlProgress = `
        SELECT * FROM wf_admin_user_msg WHERE wf_admin_user_msg.id = ${req.id}
        `;

        let progressArr = await managerConn.query(sqlProgress, []);
        logger.error("progress  " + progressArr[0].progress);

        if(progressArr[0].progress > 0){//开启编辑后不可操作
            await managerConn.commit();
            return;
        }

        try {

            if (req.type == 1) {
                // 禁止修改已经发送红包的 type
                const sql = `SELECT type
                             FROM wf_admin_user_msg
                             WHERE id = ?`;
                const arr = await managerConn.query(sql, [req.id]);
                // logger.info('arrrrrr:',arr)
                if (arr[0] && arr[0].type == 2) {
                    return '该红包已经发送'
                }
            }


            let sql;
            let update_datails_type_sql;

            if (req.type == 0) {
                // 立即发送
                sql = `
                    UPDATE
                        wf_admin_user_msg
                    SET type      = 0,
                        send_time = DATE_ADD(NOW(), INTERVAL 1 MINUTE)
                    WHERE id = ?
                `;

                update_datails_type_sql = `
                UPDATE
                wd_admin_user_msg_details
                SET type     = 0
                WHERE admin_user_msg_id = ?`;

                await managerConn.query(update_datails_type_sql, [req.id]);
                await managerConn.query(sql, [req.id]);

                let selectDetailsSql = `SELECT * FROM wd_admin_user_msg_details WHERE admin_user_msg_id = ${req.id};`;

                let selectDetailsList = await managerConn.query(selectDetailsSql, []);


                let timestamp = Date.parse(new Date().toString()) + 60 * 1000;

                for (let i = 0; i < selectDetailsList.length; i++) {

                    const addDate: Date = new Date(timestamp);
                    let dateSeconds: number = addDate.getTime();//获取此date时间的毫秒数。
                    dateSeconds += i * time * 1000;
                    let send_time = new Date(dateSeconds);
                    let Y = send_time.getFullYear();
                    let M = send_time.getMonth() + 1 < 10 ? '0' + (send_time.getMonth() + 1) : send_time.getMonth() + 1;
                    let D = send_time.getDate() < 10 ? '0' + (send_time.getDate()) : send_time.getDate();
                    let h = send_time.getHours() < 10 ? '0' + (send_time.getHours()) : send_time.getHours();
                    let m = send_time.getMinutes() < 10 ? '0' + (send_time.getMinutes()) : send_time.getMinutes();
                    let s = send_time.getSeconds() < 10 ? '0' + (send_time.getSeconds()) : send_time.getSeconds();

                    let send_time2 = Y + "-" + M + "-" + D + ' ' + h + ":" + m + ":" + s;

                    let detailsItem = selectDetailsList[i];



                    let sql3 = `
                    UPDATE
                    wd_admin_user_msg_details
                    SET send_time = \'${send_time2}\'
                    WHERE id = ${detailsItem.id};`;
                    // logger.error("================================================================"+send_time2+"  detailsItem.id = "+detailsItem.id+"  sql = "+sql3);

                    await managerConn.query(sql3, []);

                }


            } else {
                sql = `
                    UPDATE
                        wf_admin_user_msg
                    SET type     = 1
                    WHERE id = ?`;

                update_datails_type_sql = `
                    UPDATE
                    wd_admin_user_msg_details
                    SET type = 1
                    WHERE admin_user_msg_id = ?`;

                await managerConn.query(update_datails_type_sql, [req.id]);
                await managerConn.query(sql, [req.id]);
            }


            await managerConn.commit();
        } catch (error) {
            await managerConn.rollback();
            logger.error(error);
            throw error
        }
    }

    public async update(req: InewPlayerRobotUpdateParams) {
        const { app, ctx, logger } = this;
        const manager = app.mysql.get(managerDb);
        const managerConn = await manager.beginTransaction();
        let send_time = req.send_time ;
        // let send_time = req.send_time && req.send_time != "" ? `${req.send_time}` : "DATE_ADD(NOW(),INTERVAL 1 MINUTE)";

        let sqlProgress = `
                SELECT * FROM wf_admin_user_msg WHERE wf_admin_user_msg.id = ${req.id}
                `;

        let progressArr = await managerConn.query(sqlProgress, []);
        logger.error("progress  " + progressArr[0].progress);

        if(progressArr[0].progress > 0){//开启编辑后不可操作
            await managerConn.commit();
            return;
        }

        try {
            let sql1 = `
                UPDATE wf_admin_user_msg
                SET admin_id  = '${req.admin_id}',
                    \`sum\`  = '${req.sum}',
                    \`desc\`  = '${req.desc}',
                    send_time = '${send_time}',
                    msg_type = '${req.msg_type}',
                    tend_type = '${req.tend_type}',
                    tend_page = '${req.tend_page}',
                    acTendUrl = '${req.acTendUrl}',
                    tend_shop = '${req.tendShop}',
                    tab1 = '${req.tab1}',
                    tab2 = '${req.tab2}',
                    type      = 0
                WHERE id = '${req.id}';
            `
            
            await managerConn.query(sql1, []);

            let delSql = ` DELETE FROM wd_admin_user_msg_details WHERE admin_user_msg_id = ? `;
            await managerConn.query(delSql, [req.id]);

            if (req.allTagDisabled == true) {


                let sql2 = `
                SELECT no,nickname FROM tuser WHERE no in (
                SELECT userBNo FROM tfriending WHERE userANo = 7 AND design = 0
                ) AND delsign = 0 AND logintime > DATE_SUB(CURDATE(),INTERVAL 30 DAY)
                `;

                // if(isAll == 0){
                //     sql2 = `
                //     SELECT no,nickname FROM tuser WHERE no < 100000 AND no > 10000 AND logintime < '2022-01-01' LIMIT 65432
                // `;
                // }

                const werewolf = app.mysql.get(werewolfDB);
                const werewolfConn = await werewolf.beginTransaction();

                let list = await werewolfConn.query(sql2, []);

                let chunk = (arr, size) => Array.from({
                    length: Math.ceil(arr.length / size)
                }, (v, i) => arr.slice(i * size, i * size + size));


                let resultList: any[] = chunk(list, groupCount);
                let msg_user_list: any[] = new Array();

                for (let firstList of resultList) {

                    let msg_user = "";
                    let i = 0;
                    for (let item of firstList) {

                        msg_user = msg_user + item.no  + ",";
                        i++;
                        
                    }
                    i = 0;
                    if(msg_user.length > 1){
                        let listStr = msg_user.substring(0,msg_user.length-1);
                        msg_user_list.push(listStr);

                    }
                }
                


                for (let i = 0; i < msg_user_list.length; i++) {

                    const addDate: Date = new Date(send_time);
                    let dateSeconds: number = addDate.getTime();//获取此date时间的毫秒数。
                    dateSeconds += i * time * 1000;
                    let send_timeDate = new Date(dateSeconds);
                    let Y = send_timeDate.getFullYear();
                    let M = send_timeDate.getMonth() + 1 < 10 ? '0' + (send_timeDate.getMonth() + 1) : send_timeDate.getMonth() + 1;
                    let D = send_timeDate.getDate() < 10 ? '0' + (send_timeDate.getDate()) : send_timeDate.getDate();
                    let h = send_timeDate.getHours() < 10 ? '0' + (send_timeDate.getHours()) : send_timeDate.getHours();
                    let m = send_timeDate.getMinutes() < 10 ? '0' + (send_timeDate.getMinutes()) : send_timeDate.getMinutes();
                    let s = send_timeDate.getSeconds() < 10 ? '0' + (send_timeDate.getSeconds()) : send_timeDate.getSeconds();

                    let send_time2 = Y + "-" + M + "-" + D + ' ' + h + ":" + m + ":" + s;

                    let all_msg_user = msg_user_list[i];

                    let sql3 = `
                    INSERT INTO wd_admin_user_msg_details (admin_user_msg_id, msg_user, all_msg_user, send_time, isFirst,type) VALUES (?,?,?,?,?,0)
                    `;

                    await managerConn.query(sql3, [req.id, "全部", all_msg_user, send_time2, i==0?1:0]);
                }




                await werewolfConn.commit();

            } else {


                let sql2 = `
                INSERT INTO wd_admin_user_msg_details (admin_user_msg_id,msg_user,send_time,isFirst) VALUES (?,?,?,1);
                `;

                await managerConn.query(sql2, [req.id, req.msg_user, send_time]);

            }


            await managerConn.commit();
        } catch (error) {
            await managerConn.rollback();
            logger.error(error);
            throw error
        }
    }

    public async insert(req: InewPlayerRobotInsertParams) {
        const { app, ctx, logger } = this;
        const manager = app.mysql.get(managerDb);
        const managerConn = await manager.beginTransaction();
        let send_time = req.send_time ;
        // let send_time = req.send_time && req.send_time != "" ? `${req.send_time}` : "DATE_ADD(NOW(),INTERVAL 1 MINUTE)";


        try {

            let sql1 = `
                INSERT INTO wf_admin_user_msg (admin_id,
                                                 msg_type,
                                                 sum,
                                                 \`desc\`,
                                                 tend_type,
                                                 tend_page,
                                                 acTendUrl,
                                                 send_time,
                                                 domainName,
                                                 type,
                                                 tend_shop,
                                                 tab1,
                                                 tab2
                                                 )
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?,?,?,?,?);
            `;



            const result = await managerConn.query(sql1, [req.admin_id, req.msg_type, req.sum, req.desc,req.tend_type, req.tend_page, req.acTendUrl, send_time,req.domainName, 0,req.tendShop,req.tab1,req.tab2]);
            let lastId = result.insertId


            if (req.allTagDisabled == true) {

                let sql2 = `
                SELECT no,nickname FROM tuser WHERE no in (
                SELECT userBNo FROM tfriending WHERE userANo = 7 AND design = 0
                ) AND delsign = 0 AND logintime > DATE_SUB(CURDATE(),INTERVAL 30 DAY)
                `;

                // if(isAll == 0){
                //     sql2 = `
                //     SELECT no,nickname FROM tuser WHERE no < 100000 AND no > 10000 AND logintime < '2022-01-01' LIMIT 65432
                // `;
                // }

                const werewolf = app.mysql.get(werewolfDB);
                const werewolfConn = await werewolf.beginTransaction();

                let list = await werewolfConn.query(sql2, []);

                let chunk = (arr, size) => Array.from({
                    length: Math.ceil(arr.length / size)
                }, (v, i) => arr.slice(i * size, i * size + size));


                let resultList: any[] = chunk(list, groupCount);
                let msg_user_list: any[] = new Array();

                for (let firstList of resultList) {

                    let msg_user = "";
                    let i = 0;
                    for (let item of firstList) {

                        msg_user = msg_user + item.no  + ",";
                        i++;
                        
                    }
                    i = 0;
                    if(msg_user.length > 1){
                        let listStr = msg_user.substring(0,msg_user.length-1);
                        msg_user_list.push(listStr);
                    }
                }
                
                

                for (let i = 0; i < msg_user_list.length; i++) {

                    const addDate: Date = new Date(send_time);
                    let dateSeconds: number = addDate.getTime();//获取此date时间的毫秒数。
                    dateSeconds += i * time * 1000;
                    let send_timeDate = new Date(dateSeconds);
                    let Y = send_timeDate.getFullYear();
                    let M = send_timeDate.getMonth() + 1 < 10 ? '0' + (send_timeDate.getMonth() + 1) : send_timeDate.getMonth() + 1;
                    let D = send_timeDate.getDate() < 10 ? '0' + (send_timeDate.getDate()) : send_timeDate.getDate();
                    let h = send_timeDate.getHours() < 10 ? '0' + (send_timeDate.getHours()) : send_timeDate.getHours();
                    let m = send_timeDate.getMinutes() < 10 ? '0' + (send_timeDate.getMinutes()) : send_timeDate.getMinutes();
                    let s = send_timeDate.getSeconds() < 10 ? '0' + (send_timeDate.getSeconds()) : send_timeDate.getSeconds();

                    let send_time2 = Y + "-" + M + "-" + D + ' ' + h + ":" + m + ":" + s;


                    let all_msg_user = msg_user_list[i];

                    let sql3 = `
                    INSERT INTO wd_admin_user_msg_details (admin_user_msg_id, msg_user, all_msg_user, send_time, isFirst,type) VALUES (?,?,?,?,?,0)
                    `;

                    await managerConn.query(sql3, [lastId, "全部", all_msg_user, send_time2, i==0?1:0]);

                }




                werewolfConn.commit();

            } else {
                let sql2 = `
            INSERT INTO wd_admin_user_msg_details (admin_user_msg_id,msg_user,send_time,isFirst) VALUES (?,?,?,1);
            `;
                await managerConn.query(sql2, [lastId, req.msg_user, send_time]);
            }



            await managerConn.commit();
        } catch (error) {
            await managerConn.rollback();
            logger.error(error);
            throw error
        }
    }

    public async checkBroadcast() {
        const { app, ctx, logger } = this;
        const manager = app.mysql.get(managerDb);

        logger.info('检测新手辅助机器人消息待发送！')

        if (this.app.config.newPlayerRobotRun != 1) {
            return;
        }

        logger.info('检测新手辅助机器人消息发送！')

        const managerConn = await manager.beginTransaction();
        try {
            let sql = `
            SELECT b.id,d.id AS details_id,b.desc,b.tend_type,b.tend_page,b.acTendUrl,b.msg_type,d.msg_user,d.type,b.domainName,d.all_msg_user AS all_msg_user,b.tend_shop,b.tab1,b.tab2
            FROM wf_admin_user_msg b
            INNER JOIN wd_admin_user_msg_details d  ON d.admin_user_msg_id = b.id 
            WHERE d.type = 0
            AND d.send_time <= NOW()
            `;

            let list = await managerConn.query(sql, []);

            let resultList: any[] = new Array();
            for (const item of list) {

                const sendPara = {
                    userNo: 7,
                    friendNo: item.msg_user == '全部' ? item.all_msg_user : item.msg_user,
                    content: item.msg_type == 0 ? item.desc : (item.domainName + item.desc),
                    tendType: item.tend_type,
                    tendPage: item.tend_page == null ? "" : item.tend_page,
                    tendUrl: item.acTendUrl == null ? "" : item.acTendUrl,
                    msgType: item.msg_type,
                    tendShop: item.tend_shop == null ? "" : item.tend_shop,
                    tab1: item.tab1 == null ? "" : item.tab1,
                    tab2: item.tab2 == null ? "" : item.tab2,
                }


                let success = await this.sendRedPacketByExchange(ctx, sendPara)
                if (!success) {
                    logger.error("新手辅助机器人消息发送失败4 param:", sendPara);
                } else {
                    logger.error("新手辅助机器人消息发送成功 id:", item.id);
                    resultList.push(item);
                }
            }
            for (const item of list) {
                let updateSql = `
                    UPDATE
                    wd_admin_user_msg_details
                    SET type = 2
                    WHERE id = ${item.details_id}
                `;


                let updateSql2 = `
                UPDATE
                wf_admin_user_msg
                SET type = 3
                WHERE id = ${item.id}
               `;
                await managerConn.query(updateSql, []);

                await managerConn.query(updateSql2, []);

                //分母
                let selectAll = `
            SELECT * FROM wf_admin_user_msg b
            INNER JOIN wd_admin_user_msg_details d  ON d.admin_user_msg_id = b.id 
                    WHERE d.admin_user_msg_id = ${item.id}
                `;

                let selectAllList = await managerConn.query(selectAll, []);

                //分子
                let selectDone = `
                SELECT * FROM wf_admin_user_msg b
                INNER JOIN wd_admin_user_msg_details d  ON d.admin_user_msg_id = b.id 
                        WHERE d.admin_user_msg_id = ${item.id} AND d.type = 2
                    `;

                let selectDoneList = await managerConn.query(selectDone, []);

                //改进度
                let progress = selectDoneList.length / selectAllList.length;
                let progressString = progress.toFixed(2);
                let progress100 = Number(progressString) * 100;

                let updateProgressSql = `
                    UPDATE
                    wf_admin_user_msg
                    SET progress=${Number(progress100).toFixed(2)} ,residueTime=${(selectAllList.length-selectDoneList.length)*time}
                    WHERE id = ${item.id}
                   `;
                await managerConn.query(updateProgressSql, []);

                ;

                let selectSql = `
            SELECT * FROM wf_admin_user_msg b
            INNER JOIN wd_admin_user_msg_details d  ON d.admin_user_msg_id = b.id 
                    WHERE d.admin_user_msg_id = ${item.id} AND d.type = 0
                `;

                let selectList = await managerConn.query(selectSql, []);

                if (!selectList || selectList.length === 0) {
                    let updateSql3 = `
                    UPDATE
                    wf_admin_user_msg
                    SET type = 2 ,progress=100
                    WHERE id = ${item.id}
                   `;
                    await managerConn.query(updateSql3, []);
                }
            }


            //----检查有没有发送完成的卡进度的
            let sqlStrIng = `
            SELECT * FROM wf_admin_user_msg WHERE type = 3
            `;

            let selectDetailsList = await managerConn.query(sqlStrIng, []);

            // for (let i = 0; i < selectDetailsList.length; i++) {
            for (let item of selectDetailsList) {

                // let item = selectDetailsList[i];
                let sqlStrCount = `
                SELECT * FROM wd_admin_user_msg_details WHERE type = 0 AND admin_user_msg_id = '${item.id}'
                `;

                let selectDetailsCount = await managerConn.query(sqlStrCount, []);

                if(selectDetailsCount.length == 0) {
                    let updateSql = `
                    UPDATE
                    wf_admin_user_msg
                    SET type = 2 ,progress=100,residueTime = 0
                    WHERE id = ${item.id}
                   `;
                    await managerConn.query(updateSql, []);
                }

            }
            //----


            await managerConn.commit();
        } catch (error) {
            await managerConn.rollback();
            logger.error(error);
            throw error
        }
    }

    async sendRedPacketByExchange(ctx, params) {
        const { app, logger } = this;

        let res = await ctx.curl('http://coder.53site.com/Werewolf/v/rongMsgImgTools.php', {
            method: 'POST',

            contentType: 'application/x-www-form-urlencoded',
            dataType: 'json',
            data: params,
            timeout: 3000, // 3 秒超时
        });

        logger.error("121321231231313132 param:", params);

        if (res && res.sign == 200) {
            // let arr = res.data.data.groupRedbagGetRecordList.map((v) => ({ id: Number(v), s: 0 }))
            // arr = shuffle(arr)
            // let redis = this.app.redis.get('tokenRedis');
            // const success = await redis.hset('wf_group_luckybox', res.data.data.groupRedBagRecordId, JSON.stringify(arr))
            return true
        } else {
            logger.error("新手辅助机器人消息发送失败3 param:", params, 'result: ', res);
        }

        return false
    }

}
