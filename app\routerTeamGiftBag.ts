
import { Router, Application } from "egg";
import { AccessRouteId, AccessRouteName } from "./model/accessRouteCof";
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

    const { controller, router } = app;

    router.post(`${API_VERSION}/werewolf/teamGiftBag/getTeamBagList`, accCtr(AccessRouteId.wolf_mall), controller.werewolf.teamGiftBag.getTeamGiftBagList)
    router.post(`${API_VERSION}/werewolf/teamGiftBag/insertTeamBag`, accCtr(AccessRouteId.wolf_mall), controller.werewolf.teamGiftBag.insertTeamGiftBag)
    router.post(`${API_VERSION}/werewolf/teamGiftBag/updateTeamBag`, accCtr(AccessRouteId.wolf_mall), controller.werewolf.teamGiftBag.updateTeamGiftBag)

    router.post(`${API_VERSION}/werewolf/teamGiftBag/getTeamBagItemList`, accCtr(AccessRouteId.wolf_mall), controller.werewolf.teamGiftBag.getTeamGiftBagItemList)
    router.post(`${API_VERSION}/werewolf/teamGiftBag/insertTeamBagItem`, accCtr(AccessRouteId.wolf_mall), controller.werewolf.teamGiftBag.insertTeamGiftBagItem)
    router.post(`${API_VERSION}/werewolf/teamGiftBag/updateTeamBagItem`, accCtr(AccessRouteId.wolf_mall), controller.werewolf.teamGiftBag.updateTeamGiftBagItem)

    router.post(`${API_VERSION}/werewolf/teamGiftBag/getItemDicList`, accCtr(AccessRouteId.wolf_mall), controller.werewolf.teamGiftBag.getItemDicList)

}


export default load