/*
 * @Description: 商城道具管理-控制类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: z<PERSON><PERSON>
 * @Date: 2020-04-24 13:00:00
 * @LastEditors: zhang<PERSON>
 * @LastEditTime: 2021-01-14 09:34:20
 */
import { Controller } from 'egg';
import BaseMegaController from './BaseMegaController';
import { IerrorMsg, HttpErr } from '../../model/common';

export default class MallItemController extends BaseMegaController {

    public async getBaseTypeList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.mallItem.getBaseTypeList();
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getLotteryBoxLevel(){
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const data = await ctx.service.werewolf.mallItem.getLotteryBoxLevel(requestBody);
            this.respSuccData(data)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async addRealityLotteryBox(){
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const data = await ctx.service.werewolf.mallItem.addRealityLotteryBox(requestBody);
            this.respSuccData(data)
        } catch (err) {
            this.respFail(err)
        }
    }
    
    public async deleteRealityLotteryBox(){
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const data = await ctx.service.werewolf.mallItem.deleteRealityLotteryBox(requestBody);
            this.respSuccData(data)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async updateRealityLotteryBox(){
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const data = await ctx.service.werewolf.mallItem.updateRealityLotteryBox(requestBody);
            this.respSuccData(data)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async getRealityLotteryBoxRecord(){
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const data = await ctx.service.werewolf.mallItem.getRealityLotteryBoxRecord(requestBody);
            this.respSuccData(data)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async getPropList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.mallItem.getTypeList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }
    public async getMallTagList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.mallItem.getMallTagList();
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }
    public async getMallConditionList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.mallItem.getMallConditionList();
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }
    public async insertMallItem() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mallItem.insertMallItem(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }
    public async updateMallItem() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mallItem.updateMallItem(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) {
            this.respFail(err)
        }
    }
    public async getJumpTypeList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.mallItem.getJumpTypeList();
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }
    public async getOpreateList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.mallItem.getOpreateList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async uploadTagInfo() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const result = await ctx.service.werewolf.mallItem.uploadTagInfo(requestBody);
            if (!result) {
                ctx.body = { err_code: HttpErr.BadRequest, err_msg: '上传图片信息重复' };
            } else {
                ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            }
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }
    public async delMallTag() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mallItem.delMallTag(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }
    public async changeMallItemDelsign() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mallItem.changeMallItemDelsign(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }
    public async changeMallItemDelsignConsole() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mallItem.changeMallItemDelsignConsole(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }
    public async changeEditManagerNum() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mallItem.changeEditManagerNum(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }

    public async getCoinList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.mallItem.getCoinList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }
}
