import BaseMegaController from "./BaseMegaController";
import {HttpErr, IerrorMsg} from "../../model/common";

export default class NoviceGroupController extends BaseMegaController {
    public async getGroups() {
        const {ctx, logger, app} = this;
        // 校验规则
        const rule = {};
        try {
            // 校验
            ctx.validate(rule);
            const responseBody = await ctx.service.werewolf.noviceGroup.getGroups(ctx.request.body);
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = {err_code: HttpErr.BadRequest, err_msg: '参数有误'};
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401 Unauthorized未鉴权
        }
    }

    public async getUsers() {
        const {ctx, logger, app} = this;
        // 校验规则
        const rule = {};
        try {
            // 校验
            ctx.validate(rule);
            const responseBody = await ctx.service.werewolf.noviceGroup.getUsers(ctx.request.body);
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = {err_code: HttpErr.BadRequest, err_msg: '参数有误'};
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401 Unauthorized未鉴权
        }
    }

    public async addUser() {
        const {ctx, logger, app} = this;
        // 校验规则
        const rule = {};
        try {
            // 校验
            ctx.validate(rule);
            const responseBody = await ctx.service.werewolf.noviceGroup.addUser(ctx.request.body);
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = {err_code: HttpErr.BadRequest, err_msg: '参数有误'};
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401 Unauthorized未鉴权
        }
    }

    public async kickUser() {
        const {ctx, logger, app} = this;
        // 校验规则
        const rule = {};
        try {
            // 校验
            ctx.validate(rule);
            const responseBody = await ctx.service.werewolf.noviceGroup.kickUser(ctx.request.body);
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = {err_code: HttpErr.BadRequest, err_msg: '参数有误'};
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401 Unauthorized未鉴权
        }
    }

}