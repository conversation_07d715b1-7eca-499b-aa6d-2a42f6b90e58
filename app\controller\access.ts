import { Controller } from 'egg';
import {
	IaccessRoutesRequest,
	IaccessRoutesResponse,
	IupdateRouteRequest,
	IupdateRouteResponse
} from '../model/manager';
import { IerrorMsg, HttpErr } from '../model/common';

/*
 * @Description: 访问权限控制类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2019-02-01 10:31:14
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2020-06-19 14:23:27
 */

export default class AccessController extends Controller {
	/**
  * @name: 用户路由列表
  * @msg: 
  * @param {type} 
  * @return: 
  */
	public async routes() {
		const { ctx, logger } = this;
		// 校验规则
		const loginRule = {
			uid: { type: 'int',min :1 }
		};
		try {
			//1 校验
			ctx.validate(loginRule);
			const request: IaccessRoutesRequest = ctx.request.body;
			// tslint:disable-next-line:one-variable-per-declaration
			const response: IaccessRoutesResponse = await ctx.service.access.routes(request);
			ctx.body = response;
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: "参数错误" };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest;
		}
	}

	public async getUserAccessList() {
		const { ctx, logger } = this;
		// 校验规则
		const loginRule = {
			uid: { type: 'int',min :1 }
		};
		try {
			//1 校验
			ctx.validate(loginRule);
			const request: IaccessRoutesRequest = ctx.request.body;
			// tslint:disable-next-line:one-variable-per-declaration
			const response: IaccessRoutesResponse = await ctx.service.access.getUserAccessList(request);
			ctx.body = response;
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: "参数错误" };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest;
		}
	}
	

	/**
     * @name: 权限更新
     * @msg: 
     * @param {type} 
     * @return: 
     */
	public async update() {
		const { ctx, logger } = this;
		// 校验规则
		const loginRule = {
			uid: { type: 'int',min :1 },
			routeId: { type: 'int',min :1,max:1000 },
			accessEnable: { type: 'int' }
		};
		try {
			//1 校验
			ctx.validate(loginRule);
			const request: IupdateRouteRequest = ctx.request.body;
			logger.info("更新权限",request);
			const response: IupdateRouteResponse = await ctx.service.access.update(request);
			//清理玩家token
			await ctx.service.user.clearRedisToken(request.uid);
			ctx.body = response;
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: "参数错误" };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest;
		}
	}
}
