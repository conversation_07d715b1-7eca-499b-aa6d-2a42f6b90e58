import BaseMegaController from "./BaseMegaController";
import {InewPlayerRobotListReq, InewPlayerRobotListResp} from "../../model/werewolf";
import {HttpErr, IerrorMsg} from "../../model/common";
import {
    InewPlayerRobotInsertParams,
    InewPlayerRobotUpdateParams,
    InewPlayerRobotUpdateTypeParams,
    InewPlayerRobotSearchParams
} from "../../model/newPlayerRobotDto";

export default class SsFrameTimeController extends BaseMegaController{
  
    public async searchSSFrames(req: any){
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: any = ctx.request.body;
            const responseBody: any = await ctx.service.werewolf.ssFrameTime.searchSSFrames(requestBody);
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }



    public async updateSSFrameTime(req: any){
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: any = ctx.request.body;
            const responseBody = await ctx.service.werewolf.ssFrameTime.updateSSFrameTime(requestBody);
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

}