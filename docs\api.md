# API文档

## 用户登录认证

- GET `${API_VERSION}/user/env`: 获取环境信息
- GET `${API_VERSION}/user/publicKey`: 获取公钥
- POST `${API_VERSION}/user/auth`: 用户认证

## 管理员操作

- GET `${API_VERSION}/manager/:id/info`: 获取管理员信息
- POST `${API_VERSION}/manager/logoutAll`: 登出所有管理员
- GET `${API_VERSION}/manager/userList`: 获取管理员列表
- POST `${API_VERSION}/manager/analysis/resetPwd`: 重置密码
- POST `${API_VERSION}/manager/Deleteuser`: 删除管理员
- POST `${API_VERSION}/manager/Createuser`: 创建管理员

## 权限管理

- POST `${API_VERSION}/manager/access/routes`: 获取路由权限
- POST `${API_VERSION}/manager/access/getUserAccessList`: 获取用户权限列表
- POST `${API_VERSION}/manager/access/update`: 更新权限

## 狼人杀游戏

### 玩家资产

- POST `${API_VERSION}/werewolf/treasure/updateFrameDelsign`: 更新头像框设计
- POST `${API_VERSION}/werewolf/treasure/uploadLettering`: 上传刻字
- POST `${API_VERSION}/werewolf/treasure/delLettering`: 删除刻字
- POST `${API_VERSION}/werewolf/treasure/updateUserLettering`: 更新用户刻字
- POST `${API_VERSION}/werewolf/treasure/updateAchieveDelsign`: 更新成就设计
- POST `${API_VERSION}/werewolf/treasure/redbag`: 获取红包详情
- GET `${API_VERSION}/werewolf/treasure/tabnormalDescs`: 获取玩家资产tab描述

### 玩家状态

- POST `${API_VERSION}/werewolf/playerStatus/resetAvatar`: 重置头像
- POST `${API_VERSION}/werewolf/playerStatus/updateNickname`: 更新昵称
- POST `${API_VERSION}/werewolf/playerStatus/updateScore`: 更新分数
- POST `${API_VERSION}/werewolf/playerStatus/default`: 获取默认状态
- POST `${API_VERSION}/werewolf/playerStatus/getPlayerId`: 获取玩家ID
- POST `${API_VERSION}/werewolf/playerStatus/getPhoneNumber`: 获取手机号
- POST `${API_VERSION}/werewolf/playerStatus/brushScore`: 刷新分数
- POST `${API_VERSION}/werewolf/playerStatus/newBrushScore`: 新版刷新分数
- POST `${API_VERSION}/werewolf/playerStatus/escape`: 逃跑
- POST `${API_VERSION}/werewolf/playerStatus/gameSpeak`: 游戏发言
- POST `${API_VERSION}/werewolf/playerStatus/playerBg`: 玩家背景
- POST `${API_VERSION}/werewolf/playerStatus/report`: 举报
- POST `${API_VERSION}/werewolf/playerStatus/report/video`: 举报视频列表
- POST `${API_VERSION}/werewolf/playerStatus/illegalImages`: 非法图片
- POST `${API_VERSION}/werewolf/playerStatus/list/shutter`: 获取禁言列表
- POST `${API_VERSION}/werewolf/playerStatus/list/boardcastImprison`: 获取广播禁言列表
- POST `${API_VERSION}/werewolf/playerStatus/analysis/doBanned`: 封禁用户
- POST `${API_VERSION}/werewolf/playerStatus/analysis/getUserBanId`: 获取封禁用户ID
- POST `${API_VERSION}/werewolf/playerStatus/analysis/doShutter`: 禁言
- POST `${API_VERSION}/werewolf/playerStatus/analysis/dobarrage`: 弹幕封禁
- POST `${API_VERSION}/werewolf/playerStatus/analysis/doMobile`: 手机封禁
- POST `${API_VERSION}/werewolf/playerStatus/analysis/doBanEntertainment`: 娱乐模式封禁
- POST `${API_VERSION}/werewolf/playerStatus/entertainment/searchReportUser`: 娱乐模式搜索举报用户
- POST `${API_VERSION}/werewolf/playerStatus/entertainment/searchReportRoom`: 娱乐模式搜索举报房间
- POST `${API_VERSION}/werewolf/playerStatus/entertainment/searchGroupRooms`: 娱乐模式搜索群组房间
- POST `${API_VERSION}/werewolf/playerStatus/entertainment/searchReportUserDetail`: 娱乐模式搜索举报用户详情

### 清逃跑

- POST `${API_VERSION}/werewolf/clearEscape/list`: 获取逃跑列表
- POST `${API_VERSION}/werewolf/clearEscape/do`: 清除逃跑

### udid查询

- POST `${API_VERSION}/werewolf/udidQuery/getUdidQueryList`: 获取udid查询列表
- POST `${API_VERSION}/werewolf/udidQuery/userVerifyStatus`: 用户验证状态
- POST `${API_VERSION}/werewolf/udidQuery/removeIdentifyByUdid`: 通过udid移除认证

### 用户认证

- POST `${API_VERSION}/werewolf/identification/identify`: 用户认证
- POST `${API_VERSION}/werewolf/identification/identifyByUdid`: 通过udid认证
- POST `${API_VERSION}/werewolf/identification/cancelIdentify`: 取消认证

### 弹幕封禁

- POST `${API_VERSION}/werewolf/banDanMu/getBanDanMuList`: 获取弹幕封禁列表

### 竞技场

- POST `${API_VERSION}/werewolf/arenaArea/getArenaAreaList`: 获取竞技场列表

### 阿里udid

- POST `${API_VERSION}/werewolf/aliUmid/getAliUmidList`: 获取阿里umid列表
- POST `${API_VERSION}/werewolf/aliUmid/banUserAliUmid`: 封禁用户阿里umid

### 应用管理

#### 广告位管理

- POST `${API_VERSION}/werewolf/advertising/list`: 获取广告位列表
- POST `${API_VERSION}/werewolf/advertising/analysis/operation`: 广告位操作

#### 开屏管理

- POST `${API_VERSION}/werewolf/advertising/addScreenAdImg`: 添加开屏图
- POST `${API_VERSION}/werewolf/advertising/delScreenAdImg`: 删除开屏图
- POST `${API_VERSION}/werewolf/advertising/saveAdInfo`: 保存开屏图信息

#### 头像框管理

- POST `${API_VERSION}/werewolf/avatarframe/userName`: 根据ID查询用户名
- POST `${API_VERSION}/werewolf/avatarframe/getAllFrames`: 获取所有头像框
- POST `${API_VERSION}/werewolf/avatarframe/getStaticAvatarFrameList`: 获取静态头像框列表
- POST `${API_VERSION}/werewolf/avatarframe/giveFrameToUser`: 赠送头像框给用户
- POST `${API_VERSION}/werewolf/avatarframe/frameIsShow`: 头像框上下架
- POST `${API_VERSION}/werewolf/avatarframe/uploadAvatarFrame`: 上传头像框基本信息
- POST `${API_VERSION}/werewolf/avatarframe/uploadFrameComplete`: 上传头像框完成

#### 成就管理

- POST `${API_VERSION}/werewolf/achievement/getList`: 获取成就列表
- POST `${API_VERSION}/werewolf/achievement/updateDelsign`: 成就上下架
- POST `${API_VERSION}/werewolf/achievement/sendAchieve`: 发送成就
- POST `${API_VERSION}/werewolf/achievement/uploadBase`: 上传成就基本信息
- POST `${API_VERSION}/werewolf/achievement/complete`: 完成成就
- POST `${API_VERSION}/werewolf/achievement/completeNoteOrder`: 完成头像框刻字成就

#### 活动状态管理

- POST `${API_VERSION}/werewolf/activity/getActivityList`: 获取活动列表
- POST `${API_VERSION}/werewolf/activity/operHallState`: 大厅入口显示隐藏
- POST `${API_VERSION}/werewolf/activity/operActivityState`: 活动开启关闭
- POST `${API_VERSION}/werewolf/activity/changeActivityName`: 修改活动名称
- POST `${API_VERSION}/werewolf/activity/changeActivityPrizeNum`: 修改活动奖励数量

#### 发送邮件

- POST `${API_VERSION}/werewolf/sendEmail/sendEmailsToUsers`: 给用户发送邮件

#### banner图管理

- POST `${API_VERSION}/werewolf/bannerControl/bannerList`: 获取banner列表
- POST `${API_VERSION}/werewolf/bannerControl/uploadBannerInfo`: 上传banner信息
- POST `${API_VERSION}/werewolf/bannerControl/bannerIsShow`: 控制banner显示隐藏
- POST `${API_VERSION}/werewolf/bannerControl/delBannerInfo`: 删除banner信息
- POST `${API_VERSION}/werewolf/bannerControl/getBetaBannerList`: 获取灰度banner列表
- POST `${API_VERSION}/werewolf/bannerControl/addBetaBanner`: 添加灰度banner
- POST `${API_VERSION}/werewolf/bannerControl/updateBetaBannerInfo`: 更新灰度banner信息

#### 游戏配置管理

- POST `${API_VERSION}/werewolf/gameConfig/createOpen`: 创建开放配置
- POST `${API_VERSION}/werewolf/gameConfig/createOpenList`: 获取开放配置列表
- POST `${API_VERSION}/werewolf/gameConfig/getTboxSeason`: 获取宝箱赛季列表
- POST `${API_VERSION}/werewolf/gameConfig/updateEndTime`: 更新结束时间
- POST `${API_VERSION}/werewolf/gameConfig/createNewSeason`: 创建新赛季
- GET `${API_VERSION}/werewolf/gameConfig/getBoxList`: 获取宝箱列表
- GET `${API_VERSION}/werewolf/gameConfig/getAllBoxList`: 获取所有宝箱列表
- GET `${API_VERSION}/werewolf/gameConfig/getProps`: 获取道具列表
- GET `${API_VERSION}/werewolf/gameConfig/getAllProps`: 获取所有道具列表
- POST `${API_VERSION}/werewolf/gameConfig/updateProps`: 更新道具
- POST `${API_VERSION}/werewolf/gameConfig/insertProps`: 插入道具
- GET `${API_VERSION}/werewolf/gameConfig/getAnimation`: 获取动画列表
- GET `${API_VERSION}/werewolf/gameConfig/getAnimationRole`: 获取动画角色
- POST `${API_VERSION}/werewolf/gameConfig/updateAni`: 更新动画

#### 推送管理

- POST `${API_VERSION}/werewolf/push/newUser`: 给新注册用户推送
- POST `${API_VERSION}/werewolf/push/oldUser15`: 给沉默15天老用户推送
- POST `${API_VERSION}/werewolf/push/oldUser30`: 给沉默30天老用户推送
- POST `${API_VERSION}/werewolf/push/customUser`: 给指定用户推送
- POST `${API_VERSION}/werewolf/pushV2/create`: 创建推送
- POST `${API_VERSION}/werewolf/pushV2/list`: 获取推送列表
- POST `${API_VERSION}/werewolf/pushV2/edit`: 编辑推送
- POST `${API_VERSION}/werewolf/pushV2/delete`: 删除推送
- GET `${API_VERSION}/werewolf/pushV2/test`: 测试推送服务

### 游戏记录

- POST `${API_VERSION}/werewolf/gameRecord/list`: 获取玩家游戏列表
- POST `${API_VERSION}/werewolf/gameRecord/gameInfoList`: 获取游戏信息列表
- POST `${API_VERSION}/werewolf/gameRecord/downloadGameInfo`: 下载游戏信息
- POST `${API_VERSION}/werewolf/gameRecord/detail`: 获取游戏详情
- POST `${API_VERSION}/werewolf/gameRecord/replay`: 游戏复盘

### 融云记录查询

- POST `${API_VERSION}/werewolf/rongCloud/info`: 获取融云记录信息

### 世界频道

- POST `${API_VERSION}/werewolf/broadcast/list`: 获取世界频道聊天列表
- POST `${API_VERSION}/werewolf/broadcast/ban`: 屏蔽聊天记录

### 邮件查询

- POST `${API_VERSION}/werewolf/email/list`: 获取邮件列表
- POST `${API_VERSION}/werewolf/email/detail`: 获取邮件详情

### 服务器监控

- POST `${API_VERSION}/dev/server/monitor/business/list`: 获取服务器监控业务列表


### 运营活动配置

- POST `${API_VERSION}/werewolf/atyAward/atyList`: 获取活动列表
- POST `${API_VERSION}/werewolf/atyAward/groupList`: 获取活动奖励组列表
- POST `${API_VERSION}/werewolf/atyAward/confList`: 获取活动奖励列表
- POST `${API_VERSION}/werewolf/atyAward/insertAwardGroup`: 插入奖励组
- POST `${API_VERSION}/werewolf/atyAward/insertAward`: 插入奖励
