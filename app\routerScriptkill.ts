/*
 * @Description: 剧本杀
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhanglu
 * @Date: 2020-04-22 11:50:11
 * @LastEditors: zhanglu
 */
import { Router, Application } from "egg";
import { AccessRouteId, AccessRouteName } from "./model/accessRouteCof";
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

    const { controller, router } = app;

    router.post(`${API_VERSION}/scriptkill/merchant/getMerchantList`, accCtr(AccessRouteId.app_script), controller.werewolf.scriptkill.getMerchantList)
    router.post(`${API_VERSION}/scriptkill/merchant/getMerchantSimpleList`, accCtr(AccessRouteId.app_script), controller.werewolf.scriptkill.getMerchantSimpleList)
    router.post(`${API_VERSION}/scriptkill/merchant/getMerchantListCount`, accCtr(AccessRouteId.app_script), controller.werewolf.scriptkill.getMerchantListCount)
    router.post(`${API_VERSION}/scriptkill/merchant/getScriptList`, accCtr(AccessRouteId.app_script), controller.werewolf.scriptkill.getScriptList)
    router.post(`${API_VERSION}/scriptkill/merchant/confirmMerchant`, accCtr(AccessRouteId.app_script), controller.werewolf.scriptkill.confirmMerchant)
    router.post(`${API_VERSION}/scriptkill/merchant/confirmScript`, accCtr(AccessRouteId.app_script), controller.werewolf.scriptkill.confirmScript)
    router.post(`${API_VERSION}/scriptkill/merchant/getScriptSimpleList`, accCtr(AccessRouteId.app_script), controller.werewolf.scriptkill.getScriptSimpleList)
    router.post(`${API_VERSION}/scriptkill/merchant/refuseMerchant`, accCtr(AccessRouteId.app_script), controller.werewolf.scriptkill.refuseMerchant)
    router.post(`${API_VERSION}/scriptkill/merchant/updateMerchant`, accCtr(AccessRouteId.app_script), controller.werewolf.scriptkill.updateMerchant)
    router.post(`${API_VERSION}/scriptkill/merchant/getCityList`, accCtr(AccessRouteId.app_script), controller.werewolf.scriptkill.getCityList)
    router.post(`${API_VERSION}/scriptkill/merchant/refuseScript`, accCtr(AccessRouteId.app_script), controller.werewolf.scriptkill.refuseScript)
    router.post(`${API_VERSION}/scriptkill/merchant/getPictureList`, accCtr(AccessRouteId.app_script), controller.werewolf.scriptkill.getPictureList)
    router.post(`${API_VERSION}/scriptkill/merchant/updatePictureUrl`, accCtr(AccessRouteId.app_script), controller.werewolf.scriptkill.updatePictureUrl)
    router.post(`${API_VERSION}/scriptkill/merchant/updatePicture`, accCtr(AccessRouteId.app_script), controller.werewolf.scriptkill.updatePicture)
    router.post(`${API_VERSION}/scriptkill/merchant/insertPicture`, accCtr(AccessRouteId.app_script), controller.werewolf.scriptkill.insertPicture)
    router.post(`${API_VERSION}/scriptkill/merchant/updateScriptUrl`, accCtr(AccessRouteId.app_script), controller.werewolf.scriptkill.updateScriptUrl)

    router.post(`${API_VERSION}/scriptkill/merchant/getAuditFlowList`, accCtr(AccessRouteId.script_audit_list), controller.werewolf.scriptkill.getAuditFlowList)
    router.post(`${API_VERSION}/scriptkill/merchant/getAuditFlowListCount`, accCtr(AccessRouteId.script_audit_list), controller.werewolf.scriptkill.getAuditFlowListCount)
    router.post(`${API_VERSION}/scriptkill/merchant/getOperateUserList`, accCtr(AccessRouteId.script_audit_list), controller.werewolf.scriptkill.getOperateUserList)
    router.post(`${API_VERSION}/scriptkill/merchant/getScriptEditAuditFlowListCount`, accCtr(AccessRouteId.script_audit_list), controller.werewolf.scriptkill.getScriptEditAuditFlowListCount)
    router.post(`${API_VERSION}/scriptkill/merchant/getScriptEditAuditFlowList`, accCtr(AccessRouteId.script_audit_list), controller.werewolf.scriptkill.getScriptEditAuditFlowList)

    router.post(`${API_VERSION}/scriptkill/merchant/getMerchantTempList`, accCtr(AccessRouteId.app_script), controller.werewolf.scriptkill.getMerchantTempList)
    router.post(`${API_VERSION}/scriptkill/merchant/getMerchantTempListCount`, accCtr(AccessRouteId.app_script), controller.werewolf.scriptkill.getMerchantTempListCount)
    router.post(`${API_VERSION}/scriptkill/merchant/refuseMerchantTemp`, accCtr(AccessRouteId.app_script), controller.werewolf.scriptkill.refuseMerchantTemp)
    router.post(`${API_VERSION}/scriptkill/merchant/confirmMerchantTemp`, accCtr(AccessRouteId.app_script), controller.werewolf.scriptkill.confirmMerchantTemp)
    router.post(`${API_VERSION}/scriptkill/merchant/updateScript`, accCtr(AccessRouteId.app_script), controller.werewolf.scriptkill.updateScript)
    router.post(`${API_VERSION}/scriptkill/merchant/getMerchantDicList`, accCtr(AccessRouteId.app_script), controller.werewolf.scriptkill.getMerchantDicList)
    router.post(`${API_VERSION}/scriptkill/merchant/refuseMerchantPassed`, accCtr(AccessRouteId.app_script), controller.werewolf.scriptkill.refuseMerchantPassed)

    router.post(`${API_VERSION}/scriptkill/merchant/getUserInfo`, accCtr(AccessRouteId.script_user_info_manager), controller.werewolf.scriptkill.getUserInfo)
    router.post(`${API_VERSION}/scriptkill/merchant/getUserInfoAvatar`, accCtr(AccessRouteId.script_user_info_manager), controller.werewolf.scriptkill.getUserInfoAvatar)
    router.post(`${API_VERSION}/scriptkill/merchant/refuseUserAvatar`, accCtr(AccessRouteId.script_user_info_manager), controller.werewolf.scriptkill.refuseUserAvatar)
    router.post(`${API_VERSION}/scriptkill/merchant/getUserInfoBg`, accCtr(AccessRouteId.script_user_info_manager), controller.werewolf.scriptkill.getUserInfoBg)
    router.post(`${API_VERSION}/scriptkill/merchant/refuseUserBg`, accCtr(AccessRouteId.script_user_info_manager), controller.werewolf.scriptkill.refuseUserBg)
    router.post(`${API_VERSION}/scriptkill/merchant/getReportInfo`, accCtr(AccessRouteId.script_user_info_manager), controller.werewolf.scriptkill.getReportInfo)
    router.post(`${API_VERSION}/scriptkill/merchant/getReportInfoCount`, accCtr(AccessRouteId.script_user_info_manager), controller.werewolf.scriptkill.getReportInfoCount)


    // 热门剧本
    router.post(`${API_VERSION}/scriptkill/merchant/getScriptRecommendList`, accCtr(AccessRouteId.script_hot_script), controller.werewolf.scriptkill.getScriptRecommendList)
    router.post(`${API_VERSION}/scriptkill/merchant/getScript`, accCtr(AccessRouteId.script_hot_script), controller.werewolf.scriptkill.getScript)
    router.post(`${API_VERSION}/scriptkill/merchant/getScriptSourceList`, accCtr(AccessRouteId.script_hot_script), controller.werewolf.scriptkill.getScriptSourceList)
    router.post(`${API_VERSION}/scriptkill/merchant/insertScriptRecommend`, accCtr(AccessRouteId.script_hot_script), controller.werewolf.scriptkill.insertScriptRecommend)
    router.post(`${API_VERSION}/scriptkill/merchant/updateScriptRecommend`, accCtr(AccessRouteId.script_hot_script), controller.werewolf.scriptkill.updateScriptRecommend)


    router.post(`${API_VERSION}/scriptkill/merchant/getMomentList`, accCtr(AccessRouteId.script_moment_list), controller.werewolf.scriptkill.getMomentList)
    router.post(`${API_VERSION}/scriptkill/merchant/getMomentListCount`, accCtr(AccessRouteId.script_moment_list), controller.werewolf.scriptkill.getMomentListCount)
    router.post(`${API_VERSION}/scriptkill/merchant/insertMoment`, accCtr(AccessRouteId.script_moment_list), controller.werewolf.scriptkill.insertMoment)
    router.post(`${API_VERSION}/scriptkill/merchant/updateMoment`, accCtr(AccessRouteId.script_moment_list), controller.werewolf.scriptkill.updateMoment)
    router.post(`${API_VERSION}/scriptkill/merchant/updateMomentDelsign`, accCtr(AccessRouteId.script_moment_list), controller.werewolf.scriptkill.updateMomentDelsign)
    router.post(`${API_VERSION}/scriptkill/merchant/updateMomentSort`, accCtr(AccessRouteId.script_moment_list), controller.werewolf.scriptkill.updateMomentSort)
    router.post(`${API_VERSION}/scriptkill/merchant/getMomentPicList`, accCtr(AccessRouteId.script_moment_list), controller.werewolf.scriptkill.getMomentPicList)
    router.post(`${API_VERSION}/scriptkill/merchant/insertMomentPicture`, accCtr(AccessRouteId.script_moment_list), controller.werewolf.scriptkill.insertMomentPicture)
    router.post(`${API_VERSION}/scriptkill/merchant/updateMomentPicture`, accCtr(AccessRouteId.script_moment_list), controller.werewolf.scriptkill.updateMomentPicture)
    router.post(`${API_VERSION}/scriptkill/merchant/updateMomentPictureUrl`, accCtr(AccessRouteId.script_moment_list), controller.werewolf.scriptkill.updateMomentPictureUrl)
    router.post(`${API_VERSION}/scriptkill/merchant/getMomentPictureCanShowCount`, accCtr(AccessRouteId.script_moment_list), controller.werewolf.scriptkill.getMomentPictureCanShowCount)
    router.post(`${API_VERSION}/scriptkill/mainPageBanner/getMerchantBanner`, accCtr(AccessRouteId.script_banner_main_page), controller.werewolf.scriptkill.getMerchantBannerList)
    router.post(`${API_VERSION}/scriptkill/mainPageBanner/delBannerInfo`, accCtr(AccessRouteId.script_banner_main_page), controller.werewolf.scriptkill.delBanner)
    router.post(`${API_VERSION}/scriptkill/mainPageBanner/shelvesBanner`, accCtr(AccessRouteId.script_banner_main_page), controller.werewolf.scriptkill.helvesBanner)
    router.post(`${API_VERSION}/scriptkill/mainPageBanner/updateBannerInfo`, accCtr(AccessRouteId.script_banner_main_page), controller.werewolf.scriptkill.updateBannerInfo)
    router.post(`${API_VERSION}/scriptkill/mainPageBanner/updateMainPageBannerUrl`, accCtr(AccessRouteId.script_banner_main_page), controller.werewolf.scriptkill.updateMainPageBannerUrl)
    router.post(`${API_VERSION}/scriptkill/mainPageBanner/insertMainPageBanner`, accCtr(AccessRouteId.script_banner_main_page), controller.werewolf.scriptkill.insertMainPageBanner)
    router.post(`${API_VERSION}/scriptkill/mainPageBanner/getOpenAdList`, accCtr(AccessRouteId.script_banner_main_page), controller.werewolf.scriptkill.getOpenAdList)
    router.post(`${API_VERSION}/scriptkill/mainPageBanner/shelvesOpenAd`, accCtr(AccessRouteId.script_banner_main_page), controller.werewolf.scriptkill.shelvesOpenAd)
    router.post(`${API_VERSION}/scriptkill/mainPageBanner/delOpenAd`, accCtr(AccessRouteId.script_banner_main_page), controller.werewolf.scriptkill.delOpenAd)
    router.post(`${API_VERSION}/scriptkill/mainPageBanner/insertOpenAd`, accCtr(AccessRouteId.script_banner_main_page), controller.werewolf.scriptkill.insertOpenAd)
    router.post(`${API_VERSION}/scriptkill/mainPageBanner/updateOpenAdImgUrl`, accCtr(AccessRouteId.script_banner_main_page), controller.werewolf.scriptkill.updateOpenAdImgUrl)
    router.post(`${API_VERSION}/scriptkill/mainPageBanner/updateOpenAdInfo`, accCtr(AccessRouteId.script_banner_main_page), controller.werewolf.scriptkill.updateOpenAdInfo)

}

export default load
