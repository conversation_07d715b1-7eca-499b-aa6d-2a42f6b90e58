/*
 * @Description: 商城道具服务类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: z<PERSON><PERSON>
 * @Date: 2020-04-24 11:35:39
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-05-18 14:54:25
 */
import BaseMegaService from './BaseMegaService';
import { IgroupBadge, IgroupBanner, IgroupFrame } from './../../model/groupPropsManager';
export default class GroupPropsService extends BaseMegaService {

    public async getGroupBadgeList() {
        const { app, ctx, logger } = this;
        try {
            const sqlStr = `
            SELECT \`id\`, \`name\`, \`imgname\`, \`remark\`, \`delsign\`, \`unlock_level\` FROM \`group_badge\`
            `;
            return await this.selectList(sqlStr, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getGroupFrameList() {
        const { app, ctx, logger } = this;
        try {
            const sqlStr = `
            SELECT \`id\`, \`name\`, \`imgname\`, \`remark\`, \`delsign\` FROM \`group_frame\` WHERE id > 1 
            `;
            return await this.selectList(sqlStr, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getGroupBannerList() {
        const { app, ctx, logger } = this;
        try {
            const sqlStr = `
            SELECT \`id\`, \`name\`,\`delsign\` ,\`createtime\`  FROM \`group_banner\`
            `;
            return await this.selectList(sqlStr, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getGroupLevelList() {
        const { app, ctx, logger } = this;
        try {
            const sqlStr = `
            SELECT \`id\`, \`name\`, \`max_user\`, \`second_num\`, \`third_num\`, \`max_active_num\`, \`limit_active\`, \`delsign\`, \`last_level_active_num\` FROM \`group_level\` WHERE id < 6 ORDER BY id ASC 
            `;
            return await this.selectList(sqlStr, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateGroupBadge(req: IgroupBadge) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = `UPDATE  group_badge
                        SET  
                         name = ?,
                         imgname = ? ,
                         remark = ? ,
                         unlock_level = ? ,
                         delsign = ? 
                        WHERE
                             id  = ? `;
            await conn.query(sql, [
                req.name,
                req.imgname,
                req.remark,
                req.unlock_level,
                req.delsign,
                req.id]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async updateGroupFrame(req: IgroupFrame) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = `UPDATE group_frame
                        SET  
                         name = ?,
                         imgname = ? ,
                         remark = ? ,
                         delsign = ? 
                        WHERE
                             id  = ? `;
            await conn.query(sql, [
                req.name,
                req.imgname,
                req.remark,
                req.delsign,
                req.id]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async updateGroupBanner(req: IgroupBanner) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');

        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = `UPDATE group_banner
                        SET  
                         name = ?,
                         delsign = ? 
                        WHERE
                             id  = ? `;
            await conn.query(sql, [
                req.name,
                req.delsign,
                req.id]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async insertGroupBadge(req: IgroupBadge) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
           INSERT INTO \`group_badge\` (
               \`name\`,
               \`remark\`,
               \`unlock_level\`,
               \`delsign\`
           )
           VALUES
               (
                   ?,
                   ?,
                   ?,
                   ?
               );
           `;
            const result = await conn.query(sql, [
                req.name,
                req.remark,
                req.unlock_level,
                req.delsign]);

            req.id = result.insertId;

            let sqlInsertItemDic = ` 
                INSERT INTO \`item_dic\` (\`item_cate_id\`, \`item_id\`, \`name\`, \`version\`, \`delsign\`,remark )
                VALUES (?,?,?,?,?,?);
            `;
            await conn.query(sqlInsertItemDic, [1051, req.id, req.name, 0, 0, req.remark]);

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async insertGroupFrame(req: IgroupFrame) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
            INSERT INTO \`group_frame\` (
                \`name\`,
                \`remark\`,
                \`delsign\`
            )
            VALUES
                (
                    ?,
                    ?,
                    ?
                );
            `;
            const result = await conn.query(sql, [
                req.name,
                req.remark,
                req.delsign]);
            req.id = result.insertId;
            let sqlInsertItemDic = ` 
                INSERT INTO \`item_dic\` (\`item_cate_id\`, \`item_id\`, \`name\`, \`version\`, \`delsign\`,remark )
                VALUES (?,?,?,?,?,?);
            `;
            await conn.query(sqlInsertItemDic, [1052, req.id, req.name, 0, 0, req.remark]);

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async insertGroupBanner(req: IgroupBanner) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
            INSERT INTO \`group_banner\` (
                \`name\`,
                \`delsign\`
            )
            VALUES
                (
                    ?,
                    ?
                );
            `;
            const result = await conn.query(sql, [
                req.name,
                req.delsign]);
            req.id = result.insertId;
            let sqlInsertItemDic = ` 
                INSERT INTO \`item_dic\` (\`item_cate_id\`, \`item_id\`, \`name\`, \`version\`, \`delsign\` )
                VALUES (?,?,?,?,?);
            `;
            await conn.query(sqlInsertItemDic, [1053, req.id, req.name, 0, 0]);

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }
}
