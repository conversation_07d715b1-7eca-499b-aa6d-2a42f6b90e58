/*
 * @Description: 巅峰狼王路由
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-03-24 11:50:11
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2020-06-18 19:22:08
 */
import { Router, Application } from "egg";
import { AccessRouteId } from './model/accessRouteCof';
import { once } from 'process';
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

    const { controller, router } = app;
    //【狼王集结】
    // 基础信息
    router.post(`${API_VERSION}/werewolf/wolfking/baseInfo`,accCtr(AccessRouteId.wolf_wolf_king), controller.werewolf.wolfKing.baseInfo);
    // 每期状态信息
    router.post(`${API_VERSION}/werewolf/wolfking/baseItem`, accCtr(AccessRouteId.wolf_wolf_king), controller.werewolf.wolfKing.baseItem);
    // 积分排行榜
    router.post(`${API_VERSION}/werewolf/wolfking/score/list`,accCtr(AccessRouteId.wolf_wolf_king),  controller.werewolf.wolfKing.scoreRank);
    // 人气排行榜
    router.post(`${API_VERSION}/werewolf/wolfking/popular/list`, accCtr(AccessRouteId.wolf_wolf_king), controller.werewolf.wolfKing.popularRank);
    // 狼王开关
    router.post(`${API_VERSION}/werewolf/wolfking/king/switch`, accCtr(AccessRouteId.wolf_wolf_king), controller.werewolf.wolfKing.kingSwitch);
    // 投票开关
    router.post(`${API_VERSION}/werewolf/wolfking/vote/switch`,accCtr(AccessRouteId.wolf_wolf_king),  controller.werewolf.wolfKing.voteSwitch);
    // 增加主播1积分
    router.post(`${API_VERSION}/werewolf/wolfking/score/add`, accCtr(AccessRouteId.wolf_wolf_king), controller.werewolf.wolfKing.scoreAdd);
    // 减少主播1积分
    router.post(`${API_VERSION}/werewolf/wolfking/score/sub`, accCtr(AccessRouteId.wolf_wolf_king), controller.werewolf.wolfKing.scoreSub);
    // 平分代币
    router.post(`${API_VERSION}/werewolf/wolfking/gradecoin`,accCtr(AccessRouteId.wolf_wolf_king),  controller.werewolf.wolfKing.gradeCoin);
    // 爬虫一次
    router.post(`${API_VERSION}/werewolf/wolfking/crawler/once`,accCtr(AccessRouteId.wolf_wolf_king),  controller.werewolf.wolfKing.crawlerOnce);
    // 人气榜开关
    router.post(`${API_VERSION}/werewolf/wolfking/popular/switch`, accCtr(AccessRouteId.wolf_wolf_king), controller.werewolf.wolfKing.popularSwitch);
    // 爬虫开关
    router.post(`${API_VERSION}/werewolf/wolfking/crawler/switch`,accCtr(AccessRouteId.wolf_wolf_king),  controller.werewolf.wolfKing.crawlerSwitch);
    // 变更期数
    router.post(`${API_VERSION}/werewolf/wolfking/changeActingQuarter`, accCtr(AccessRouteId.wolf_wolf_king), controller.werewolf.wolfKing.changeActingQuarter);
    // test
    router.get(`${API_VERSION}/test/crawler`,controller.werewolf.wolfKing.getPopular);

}

export default load
