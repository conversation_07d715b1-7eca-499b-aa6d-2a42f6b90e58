<!--
 * @Description: 无
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2018-11-20 18:00:27
 * @LastEditors: hammercui
 * @LastEditTime: 2020-06-13 18:47:31
 -->
>typescript && egg

### 开发
```bash
$master分支
$ yarn
$ npm run dev
$ open http://localhost:7002/
```

### 部署

#### 青岛beta
```bash
$master分支
$ yarn
$ npm run ci
$ npm run beta
```
#### 生产服务器
```bash
$product分支
$ yarn
$ npm run ci
$ npm run prod
```
### 技术方案
1. 登陆认证：基于rsa的非对称加密,认证成功，返回jwt
2. 接口访问鉴权：header携带jwt
### Requirement

- Node.js 8.x
- Typescript 2.8+

日志目录：`~/log/ConsoleSystemServer/comm.err.log`

### 项目文档

#### 1 登陆和授权

> 登陆密码使用rsa加密，api鉴权使用jwt,流程图如下

![](./image/登陆认证.png)

#### 2 跨域请求

>使用nginx的反向代理功能，解决cros问题。


+ `/megaconsole`:匹配静态资源目录/opt/megaconsole
+ `/api`:匹配egg接口，并使用负载均衡,负载：	
	 - 127.0.0.1:7001;
	 - 127.0.0.1:7002;
+ `/megaupload`:匹配上传接口，php实现

### 3 jenkins自动打包docker部署 

