import BaseMegaController from "./BaseMegaController";
import {IaddTeamWeekGiftBagParams, IupdateTeamWeekGiftBagParams} from "../../model/teamWeekGiftBagDto";

export default class TeamWeekGiftBag extends BaseMegaController {

    public async getItems(){
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.teamWeekGiftBag.getItems(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
            logger.error(err)
        }
    }

    public async addItem(){
        const { ctx, logger } = this;
        const rule = {
            gift_id: {type: "number"},
            item_dic_id: {type: "number"},
            item_cate_id: {type: "number"},
            num_limit: {type: "number"},
            num: {type: "number"},
            weight: {type: "number"},
            sort: {type: "number"},
            delsign: {type: "number"},
            name: {type: "string"},
            percent: {type: "string",required:false},
            start_time: {type: "string",required:false},
            end_time: {type: "string",required:false},
        };

        try {
            ctx.validate(rule)
            const requestBody = ctx.request.body as IaddTeamWeekGiftBagParams;
            const list = await ctx.service.werewolf.teamWeekGiftBag.addItem(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
            logger.error(err)
        }
    }

    public async updateItem(){
        const { ctx, logger } = this;
        const rule = {
            id: {type: "number"},
            gift_id: {type: "number"},
            item_dic_id: {type: "number"},
            item_cate_id: {type: "number"},
            num_limit: {type: "number"},
            num: {type: "number"},
            weight: {type: "number"},
            sort: {type: "number"},
            delsign: {type: "number"},
            name: {type: "string"},
            percent: {type: "string",required:false},
            start_time: {type: "string",required:false},
            end_time: {type: "string",required:false},
        };
        try {
            ctx.validate(rule)
            const requestBody = ctx.request.body as IupdateTeamWeekGiftBagParams;
            const list = await ctx.service.werewolf.teamWeekGiftBag.updateItem(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
            logger.error(err)
        }
    }

}