/*
 * @Description: 玩家资产
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2018-11-26 16:25:52
 * @LastEditors: hammercui
 * @LastEditTime: 2019-01-08 15:05:02
 */
import { Controller } from 'egg';
import { HttpErr, IerrorMsg } from '../../model/common';
import { IadListRequest, IadListResponse, IadOperationRequest, IadOperationResponse, AddScreenAdImgRequest, SaveAdInfoRequest } from '../../model/werewolf';

export default class AdvertisingController extends Controller {
	/**
  * @name: 查询广告位列表
  * @msg: 
  * @param {type} 
  * @return: 
  */

	public async list() {
		const { ctx, logger, app } = this;
		// 校验规则
		const rule = {
			uid: { type: 'number' },
			start: { type: 'number' },
			offset: { type: 'number' }
		};
		try {
			//1 校验
			ctx.validate(rule);
			const request: IadListRequest = ctx.request.body;
			const response: IadListResponse = await ctx.service.werewolf.advertising.list(request);
			ctx.body = response;
			ctx.status = HttpErr.Success;
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}

	/**
  * @name: 操作广告位
  * @msg: 
  * @param {type} 
  * @return: 
  */
	public async operation() {
		const { ctx, logger, app } = this;
		// 校验规则
		const rule = {
			uid: { type: 'number' },
			aid: { type: 'number' },
			type: { type: 'number' }
		};
		try {
			//1 校验
			ctx.validate(rule);
			const request: IadOperationRequest = ctx.request.body;
			const response: IadOperationResponse = await ctx.service.werewolf.advertising.operation(request);
			ctx.body = response;
			ctx.status = HttpErr.Success;

		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}

	/**
	 * @name: 添加开屏图
	 */
	public async addScreenAdImg() {
		const { ctx, logger, app } = this;
		// 校验规则
		const rule = {
			name: { type: 'string' },
			icon: { type: 'string' },
			loading: { type: 'string' },
			loadimgname: { type: 'string' },
			tend: { type: 'string' },
			ad_tend: { type: 'string',allowEmpty:true },
			state: { type: 'number' },
			anchor: { type: 'number' }
		};
		try {
			//1 校验
			ctx.validate(rule);
			const request: AddScreenAdImgRequest = ctx.request.body;
			await ctx.service.werewolf.advertising.addScreenAdImg(request);
			ctx.body = {
				err_code: HttpErr.Success,
				err_msg: '上传成功'
			};
			ctx.status = HttpErr.Success;

		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: e.message };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}

	/**
	 * @name: 删除开屏图
	 */
	public async delScreenAdImg() {
		const { ctx, logger, app } = this;
		const rule = {
			id: { type: 'number' }
		}
		try {
			//1 校验
			ctx.validate(rule);
			const id: number = ctx.request.body.id;
			await ctx.service.werewolf.advertising.delScreenAdImg(id);
			ctx.body = {
				err_code: HttpErr.Success,
				err_msg: '删除成功'
			};
			ctx.status = HttpErr.Success;
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: e.message };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}

	/**
	 * @name: 保存开屏图信息
	 */
	public async saveAdInfo(){
		const { ctx, logger, app } = this;
		const rule = {
			id: { type: 'number' },
			open: { type: 'enum',values: [0, 1] }
		}
		try {
			//1 校验
			ctx.validate(rule);
			const request: SaveAdInfoRequest = ctx.request.body;
			await ctx.service.werewolf.advertising.saveAdInfo(request);
			ctx.body = {
				err_code: HttpErr.Success,
				err_msg: '保存成功'
			};
			ctx.status = HttpErr.Success;
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: e.message };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}
}
