/*
 * @Description: 道具管理-控制类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2020-10-28 13:00:00
 */
import BaseMegaController from './BaseMegaController';

export default class Item extends BaseMegaController {

    public async getItemTypeList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            logger.info(requestBody);
            const list = await ctx.service.werewolf.item.getItemTypeList(requestBody);
            logger.info(list);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getItemList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            logger.info(requestBody);
            const list = await ctx.service.werewolf.item.getItemList(requestBody);
            logger.info(list);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async createItemRecord() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            logger.info(requestBody);
            const list = await ctx.service.werewolf.item.createItemRecord(requestBody);
            logger.info(list);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getRecordList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            logger.info(requestBody);
            const list = await ctx.service.werewolf.item.getRecordList(requestBody);
            logger.info(list);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getItem() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            logger.info(requestBody);
            const list = await ctx.service.werewolf.item.getItem(requestBody);
            logger.info('resp',list);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async deleteItemRecord() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            logger.info(requestBody);
            const list = await ctx.service.werewolf.item.deleteItemRecord(requestBody);
            logger.info(list);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async sendReward() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            logger.info(requestBody);
            const list = await ctx.service.werewolf.item.sendReward(requestBody);
            logger.info(list);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getUserList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            logger.info(requestBody);
            const list = await ctx.service.werewolf.item.getUserList(requestBody);
            logger.info('resp',list);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async checkUserList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            logger.info(requestBody);
            const list = await ctx.service.werewolf.item.checkUserList(requestBody);
            logger.info('resp',list);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

}
