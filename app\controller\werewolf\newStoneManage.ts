import BaseMegaController from "./BaseMegaController";
import { IerrorMsg, HttpErr } from "../../model/common";

export default class NewStoneManageController extends BaseMegaController {
  public async getStoneList() {
    const { ctx, logger } = this;
    try {
      const list = await ctx.service.werewolf.newStoneManage.getStoneList();
      this.respSuccData(list);
    } catch (err) {
      this.respFail(err);
    }
  }
  public async createStoneList() {
    const { ctx, logger, app } = this;
    // 校验规则
    const rule = {
      name: { type: "string" },
      level: { type: "number" },
      season_id: { type: "number" },
    };

    try {
      // 校验
      ctx.validate(rule);
      const responseBody =
      await ctx.service.werewolf.newStoneManage.createStoneList(
          ctx.request.body
        );
      ctx.body = {
        err_code: HttpErr.Success,
        err_msg: "成功",
      };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401 Unauthorized未鉴权
    }
  }
  public async updateStoneList() {
    const {ctx, logger, app} = this;
        // 校验规则
        const rule = {
            id: { type: 'number' },
            name: { type: 'string' },
            level: { type: "number" },
            season_id: { type: "number" },
            // pic: { type: "string" },
        };
        try {
            // 校验
            ctx.validate(rule);
            await ctx.service.werewolf.newStoneManage.updateStoneList(ctx.request.body);
            ctx.body = {
				err_code: HttpErr.Success,
				err_msg: '成功'
			};
			ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = {err_code: HttpErr.BadRequest, err_msg: '参数有误'};
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401 Unauthorized未鉴权
        }
  }
  public async getSeasonPoolList() {
    const { ctx, logger } = this;
    try {
      const list = await ctx.service.werewolf.newStoneManage.getSeasonPoolList(ctx.request.body);
            this.respSuccData(list)
      this.respSuccData(list);
    } catch (err) {
      this.respFail(err);
    }
  }
  public async createSeasonPoolList() {
    const { ctx, logger, app } = this;
    // 校验规则
    const rule = {
      frame_id: { type: "number" },
      card_level: { type: "number" },
      weight: { type: "number" },
    };

    try {
      // 校验
      ctx.validate(rule);
      const responseBody =
      await ctx.service.werewolf.newStoneManage.createSeasonPoolList(
          ctx.request.body
        );
      ctx.body = {
        err_code: HttpErr.Success,
        err_msg: "成功",
      };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401 Unauthorized未鉴权
    }
  }
  public async updateSeasonPoolList() {
    const {ctx, logger, app} = this;
        // 校验规则
        const rule = {
            id: { type: 'number' },
            card_level: { type: "number" },
            weight: { type: "number" },
        };
        try {
            // 校验
            ctx.validate(rule);
            await ctx.service.werewolf.newStoneManage.updateSeasonPoolList(ctx.request.body);
            ctx.body = {
				err_code: HttpErr.Success,
				err_msg: '成功'
			};
			ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = {err_code: HttpErr.BadRequest, err_msg: '参数有误'};
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401 Unauthorized未鉴权
        }
  }
  public async getRuleList() {
    const { ctx, logger } = this;
    try {
      const list = await ctx.service.werewolf.newStoneManage.getRuleList(ctx.request.body);
            this.respSuccData(list)
      this.respSuccData(list);
    } catch (err) {
      this.respFail(err);
    }
  }
  public async createRuleList() {
    const { ctx, logger, app } = this;
    // 校验规则
    const rule = {
      desc: { type: "string" },
      rate: { type: "number" },
      type: { type: "number" },
    };

    try {
      // 校验
      ctx.validate(rule);
      const responseBody =
      await ctx.service.werewolf.newStoneManage.createRuleList(
          ctx.request.body
        );
      ctx.body = {
        err_code: HttpErr.Success,
        err_msg: "成功",
      };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401 Unauthorized未鉴权
    }
  }
  public async updateRuleList() {
    const {ctx, logger, app} = this;
        // 校验规则
        const rule = {
            id: { type: 'number' },
            desc: { type: "string" },
            rate: { type: "number" },
      type: { type: "number" },

        };
        try {
            // 校验
            ctx.validate(rule);
            await ctx.service.werewolf.newStoneManage.updateRuleList(ctx.request.body);
            ctx.body = {
				err_code: HttpErr.Success,
				err_msg: '成功'
			};
			ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = {err_code: HttpErr.BadRequest, err_msg: '参数有误'};
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401 Unauthorized未鉴权
        }
  }
  public async getStoneRuleList() {
    const { ctx, logger } = this;
    try {
      const list = await ctx.service.werewolf.newStoneManage.getStoneRuleList(ctx.request.body);
            this.respSuccData(list)
      this.respSuccData(list);
    } catch (err) {
      this.respFail(err);
    }
  }
  public async createStoneRuleList() {
    const { ctx, logger, app } = this;
    // 校验规则
    const rule = {
      rule_id: { type: "number"  },
      frame_id: { type: "number" },
    };

    try {
      // 校验
      ctx.validate(rule);
      const responseBody =
      await ctx.service.werewolf.newStoneManage.createStoneRuleList(
          ctx.request.body
        );
      ctx.body = {
        err_code: HttpErr.Success,
        err_msg: "成功",
      };
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误",
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401 Unauthorized未鉴权
    }
  }
  public async updateStoneRuleList() {
    const {ctx, logger, app} = this;
        // 校验规则
        const rule = {
            id: { type: 'number' },
            rule_id: { type: "number"  },
            frame_id: { type: "number" },
        };
        try {
            // 校验
            ctx.validate(rule);
            await ctx.service.werewolf.newStoneManage.updateStoneRuleList(ctx.request.body);
            ctx.body = {
				err_code: HttpErr.Success,
				err_msg: '成功'
			};
			ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = {err_code: HttpErr.BadRequest, err_msg: '参数有误'};
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401 Unauthorized未鉴权
        }
  }
}
