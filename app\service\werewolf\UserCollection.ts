/**
 * <AUTHOR>
 */

import { Service } from 'egg';
import * as Console from "console";
import {
    UserCollectionBean,
    IuserCollectionCleanRecordsParams,
    UserCollectionResult
} from "../../model/UserCollectionBean";

export default class UserCollectionService extends Service {

    public async insertUserCollectionCleanRecords(params: IuserCollectionCleanRecordsParams) {
        const { app } = this;
        try {
            const db = app.mysql.get('manager');

            //查询用户收藏
            const sql = `INSERT INTO wf_user_collection_clean_record (operate_user_id, user_id, \`index\`, url)
                       VALUES (?, ?, ?, ?)`;

            const res = await db.query(sql, [params.uid, params.user_id, params.index, params.url]);

            return res.affectedRows == 1;

        } catch (error) {
            throw error;
        }
    }

    public async getUserCollectionCleanRecords() {
        const { app } = this;
        try {
            const db = app.mysql.get('manager');

            //查询用户收藏
            const sql = `SELECT id,
                                operate_user_id,
                                user_id,
                                \`index\`,
                                url,
                                DATE_FORMAT(createtime, '%Y-%m-%d %H:%i:%s') createtime
                         FROM wf_user_collection_clean_record
                         ORDER BY id`;

            const res = await db.query(sql, []);

            return res;

        } catch (error) {
            throw error;
        }
    }

    public async getUserCollectionList(req: any) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        try {
            //查询用户收藏
            let sql = `SELECT i,pic FROM user_collection WHERE user_id = ${ req.user_id } AND LENGTH(pic) > 0 AND type = 2 AND webp = 0`;

            const responseBody: UserCollectionBean = {
                    dataArray: await db.query(sql)
             };
            return responseBody;
        } catch (error) {
            throw error;
        }
    }

    public async upDateUserCollectionList(req: any) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        Console.log("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$="+req.user_id)
        Console.log("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$="+req.index)
        try {
            //查询用户收藏
            let sql = `UPDATE user_collection SET pic = '' WHERE user_id= ${ req.user_id } AND i= ${ req.index } LIMIT 1`;
            const result =  await db.query(sql)
            const code =  result.affectedRows
            const responseBody: UserCollectionResult = {
                code: code
            };

            return responseBody;
        } catch (error) {
            throw error;
        }
    }

}
