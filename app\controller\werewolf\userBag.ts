/*
 * @Description: 商城道具管理-控制类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2020-04-24 13:00:00
 * @LastEditors: jiawen.wang
 * @LastEditTime: 2021-12-23 11:31:07
 */
import { Controller } from 'egg'
import BaseMegaController from './BaseMegaController'
import { IerrorMsg, HttpErr } from '../../model/common'

export default class UserBagController extends BaseMegaController {
    public async getUserCoin() {
        const { ctx, logger } = this
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.userBag.getUserCoin(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async getUserCoupon() {
        const { ctx, logger } = this
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.userBag.getUserCoupon(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async getUserGift() {
        const { ctx, logger } = this
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.userBag.getUserGift(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async getUserItems() {
        const { ctx, logger } = this
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.userBag.getUserItems(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async getUserAvatarframe() {
        const { ctx, logger } = this
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.userBag.getUserAvatarframe(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async getUserAchievement() {
        const { ctx, logger } = this
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.userBag.getUserAchievement(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async getUserAnimation() {
        const { ctx, logger } = this
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.userBag.getUserAnimation(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }
    public async getUserLiveAvatar() {
        const { ctx, logger } = this
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.userBag.getUserLiveAvatar(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async getGiftContent() {
        const { ctx, logger } = this
        try {
            const requestBody = ctx.request.body
            const list = await ctx.service.werewolf.userBag.getGiftContent(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }
}
