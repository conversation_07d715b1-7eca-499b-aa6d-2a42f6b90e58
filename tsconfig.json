{"compileOnSave": true, "compilerOptions": {"target": "es6", "module": "commonjs", "declaration": false, "strict": false, "noImplicitAny": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "allowJs": false, "pretty": true, "noEmitOnError": false, "noUnusedLocals": false, "noUnusedParameters": false, "allowUnreachableCode": false, "allowUnusedLabels": false, "strictPropertyInitialization": false, "noFallthroughCasesInSwitch": true, "skipLibCheck": true, "skipDefaultLibCheck": true, "inlineSourceMap": true, "importHelpers": true}, "exclude": ["app/public", "app/views", "node_modules", "**/*.d.ts", "typings"]}