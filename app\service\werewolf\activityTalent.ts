import BaseMegaService from './BaseMegaService';
import { IactivityAwardConf, IactivityAwardGroup, IactivityIndex, IgetAwardConfReq, IgetAwardGroupReq } from '../../model/wfActivityAward';
import moment = require('moment');

export default class ActivityTalentService extends BaseMegaService {

    /**
     * 状态信息
     * @returns 
     */
    public async stateInfo() {
        const { logger } = this;
        try {
            const sqlStr = `SELECT * FROM activity2021_talent_match_state;`
            // tslint:disable-next-line:one-variable-per-declaration
            let ret = await this.selectOne(sqlStr, [])
            return ret;
        } catch (err) {
            logger.error(err);
            throw err;
        }
    }

    /**
     * 状态更新
     * @param req 
     * @returns 
     */
    public async stateUpdate(req) {
        const { logger } = this;
        try {
            //1 查询旧信息
            let sqlStr = `SELECT * FROM activity2021_talent_match_state;`
            // tslint:disable-next-line:one-variable-per-declaration
            let oldInfo = await this.selectOne(sqlStr, [])
            let task_start_time = moment().format("YYYY-MM-DD HH:mm:ss")
            //第二天
            if (oldInfo && oldInfo.second_stage_start == 0 && req.second_stage_start == 1) {
                let tomorrow = moment().add(1,"d")
                task_start_time = tomorrow.format("YYYY-MM-DD")
                logger.info("第二阶段变更时间",task_start_time);
            } else if (oldInfo) {
                task_start_time = moment(oldInfo.task_start_time).format("YYYY-MM-DD HH:mm:ss")
            }

            //2更新new
            sqlStr = `update activity2021_talent_match_state set 
           first_stage_start = ?,
           second_stage_start = ?,
           third_stage_start = ?,
           popular_rank_start = ?,
           task_start_time = ?,
           receiveState = ?
           where id = ${req.id}
           `
            // tslint:disable-next-line:one-variable-per-declaration
            await this.execSql(sqlStr, [req.first_stage_start,
            req.second_stage_start,
            req.third_stage_start,
            req.popular_rank_start,
            task_start_time,
            req.receiveState
        ])

        } catch (err) {
            logger.error(err);
            throw err;
        }
    }

    /**
     * 主播列表
     * @returns 
     */
    public async playerList(req) {
        const { logger } = this;
        try {
            const sqlStr = `SELECT a.*,b.nickname FROM 
            activity2021_talent_match_rank_score as a,
            tuser as b
            WHERE a.user_id = b.no AND a.season = ${req.season};`
            // tslint:disable-next-line:one-variable-per-declaration
            let ret: IactivityIndex[] = await this.selectList(sqlStr, [])
            return ret;
        } catch (err) {
            logger.error(err);
            throw err;
        }
    }

    /**
     * 主播信息更新
     * @param req 
     * @returns 
     */
    public async playerUpadte(req) {
        const { logger } = this;
        try {
            const sqlStr = `
            UPDATE activity2021_talent_match_rank_score SET 
first_stage_score = ?,
second_stage_score = ?,
third_stage_score = ?,
knockout_score = ?,
is_knockout = ?,
popular_score = ?,
group_id = ?,
rank_stage = ?
WHERE id = ${req.id}
            `
            // tslint:disable-next-line:one-variable-per-declaration
            await this.execSql(sqlStr, [
                req.first_stage_score,
                req.second_stage_score,
                req.third_stage_score,
                req.knockout_score,
                req.is_knockout,
                req.popular_score,
                req.group_id,
                req.rank_stage
            ])
        } catch (err) {
            logger.error(err);
            throw err;
        }
    }
}