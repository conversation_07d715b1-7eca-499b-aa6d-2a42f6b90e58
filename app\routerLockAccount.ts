import { Router, Application } from "egg";
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

    const { controller, router } = app;
    //账号封禁 根据用户id查询,
    router.post(`${API_VERSION}/werewolf/lockAccount/getUserListByUserId`, controller.werewolf.lockAccount.getUserListByUserId)
    //账号封禁 根据阿里云id查询,
    router.post(`${API_VERSION}/werewolf/lockAccount/getUserListByAliId`, controller.werewolf.lockAccount.getUserListByAliId)
    //账号封禁 根据udid查询,
    router.post(`${API_VERSION}/werewolf/lockAccount/getUserListByUdid`, controller.werewolf.lockAccount.getUserListByUdid)
    //账号封禁 根据身份证号查询,
    router.post(`${API_VERSION}/werewolf/lockAccount/getUserListByIDNumber`, controller.werewolf.lockAccount.getUserListByIDNumber)
    //账号封禁 根据注册ip查询,
    router.post(`${API_VERSION}/werewolf/lockAccount/getUserListByRegisterIP`, controller.werewolf.lockAccount.getUserListByRegisterIP)
    //账号封禁,
    // router.post(`${API_VERSION}/werewolf/lockAccount/getUserListByUserId`, controller.werewolf.lockAccount.getUserListByUserId)


}

export default load