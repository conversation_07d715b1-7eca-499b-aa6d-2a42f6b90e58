/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-09-29 15:57:18
 * @LastEditTime: 2020-10-30 16:36:32
 * @LastEditors: jiawen.wang
 */
import { Service } from 'egg';
import { IanchorListItemRes, IcreateAnchorBannerReq, IdelAnchorBannerReq } from "../../model/anchorBanner"
const pyfl = require('pyfl').default
export default class AnchorBanner extends Service {
    public async getAnchorList(): Promise<IanchorListItemRes[]> {
        const { app, logger } = this;
        const db = app.mysql.get('werewolf');
        try {
            const sqlStr = `SELECT 
               id, recommend_tag, show_type, recommend_content As content, remark, turn_to_page, page_url, img_url, sort
            FROM
                activity_anchor_star
            WHERE
                delsign = 0
            ORDER BY 
                sort DESC
            `
            const result: IanchorListItemRes[] = await db.query(sqlStr);
            return result;
        } catch (error) {
            logger.error(error);
            throw error;
        }
    }
    public async createAnchorBanner(req: IcreateAnchorBannerReq) {
        const { app, logger } = this
        try {
            const db = app.mysql.get('werewolf');
            const recommend_tag = db.escape(req.recommend_tag);
            const show_type = db.escape(req.show_type);
            const recommend_content = db.escape(req.content);
            const remark = db.escape(req.remark);
            const turn_to_page = db.escape(req.turn_to_page);
            const page_url = db.escape(req.page_url);
            const sort = db.escape(req.sort);

            const sqlStr = `INSERT INTO 
                activity_anchor_star (recommend_tag,show_type,recommend_content,remark,turn_to_page,page_url,sort)
            VALUES
                (${recommend_tag},${show_type},${recommend_content},${remark},${turn_to_page},${page_url},${sort})`;
            const result = await db.query(sqlStr);
            const name = pyfl(remark)
            return { imgageName: `anchorBanner_${result.insertId}_${new Date().getTime()}`, id: result.insertId }

        } catch (error) {
            throw new Error('上传信息失败!');
        }
    }
    public async uploadBannerImg(req) {
        const { app, logger } = this
        const db = app.mysql.get('werewolf');
        try {
            const sqlStr = `UPDATE activity_anchor_star SET img_url = ? WHERE id = ?`
            const result = await db.query(sqlStr, [req.img_url, req.id]);

        } catch (error) {
            throw new Error('上传信息失败!');
        }
    }
    public async delAnchorBanner(req: IdelAnchorBannerReq) {
        const { app, logger } = this
        try {
            const db = app.mysql.get('werewolf');
            const id = db.escape(req.id);
            const sqlStr = `UPDATE
            activity_anchor_star
            SET
            delsign = 1
            WHERE
            id = ${id}; `;
            await db.query(sqlStr);

        } catch (error) {
            logger.error(error);
            throw error;
        }
    }
    public async updateAnchorBanner(req) {
        const { app, logger } = this
        const db = app.mysql.get('werewolf');
        const conn = await db.beginTransaction();
        const id = db.escape(req.id);
        const recommend_tag = db.escape(req.recommend_tag)
        const show_type = db.escape(req.show_type)
        const content = db.escape(req.content)
        const remark = db.escape(req.remark)
        const turn_to_page = db.escape(req.turn_to_page)
        const page_url = db.escape(req.page_url)
        const sort = db.escape(req.sort)
        try {
            const sqlStr = `UPDATE
            activity_anchor_star
            SET
            recommend_tag = ${recommend_tag}, show_type =${show_type}, recommend_content = ${content}, remark = ${remark}, turn_to_page = ${turn_to_page}, page_url = ${page_url} ,sort=${sort}
                WHERE 
            id = ${id} `;
            await conn.query(sqlStr);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

}