import BaseMegaController from './BaseMegaController';
import { IplayerBeAnchor } from '../../model/wfPlayerInfo';
import { HttpErr, IerrorMsg } from '../../model/common';

/*
 * @Description: 玩家信息控制类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-05-19 17:11:16
 * @LastEditors: jiawen.wang
 * @LastEditTime: 2021-05-24 09:36:59
 */

export default class PlayerInfoController extends BaseMegaController {

    public async toAnchor() {
        const { ctx, logger } = this;
        const rule = {
            playerId: { type: 'number' },
        }
        try {
            ctx.validate(rule);
            const request: IplayerBeAnchor = ctx.request.body;
            await ctx.service.werewolf.playerInfo.toAnchor(request);
            this.respSucc();
        } catch (err) {
            logger.error(err);
            this.respFail(err.message)
        }
    }
    public async beAnchor() {
        const { ctx, logger } = this;
        const rule = {
            playerId: { type: 'number' },
        }
        try {
            ctx.validate(rule);
            const request: IplayerBeAnchor = ctx.request.body;
            await ctx.service.werewolf.playerInfo.beAnchor(request);
            this.respSucc();
        } catch (err) {
            logger.error(err);
            this.respFail(err.message)
        }
    }
    public async removeBan() {
        const { ctx, logger } = this;
        const rule = {
            udid: { type: 'string' },
        }
        try {
            ctx.validate(rule);
            const request = ctx.request.body;
            await ctx.service.werewolf.playerInfo.removeBan(request);
            this.respSucc();
        } catch (err) {
            logger.error(err);
            this.respFail(err.message)
        }
    }
    public async clickRemoveResign() {
        const { ctx, logger } = this;
        const rule = {
            udid: { type: 'string' },
        }
        try {
            ctx.validate(rule);
            const request = ctx.request.body;
            await ctx.service.werewolf.playerInfo.clickRemoveResign(request);
            this.respSucc();
        } catch (err) {
            logger.error(err);
            this.respFail(err.message)
        }
    }
    public async addOverseaItem() {
        const { ctx, logger } = this;
        const rule = {
            playerId: { type: 'number' },
        }
        try {
            ctx.validate(rule);
            const request = ctx.request.body;
            await ctx.service.werewolf.playerInfo.addOverseaItem(request);
            this.respSucc();
        } catch (err) {
            logger.error(err);
            this.respFail(err.message)
        }
    }
    public async clearSex() {
        const { ctx, logger } = this;
        const rule = {
            playerId: { type: 'number' },
        }
        try {
            ctx.validate(rule);
            const request: IplayerBeAnchor = ctx.request.body;
            await ctx.service.werewolf.playerInfo.clearSex(request);
            this.respSucc();
        } catch (err) {
            logger.error(err);
            this.respFail(err.message)
        }
    }
    public async clearTime() {
        const { ctx, logger } = this;
        const rule = {
            playerId: { type: 'number' },
        }
        try {
            ctx.validate(rule);
            const request: IplayerBeAnchor = ctx.request.body;
            await ctx.service.werewolf.playerInfo.clearTime(request);
            this.respSucc();
        } catch (err) {
            logger.error(err);
            this.respFail(err.message)
        }
    }
    public async clearBoard() {
        const { ctx, logger } = this;
        const rule = {
            playerId: { type: 'number' },
        }
        try {
            ctx.validate(rule);
            const request: IplayerBeAnchor = ctx.request.body;
            await ctx.service.werewolf.playerInfo.clearBoard(request);
            this.respSucc();
        } catch (err) {
            logger.error(err);
            this.respFail(err.message)
        }
    }
    public async getNodleStatus() {
        const { ctx, logger } = this;
        const rule = {
            playerId: { type: 'number' },
        }
        try {
            ctx.validate(rule);
            const request: IplayerBeAnchor = ctx.request.body;
            const resp = await ctx.service.werewolf.playerInfo.getNodleStatus(request);
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (err) {
            logger.error(err);
            this.respFail(err.message)
        }
    }
    public async addUserCoe() {
        const { ctx, logger } = this;
        const rule = {
            playerId: { type: 'number' },
            user_coe: { type: 'number' },
        }
        try {
            ctx.validate(rule);
            const request = ctx.request.body;
            const resp = await ctx.service.werewolf.playerInfo.addUserCoe(request);
            ctx.body = resp;
            this.respSucc()
        } catch (err) {
            logger.error(err);
            this.respFail(err.message)
        }
    }
    public async groupWhite() {
        const { ctx, logger } = this;
        const rule = {
            playerId: { type: 'number' },
        }
        try {
            ctx.validate(rule);
            const request = ctx.request.body;
            const resp = await ctx.service.werewolf.playerInfo.getGroupWhiteStatus(request);
            ctx.body = resp;
            ctx.status = HttpErr.Success;
            // this.respSucc()

        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
}
