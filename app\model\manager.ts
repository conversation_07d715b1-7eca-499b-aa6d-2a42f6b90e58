// tslint:disable-next-line:no-var-requires
/**
 * @name: manager数据库建模
 * @msg: 
 * @param {type} 
 * @return: 
 */
export interface IloginRequest {
	email: string;
	password: string;
	ps2: string;
}

export interface IloginResponse {
	token: string;
	tokenType: string;
	uid: number;
	accessRoutes: [];
}

export interface IuserModel {
	id: number;
	email: string;
	password: string;
	nickname: string;
	avatar: string;
	createtime: string;
	logintime: string;
	createip: string;
	loginip: string;
	access_level: number;
	access_type: number;
	delsign: number;
	accessRoutes: [];
}
export interface IuserResponse {
	id: number;
	email: string;
	nickname: string;
	avatar: string;
	createtime: string;
	logintime: null;
	createip: string;
	loginip: string;
	access_level: number;
	access_type: number;
	delsign: number;
}

//映射wf_admin_token表
export interface IadminToken {
	id: number; // 自增tokenId,
	admin_id: number; //登陆用户id
	login_device: string; //登陆时的设备
	create_time: string; //创建时间
	expire_time: string; //超时时间
	login_ip: string; //创建时的ip
}
export interface ItokenIdList {
	id: number;
}

export interface IuserItem {
	uid: number;
	nickname: string;
}
export interface IuserListResponse {
	list: IuserItem[];
}
export interface IuserCreateResponse {
	list: IuserItem[];
}
export interface IresetPwdRequest {
	uid: number;
	oldPwd: string;
	newPwd: string;
}

export interface IlogoutAllRequest {
	uid: number;
}
export interface IlogoutAllResponse {
	uid: number;
}

// 用户权限列表
export interface IaccessRoutesRequest{
	uid: number;
}
export interface IaccessRoutesResponse{
	uid: number;
	accessRoutes: IaccessRoute[];
}
export interface IaccessRoute{
	routeId: number;
	routeDesc: string;
	accessEnabel: number;
}

export interface IupdateRouteRequest{
	uid: number;
	routeId: number;
	accessEnable: number;
	index: number;
}
export interface IupdateRouteResponse{
	uid: number;
	routeId: number;
	accessEnable: number;
	index: number;
}
