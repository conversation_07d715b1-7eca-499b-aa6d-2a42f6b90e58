/*
 * @Description: 用户违规查询
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: 赵宝强
 * @Date: 2020-11-05 17:52
 * @LastEditors: 赵宝强
 * @LastEditTime: 2020-09-23 09:34:13
 */
import {IgameMatch, IuserList, LittleOpenBlack} from '../../model/deductionRecord';
import {IdeductionGameMatchRequest, IdeductionRecoreRequest, LittleOpenBlackRequest} from "../../model/werewolf2";
import BaseMegaService from './BaseMegaService';

export default class DeductionService extends BaseMegaService {

    // 1 查询指定违规用户
    public async getUserList(req: IdeductionRecoreRequest): Promise<IuserList[]> {
        const { app, ctx, logger } = this;

        try {
            const sqlStr = 'SELECT user_no,game_list,game_num,cut_score,user_little, createtime, id_delsign from user_cheat_game WHERE user_no IN ('+req.playerId+'); '
            const array: IuserList[] = await this.selectList(sqlStr);
            return array;
        } catch (err) {
            logger.error(err);
            throw err;
        }
    }

    // 2 指定用户违规游戏详情

    public async getGameMatch(req: IdeductionGameMatchRequest): Promise<IgameMatch[]> {

        const { app, ctx, logger} = this;
        try{
            const sqlStr = `SELECT a.no, a.server_type, b.desc, a.round, a.starttime,d.user_no, 
                            IF(d.valid = 1,'是','否') AS valid,c.name, d.seat_index, d.camp_no, d.role_no,d.life, 
                            d.win, IF(d.escape = 1,'逃跑','未逃跑') AS escape, e.name as "role_name"
                            from tgamerecord a
                            left join tgameconfig b on a.game_type = b.desc
                            left join tserver_config c on a.server_type = c.type
                            left join tusergamerecord d on a.no = d.game_no 
                            left join animation_role e on d.role_no = e.role 
                            where a.no = ${req.gamelist}; `
            const array: IgameMatch[] = await this.selectList(sqlStr);
            return array;

        } catch (err) {
            logger.enable(err);
            throw err;
        }

    }

    //根据gameId查询小号黑名单开黑
    public async getLittleOpenBlack(req: LittleOpenBlackRequest): Promise<LittleOpenBlack[]> {

        const {app, ctx, logger} = this;
        let ids: any = req.gameIds.split(",");
        try {
            const sqlStr = `SELECT t1.user_no, COUNT(*) AS gameNum, t3.remark AS remark, t3.createtime AS createTime, t3.is_delsign AS is_delsign
            FROM tusergamerecordmatch t1, tusergamerecordmatch t2, user_cheat_little t3
            WHERE t1.game_no = t2.game_no AND t1.room_no = t2.room_no
            AND t2.game_no IN (?) AND t2.user_no = ${req.userId}
            AND t1.user_no = t3.user_no GROUP BY t1.user_no; `
            const array: LittleOpenBlack[] = await this.selectList(sqlStr, [ids]);
            return array;

        } catch (err) {
            logger.enable(err);
            throw err;
        }
    }


}