/*
 * @Description: 新手辅助
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: wb
 * @Date: 2020-04-24 11:35:39
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-07-21 10:54:23
 */
import BaseMegaService from './BaseMegaService';

// const {ObjectId} = require('mongodb')

const werewolfDB = "werewolf";

export default class MessageBoardService extends BaseMegaService {

   

    public async getMessageBoardList(req: any) {
        const { app,logger } = this;

        try {
            const werewolf = app.mysql.get(werewolfDB);
            const werewolfConn = await werewolf.beginTransaction();

            const mongo = app['mongo'];
            let searchString = req.searchString;
            let list ;
           

            if(req.isReply == '1'){

                list = await mongo.find('user_message_board_reply', {
                    query: {
                        messageId:searchString,
                        delsign:Number('0')
                    },
                });
            }else{
                let table = 'user_message_board';

                if(searchString == ''){
                    list = await mongo.find(table, {
                        limit: 500,
                        query: {
                            delsign:Number('0')
                        },
                    });
                }else{
                    
                    list = await mongo.find(table, {
                        query: {
                            // text: { $regex: new RegExp(searchString) },
                            owner:Number(searchString),
                            delsign:Number('0')
                        },
                    });    
                }

            }

            if(list.length == 0){
                return [];
            }

            let IDList=new Array();

            for (const v of list) {

                if(v.receiver != undefined){
                    IDList.push(v.receiver);
                }

                if(v.sender != undefined){
                    IDList.push(v.sender);
                }

                if(v.owner != undefined){
                    IDList.push(v.owner);
                }
            }
            
            let ids = "";

            for (const v of IDList) {
                ids += v + ",";
            }

            ids += ids + "0";

            let sqlStr = `SELECT * FROM tuser WHERE no in (${ids}) `;
            const userList = await this.selectList(sqlStr, [], werewolfDB);
            werewolfConn.commit();

            let m = new Map();


            for (const v of userList) {
                let no = v.no;
                m.set(no,v.nickname);
            }

            
            for (const v of list) {
                v.receiverName = m.get(v.receiver);
                v.senderName = m.get(v.sender);
                v.ownerName = m.get(v.owner);
            }

            return list;

        } catch (error) {
            console.log("logger")
            throw error
        }
    }

    public async delMessageOrReply(req: any) {
        const { app, ctx,logger } = this;
       
        try {

            if(req.isReply == '1'){

                let res = await ctx.curl(app.config.WerewolfJPFunRoom + 'board/del/reply', {
                    method: 'POST',
                    headers: {
                        'content-type': 'application/json',
                    },
                    data: req,
                    timeout: 3000, // 3 秒超时
                })

            }else{

                // if(req.owner){

                //     const mongo = app['mongo'];

                //     let table = 'user_message_board';

                //     let list = await mongo.find(table, {
                //         query: {
                //             // text: { $regex: new RegExp(searchString) },
                //             owner:Number(req.owner),
                //             sender:Number(req.userId),
                //             "delsign":Number('0')
                //         },
                //     });  


                //     for (const v of list) {

                //         let req1 = req;
                //         req1.id = v._id;

                //         let res = await ctx.curl(app.config.WerewolfJPFunRoom + 'board/del/message', {
                //             method: 'POST',
                //             headers: {
                //                 'content-type': 'application/json',
                //             },
                //             data: {"messageId":v._id,'userId': v.sender, 'delsign': v.delsign,},
                //             timeout: 3000, // 3 秒超时
                //         })

                       
                //     }

                   


                // }else{
                    let res = await ctx.curl(app.config.WerewolfJPFunRoom + 'board/del/message', {
                        method: 'POST',
                        headers: {
                            'content-type': 'application/json',
                        },
                        data: req,
                        timeout: 3000, // 3 秒超时
                    })
                // }

               

            }
            
            // const mongo = app['mongo'];
            // let id = req.id;
            // let delsign = req.delsign == '1'?0:1;
            // let table = 'user_message_board';

            // if(req.isReply == '1'){
            //     table = 'user_message_board_reply';
                
            // }else{
            //     table = 'user_message_board';
            // }

            // await mongo.findOneAndUpdate(table,{
            //     filter:{
            //         _id:ObjectId(id)
            //     },
            //     update: {
            //        $set: {
            //         delsign: delsign
            //        }

            //     },
            // });

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

}
