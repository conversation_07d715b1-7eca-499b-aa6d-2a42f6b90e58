import { Controller } from 'egg';
import { IerrorMsg, HttpErr } from '../../model/common';
import { IrecordList, IupdateRecord } from '../../model/randomRecordCof'

export default class RandomRecordContorller extends Controller {
    public async getRecordList() {
        const { ctx, logger } = this;
        try {
            const resp = await ctx.service.werewolf.randomRecord.getRecordList();
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    public async getRecordBoard() {
        const { ctx, logger } = this;
        const rule = {
            weekTime: { type: 'string' },
        };
        ctx.validate(rule);
        try {
            const req = ctx.request.body
            const res = await ctx.service.werewolf.randomRecord.getRecordBoard(req);
            ctx.body = res;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    public async createRecordBoard() {
        const { ctx, logger } = this;
        const rule = {
            weekTime: { type: 'string' },
            board1: { type: 'number' },
            board2: { type: 'number' },
            createTime: { type: 'string' },

        }
        ctx.validate(rule);
        try {
            const req = ctx.request.body;
            const res = await ctx.service.werewolf.randomRecord.createRecordBoard(req);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }

    }
    public async updateRecordBoard() {
        const { ctx, logger } = this;
        const rule = {
            id1: { type: 'number' },
            id2: { type: 'number' },
            gameBoardId1: { type: 'number' },
            gameBoardId2: { type: 'number' },
        }
        try {
            ctx.validate(rule);
            const req: IupdateRecord = ctx.request.body;
            const res = await ctx.service.werewolf.randomRecord.updateRecordBoard(req);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    public async deleteRecordBoard() {
        const { ctx, logger } = this;
        const rule = {
            weekTime: { type: 'string' },
        }
        try {
            ctx.validate(rule);
            const req = ctx.request.body;
            const res = await ctx.service.werewolf.randomRecord.deleteRecordBoard(req);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
}