/**
 * 设计之星
 */
export interface IdesignStarItem {
    id: number;
    user_id: number;
    nickname: string;//昵称
    design_name: string;//作品名称
    design_desc: string;//作品描述
    img: string;//作品图片地址
    design_type: number;//1头像框，2麦克风
    vote_status: number; //投票状态 0不可投票，1可投票
    vote_num: number; //投票数量
    create_time: number; //投稿时间
    index: number;
    truing_img: string;//作
    final_img: string;//
}


export interface IdesignStarUpdateStatus {
    id: number;
    newStatus: number;
}

export interface IdesignStarUpdateNum {
    id: number;
    newNum: number;
}

export interface IfliteListReq {
    designType?: number;
    voteStatus?: number;
    isShowUnVote?: number;
}