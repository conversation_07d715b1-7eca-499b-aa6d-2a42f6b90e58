/*
 * @Description: 无
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: leeou
 * @LastEditors: Please set LastEditors
 * @Date: 2019-04-08 10:36:00
 * @LastEditTime: 2021-09-08 17:23:34
 */

import { HttpErr, IerrorMsg } from "../../model/common";
import { Controller } from 'egg';
import { IachievementListRequest, IachievementListResponse, IupdateAchieveDelsignRequest, IupdateAchieveDelsignResponse, IsendAchieveToUserRequest, IuploadAchieveBaseRequest, Iachievement, IuploadAchieveCompleteRequest, IuploadAchieveCompleteResponse } from '../../model/werewolf';

export default class AchievementController extends Controller {

    /**
     * 查询成就列表
     */
    public async getList() {
        const { ctx, logger } = this;
        // 校验规则
        const rule = {
            uid: { type: "number" },
            start: { type: "number" },
            offset: { type: "number" },
            isComplete: { type: "number" }
        };
        try {
            // 校验
            ctx.validate(rule);
            //service
            const requestBody: IachievementListRequest = ctx.request.body;
            const responseBody: IachievementListResponse = await ctx.service.werewolf.achievement.getList(
                requestBody
            );
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = {
                err_code: HttpErr.BadRequest,
                err_msg: "参数有误"
            };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    /**
     * 上架/下架应用内成就
     */
    public async updateDelsign(){
        const { ctx, logger } = this;
        // 校验规则
        const rule = {
            uid: { type: "number" },
            achieveId: { type: "number" },
            delsign: { type: "number" }
        };
        try {
            // 校验
            ctx.validate(rule);
            //service
            const requestBody: IupdateAchieveDelsignRequest = ctx.request.body;
            const responseBody: IupdateAchieveDelsignResponse = await ctx.service.werewolf.achievement.updateDelsign(
                requestBody
            );
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = {
                err_code: HttpErr.BadRequest,
                err_msg: "参数有误"
            };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    /**
     * 发成就
     */
    public async sendAchieve(){
        const { ctx, logger } = this;
        // 校验规则
        const rule = {
            uid: { type: "number" },
            playerId: { type: "string" },
            achieveId: { type: "number" }
        };
        try {
            // 校验
            ctx.validate(rule);
            //service
            const requestBody: IsendAchieveToUserRequest = ctx.request.body;
            const responseBody: boolean = await ctx.service.werewolf.achievement.sendAchieve(
                requestBody
            );
            if(responseBody){
                ctx.body = { code: 200 };
                ctx.status = HttpErr.Success;
            }else{
                const err: IerrorMsg = {
                    err_code: HttpErr.BadRequest,
                    err_msg: "参数有误,sql或者curl错误"
                };
                ctx.body = err;
                ctx.status = HttpErr.BadRequest;
            }
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = {
                err_code: HttpErr.BadRequest,
                err_msg: "参数有误，未知错误"
            };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    /**
     * 上传成就基本信息
     */
    public async uploadBase(){
        const { ctx, logger } = this;
        // 校验规则
        const rule = {
            uid: { type: "number" },
            name: { type: "string" },
            remark: { type: "string" }
        };
        try {
            // 校验
            ctx.validate(rule);
            //service
            const requestBody: IuploadAchieveBaseRequest = ctx.request.body;
            const responseBody: Iachievement = await ctx.service.werewolf.achievement.uploadBase(
                requestBody
            );
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = {
                err_code: HttpErr.BadRequest,
                err_msg: "参数有误"
            };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    /**
     * 上传完成
     */
    public async complete(){
        const { ctx, logger } = this;
        // 校验规则
        const rule = {
            uid: { type: "number" },
            achieveId: { type: "number" }
        };
        try {
            // 校验
            ctx.validate(rule);
            //service
            const requestBody: IuploadAchieveCompleteRequest = ctx.request.body;
            const responseBody: IuploadAchieveCompleteResponse = await ctx.service.werewolf.achievement.complete(
                requestBody
            );
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = {
                err_code: HttpErr.BadRequest,
                err_msg: "参数有误"
            };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    /**
     * 上传完成 头像框刻字
     */
    public async completeNoteOrder(){
    const { ctx, logger } = this;
    // 校验规则
    const rule = {
        uid: { type: "number" },
        achieveId: { type: "number" }
    };
    try {
        // 校验
        ctx.validate(rule);
        //service
        const requestBody: IuploadAchieveCompleteRequest = ctx.request.body;
        const responseBody: IuploadAchieveCompleteResponse = await ctx.service.werewolf.achievement.completeNoteOrder(
            requestBody
        );
        ctx.body = responseBody;
        ctx.status = HttpErr.Success;
    } catch (e) {
        logger.error(e);
        const err: IerrorMsg = {
            err_code: HttpErr.BadRequest,
            err_msg: "参数有误"
        };
        ctx.body = err;
        ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
}
}
