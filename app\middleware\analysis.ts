/*
 * @Description: 埋点中间件
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2018-11-26 15:23:59
 * @LastEditors: hammercui
 * @LastEditTime: 2018-11-26 17:18:37
 */
import { Application, Context, EggAppConfig} from 'egg';

export default function analysisMiddleWare(options: EggAppConfig['analysis'], app: Application): any {
    return async (ctx: Context, next: () => Promise<any>) => {
        //console.info("埋点记录信息body", ctx.request.body);
        ctx.getLogger('analysisLogger').info('requestIP:',ctx.request.ip,'requestBody:',ctx.request.body);
        await next();
        ctx.getLogger('analysisLogger').info('responseStatus:',ctx.response.status,'responseBody:',ctx.response.body);
    };
}
