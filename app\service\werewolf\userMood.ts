import BaseMegaService from "../../service/werewolf/BaseMegaService";

export default class UserMoodService extends BaseMegaService {
    public async getList(req) {
        const {app, ctx, logger} = this;
        try {
            let sqlStr = `
                SELECT b.\`id\`,
                       b.user_id,
                       b.mood,
                       b.animation_id,
                       DATE_FORMAT(b.updatetime, '%Y-%m-%d %H:%i:%s') updatetime,
                       u.nickname,
                       c.is_delsign                                   ban_delsign,
                       CASE
                           WHEN d.release_time > CURRENT_TIME
                               THEN DATE_FORMAT(d.release_time, '%Y-%m-%d %H:%i:%s')
                           ELSE null
                           END AS                                     imprison_release_time

                FROM user_mood_state b
                         LEFT JOIN tuser u ON u.no = b.user_id
                         LEFT JOIN tuser_ban_deal c ON b.user_id = c.user_no AND c.type = 6
                         LEFT JOIN tuser_imprison_deal d ON b.user_id = d.user_no AND d.type = 26
                WHERE b.user_id = ?
                ORDER BY b.\`id\` DESC
            `;
            return await this.selectList(sqlStr, [req.user_id]);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async update(req) {
        const {app, ctx, logger} = this;
        try {
            let sqlStr = `
                UPDATE user_mood_state
                SET mood = ?
                WHERE \`id\` = ?
            `;
            return await this.execSql(sqlStr, [req.mood, req.id]);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }
}