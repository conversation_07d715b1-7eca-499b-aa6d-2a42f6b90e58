# 使用多阶段构建
#
# ---- 1 Base Node ----
# FROM node:carbon AS base
# # 创建 app 目录
# WORKDIR /app

# ---- 2 Dependencies ----
FROM node:carbon AS dependencies  
WORKDIR /nodeapp
# 使用通配符复制 package.json 
COPY package.json /nodeapp/
# 安装在‘devDependencies’中包含的依赖
RUN npm install --registry=https://registry.npm.taobao.org

# ---- 3 Copy文件进行编译 ----
FROM dependencies AS build  
WORKDIR /nodeapp/
RUN ls -lh
# COPY /app ./app
COPY . .
# COPY /config ./config
# COPY app.js  .
# COPY tsconfig.json .
# COPY tslint.json .
# COPY rsa* /nodeapp/
RUN ls -lh
RUN npm install --registry=https://registry.npm.taobao.org
# 如需对 react/vue/angular 打包，生成静态文件，使用：
RUN npm run ci
RUN ls -lh


# --- 4 Release with Alpine ----
# 4.1 第一层 基础景象
FROM node:lts-alpine AS release  
ENV HOST 0.0.0.0
ENV NODE_ENV=production

# 4.2 第二层 拷贝已编译
WORKDIR /nodeapp/
COPY --from=build /nodeapp .
RUN ls -lh
RUN pwd -LP
# 5 执行默认
CMD ["npm","run","start"]