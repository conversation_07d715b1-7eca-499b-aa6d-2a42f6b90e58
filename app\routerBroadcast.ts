/*
 * @Description: 商城道具路由
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: z<PERSON>yu
 * @Date: 2020-04-24 11:50:11
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-06-18 10:17:57
 */
import { Router, Application } from "egg";
import { AccessRouteId, AccessRouteName } from "./model/accessRouteCof";
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

    const { controller, router } = app;

    router.post(`${API_VERSION}/werewolf/broadcastSend/getBroadcastList`, controller.werewolf.broadcastSend.getBroadcastList)
    router.post(`${API_VERSION}/werewolf/broadcastSend/updateBroadcastType`, controller.werewolf.broadcastSend.updateBroadcastType)
    router.post(`${API_VERSION}/werewolf/broadcastSend/updateBroadcast`, controller.werewolf.broadcastSend.updateBroadcast)
    router.post(`${API_VERSION}/werewolf/broadcastSend/insertBroadcast`, controller.werewolf.broadcastSend.insertBroadcast)
    
    
}

export default load
