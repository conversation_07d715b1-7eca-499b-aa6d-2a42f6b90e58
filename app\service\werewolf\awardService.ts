

import BaseMegaService from './BaseMegaService';

export interface ItemObtainReq {
    userId: number; //用户id
    itemDicList: ItemInfo[];
    activityId: number; //分页开始
}
export interface ItemInfo {
    itemDicId: number;
    num: number;
}
export default class AwardService extends BaseMegaService {

    public async ObtainAwardByNet(ctx, params) {
        const { app, logger } = this;
        let res = await ctx.curl(app.config.WerewolfJPExchange + 'item/obtain', {
            method: 'POST',
            headers: {
                "content-type": "application/json",
            },
            contentType: 'json',
            dataType: 'json',
            data: params,
            timeout: 3000, // 3 秒超时
        });
        return res
    }
    //生产下发奖励请求
    public GenObtainAwardReq(userId,activityId,ItemInfoList) {
        let userList: any[] = [];
        let row = {
            userId: userId,
            itemDicList: ItemInfoList
        }
        userList.push(row);

        let jsonStr = JSON.stringify({
            type: 0,
            userList: userList,
            activityId: activityId,
        });
        return jsonStr
    }
    ////生成减少item请求
    public GenDescItemReq(userId,activityId,ItemInfoList) {
        let userList: any[] = [];
        let row = {
            userId: userId,
            itemDicList: ItemInfoList
        }
        userList.push(row);

        let jsonStr = JSON.stringify({
            type: 1,
            userList: userList,
            activityId: activityId,
        });
        return jsonStr
    }
}