/*
 * @Description: 了了提现外部暴露接口
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: leeou
 * @Date: 2019-02-02 11:47:41
 * @LastEditors: leeou
 * @LastEditTime: 2019-03-26 09:59:07
 */
import { Controller } from "egg";
import { HttpErr, IerrorMsg } from "../../model/common";
import { IwithDrawListRequest, IwithDrawListResponse, IwithDrawOperateRequest, IwithDrawOperateResponse } from "../../model/aiim";

export default class WithDrawController extends Controller {
  /**
   * 提现列表
   */
  public async list() {
    const { ctx, logger } = this;
    // 校验规则
    const rule = {
      uid: { type: "number" },
      start: { type: "number" },
      offset: { type: "number" },
      type: { type: "number" }
    };
    try {
      // 校验
      ctx.validate(rule);
      //service
      const requestBody: IwithDrawListRequest = ctx.request.body;
      const responseBody: IwithDrawListResponse = await ctx.service.aiim.withdraw.list(
        requestBody
      );
      ctx.body = responseBody;
      ctx.status = HttpErr.Success;
    } catch (e) {
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    }
  }

  /**
   * 更新状态
   */
  public async changeState(){
    const { ctx, logger } = this;
    const rule={
      uid:{type:"number"},
      type:{type:"number"},
      reportId:{type:"number"}
    }
    try{
      ctx.validate(rule);
    const requestBody: IwithDrawOperateRequest = ctx.request.body;
    const responseBody: IwithDrawOperateResponse = await ctx.service.aiim.withdraw.changeState(
      requestBody
    );
    ctx.body = responseBody;
    ctx.status = HttpErr.Success;
    }catch(e){
      logger.error(e);
      const err: IerrorMsg = {
        err_code: HttpErr.BadRequest,
        err_msg: "参数有误"
      };
      ctx.body = err;
      ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
    } 
  }
}
