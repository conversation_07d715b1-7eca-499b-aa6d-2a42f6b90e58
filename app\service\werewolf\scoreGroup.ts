/*
 * @Description: 团队刷分查询
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: 赵宝强
 * @Date: 2020-11-05 17:52
 * @LastEditors: 赵宝强
 * @LastEditTime: 2020-09-23 09:34:13
 */
import {IgameMatch, LittleOpenBlack} from '../../model/deductionRecord';
import {
    IdeductionGameMatchRequest,
    LittleOpenBlackRequest,
    ScoreGroupRequest,
    ScoreGroupRequest1
} from "../../model/werewolf2";
import BaseMegaService from './BaseMegaService';
import {Group, Group1} from "../../model/scoreGroup";

export default class ScoreGroupService extends BaseMegaService {

    // 1 团队刷分
    public async getGroup(req: ScoreGroupRequest): Promise<Group[]> {
        const { app, ctx, logger } = this;

        try {
            const sqlStr = 'SELECT * FROM user_cheat_group WHERE user_no IN ('+req.userNo+');'
            const array: Group[] = await this.selectList(sqlStr);
            return array;
        } catch (err) {
            logger.error(err);
            throw err;
        }
    }

    // 2 指定用户违规游戏详情
    public async getGroup1(req: any) {
        const { app, ctx, logger } = this;
        const werewolf = app.mysql.get("werewolf");
        let count = 0;


        try {
            let result = await this.selectList(
                `select count(*) AS count from user_cheat_group WHERE cheat_group_id = ${req.gameId}`
            );
            const sqlStr = `SELECT * FROM user_cheat_group WHERE cheat_group_id = ${req.gameId} limit ${req.start}, ${req.offset};`
            const array: Group1[] = await this.selectList(sqlStr);

            if (!!result && result.length > 0) {
                count = result[0].count;
            }

            return {
                count: count,
                data: array,
            };
        } catch (err) {
            logger.error(err);

            throw err;
        }
    }



}