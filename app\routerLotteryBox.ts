/*
 * @Description: 商城道具路由
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: z<PERSON><PERSON>
 * @Date: 2020-04-24 11:50:11
 * @LastEditors: zhang<PERSON>
 * @LastEditTime: 2021-04-19 17:22:28
 */
import { Router, Application } from "egg";
import { AccessRouteId, AccessRouteName } from "./model/accessRouteCof";
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

    const { controller, router } = app;

    router.post(`${API_VERSION}/werewolf/lotteryBox/getActivityList`, controller.werewolf.lotteryBox.getActivityList)
    router.post(`${API_VERSION}/werewolf/lotteryBox/getAwardList`, controller.werewolf.lotteryBox.getAwardList)
    router.post(`${API_VERSION}/werewolf/lotteryBox/updateActivity`, controller.werewolf.lotteryBox.updateActivity)
    router.post(`${API_VERSION}/werewolf/lotteryBox/insertActivity`, controller.werewolf.lotteryBox.insertActivity)
    router.post(`${API_VERSION}/werewolf/lotteryBox/insertLotteryBoxAward`, controller.werewolf.lotteryBox.insertLotteryBoxAward)
    router.post(`${API_VERSION}/werewolf/lotteryBox/updateLotteryBoxAward`, controller.werewolf.lotteryBox.updateLotteryBoxAward)
    router.post(`${API_VERSION}/werewolf/lotteryBox/getBoxList`, controller.werewolf.lotteryBox.getBoxList)
    router.post(`${API_VERSION}/werewolf/lotteryBox/insertLotteryBox`, controller.werewolf.lotteryBox.insertLotteryBox)
    router.post(`${API_VERSION}/werewolf/lotteryBox/updateLotteryBox`, controller.werewolf.lotteryBox.updateLotteryBox)
    router.post(`${API_VERSION}/werewolf/lotteryBox/updateLotteryBoxDelsign`, controller.werewolf.lotteryBox.updateLotteryBoxDelsign)
    router.post(`${API_VERSION}/werewolf/lotteryBox/updateLotteryBoxImg`, controller.werewolf.lotteryBox.updateLotteryBoxImg)
    router.post(`${API_VERSION}/werewolf/lotteryBox/getCoinList`, controller.werewolf.lotteryBox.getCoinList)
    router.post(`${API_VERSION}/werewolf/lotteryBox/getRuleList`, controller.werewolf.lotteryBox.getRuleList)
    router.post(`${API_VERSION}/werewolf/lotteryBox/updateLotteryBoxRule`, controller.werewolf.lotteryBox.updateLotteryBoxRule)
    router.post(`${API_VERSION}/werewolf/lotteryBox/insertLotteryBoxRule`, controller.werewolf.lotteryBox.insertLotteryBoxRule)
    
    router.post(`${API_VERSION}/werewolf/lotteryBox/getActivityRuleList`, controller.werewolf.lotteryBox.getActivityRuleList)
    router.post(`${API_VERSION}/werewolf/lotteryBox/insertActivityRule`, controller.werewolf.lotteryBox.insertActivityRule)
    router.post(`${API_VERSION}/werewolf/lotteryBox/updateActivityRule`, controller.werewolf.lotteryBox.updateActivityRule)
    router.post(`${API_VERSION}/werewolf/lotteryBox/updateActivityRuleDelsign`, controller.werewolf.lotteryBox.updateActivityRuleDelsign)
    
    router.post(`${API_VERSION}/werewolf/lotteryBox/getBoxRuleList`, controller.werewolf.lotteryBox.getBoxRuleList)
    router.post(`${API_VERSION}/werewolf/lotteryBox/insertBoxRule`, controller.werewolf.lotteryBox.insertBoxRule)
    router.post(`${API_VERSION}/werewolf/lotteryBox/updateBoxRule`, controller.werewolf.lotteryBox.updateBoxRule)
    router.post(`${API_VERSION}/werewolf/lotteryBox/updateBoxRuleDelsign`, controller.werewolf.lotteryBox.updateBoxRuleDelsign)

    router.post(`${API_VERSION}/werewolf/lotteryBox/getBoxActivityList`, controller.werewolf.lotteryBox.getBoxActivityList)
    router.post(`${API_VERSION}/werewolf/lotteryBox/getBoxDiscountRuleList`, controller.werewolf.lotteryBox.getBoxDiscountRuleList)
    router.post(`${API_VERSION}/werewolf/lotteryBox/updateBoxDiscountRule`, controller.werewolf.lotteryBox.updateBoxDiscountRule)
    router.post(`${API_VERSION}/werewolf/lotteryBox/updateBoxDiscountRuleDelsign`, controller.werewolf.lotteryBox.updateBoxDiscountRuleDelsign)

    router.post(`${API_VERSION}/werewolf/lotteryBox/getLeadBoxList`, controller.werewolf.lotteryBox.getLeadBoxList)
    router.post(`${API_VERSION}/werewolf/lotteryBox/insertLeadBoxList`, controller.werewolf.lotteryBox.insertLeadBoxList)
    router.post(`${API_VERSION}/werewolf/lotteryBox/getBoxSelectImgList`, controller.werewolf.lotteryBox.getBoxSelectImgList)
    
}

export default load
