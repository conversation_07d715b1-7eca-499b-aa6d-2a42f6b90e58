/*
 * @Description: 游戏板子模块
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2019-10-10 17:42:19
 * @LastEditors: hammercui
 * @LastEditTime: 2020-02-10 17:12:26
 */
import { Controller } from 'egg';
import { IerrorMsg, HttpErr } from '../../model/common';
import { SendEmailsToUsersRequest, SearchNewUserListResponse, PushMessageResquest } from '../../model/werewolf';
import { IupdateGameBoardOpenReq, IcreateGameBoardOpenReq, IupdateGameBoardOpenSortReq } from '../../model/werewolf3';

export default class GameBoardigController extends Controller {
    
     //获得open表板子列表
     public async getOpenList() {
        const { ctx, logger } = this;
        // 校验规则
        // const rule = {
        //     userList: { type: 'array' },
        //     title: { type: 'string' },
        //     content: { type: 'string' }
        // };
        try {
            // 校验
            // ctx.validate(rule);
            // const requestBody: SendEmailsToUsersRequest = ctx.request.body;
            const resp = await ctx.service.werewolf.gameBoard.getOpenList();
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    //更新open板子
    public async updateOpen() {
        const { ctx, logger } = this;
        // 校验规则
        const rule = {
            gameBoardId: { type: 'number' },
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: IupdateGameBoardOpenReq = ctx.request.body;
            // console.info("requestBody",requestBody);
            if(!requestBody.openTime &&  !requestBody.closeTime){
                console.info("更新下 by Now")
                await ctx.service.werewolf.gameBoard.updateOpenFlagByNow(requestBody);
            }else{
                console.info("更新下 by Time")
                await ctx.service.werewolf.gameBoard.updateOpenFlagByTime(requestBody);
            }
            
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    //新建板子
    public async createOpen(){
        const { ctx, logger } = this;
        // 校验规则
        const rule = {
            gameBoardId: { type: 'number' },
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: IcreateGameBoardOpenReq = ctx.request.body;
            await ctx.service.werewolf.gameBoard.createOpen(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    //获得未放入open的板子
    public async  geUnOpenList() {
        const { ctx, logger } = this;
        try {
            const resp = await ctx.service.werewolf.gameBoard.geUnOpenList();
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateOpenSort(){
        const { ctx, logger } = this;
         // 校验规则
         const rule = {
            newSort: { type: 'array' },
        };
        try {
              // 校验
              ctx.validate(rule);
            const requestBody: IupdateGameBoardOpenSortReq = ctx.request.body;
           await ctx.service.werewolf.gameBoard.updateOpenSort(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async deleteOpen(){
        const { ctx, logger } = this;
        // 校验规则
        const rule = {
            gameBoardId: { type: 'number' },
        };
        try {
              // 校验
              ctx.validate(rule);
            const {gameBoardId} = ctx.request.body;
            await ctx.service.werewolf.gameBoard.deleteOpen(gameBoardId);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

}
