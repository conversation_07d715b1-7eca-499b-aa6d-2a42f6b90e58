/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-10-15 15:45:48
 * @LastEditTime: 2020-10-16 16:47:35
 * @LastEditors: jiawen.wang
 */
export interface IupdateContentItemRes {
    id: number
    content: string
    start_time: string
    end_time: string
    create_time: string
}
export interface IcreateupdateContentReq {
    id: number
    content: string
    start_time: string
    end_time: string
}
export interface IeditUpdateContent {
    id: number;
    content: string
    start_time: string
    end_time: string
}