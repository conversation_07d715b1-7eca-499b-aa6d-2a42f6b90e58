/*
 * @Description: 玩家头像管理
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: 张宇
 * @Date: 2019-06-15 15:02:09
 * @LastEditors: 张宇
 * @LastEditTime: 2019-06-15 15:02:09
 */
import { Controller } from 'egg';
import { ActivityStateOperReq, ActivityStateOperRes, ActivityChangeNameRequest, ActivityChangePrizeNumRequest } from '../../model/werewolf';
import { HttpErr, IerrorMsg } from '../../model/common';

export default class ActivityStateController extends Controller {

	//获取活动列表
	public async getActivityList() {
		const { ctx, logger } = this;
		// 校验规则
		// const rule = {
		//     activityId: { type: 'number' },
		//     operState: { type: 'number' }
		// };
		try {
			// 校验
			// ctx.validate(rule);
			// const requestBody: ActivityStateOperReq = ctx.request.body;
			const ActivityList: ActivityStateOperRes[] = await ctx.service.werewolf.activityState.getActivityList();
			ctx.body = ActivityList;
			ctx.status = HttpErr.Success;
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}

	//大厅入口显示和隐藏
	public async operHallState() {
		const { ctx, logger } = this;
		// 校验规则
		const rule = {
			operState: { type: 'number' }
		};
		try {
			// 校验
			ctx.validate(rule);
			const operState = ctx.request.body.operState;
			switch (operState) {
				case 0:
					await ctx.service.werewolf.activityState.operHallStateHide();
					break;
				case 1:
					await ctx.service.werewolf.activityState.operHallStateShow();
					break;
				default:
					const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
					throw err;
			}

			const ActivityList: ActivityStateOperRes[] = await ctx.service.werewolf.activityState.getActivityList();
			ctx.body = ActivityList;
			ctx.status = HttpErr.Success;

		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}

	//活动开启和关闭
	public async operActivityState() {
		const { ctx, logger } = this;
		// 校验规则
		const rule = {
			operState: { type: 'number' },
			activityId: { type: 'number' }
		};
		try {
			// 校验
			ctx.validate(rule);
			const request: ActivityStateOperReq = ctx.request.body;
			const operState = request.operState;
			switch (operState) {
				case 0:
					await ctx.service.werewolf.activityState.operActivityStateEnd(request);
					break;
				case 1:
					await ctx.service.werewolf.activityState.operActivityStateStart(request);
					break;
				default:
					const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
					throw err;
			}

			const ActivityList: ActivityStateOperRes[] = await ctx.service.werewolf.activityState.getActivityList();
			ctx.body = ActivityList;
			ctx.status = HttpErr.Success;

		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}

	//更改活动名称
	public async changeActivityName() {
		const { ctx, logger } = this;
		// 校验规则
		const rule = {
			activityId: { type: 'number' },
			name: { type: 'string' }
		};
		try {
			// 校验
			ctx.validate(rule);
			const request: ActivityChangeNameRequest = ctx.request.body;
			await ctx.service.werewolf.activityState.changeActivityName(request);
			
			const ActivityList: ActivityStateOperRes[] = await ctx.service.werewolf.activityState.getActivityList();
			ctx.body = ActivityList;
			ctx.status = HttpErr.Success;
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}

		//更改活动奖励数量
		public async changeActivityPrizeNum() {
			const { ctx, logger } = this;
			// 校验规则
			const rule = {
				activityId: { type: 'number' },
				num: { type: 'number' }
			};
			try {
				// 校验
				ctx.validate(rule);
				const request: ActivityChangePrizeNumRequest = ctx.request.body;
				await ctx.service.werewolf.activityState.changeActivityPrizeNum(request);
				
				const ActivityList: ActivityStateOperRes[] = await ctx.service.werewolf.activityState.getActivityList();
				ctx.body = ActivityList;
				ctx.status = HttpErr.Success;
			} catch (e) {
				logger.error(e);
				const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
				ctx.body = err;
				ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
			}
		}

}
