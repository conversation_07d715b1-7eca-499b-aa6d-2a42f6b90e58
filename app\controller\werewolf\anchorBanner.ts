/*
 * @Description: 主播板块路由
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: jiawen.wang
 * @Date: 2020-09-29 15:23:11
 */
import { Controller } from 'egg';
import { IanchorListItemRes, IcreateAnchorBannerReq, IdelAnchorBannerReq } from "../../model/anchorBanner"
import BaseMegaController from './BaseMegaController';
import { HttpErr, IerrorMsg } from '../../model/common';

export default class AnchorBannerController extends BaseMegaController {
    public async getAnchorList() {
        const { ctx, logger } = this
        try {
            const resp = await ctx.service.werewolf.anchorBanner.getAnchorList();
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    public async createAnchorBanner() {
        const { ctx, logger } = this;
        const rule = {
            recommend_tag: { type: 'number' },
            show_type: { type: 'number' },
            content: { type: 'string' },
            remark: { type: 'string' },
            turn_to_page: { type: 'number' },
            page_url: { type: 'string' },
        }
        try {
            ctx.validate(rule);
            const req: IcreateAnchorBannerReq = ctx.request.body;
            const res = await ctx.service.werewolf.anchorBanner.createAnchorBanner(req);
            ctx.body = res;

            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    public async uploadBannerImg() {
        const { ctx, logger } = this;
        const rule = {
            id: { type: 'number' },
            img_url: { type: 'string' },
        }
        try {
            ctx.validate(rule);
            const req = ctx.request.body;
            const res = await ctx.service.werewolf.anchorBanner.uploadBannerImg(req);
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    public async delAnchorBanner() {
        const { ctx, logger } = this;
        const rule = {
            id: { type: 'number' }
        }
        try {
            this.ctx.validate(rule);
            const req: IdelAnchorBannerReq = ctx.request.body;
            await ctx.service.werewolf.anchorBanner.delAnchorBanner(req);
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    public async updateAnchorBanner() {
        const { ctx, logger } = this;
        const rule = {
            id: { type: "number" },
            sort: { type: "number" },
            recommend_tag: { type: 'number' },
            show_type: { type: 'number' },
            content: { type: 'string' },
            remark: { type: 'string' },
            turn_to_page: { type: 'number' },
            page_url: { type: 'string' },
        }
        try {
            this.ctx.validate(rule);
            const req = ctx.request.body;
            await ctx.service.werewolf.anchorBanner.updateAnchorBanner(req);
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
}