import BaseMegaService from './BaseMegaService'

export default class ActivityMagicFactoryService extends BaseMegaService {

    public async findFrameItemDicList(params) {
        const {logger} = this
        try {
            const sql = `SELECT id, name
                         FROM item_dic
                         WHERE item_cate_id = 2010`

            return await this.app.mysql.get('werewolf').query(sql, [])
        } catch (err) {
            logger.error(err)
            throw err
        }
    }

    public async findItems(params) {
        const {logger} = this
        try {
            const sql = `SELECT *
                         FROM activity_item_part_info`

            return await this.app.mysql.get('werewolf').query(sql, [])
        } catch (err) {
            logger.error(err)
            throw err
        }
    }

    public async updateItem(params) {
        const {logger} = this
        const db = this.app.mysql.get('werewolf')
        const transaction = await db.beginTransaction()

        try {
            const gameSql = `UPDATE activity_item_part_info
                             SET item_dic_id = ?,
                                 remark      = ?
                             WHERE id = ?`

            const results = await transaction.query(gameSql, [params.item_dic_id, params.remark, params.id])

            await transaction.commit()

            return results
        } catch (err) {
            await transaction.rollback()

            logger.error(err)
            throw err
        }
    }
}
