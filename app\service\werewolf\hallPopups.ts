import BaseMegaService from "./BaseMegaService";
import { IhallPopupsAddRequest, IhallPopupsDeleteRequest, IhallPopupsUpdateRequest } from "../../model/werewolf";


export default class HallPopupsService extends BaseMegaService {

    private async updateRecordsTag(db) {
        const updateSql = `UPDATE hall_pop_config
                               SET tag = IFNULL(tag, 0) + 1
                               WHERE delsgin = 1`;
        const res = await db.query(updateSql);
        return res;
    }

    public async addRecord(params: IhallPopupsAddRequest) {
        const { app } = this;


        const db = app.mysql.get("werewolf");
        // const mongo = app['mongo'];

        const transaction = await db.beginTransaction();

        try {

            const sql = `INSERT INTO hall_pop_config (\`name\`, url, starttime, endtime, delsgin)
                         VALUES (?, ?, ?, ?, ?)`;

            let res = await db.query(sql, [params.name, params.url, params.starttime, params.endtime, params.delsgin]);

            res = await this.updateRecordsTag(db);

            transaction.commit();

            return true;

        } catch (error) {
            await transaction.rollback();
            throw error;
        }
    }

    public async loadRecord() {
        const { app } = this;

        const db = app.mysql.get("werewolf");

        try {

            const sql = `SELECT id,
                                \`name\`,
                                url,
                                tag,
                                DATE_FORMAT(starttime, '%Y-%m-%d %H:%i:%s') starttime,
                                DATE_FORMAT(endtime, '%Y-%m-%d %H:%i:%s')   endtime,
                                delsgin
                         FROM hall_pop_config`;

            let res = await db.query(sql);

            return res;

        } catch (error) {
            throw error;
        }
    }

    public async updateRecord(params: IhallPopupsUpdateRequest) {
        const { app } = this;

        const db = app.mysql.get("werewolf");
        const mongo = app['mongo'];

        const transaction = await db.beginTransaction();

        try {

            const sql = `UPDATE hall_pop_config
                         SET \`name\` = ?,
                             url = ?,
                             starttime = ?,
                             endtime = ?,
                             delsgin = ?
                         WHERE id = ?`;

            let res = await db.query(sql, [params.name, params.url, params.starttime, params.endtime, params.delsgin, params.id]);

            res = await this.updateRecordsTag(db);

            transaction.commit()

            return true;

        } catch (error) {
            await transaction.rollback();
            throw error;
        }
    }

    public async deleteRecord(params: IhallPopupsDeleteRequest) {
        const { app } = this;


        const db = app.mysql.get("werewolf");
        // const mongo = app['mongo'];

        const transaction = await db.beginTransaction();

        try {

            const sql = `DELETE
                         FROM hall_pop_config
                         WHERE id = ?`;

            let res = await db.query(sql, [params.id]);

            res = await this.updateRecordsTag(db);

            transaction.commit();

            return true;

        } catch (error) {
            await transaction.rollback();
            throw error;
        }
    }


}