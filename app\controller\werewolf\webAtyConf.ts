/*
 * @Description: web运营活动配置中心-服务类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-03-30 16:39:13
 * @LastEditors: hammercui
 * @LastEditTime: 2020-11-24 11:07:50
 */

import BaseMegaController from './BaseMegaController';
import { IatyInfo, IsetAutoConfReq, IsetAwardConfReq, IpoolAwardItem, IgetAllConfReq, IautoConfItem, IawardConfItem } from '../../model/wfActivityConf';
export default class WebAtyConfController extends BaseMegaController {
    /**
     * @name: 新建活动
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async createAty() {
        const { ctx, logger } = this;
        const rule = {
            name: { type: 'string' },
            start_time: { type: 'string' },
            end_time: { type: 'string' },
        }

        try {
            ctx.validate(rule);
            const req: IatyInfo = ctx.request.body;
            await ctx.service.werewolf.webAtyConfRedis.createAty(req);
            this.respSucc();
        } catch (err) {
            this.respFail(err.message)
        }
    }

    /**
     * @name: 设置自由配置
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async setAutoConf() {
        const { ctx, logger } = this;
        const rule = {
            activityId: { type: 'number' },
            env: { type: 'string' },
            key: { type: 'string' },
        }
        try {
            ctx.validate(rule);
            const req: IsetAutoConfReq = ctx.request.body;
            await ctx.service.werewolf.webAtyConfRedis.setAutoConf(req);
            this.respSucc();
        } catch (err) {
            this.respFail(err.message)
        }
    }

    /**
     * @name: 获得全部自由配置
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async getAllAutoConf() {
        const { ctx, logger } = this;
        try {
            const req: IgetAllConfReq = ctx.request.body;
            const resp: IautoConfItem[] = await ctx.service.werewolf.webAtyConfRedis.getAllAutoConf(req.activityId, req.env);
            this.respSuccData(resp);
        } catch (err) {
            this.respFail(err.message)
        }
    }

    /**
     * 获得活动任务配置
     */
    public async getAtyTaskConf(){
        const { ctx, logger } = this;
        try {
            const req: IsetAutoConfReq = ctx.request.body;
            const resp = await ctx.service.werewolf.webAtyConfRedis.getAtyTaskConf(req.env);
            
            this.respSuccData(resp);
        } catch (err) {
            this.respFail(err.message)
        }
    }

    /**
     * 获得活动刷新任务配置
     */
    public async getAtyTaskRefreshConf(){
        const { ctx, logger } = this;
        try {
            const req: IsetAutoConfReq = ctx.request.body;
            const resp = await ctx.service.werewolf.webAtyConfRedis.getAtyTaskRefreshConf(req.env);
            
            this.respSuccData(resp);
        } catch (err) {
            this.respFail(err.message)
        }
    }

    /**
     * 更新活动任务配置
     */
    public async setAtyTaskConf(){
        const { ctx, logger } = this;
        const rule = {
            env: { type: 'string' },
            key: { type: 'string' },
        }
        try {
            ctx.validate(rule);
            const req: IsetAutoConfReq = ctx.request.body;
            await ctx.service.werewolf.webAtyConfRedis.setAtyTaskConf(req.env,req.key,req.value.val);
            this.respSucc();
        } catch (err) {
            this.respFail(err.message)
        }
    }

    /**
     * 更改活动刷新任务配置
     */
    public async setAtyTaskRefreshConf(){
        const { ctx, logger } = this;
        const rule = {
            env: { type: 'string' },
            key: { type: 'string' },
        }
        try {
            ctx.validate(rule);
            const req: IsetAutoConfReq = ctx.request.body;
            await ctx.service.werewolf.webAtyConfRedis.setAtyTaskRefreshConf(req.env,req.key,req.value.val);
            this.respSucc();
        } catch (err) {
            this.respFail(err.message)
        }
    }

    /**
     * 获得任务列表
     */
    public async getTaskList(){
        const { ctx, logger } = this;
        const rule = {
            activityId: { type: 'number' },
        }
        try {
            ctx.validate(rule);
            const req: IsetAutoConfReq = ctx.request.body;
            const list = await ctx.service.werewolf.webAtyConfRedis.getTaskList(req.activityId);
            if(list){
                this.respSuccData(list);
            }else{
                this.respSuccData([])
            }
            
        } catch (err) {
            this.respFail(err.message)
        }
    }


}
