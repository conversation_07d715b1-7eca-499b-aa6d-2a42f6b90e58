/*
 * @Description: 2w框相关路由
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammer<PERSON><PERSON>
 * @Date: 2020-08-28 13:53:58
 * @LastEditors: zhanglu
 * @LastEditTime: 2022-09-15 13:52:25
 */
import { Router, Application } from "egg";
import { AccessRouteId } from './model/accessRouteCof';
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

  const { controller, router } = app;

  //20000头像框
  router.get(`${API_VERSION}/werewolf/avatarperiod/getAvatarFramePeriodList`, accCtr(AccessRouteId.wolf_2w_avatar), controller.werewolf.gameConfig.getAvatarFramePeriodList);
  router.post(`${API_VERSION}/werewolf/avatarperiod/updatePeriod`, accCtr(AccessRouteId.wolf_2w_avatar), controller.werewolf.gameConfig.updatePeriod);
  router.post(`${API_VERSION}/werewolf/avatarperiod/createPeriod`, accCtr(AccessRouteId.wolf_2w_avatar), controller.werewolf.gameConfig.createPeriod);
  router.post(`${API_VERSION}/werewolf/avatarperiod/updatePeriodImg`, accCtr(AccessRouteId.wolf_2w_avatar), controller.werewolf.gameConfig.updatePeriodImg);

  //两万框v2-头像框列表
  router.post(`${API_VERSION}/werewolf/avatarperiodv2/getAvatarFramePeriodListv2`, accCtr(AccessRouteId.wolf_2w_avatar),
    controller.werewolf.avatar2wPeriod.getAvatarFramePeriodListv2);
    router.post(`${API_VERSION}/werewolf/avatarperiodv2/getAvatarFramePeriodListNewV2`, accCtr(AccessRouteId.wolf_2w_avatar),
    controller.werewolf.avatar2wPeriod.getAvatarFramePeriodListNewV2)
  //两万框v2-新建
  router.post(`${API_VERSION}/werewolf/avatarperiodv2/createPeriodv2`, accCtr(AccessRouteId.wolf_2w_avatar),
    controller.werewolf.avatar2wPeriod.createPeriodv2);
  //两万框v2-更新
  router.post(`${API_VERSION}/werewolf/avatarperiodv2/updatePeriodv2`, accCtr(AccessRouteId.wolf_2w_avatar),
    controller.werewolf.avatar2wPeriod.updatePeriodv2);
  //两万框v2-更新图片
  router.post(`${API_VERSION}/werewolf/avatarperiodv2/updatePeriodImgv2`, accCtr(AccessRouteId.wolf_2w_avatar),
    controller.werewolf.avatar2wPeriod.updatePeriodImg2);
  //两万框v2-根据您名字查询头像框
  router.post(`${API_VERSION}/werewolf/avatarperiodv2/getAvatarFrameNamev2`, accCtr(AccessRouteId.wolf_2w_avatar),
    controller.werewolf.avatar2wPeriod.getAvatarFrameNamev2);
  //两万框v2-上架列表排序
  router.post(`${API_VERSION}/werewolf/avatarperiodv2/changePeriodSort`, accCtr(AccessRouteId.wolf_2w_avatar),
    controller.werewolf.avatar2wPeriod.changePeriodSort);
  //两万框v2-主动下架
  router.post(`${API_VERSION}/werewolf/avatarperiodv2/putdown`, accCtr(AccessRouteId.wolf_2w_avatar),
  controller.werewolf.avatar2wPeriod.putDown);

  router.post(`${API_VERSION}/werewolf/avatarperiodv2/getTwoWCoinUserList`, accCtr(AccessRouteId.wolf_2w_avatar), controller.werewolf.coin2wTrade.getTwoWCoinUserList);
  //两万框订单-获得交易订单
  router.post(`${API_VERSION}/werewolf/avatarperiodv2/getTwoWCoinList`, accCtr(AccessRouteId.wolf_2w_avatar), controller.werewolf.coin2wTrade.getTwoWCoinList);
  //两万框订单-更新交易订单
  router.post(`${API_VERSION}/werewolf/avatarperiodv2/updateCoin2wInfo`, accCtr(AccessRouteId.wolf_2w_avatar), controller.werewolf.coin2wTrade.updateCoin2winfo);
  //两万框订单-上传刻字成功
  router.post(`${API_VERSION}/werewolf/avatarperiodv2/uploadNoteIdSucc`, accCtr(AccessRouteId.wolf_2w_avatar), controller.werewolf.coin2wTrade.uploadNoteIdSucc);
  //两万框订单-发放全部资源
  router.post(`${API_VERSION}/werewolf/avatarperiodv2/sendAllRes`, accCtr(AccessRouteId.wolf_2w_avatar), controller.werewolf.coin2wTrade.sendAllRes);

  //两万刻字订单列表
  router.post(`${API_VERSION}/werewolf/avatarperiodv2/getNoteOrderList`, accCtr(AccessRouteId.wolf_2w_avatar), controller.werewolf.coin2wNoteOrderTrade.getNoteOrderList);
  //更新两万刻字订单
  router.post(`${API_VERSION}/werewolf/avatarperiodv2/updateCoin2wNoteOrderInfo`, accCtr(AccessRouteId.wolf_2w_avatar), controller.werewolf.coin2wNoteOrderTrade.updateCoin2wNoteInfo);
  //两万框刻字订单-上传刻字成功
  router.post(`${API_VERSION}/werewolf/avatarperiodv2/uploadNoteOrderIdSucc`, accCtr(AccessRouteId.wolf_2w_avatar), controller.werewolf.coin2wNoteOrderTrade.uploadNoteIdSucc);
  //两万框刻字订单-发放全部资源
  router.post(`${API_VERSION}/werewolf/avatarperiodv2/sendAllNoteOrderRes`, accCtr(AccessRouteId.wolf_2w_avatar), controller.werewolf.coin2wNoteOrderTrade.sendAllRes);
  //两万框v2-头像框列表 刻字订单新增 
  router.post(`${API_VERSION}/werewolf/avatarperiodv2/getAvatarFramePeriodListv2NoteOrder`, accCtr(AccessRouteId.wolf_2w_avatar),
    controller.werewolf.avatar2wPeriod.getAvatarFramePeriodListv2NoteOrder);
  
}

export default load
