/*
 * @Description: 商城道具管理-控制类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: z<PERSON><PERSON>
 * @Date: 2020-04-24 13:00:00
 * @LastEditors: zhang<PERSON>
 * @LastEditTime: 2021-04-23 17:05:23
 */
import { Controller } from 'egg';
import BaseMegaController from './BaseMegaController';
import { IerrorMsg, HttpErr } from '../../model/common';

export default class NobleController extends BaseMegaController {

    public async getUserNobleEmperor() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.noble.getUserNobleEmperor(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getAwardList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.noble.getAwardList();
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async insertNoble() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.noble.insertNoble(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async updateNoble() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.noble.updateNoble(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async getUserNobleInfo() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.noble.getUserNobleInfo(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getUserOrderList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.noble.getUserOrderList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async insertOrder() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.noble.insertOrder(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async updateUploadOrder() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.noble.updateUploadOrder(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async updateProvideOrder() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.noble.updateProvideOrder(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }
    
    public async getOpreateList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.noble.getOpreateList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getUserNobleNumberList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.noble.getUserNobleNumberList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async updateUserRoomNo() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.noble.updateUserRoomNo(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async renewalNoble() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.noble.renewalNoble(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }
}
