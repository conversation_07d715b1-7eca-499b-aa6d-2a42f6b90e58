/*
 * @Description: 长图链接
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: wb
 * @Date: 2020-04-24 11:35:39
 * @LastEditTime: 2021-07-21 10:54:23
 */
import BaseMegaService from './BaseMegaService';


const werewolfDb = "werewolf";

export default class SsFrameTimeService extends BaseMegaService {

    public async searchSSFrames(req: any) {
        const { app, ctx, logger } = this;
        try {

            let sqlStr = ``;

            if(req.isNow == '1'){

                sqlStr = `
                    SELECT * FROM avatarframe WHERE delsign = 0 AND  NOW() > ss_start_time AND NOW() < ss_end_time AND level = 5 ORDER BY id DESC;
                `;

            }else if(req.id.length > 0){
                
                sqlStr = `
                    SELECT * FROM avatarframe WHERE delsign = 0 AND  id = '${req.id}' AND level = 5 ORDER BY id DESC;
                `;

            }else{

                sqlStr = `
            SELECT * FROM avatarframe WHERE delsign = 0 AND name LIKE '%${req.name}%' AND level = 5 
            `;

            if(req.ss_start_time.length > 0 && req.ss_end_time.length > 0)
                {
                    sqlStr += `AND '${req.ss_start_time}' < ss_start_time AND '${req.ss_end_time}' > ss_end_time`;
                }
                sqlStr += ` ORDER BY id DESC;`;

            }

           


            let list = await this.selectList(sqlStr, [], werewolfDb);

            return list;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }
    public async updateSSFrameTime(req: any) {
        const { app, ctx, logger } = this;
        const manager = app.mysql.get(werewolfDb);
        const managerConn = await manager.beginTransaction();
        
        try {

            let updateSql = `UPDATE avatarframe SET ss_start_time='${req.ss_start_time}',ss_end_time='${req.ss_end_time}' WHERE id=${req.id};`
            await managerConn.query(updateSql, []);

            await managerConn.commit();
        } catch (error) {
            await managerConn.rollback();
            logger.error(error);
            throw error
        }
    }

}
