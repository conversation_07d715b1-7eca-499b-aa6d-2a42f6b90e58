import { IexchangeResp, IitemDic, IobtianItemReq } from '../../model/wfExchange';
import BaseMegaService from './BaseMegaService';
export default class ExchangeRpcService extends BaseMegaService {

    public async itemObtain(req: IobtianItemReq) {
        try {
            await this.postExhange('item/obtain', req)
        } catch (error) {
            throw error;
        }
    }

    // tslint:disable-next-line:no-empty
    public geneAddReqByItemList(uid,itemDicList: IitemDic[]): IobtianItemReq{
        let obtianReq: IobtianItemReq = {
            type: 0,
            activityId: -1,
            userList: [
                {
                    userId: uid,
                    itemDicList: itemDicList
                }
            ]
        }
        return obtianReq
    }


    /**
     * 发送post类型exchange请求
     * @param route 
     * @param req 
     * @returns 
     */
    public async postExhange(route: string, req: IobtianItemReq) {
        const { app, ctx, logger } = this;
        try {
            let jsonStr = JSON.stringify(req)
            const url = app.config.WerewolfJPExchange + route
            logger.info("req---> url",url)
            logger.info("req---> data",jsonStr)
            let res = await ctx.curl(url, {
                method: 'POST',
                headers: {
                    "content-type": "application/json",
                },
                data: jsonStr,
                timeout: 10000, // 10 秒超时
            });
            
            if (!!res && res.status == 200) {
                let data: IexchangeResp = JSON.parse(res.data);
                logger.info("resp---> body",res.data)
                if (data.code == 1) {
                    return
                } else {
                    throw new Error(res.data)
                }
            } else {
                logger.info("resp---> err",res.status)
                throw new Error("errCode：" + res.status)
            }
        } catch (err) {
            logger.error("postExhange err:" + err)
            throw err;
        }
    }

    public async post(route: string, req: any) {
        const { app, ctx, logger } = this;
        try {
            let jsonStr = JSON.stringify(req)
            const url = app.config.WerewolfJPExchange + route
            logger.info("req---> url",url)
            logger.info("req---> data",jsonStr)
            let res = await ctx.curl(url, {
                method: 'POST',
                headers: {
                    "content-type": "application/json",
                },
                data: jsonStr,
                timeout: 10000, // 10 秒超时
            });

            if (!!res && res.status == 200) {
                let data: IexchangeResp = JSON.parse(res.data);
                logger.info("resp---> body",res.data)
                if (data.code == 1) {
                    return data;
                } else {
                    throw new Error(res.data)
                }
            } else {
                logger.info("resp---> err",res.status)
                throw new Error("errCode：" + res.status)
            }
        } catch (err) {
            logger.error("postExhange err:" + err)
            throw err;
        }
    }

    public async gatePost(route: string, req: any) {
        const { app, ctx, logger } = this;
        try {
            let jsonStr = JSON.stringify(req)
            const url = app.config.WerewolfJPExchangeGate + route
            logger.info("req---> url",url)
            logger.info("req---> data",jsonStr)
            let res = await ctx.curl(url, {
                method: 'POST',
                headers: {
                    "content-type": "application/json",
                },
                data: jsonStr,
                timeout: 10000, // 10 秒超时
            });

            if (!!res && res.status == 200) {
                let data: IexchangeResp = JSON.parse(res.data);
                logger.info("resp---> body",res.data)
                if (data.code == 1) {
                    return
                } else {
                    throw new Error(res.data)
                }
            } else {
                logger.info("resp---> err",res.status)
                throw new Error("errCode：" + res.status)
            }
        } catch (err) {
            logger.error("post err:" + err)
            throw err;
        }
    }


}