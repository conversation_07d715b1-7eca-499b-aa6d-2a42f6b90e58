import BaseMegaController from './BaseMegaController'
import { IerrorMsg, HttpErr } from '../../model/common'

export default class MiningSeasonController extends BaseMegaController {
    public async getMiningSeasonList() {
        const { ctx, logger } = this
        try {
            const list = await ctx.service.werewolf.miningSeason.getMiningSeasonList()
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }
      /*添加图鉴赛季*/
      public async createMiningSeasonList() {
        const {ctx, logger, app} = this;
        // 校验规则
        const rule = {
            name: { type: 'string' },
            start_time: { type: 'string' },
            end_time: { type: 'string' }
        };
        try {
            // 校验
            ctx.validate(rule);
            const responseBody = await ctx.service.werewolf.miningSeason.createMiningSeasonList(ctx.request.body);
            ctx.body = {
				err_code: HttpErr.Success,
				err_msg: '成功'
			};
			ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = {err_code: HttpErr.BadRequest, err_msg: '参数有误'};
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401 Unauthorized未鉴权
        }
    }
      /*更新图鉴赛季*/
      public async updateMiningSeasonList() {
        const {ctx, logger, app} = this;
        // 校验规则
        const rule = {
            id: { type: 'number' },
            name: { type: 'string' },
            start_time: { type: 'string' },
            end_time: { type: 'string' }
        };
        try {
            // 校验
            ctx.validate(rule);
            const responseBody = await ctx.service.werewolf.miningSeason.updateMiningSeasonList(ctx.request.body);
            ctx.body = {
				err_code: HttpErr.Success,
				err_msg: '成功'
			};
			ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = {err_code: HttpErr.BadRequest, err_msg: '参数有误'};
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401 Unauthorized未鉴权
        }
    }


    public async getMiningIllustratedList() {
        const { ctx, logger } = this
        const rule = {
            id: { type: 'number' },
        
        };
        try {
            const list = await ctx.service.werewolf.miningSeason.getMiningIllustratedList(ctx.request.body);
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }
    public async createMiningIllustratedList() {
        const {ctx, logger, app} = this;
        // 校验规则
        const rule = {
            s_no: { type: 'string' },
            level: { type: 'number' },
            name: { type: 'string' },
            desc: { type: 'string' },
            seasonNum: { type: 'number'}
        };
        try {
            // 校验
            ctx.validate(rule);
            const responseBody = await ctx.service.werewolf.miningSeason.createMiningIllustratedList(ctx.request.body);
            ctx.body = {
				err_code: HttpErr.Success,
				err_msg: '成功'
			};
			ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = {err_code: HttpErr.BadRequest, err_msg: '参数有误'};
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401 Unauthorized未鉴权
        }
    }
    public async updateMiningIllustratedList() {
        const {ctx, logger, app} = this;
        // 校验规则
        const rule = {
            id: { type: 'number' },
            s_no: { type: 'string' },
            level: { type: 'number' },
            name: { type: 'string' },
            desc: { type: 'string' },
            // img: { type: 'string' },
            // detail_img: { type: 'string' },
            seasonNum: { type: 'number'}
        };
        try {
            // 校验
            ctx.validate(rule);

            const responseBody = await ctx.service.werewolf.miningSeason.updateMiningIllustratedList(ctx.request.body);
            ctx.body = {
				err_code: HttpErr.Success,
				err_msg: '成功'
			};
			ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = {err_code: HttpErr.BadRequest, err_msg: '参数有误'};
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401 Unauthorized未鉴权
        }
    }
}