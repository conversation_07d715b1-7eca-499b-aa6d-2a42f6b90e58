/*
 * @Description: 长图链接
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: wb
 * @Date: 2020-04-24 11:35:39
 * @LastEditTime: 2021-07-21 10:54:23
 */
import BaseMegaService from './BaseMegaService';
// import {
//     InewPlayerRobotInsertParams,
//     InewPlayerRobotUpdateParams,
//     InewPlayerRobotUpdateTypeParams,
//     InewPlayerRobotSearchParams
// } from "../../model/newPlayerRobotDto";

const managerDb = "manager";
const werewolfDb = "werewolf";
// const mysql = require('mysql');

export default class IllustratedBookService extends BaseMegaService {

    public async getList() {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
                SELECT * FROM wf_admin_long_image  ORDER BY id DESC
            `;

            // const connection = mysql.createConnection({
            //     host: '*************',
            //     user: 'admin',
            //     port: "3306",
            //     password: 'Mangosteen0!',
            //     database: 'mega_manager'
            //   });

            // let db = await connection.connect((err) => {
            //     if (err) throw err;
            //     console.log('Connected to the MySQL server.');
            //   });
              
            //   let data = new Promise<any>((resolve, reject) => {

            //     // 定义一个简单的API端点
            //    connection.query(sqlStr, (err, result) => {
            //     if (err) throw err;
            //     resolve(result)
            //   });

            //   })

            //   let result = await data;
            // //   db.end();
            //   return result;
              
            let sqlStr_pop = `
            SELECT id,name,tag,starttime,endtime,delsgin as delsign FROM hall_pop_config
        `;

            let list = await this.selectList(sqlStr, [], managerDb);
            let popList = await this.selectList(sqlStr_pop, [], werewolfDb);

            if(list != null && popList != null) {
                for (const value1 of list) {
                    value1.delsign = 0;
                    for (const value2 of popList) {
                        if(value1.pop_id == value2.id){

                            value1.name = value2.name;
                            value1.tag = value2.tag;
                            value1.starttime = value2.starttime;
                            value1.endtime = value2.endtime;

                            value1.delsign = value2.delsign;
                        }
                    }
                }
            }

            

            return list;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }
    public async fetchFilterItems() {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
                SELECT * FROM wf_admin_long_image WHERE web_type = 1 ORDER BY id DESC
            `;

              
            let sqlStr_pop = `
                SELECT id,name,tag,starttime,endtime,delsgin as delsign FROM hall_pop_config
            `;

            let list = await this.selectList(sqlStr, [], managerDb);
            let popList = await this.selectList(sqlStr_pop, [], werewolfDb);


            if(list != null && popList != null) {
                for (const value1 of list) {
                    value1.delsign = 0;
                    for (const value2 of popList) {
                        if(value1.pop_id == value2.id){

                            value1.name = value2.name;
                            value1.tag = value2.tag;
                            value1.starttime = value2.starttime;
                            value1.endtime = value2.endtime;

                            value1.delsign = value2.delsign;
                        }
                    }
                }
            }

            return list;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }
    public async update(req: any) {
        const { app, ctx, logger } = this;
        const manager = app.mysql.get(managerDb);
        const managerConn = await manager.beginTransaction();
        
        try {

            let sql = '';
            if(req.msg_type == 0){

                sql = `
                UPDATE wf_admin_long_image
                SET msg_type  = '${req.msg_type}',
                    longImage = '${req.longImage}',
                    url_name = '${req.url_name}',
                    web_type  = '${req.web_type}',
                    buttonImage = '',
                    tend_type = '',
                    tend_page = '',
                    acTendUrl = '',
                    tend_shop = '',
                    tab1 = '',
                    tab2 = ''
                WHERE id = '${req.id}';
            `

            }else{

                if(req.web_type == 0){
                    sql = `
                    UPDATE wf_admin_long_image
                    SET msg_type  = '${req.msg_type}',
                        longImage = '${req.longImage}',
                        url_name = '${req.url_name}',
                        buttonImage = '${req.buttonImage}',
                        tend_type = '${req.tend_type}',
                        web_type  = '${req.web_type}',
                        tend_page = '${req.tend_page}',
                        acTendUrl = '${req.acTendUrl}',
                        tend_shop = '${req.tend_shop}',
                        tab1 = '${req.tab1}',
                        tab2 = '${req.tab2}'
                    WHERE id = '${req.id}';
                    `

                }else{

                    sql = `
                    UPDATE wf_admin_long_image
                    SET msg_type  = '${req.msg_type}',
                        longImage = '${req.longImage}',
                        url_name = '${req.url_name}',
                        buttonImage = '',
                        tend_type = '${req.tend_type}',
                        web_type  = '${req.web_type}',
                        tend_page = '${req.tend_page}',
                        acTendUrl = '${req.acTendUrl}',
                        tend_shop = '${req.tend_shop}',
                        tab1 = '${req.tab1}',
                        tab2 = '${req.tab2}'
                    WHERE id = '${req.id}';
                    `

                }
              

            }
          
            // const connection = mysql.createConnection({
            //     host: '*************',
            //     user: 'admin',
            //     port: "3306",
            //     password: 'Mangosteen0!',
            //     database: 'mega_manager'
            //   });

            // let db = await connection.connect((err) => {
            //     if (err) throw err;
            //     console.log('Connected to the MySQL server.');
            //   });
              
            //   let data = new Promise<any>((resolve, reject) => {

            //     // 定义一个简单的API端点
            //    connection.query(sql, (err, result) => {
            //     if (err) throw err;
            //     console.log("Query    "+result);
            //     logger.info("Query "+result);
            //     //  return result;
            //     resolve(result)
            //   });

            //   })

            //   return await data;
              

            
            await managerConn.query(sql, []);

            await managerConn.commit();
        } catch (error) {
            await managerConn.rollback();
            logger.error(error);
            throw error
        }
    }

    public async insert(req: any) {
        const { app, ctx, logger } = this;
        const manager = app.mysql.get(managerDb);
        const managerConn = await manager.beginTransaction();

        try {


            let insertSql = `
            INSERT INTO wf_admin_long_image (msg_type,url_name,web_type,domainName)
            VALUES ('${req.msg_type}','${req.url_name}','${req.web_type}','${req.domainName}');
        `;

            const result = await managerConn.query(insertSql, []);
            let lastId = result.insertId


            // let sql = '';
            // if(req.msg_type == 0){

            //     sql = `
            //     UPDATE  wf_admin_long_image SET 
            //     longImage = '"longImage_"+${lastId}+".png"',
            //     buttonImage = '"buttonImage_"+${lastId}+".png"'
            //     WHERE id = '${lastId}';
            //     `;



            //  await managerConn.query(sql, []);


            // }else{
            //  sql = `
            //     INSERT INTO wf_admin_long_image (longImage,
            //                                      buttonImage,
            //                                      tend_type,
            //                                      tend_page,
            //                                      acTendUrl,
            //                                      domainName,
            //                                      tend_shop,
            //                                      tab1,
            //                                      tab2
            //                                      )
            //     VALUES ( '${req.longImage}', '${req.buttonImage}', '${req.tend_type}', '${req.tend_page}', '${req.acTendUrl}', '${req.domainName}', '${req.tend_shop}','${req.tab1}','${req.tab2}');
            // `;

            //  await managerConn.query(sql, []);

            // }

        

            await managerConn.commit();

            // const connection = mysql.createConnection({
            //     host: '*************',
            //     user: 'admin',
            //     port: "3306",
            //     password: 'Mangosteen0!',
            //     database: 'mega_manager'
            //   });


            // let db = await connection.connect((err) => {
            //     if (err) throw err;
            //     console.log('Connected to the MySQL server.');
            //   });
              
            
            //   let data = new Promise<any>((resolve, reject) => {

            //     // 定义一个简单的API端点
            //    connection.query(sql, (err, result) => {
            //     if (err) throw err;
            //     resolve(result)
            //   });


            //   })

            //   let result = await data;
            // //   db.end();
            //   return result;

        } catch (error) {
            await managerConn.rollback();
            logger.error(error);
            throw error
        }
    }

    public async updateItemDelSign(req: any) {
        const { app, ctx, logger } = this;
        const werewolf = app.mysql.get(werewolfDb);
        const werewolfConn = await werewolf.beginTransaction();
        
        try {

            // req.delsign = 1;
            // req.pop_id = 9;
            let sql =  `UPDATE hall_pop_config SET delsgin  = ${req.delsign} WHERE id = ${req.pop_id};`;
            
            await werewolfConn.query(sql, []);

            await werewolfConn.commit();
        } catch (error) {
            await werewolfConn.rollback();
            logger.error(error);
            throw error
        }
    }

    public async insertPopItem(req: any) {
        const { app, ctx, logger } = this;
        const werewolf = app.mysql.get(werewolfDb);
        const werewolfConn = await werewolf.beginTransaction();
        const manager = app.mysql.get(managerDb);
        const managerConn = await manager.beginTransaction();

        try {


            let url = "https://datacenter.53site.com/Werewolf/DailyMission/indexPop.html?id="+req.id;

            let insertSql = `
            INSERT INTO hall_pop_config (name,url,tag,starttime,endtime,delsgin)
            VALUES ('${req.name}','${url}',${req.tag},'${req.starttime}','${req.endtime}',1);
            `;

            const result = await werewolfConn.query(insertSql, []);
            let lastId = result.insertId

            let updatelongSql = `
            UPDATE  wf_admin_long_image SET pop_id = ${lastId} WHERE id = ${req.id};
            `;
        
            await managerConn.query(updatelongSql, []);

            await werewolfConn.commit();
            await managerConn.commit();

        } catch (error) {
            await werewolfConn.rollback();
            await managerConn.rollback();

            logger.error(error);
            throw error
        }
    }

    public async updateItemPop(req: any) {
        const { app, ctx, logger } = this;
        const werewolf = app.mysql.get(werewolfDb);
        const werewolfConn = await werewolf.beginTransaction();

        try {

            let updasteSql =  `UPDATE hall_pop_config SET 
            name  = '${req.name}',
            tag  = ${req.tag+1},
            starttime  = '${req.starttime}',
            endtime  = '${req.endtime}'
             WHERE id = ${req.pop_id};`;

            const result = await werewolfConn.query(updasteSql, []);

            await werewolfConn.commit();

        } catch (error) {
            await werewolfConn.rollback();
            logger.error(error);
            throw error
        }
    }

}
