/**
 * <AUTHOR>
 */


import BaseMegaController from './BaseMegaController';
import { Icoin2wListReq, Icoin2wItem, Icoin2wUpdateInfoReq, Icoin2wUploadNoteSuccReq } from '../../model/wf2wCoin';


 export default class Coin2wTradeController extends BaseMegaController {

    /**
     * 获得订单全部列表
     */
    public async getNoteOrderList() {
        const { ctx, logger } = this;
        try {
            const req = ctx.request.body;
            const resp: Icoin2wItem[] = await ctx.service.werewolf.coin2wNote.getNoteOrderList(req);
            this.respSuccData(resp);
        } catch (error) {
            this.respFail(error);
        }
    }
    /**
     * 更新订单信息
     */
     public async updateCoin2wNoteInfo() {
        const { ctx, logger } = this;
        try {
            const req: Icoin2wUpdateInfoReq = ctx.request.body;
            await ctx.service.werewolf.coin2wNote.updateCoin2wNoteInfo(req);
            this.respSucc();
        } catch (error) {
            this.respFail(error);
        }
    }

    public async uploadNoteIdSucc(){
        const { ctx, logger } = this;
        try {
            const req: Icoin2wUploadNoteSuccReq = ctx.request.body;
            await ctx.service.werewolf.coin2wNote.uploadCoin2wNoteSuccess(req);
            this.respSucc();
        } catch (error) {
            this.respFail(error);
        }
    }  
    
    public async sendAllRes(req: Icoin2wItem) {
        const { ctx, logger } = this;
        try {
            const req: Icoin2wItem = ctx.request.body;
            await ctx.service.werewolf.coin2wNote.sendAllRes(req);
            this.respSucc();
        } catch (error) {
            this.respFail(error);
        }
    }
 }
