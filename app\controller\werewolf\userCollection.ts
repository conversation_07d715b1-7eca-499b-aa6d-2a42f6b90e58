/**
 @name:收藏室
 @description:
 @author: fan<PERSON><PERSON><PERSON>
 @time: 2021-08-07 16:04:35
 **/

import { Controller } from 'egg';
import { IerrorMsg, HttpErr } from '../../model/common';
import {
    UserCollectionBean,
    IuserCollectionCleanRecordsParams,
    UserCollectionResult
} from "../../model/UserCollectionBean";

export default class UserCollectionContorller extends Controller {

    public async getUserCollectionList() {
       // console.info('****************************************66666666666666');
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;

             console.info('requestBody='+ JSON.stringify(requestBody));
            const resp: UserCollectionBean = await ctx.service.werewolf.userCollection.getUserCollectionList(requestBody);
            ctx.body = resp;
            ctx.status = HttpErr.Success;


        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }


    public async updateCollectionList() {
        // console.info('****************************************66666666666666');
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;

            console.info('requestBody='+ JSON.stringify(requestBody));
            const resp: UserCollectionResult = await ctx.service.werewolf.userCollection.upDateUserCollectionList(requestBody);
            ctx.body = resp;
            ctx.status = HttpErr.Success;


        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async insertUserCollectionCleanRecords() {
        // console.info('****************************************66666666666666');
        const { ctx, logger } = this;
        const rules = {
            uid: {type:'number'},
            user_id: {type:'number'},
            index: {type:'number'},
            url: {type:'string'},
        };
        try {
            const requestBody: IuserCollectionCleanRecordsParams = ctx.request.body;
            ctx.validate(rules);
            const resp  = await ctx.service.werewolf.userCollection.insertUserCollectionCleanRecords(requestBody);
            ctx.body = resp;
            ctx.status = HttpErr.Success;

        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async getUserCollectionCleanRecords() {
        // console.info('****************************************66666666666666');
        const { ctx, logger } = this;
        try {
            // const requestBody: IuserCollectionCleanRecordsParams = ctx.request.body;

            const resp  = await ctx.service.werewolf.userCollection.getUserCollectionCleanRecords();
            ctx.body = resp;
            ctx.status = HttpErr.Success;

        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }


}
