/*
 * @Description: 小号黑名单查询
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: 赵宝强
 * @Date: 2020-11-09 17:52
 * @LastEditors: 赵宝强
 * @LastEditTime:
 */
import {AccountAbnormalRequest, UdidRequest} from "../../model/werewolf2";
import BaseMegaService from './BaseMegaService';
import {CheatGameSts, Tuser} from "../../model/accountAbnormal";

export default class AccountAbnormalService extends BaseMegaService {

    // 1 异常查询
    public async getCheatGameSts(req: AccountAbnormalRequest): Promise<CheatGameSts[]> {
        const { app, ctx, logger } = this;

        try {
            const sqlStr = `SELECT * FROM user_cheat_game_sts WHERE user_no = ${req.userNo} AND game_no = ${req.gameNo}; `
            const array: CheatGameSts[] = await this.selectList(sqlStr);
            return array;
        } catch (err) {
            logger.error(err);
            throw err;
        }
    }

    // udid查询
    public async getUdid(req: UdidRequest): Promise<Tuser[]> {
        const { app, ctx, logger } = this;
        logger.info(req);
        try {
            const sqlStr = `SELECT t.no, t.username,t.logintime, t.createtime, t.binding_phone, t.win/sum(t.win+t.lose) AS winningProbability FROM tuser t WHERE t.udid = '${req.udid}'; `
            const array: Tuser[] = await this.selectList(sqlStr);
            return array;
        } catch (err) {
            logger.error(err);
            throw err;
        }
    }




}