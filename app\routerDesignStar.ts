/*
 * @Description: 设计之星
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammer
 * @Date: 2021-06-17
 * @LastEditors: hammer
 * @LastEditTime:
 */
import {Application} from "egg";
import {AccessRouteId} from './model/accessRouteCof';

const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {
    const { controller, router } = app;
    //设计列表
    router.post(`${API_VERSION}/werewolf/designStar/list`,controller.werewolf.designStar.list);
    //设计过滤
    router.post(`${API_VERSION}/werewolf/designStar/fliteList`,controller.werewolf.designStar.fliteList);
    //更新设计状态
    router.post(`${API_VERSION}/werewolf/designStar/updateStatus`,controller.werewolf.designStar.updateStatus);

    //更新投票分数
    router.post(`${API_VERSION}/werewolf/designStar/updateNum`,controller.werewolf.designStar.updateNum);
    //更新图片
    router.post(`${API_VERSION}/werewolf/designStar/updateImg`,controller.werewolf.designStar.updateImg);

}

export default load
