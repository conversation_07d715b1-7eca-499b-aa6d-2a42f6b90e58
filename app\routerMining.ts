/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-02-23 13:21:37
 * @LastEditTime: 2022-03-07 16:25:55
 * @LastEditors: jiawen.wang
 */
/*
 * @Description: 框石管理路由
 */

import { Router, Application } from 'egg'
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {
    const { controller, router } = app
    // 获取框石列表   /werewolf
    router.post(`${API_VERSION}/werewolf/mining/getMiningList`, controller.werewolf.mining.getMiningList)
    // 添加框石
    router.post(`${API_VERSION}/werewolf/mining/addMiningFrame`, controller.werewolf.mining.addMiningFrame)
    // 编辑框石
    router.post(`${API_VERSION}/werewolf/mining/editMiningFrame`, controller.werewolf.mining.editMiningFrame)
    // 获取框石奖池列表
    router.post(`${API_VERSION}/werewolf/mining/getAwardPoolList`, controller.werewolf.mining.getAwardPoolList)
    // 添加框石奖池
    router.post(`${API_VERSION}/werewolf/mining/addAwardPool`, controller.werewolf.mining.addAwardPool)
    // 编辑奖池
    router.post(`${API_VERSION}/werewolf/mining/editAwardPool`, controller.werewolf.mining.editAwardPool)
    // 获取奖品列表
    router.post(`${API_VERSION}/werewolf/mining/getAwardList`, controller.werewolf.mining.getAwardList)
    // 添加奖品
    router.post(`${API_VERSION}/werewolf/mining/addAward`, controller.werewolf.mining.addAward)
    router.post(`${API_VERSION}/werewolf/mining/editAward`, controller.werewolf.mining.editAward)

    // 下架奖品列表奖品

    router.post(`${API_VERSION}/werewolf/mining/updateAward`, controller.werewolf.mining.updateAward)

    // 上下架奖品
    router.post(`${API_VERSION}/werewolf/mining/updateAwardDelsign`, controller.werewolf.mining.updateAwardDelsign)

    /*获取奖池内奖品*/
    router.post(`${API_VERSION}/werewolf/mining/getAwardInPool`, controller.werewolf.mining.getAwardInPool)
    // 为奖池添加奖品
    router.post(`${API_VERSION}/werewolf/mining/addAwardInPool`, controller.werewolf.mining.addAwardInPool)
    // 获取框石奖池列表
    router.post(`${API_VERSION}/werewolf/mining/getAwardPool`, controller.werewolf.mining.getAwardPool)
    // 编辑奖池中的奖品
    router.post(`${API_VERSION}/werewolf/mining/editAwardInPool`, controller.werewolf.mining.editAwardInPool)
    // 获取框石的奖池列表
    router.post(`${API_VERSION}/werewolf/mining/getMiningAwardPool`, controller.werewolf.mining.getMiningAwardPool)
    // 为框石添加奖池
    router.post(`${API_VERSION}/werewolf/mining/addAwardPoolToMining`, controller.werewolf.mining.addAwardPoolToMining)
    // 编辑框石的奖池
    router.post(`${API_VERSION}/werewolf/mining/editAwardPoolToMining`, controller.werewolf.mining.editAwardPoolToMining)
    // 获取框石掉率列表
    router.post(`${API_VERSION}/werewolf/mining/selectMiningDropList`, controller.werewolf.mining.selectMiningDropList)
    // 添加颜色区间框石掉率
    router.post(`${API_VERSION}/werewolf/mining/addMiningColorTotalSection`, controller.werewolf.mining.addMiningColorTotalSection)
    // 删除颜色区间块
    router.post(`${API_VERSION}/werewolf/mining/deleteMiningColorSection`, controller.werewolf.mining.deleteMiningColorSection)
    // 编辑颜色区间内容
    router.post(`${API_VERSION}/werewolf/mining/editMiningColorSection`, controller.werewolf.mining.editMiningColorSection)
    // 编辑框石权重掉率
    router.post(`${API_VERSION}/werewolf/mining/editMiningDropItem`, controller.werewolf.mining.editMiningDropItem)
    // 删除框石权重掉率
    router.post(`${API_VERSION}/werewolf/mining/deleteMiningDropItem`, controller.werewolf.mining.deleteMiningDropItem)


    router.post(`${API_VERSION}/werewolf/mining/weekAwardList`, controller.werewolf.mining.weekAwardList)
    router.post(`${API_VERSION}/werewolf/mining/addWeekAward`, controller.werewolf.mining.addWeekAward)
    router.post(`${API_VERSION}/werewolf/mining/updateWeekAward`, controller.werewolf.mining.updateWeekAward)

    router.post(`${API_VERSION}/werewolf/mining/frameCrystalList`, controller.werewolf.mining.frameCrystalList)
    router.post(`${API_VERSION}/werewolf/mining/addFrameCrystal`, controller.werewolf.mining.addFrameCrystal)
    router.post(`${API_VERSION}/werewolf/mining/updateFrameCrystal`, controller.werewolf.mining.updateFrameCrystal)

    router.post(`${API_VERSION}/werewolf/mining/roleSparList`, controller.werewolf.mining.roleSparList)
    router.post(`${API_VERSION}/werewolf/mining/addRoleSpar`, controller.werewolf.mining.addRoleSpar)
    router.post(`${API_VERSION}/werewolf/mining/updateRoleSpar`, controller.werewolf.mining.updateRoleSpar)

    router.post(`${API_VERSION}/werewolf/mining/seasonList`, controller.werewolf.mining.seasonList)
    router.post(`${API_VERSION}/werewolf/mining/insertSeason`, controller.werewolf.mining.insertSeason)
    router.post(`${API_VERSION}/werewolf/mining/updateSeason`, controller.werewolf.mining.updateSeason)


    router.post(`${API_VERSION}/werewolf/mining/illustratedList`, controller.werewolf.mining.illustratedList)
    router.post(`${API_VERSION}/werewolf/mining/updateIllustration`, controller.werewolf.mining.updateIllustration)





}

export default load
