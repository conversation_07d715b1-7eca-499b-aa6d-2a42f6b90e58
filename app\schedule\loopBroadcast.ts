/*
 * @Description: 无
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhang<PERSON>
 * @Date: 2021-06-18 10:48:12
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-06-26 10:23:13
 */
import { Subscription, Context } from "egg";

module.exports = (app) => {
    
    return {
        schedule: {
            interval: app.config.broadcastInterval, // 10s
            type: 'worker', // 指定所有的 worker 都需要执行
        },

        async task(ctx: Context) {            
            await ctx.service.werewolf.broadcastSend.checkBroadcast();
        }
    }
};
