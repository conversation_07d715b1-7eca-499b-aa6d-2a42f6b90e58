import {
  ISeasonGuideContentResponse,
  ISeasonGuideRequest,
} from "../../model/miningSeason";
import {
  IRuleListReq,
  ISeasonPoolReq,
  IStoneListReq,
  IStoneRuleListReq,
} from "../../model/NewStoneManage";
import BaseMegaService from "./BaseMegaService";

export default class NewStoneManageService extends BaseMegaService {
  /*查询框石列表*/
  public async getStoneList() {
    const { app, logger } = this;

    try {
      const sqlStr = `SELECT \`id\`, \`name\`,\`level\`, \`season_id\`,\`pic\`, \`delsign\` FROM mining_frame WHERE delsign = 0 ORDER BY id DESC;`;
      return await this.selectList(sqlStr, []);
    } catch (error) {
      logger.error(error);
      throw error;
    }
  }
  /*新增框石列表*/
  public async createStoneList(req: IStoneListReq) {
    const { app, logger } = this;
    const db = app.mysql.get("werewolf");
    const conn = await db.beginTransaction();

    try {
      let sqlStr = `INSERT INTO \`mining_frame\` (\`name\`, \`season_id\`, \`level\`, \`delsign\`) VALUES (?,?,?,0);`;
      await conn.query(sqlStr, [req.name, req.season_id, req.level]);
      await conn.commit(); //提交事务
    } catch (error) {
      await conn.rollback();
      throw error;
    }
  }
  /*更新框石列表*/
  public async updateStoneList(req: IStoneListReq) {
    const { app, logger } = this;
    const db = app.mysql.get("werewolf");
    const conn = await db.beginTransaction();

    try {
      const sqlStr =
        "UPDATE `mining_frame` SET `level` = ?, `season_id` = ?,`name` = ?, `pic` = ? WHERE `id` = ?;";
      const result = await conn.query(sqlStr, [
        req.level,
        req.season_id,
        req.name,
        req.pic,
        req.id,
      ]);
      await conn.commit(); //提交事务
    } catch (error) {
      await conn.rollback();
      logger.error(error);
      throw error;
    }
  }
  /*查询赛季奖池列表*/
  public async getSeasonPoolList(req: { frame_id: number }) {
    const { app, logger } = this;

    try {
      const sqlStr = `SELECT b.id, b.frame_id, b.card_level, b.weight, a.season_id FROM mining_frame AS a LEFT JOIN mining_frame_level_drop_weight AS b ON b.frame_id = a.id WHERE frame_id = ? ORDER BY id DESC;`;
      return await this.selectList(sqlStr, [req.frame_id]);
    } catch (error) {
      logger.error(error);
      throw error;
    }
  }
  /*新增赛季奖池列表*/
  public async createSeasonPoolList(req: ISeasonPoolReq) {
    const { app, logger } = this;
    const db = app.mysql.get("werewolf");
    const conn = await db.beginTransaction();

    try {
      let sqlStr = `INSERT INTO \`mining_frame_level_drop_weight\` (\`frame_id\`,\`card_level\`,\`weight\`) VALUES (?,?,?);`;
      await conn.query(sqlStr, [req.frame_id, req.card_level, req.weight]);
      await conn.commit(); //提交事务
    } catch (error) {
      await conn.rollback();
      throw error;
    }
  }
  /*更新赛季奖池列表*/
  public async updateSeasonPoolList(req: ISeasonPoolReq) {
    const { app, logger } = this;
    const db = app.mysql.get("werewolf");
    const conn = await db.beginTransaction();

    try {
      const sqlStr =
        "UPDATE `mining_frame_level_drop_weight` SET `card_level` = ?, `weight` = ? WHERE `id` = ?;";
      const result = await conn.query(sqlStr, [
        req.card_level,
        req.weight,
        req.id,
      ]);
      await conn.commit(); //提交事务
    } catch (error) {
      logger.error(error);
      await conn.rollback();
      throw error;
    }
  }
  /*查询规则列表*/
  public async getRuleList() {
    const { app, logger } = this;

    try {
      const sqlStr = `SELECT \`id\`, \`desc\`, \`rate\`, \`type\` FROM mining_frame_ruler;`;
      return await this.selectList(sqlStr, []);
    } catch (error) {
      logger.error(error);
      throw error;
    }
  }
  /*新增规则列表*/
  public async createRuleList(req: IRuleListReq) {
    const { app, logger } = this;
    const db = app.mysql.get("werewolf");
    const conn = await db.beginTransaction();

    try {
      let sqlStr = `INSERT INTO \`mining_frame_ruler\` (\`desc\`,\`rate\`,\`type\`) VALUES (?,?);`;
      await conn.query(sqlStr, [req.desc, req.rate,req.type]);
      await conn.commit(); //提交事务
    } catch (error) {
      await conn.rollback();
      throw error;
    }
  }
  /*更新规则列表*/
  public async updateRuleList(req: IRuleListReq) {
    const { app, logger } = this;
    const db = app.mysql.get("werewolf");
    const conn = await db.beginTransaction();

    try {
      const sqlStr =
        "UPDATE `mining_frame_ruler` SET `desc` = ?, `rate` = ?, `type` = ? WHERE `id` = ?;";
      const result = await conn.query(sqlStr, [req.desc, req.rate,req.type, req.id]);
      await conn.commit(); //提交事务
    } catch (error) {
      logger.error(error);
      await conn.rollback();
      throw error;
    }
  }
  /*查询框石规则列表*/
  public async getStoneRuleList(req: { season_id: number }) {
    const { app, logger } = this;

    try {
      const sqlStr = `SELECT b.id, c.id AS rule_id, a.name AS stone_name, b.frame_id, c.desc, c.rate, b.delsign FROM mining_frame AS a INNER JOIN mining_frame_ruler_relation AS b ON b.frame_id = a.id INNER JOIN mining_frame_ruler AS c ON b.ruler_id = c.id WHERE a.season_id = ?;`;
      return await this.selectList(sqlStr, [req.season_id]);
    } catch (error) {
      logger.error(error);
      throw error;
    }
  }
  /*新增框石规则列表*/
  public async createStoneRuleList(req: IStoneRuleListReq) {
    const { app, logger } = this;
    const db = app.mysql.get("werewolf");
    const conn = await db.beginTransaction();

    try {
      let sqlStr = `INSERT INTO \`mining_frame_ruler_relation\` (\`frame_id\`,\`ruler_id\`) VALUES (?,?);`;
      await conn.query(sqlStr, [req.frame_id, req.rule_id]);
      await conn.commit(); //提交事务
    } catch (error) {
      await conn.rollback();
      throw error;
    }
  }
  /*更新框石规则列表*/
  public async updateStoneRuleList(req: IStoneRuleListReq) {
    const { app, logger } = this;
    const db = app.mysql.get("werewolf");
    const conn = await db.beginTransaction();

    try {
      const sqlStr =
        "UPDATE `mining_frame_ruler_relation` SET `frame_id` = ?, `ruler_id` = ?, `delsign` = ? WHERE `id` = ?;";
      const result = await conn.query(sqlStr, [
        req.frame_id,
        req.rule_id,
        req.delsign,
        req.id,
      ]);
      await conn.commit(); //提交事务
    } catch (error) {
      logger.error(error);
      await conn.rollback();
      throw error;
    }
  }
}
