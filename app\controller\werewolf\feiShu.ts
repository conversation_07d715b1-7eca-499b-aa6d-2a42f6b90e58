/*
 * @Description: 飞书机器人消息发送控制器
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: AI Assistant
 * @Date: 2023-11-22 10:00:00
 * @LastEditors: AI Assistant
 * @LastEditTime: 2023-11-22 11:00:00
 */

import BaseMegaController from './BaseMegaController';

/**
 * 发送消息请求接口
 */
interface ISendMessageReq {
    title: string;
    content: string;
}

/**
 * 发送消息响应接口
 */
interface ISendMessageRes {
    success: boolean;
    message: string;
}

export default class FeiShuController extends BaseMegaController {

    /**
     * 发送飞书机器人消息
     * POST /api/werewolf/feiShu/sendMessage
     */
    public async sendMessage() {
        const { ctx, logger } = this;
        const rule = {
            title: { type: 'string' },
            content: { type: 'string' }
        };

        try {
            ctx.validate(rule);
            const request: ISendMessageReq = ctx.request.body;
            
            logger.info(`FeiShuController:sendMessage - 开始发送飞书消息, title: ${request.title}`);
            
            const result: ISendMessageRes = await ctx.service.werewolf.feiShu.sendMessage(
                request.title, 
                request.content
            );
            
            if (result.success) {
                logger.info(`FeiShuController:sendMessage - 消息发送成功: ${result.message}`);
                this.respSuccData(result);
            } else {
                logger.error(`FeiShuController:sendMessage - 消息发送失败: ${result.message}`);
                this.respFail(result.message);
            }
        } catch (error) {
            logger.error(`FeiShuController:sendMessage - 异常: ${error.message}`);
            this.respFail(error.message);
        }
    }

    /**
     * 测试飞书机器人连接
     * GET /api/werewolf/feiShu/test
     */
    public async test() {
        const { ctx, logger } = this;
        
        try {
            logger.info('FeiShuController:test - 开始测试飞书机器人连接');
            
            const result: ISendMessageRes = await ctx.service.werewolf.feiShu.sendMessage(
                '测试消息', 
                '这是一条来自系统的测试消息，用于验证飞书机器人是否正常工作。'
            );
            
            if (result.success) {
                logger.info(`FeiShuController:test - 测试成功: ${result.message}`);
                this.respSuccData({
                    success: true,
                    message: '飞书机器人连接测试成功',
                    result
                });
            } else {
                logger.error(`FeiShuController:test - 测试失败: ${result.message}`);
                this.respFail(`飞书机器人连接测试失败: ${result.message}`);
            }
        } catch (error) {
            logger.error(`FeiShuController:test - 测试异常: ${error.message}`);
            this.respFail(`飞书机器人连接测试异常: ${error.message}`);
        }
    }

    /**
     * 获取飞书机器人配置状态
     * GET /api/werewolf/feiShu/status
     */
    public async status() {
        const { ctx, logger } = this;
        
        try {
            logger.info('FeiShuController:status - 检查飞书机器人配置状态');
            
            const webhook = ctx.app.config.WerewolfFeiShuBot;
            const isConfigured = !!webhook;
            
            this.respSuccData({
                configured: isConfigured,
                webhook: isConfigured ? '已配置' : '未配置',
                message: isConfigured ? '飞书机器人已正确配置' : '飞书机器人未配置或配置错误'
            });
        } catch (error) {
            logger.error(`FeiShuController:status - 检查状态异常: ${error.message}`);
            this.respFail(`检查飞书机器人状态异常: ${error.message}`);
        }
    }

    /**
     * 发送系统通知消息
     * POST /api/werewolf/feiShu/notify
     */
    public async notify() {
        const { ctx, logger } = this;
        const rule = {
            type: { type: 'string' },
            message: { type: 'string' },
            details: { type: 'string', required: false }
        };

        try {
            ctx.validate(rule);
            const request = ctx.request.body;
            
            logger.info(`FeiShuController:notify - 发送系统通知, type: ${request.type}`);
            
            // 构造通知标题
            const title = `系统通知 - ${request.type}`;
            
            // 构造通知内容
            let content = `**通知类型**: ${request.type}\n\n`;
            content += `**消息内容**: ${request.message}\n\n`;
            if (request.details) {
                content += `**详细信息**: ${request.details}\n\n`;
            }
            content += `**发送时间**: ${new Date().toLocaleString('zh-CN')}`;
            
            const result: ISendMessageRes = await ctx.service.werewolf.feiShu.sendMessage(title, content);
            
            if (result.success) {
                logger.info(`FeiShuController:notify - 系统通知发送成功: ${result.message}`);
                this.respSuccData(result);
            } else {
                logger.error(`FeiShuController:notify - 系统通知发送失败: ${result.message}`);
                this.respFail(result.message);
            }
        } catch (error) {
            logger.error(`FeiShuController:notify - 系统通知发送异常: ${error.message}`);
            this.respFail(error.message);
        }
    }
}
