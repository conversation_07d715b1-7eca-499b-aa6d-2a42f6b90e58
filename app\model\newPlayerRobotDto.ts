export interface InewPlayerRobotInsertParams {
    admin_id: number
    isFirst: number
    msg_type: number
    sum: string
    desc: string
    tend_type: number
    tend_page: number
    acTendUrl: string
    send_time: string
    allTagDisabled: boolean
    type: number
    msg_user: string
    domainName: string
    residueTime: number
    tendShop: string
    tab1: string
    tab2: string
}

export interface InewPlayerRobotSearchParams {
    sum: string
    isUserId: string
}

export interface InewPlayerRobotUpdateParams extends InewPlayerRobotInsertParams{
    id: number
}

export interface InewPlayerRobotUpdateTypeParams{
    id: number
    admin_id: number

    type: number
}