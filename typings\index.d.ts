declare module "egg" {
    interface Application {
        mysql: MySqlClientSet;
        redis: any;
        clickhouse: any;
    }
}

export type QueryFunction = (sql: any, values?: any) => Promise</*OkPacket | any[] |*/ any>
export type MySqlClientName = 'werewolf' | 'werewolf_slave' | 'manager' | 'scriptkill' | 'aiim' | 'chatgpt'


export interface MySqlClientSet {
    get: (name: MySqlClientName) => MySqlClient,
}

export interface MySqlClient {

    query: QueryFunction,
    beginTransaction: () => Promise<MySqlClientTransaction>,

    [key: string]: any,
}

export interface MySqlClientTransaction {
    query: QueryFunction,
    commit: () => Promise,
    rollback: () => Promise,

    [key: string]: any,
}

// Result from an insert, update, or delete statement.
export interface OkPacket {
    fieldCount: number;
    /**
     * The number of affected rows from an insert, update, or delete statement.
     */
    affectedRows: number;
    /**
     * The insert id after inserting a row into a table with an auto increment primary key.
     */
    insertId: number;
    serverStatus?: number | undefined;
    warningCount?: number | undefined;
    /**
     * The server result message from an insert, update, or delete statement.
     */
    message: string;
    /**
     * The number of changed rows from an update statement. "changedRows" differs from "affectedRows" in that it does not count updated rows whose values were not changed.
     */
    changedRows: number;
    protocol41: boolean;

}
