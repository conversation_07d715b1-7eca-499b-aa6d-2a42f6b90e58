/*
 * @Description: 团队活动
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: musong
 */

import BaseMegaService from './BaseMegaService';
import { IactivityIndex, IactivityTeamRankConfig } from '../../model/wfActivityAward';

export default class TeamActivityService extends BaseMegaService {

   // 创建团队活动
   public async teamActivityCreate(req: any) {
      const { logger } = this;
      try {
        const pad = n => (n < 10 ? '0' + n : n);
        const values = [];

        const [year, month, day] = req.startTime.split(' ')[0].split('-').map(Number);
        const date = new Date(year, month - 1, day);

        const endDate = new Date(date);
        endDate.setDate(date.getDate() + 3 - 1);
        const formatDateTime = d => `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())}`;
        const fullTime = d => `${formatDateTime(d)} 00:00:00`;
        const endTime = d => `${formatDateTime(d)} 23:59:59`;
         const insertActivityIndexSql = `INSERT INTO activity_index (id, name, start_time, end_time, end_data_time, is_show, aty_type) VALUES (?, ?, ?, ?, ?, ?, ?)`;
        await this.execSql(insertActivityIndexSql, [
            req.id,
            req.name,
            new Date(fullTime(date)),
            new Date(endTime(endDate)),
            new Date(endTime(endDate)),
            1,
            1
         ])

         

        // 总榜
        values.push(`(0, 1, NULL, '${fullTime(date)}', '${endTime(endDate)}', 0, ${req.id}, '${req.name}总榜')`);
        // 日榜
        for (let i = 0; i < 3; i++) {
            const d = new Date(date);
            d.setDate(d.getDate() + i);
            const can_click = i === 0 ? 1 : 0;
            const rank_remark = `${req.name}日榜第${i + 1}天`;
            values.push(`( 1, ${can_click}, '${formatDateTime(d)}', '${fullTime(d)}', '${endTime(d)}', 0, ${req.id}, '${rank_remark}')`);
        }
        const insertRankConfigSql = `INSERT INTO \`activity_periodic_team_rank_config\`( \`type\`, \`can_click\`, \`date\`, \`start_time\`, \`end_time\`, \`state\`, \`activity_id\`, \`remark\`) VALUES ${values.join(',\n')};`;
        await this.execSql(insertRankConfigSql, []);

        const insertTeamRankConfigSql = `INSERT INTO team_rank_activity_config (name, table_name, begin_time, rank_num, activity_id) VALUES (?, ?, ?, ?, ?)`;
         await this.execSql(insertTeamRankConfigSql, [
            req.name,
            "activity_periodic_team_rank_record",
            fullTime(date),
            10,
            req.id,
         ])


         const insertCommAwardGroupSql = `INSERT INTO activity_comm_award_group (activity_id, group_name, group_index, edit_enable) SELECT ${req.id} as activity_id, group_name, group_index, edit_enable FROM activity_comm_award_group WHERE activity_id = 235;`;
         await this.execSql(insertCommAwardGroupSql, []);


         const insertCommAwardConfigSql = `
                INSERT INTO activity_comm_award_conf (
                activity_id, award_id, weight, target_num, props_no, props_type, is_limit, num,
                award_name, max_times, \`desc\`, is_optional, del_sign, item_dic_id,
                headge_num, guarante_num, award_group_id, sub_item_dic_id_list
                )
                SELECT
                ${req.id} AS activity_id,
                ac.award_id,
                ac.weight,
                ac.target_num,
                ac.props_no,
                ac.props_type,
                ac.is_limit,
                ac.num,
                ac.award_name,
                ac.max_times,
                ac.\`desc\`,
                ac.is_optional,
                ac.del_sign,
                ac.item_dic_id,
                ac.headge_num,
                ac.guarante_num,
                ag.id AS award_group_id,
                ac.sub_item_dic_id_list
                FROM activity_comm_award_conf ac
                LEFT JOIN activity_comm_award_group ag
                ON ac.award_id >= ag.group_index AND ac.award_id < ag.group_index + 100 AND ag.activity_id = ${req.id}
                WHERE ac.activity_id = 235;
                `;
         const ret: any = await this.execSql(insertCommAwardConfigSql, []);



         return ret;
      } catch (err) {
         logger.error(err);
         throw err;
      }
   }

   //2 查询指定活动的组信息
   public async teamActivityList(): Promise<IactivityIndex[]> {
      const { logger } = this;
      try {
         const sqlStr = ` SELECT * FROM activity_index WHERE aty_type = 1 ORDER BY id DESC `;
         const ret: IactivityIndex[] = await this.selectList(sqlStr, [])
         return ret;
      } catch (err) {
         logger.error(err);
         throw err;
      }
   }

   public async teamActivityDetail(req: any): Promise<IactivityTeamRankConfig[]> {
    const { logger } = this;
    try {
       const sqlStr = ` SELECT * FROM activity_periodic_team_rank_config WHERE activity_id = ${req.activityId} ORDER BY id ASC `;
       const ret: IactivityTeamRankConfig[] = await this.selectList(sqlStr, [])
       return ret;
    } catch (err) {
       logger.error(err);
       throw err;
    }
 }


    public async teamActivityUpdate(req: any) {
        const { logger } = this;
        try {
            const pad = n => (n < 10 ? '0' + n : n);
            const values = [];
    
            const [year, month, day] = req.startTime.split(' ')[0].split('-').map(Number);
            const date = new Date(year, month - 1, day);
    
            const endDate = new Date(date);
            endDate.setDate(date.getDate() + 3 - 1);
            const formatDateTime = d => `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())}`;
            const fullTime = d => `${formatDateTime(d)} 00:00:00`;
            const endTime = d => `${formatDateTime(d)} 23:59:59`;

            const updateActivityIndexSql = `UPDATE activity_index SET start_time = ?, end_time = ?, end_data_time = ? WHERE id = ?`;
            await this.execSql(updateActivityIndexSql, [
                new Date(fullTime(date)),
                new Date(endTime(endDate)),
                new Date(endTime(endDate)),
                req.id
             ])

            // activity_periodic_team_rank_config表删除后重建
            const deleteRankConfigSql = `DELETE FROM activity_periodic_team_rank_config WHERE activity_id =?`;
            await this.execSql(deleteRankConfigSql, [
                req.id
             ])
            // 总榜
            values.push(`(0, 1, NULL, '${fullTime(date)}', '${endTime(endDate)}', 0, ${req.id}, '${req.name}总榜')`);
            // 日榜
            for (let i = 0; i < 3; i++) {
                const d = new Date(date);
                d.setDate(d.getDate() + i);
                const can_click = i === 0 ? 1 : 0;
                const rank_remark = `${req.name}日榜第${i + 1}天`;
                values.push(`( 1, ${can_click}, '${formatDateTime(d)}', '${fullTime(d)}', '${endTime(d)}', 0, ${req.id}, '${rank_remark}')`);
            }
            const insertRankConfigSql = `INSERT INTO \`activity_periodic_team_rank_config\`( \`type\`, \`can_click\`, \`date\`, \`start_time\`, \`end_time\`, \`state\`, \`activity_id\`, \`remark\`) VALUES ${values.join(',\n')};`;
            await this.execSql(insertRankConfigSql, []);
            

            const updateTeamRankConfigSql = `UPDATE team_rank_activity_config SET begin_time = ?  WHERE activity_id = ?`;
            await this.execSql(updateTeamRankConfigSql, [
                fullTime(date),
                req.id
             ])
            
        } catch (err) {
           logger.error(err);
           throw err;
        }
 }


}