/*
 * @Description: 运营活动配置中心
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-03-24 11:50:11
 * @LastEditors: hammercui
 * @LastEditTime: 2020-11-24 09:58:57
 */
import { Router, Application } from "egg";
import { AccessRouteId } from './model/accessRouteCof';
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

    const { controller, router } = app;
    //【应用管理】首页弹窗
    //新建
    router.post(`${API_VERSION}/werewolf/webAtyConf/createAty`, accCtr(AccessRouteId.wolf_aty_award), controller.werewolf.webAtyConf.createAty)
    //设置自由配置
    router.post(`${API_VERSION}/werewolf/webAtyConf/setAutoConf`, accCtr(AccessRouteId.wolf_aty_conf), controller.werewolf.webAtyConf.setAutoConf)
    //某活动全部配置
    router.post(`${API_VERSION}/werewolf/webAtyConf/allAutoConf`, accCtr(AccessRouteId.wolf_aty_conf), controller.werewolf.webAtyConf.getAllAutoConf)

    //任务管理
    // wf_aty_task
    //查询活动任务配置
    router.post(`${API_VERSION}/werewolf/webAtyConf/getAtyTaskConf`, accCtr(AccessRouteId.wolf_aty_task), controller.werewolf.webAtyConf.getAtyTaskConf)
    //设置活动任务配置
    router.post(`${API_VERSION}/werewolf/webAtyConf/setAtyTaskConf`, accCtr(AccessRouteId.wolf_aty_task), controller.werewolf.webAtyConf.setAtyTaskConf)

    //获得活动刷新任务配置
    router.post(`${API_VERSION}/werewolf/webAtyConf/getAtyTaskRefreshConf`, accCtr(AccessRouteId.wolf_aty_task), controller.werewolf.webAtyConf.getAtyTaskRefreshConf)
    //设置活动刷新任务配置
    router.post(`${API_VERSION}/werewolf/webAtyConf/setAtyTaskRefreshConf`, accCtr(AccessRouteId.wolf_aty_task), controller.werewolf.webAtyConf.setAtyTaskRefreshConf)

    //查询任务列表
    router.post(`${API_VERSION}/werewolf/webAtyConf/taskList`, accCtr(AccessRouteId.wolf_aty_task), controller.werewolf.webAtyConf.getTaskList)

    //才艺赛活动配置
    router.post(`${API_VERSION}/werewolf/activityTalent/stateInfo`, accCtr(AccessRouteId.wolf_aty_task), controller.werewolf.activityTalent.stateInfo)
    router.post(`${API_VERSION}/werewolf/activityTalent/stateUpdate`, accCtr(AccessRouteId.wolf_aty_task), controller.werewolf.activityTalent.stateUpdate)
    router.post(`${API_VERSION}/werewolf/activityTalent/playerList`, accCtr(AccessRouteId.wolf_aty_task), controller.werewolf.activityTalent.playerList)
    router.post(`${API_VERSION}/werewolf/activityTalent/playerUpadte`, accCtr(AccessRouteId.wolf_aty_task), controller.werewolf.activityTalent.playerUpadte)

    //活动exchange失败处理
    router.post(`${API_VERSION}/werewolf/exchangeFail/list`, controller.werewolf.exchangeFailHandle.selectFailList)
    router.post(`${API_VERSION}/werewolf/exchangeFail/reSend`, controller.werewolf.exchangeFailHandle.reSend)

    //
}

export default load
