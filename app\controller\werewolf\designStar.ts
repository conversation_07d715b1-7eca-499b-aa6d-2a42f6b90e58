/**
 * 
 */

import { IdesignStarItem,IdesignStarUpdateStatus,IdesignStarUpdateNum, IfliteListReq } from "../../model/wfDesignStar";
import BaseMegaController from "./BaseMegaController";

export default class DesignStarController extends BaseMegaController {
   
    /**
     * 请求列表
     */
    public async list() {
        const { ctx, logger } = this;
        try {
            const retList: IdesignStarItem[] = await ctx.service.werewolf.designStar.list();
            this.respSuccData(retList)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async fliteList() {
        const { ctx, logger } = this;
        try {
            const req: IfliteListReq = await ctx.request.body;
            const retList: IdesignStarItem[] = await ctx.service.werewolf.designStar.fliteList(req);
            this.respSuccData(retList)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async updateStatus() {
        const { ctx, logger } = this;
        try {
            const req: IdesignStarUpdateStatus = await ctx.request.body;
            await ctx.service.werewolf.designStar.updateStatus(req);
            this.respSucc()
        } catch (err) {
            this.respFail(err)
        }
    }

    public async updateNum() {
        const { ctx, logger } = this;
        try {
            const req: IdesignStarUpdateNum = await ctx.request.body;
            await ctx.service.werewolf.designStar.updateNum(req);
            this.respSucc()
        } catch (err) {
            this.respFail(err)
        }
    }

    public async updateImg() {
        const { ctx, logger } = this;
        try {
            const req = await ctx.request.body;
            await ctx.service.werewolf.designStar.updateImg(req);
            this.respSucc()
        } catch (err) {
            this.respFail(err)
        }
    }

}