import {
  ISeasonGuideContentResponse,
  ISeasonGuideRequest,
} from "../../model/miningSeason";
import BaseMegaService from "./BaseMegaService";

export default class MiningSeasonService extends BaseMegaService {
  /*查询图鉴赛季列表*/
  public async getMiningSeasonList() {
    const { app, logger } = this;

    try {
      const sqlStr = `SELECT \`id\`, \`name\`,\`start_time\`, \`end_time\`, \`delsign\` FROM mining_season WHERE delsign = 0 ORDER BY id DESC;`;
      return await this.selectList(sqlStr, []);
    } catch (error) {
      logger.error(error);
      throw error;
    }
  }
  /*新增图鉴赛季列表*/
  public async createMiningSeasonList(req: ISeasonGuideRequest) {
    const { app, logger } = this;
    const db = app.mysql.get("werewolf");
    const conn = await db.beginTransaction();
    try {
      let sql = `INSERT INTO \`mining_season\` (\`name\`,\`start_time\`, \`end_time\`) VALUES (?,?,?);`;
      const result = await conn.query(sql, [
        req.name,
        req.start_time,
        req.end_time,
      ]);
    
      // 获取插入后生成的 ID
      let newId;
      if (result && result.insertId) {
        newId = result.insertId;
      } else {
        throw new Error("Failed to get the inserted ID.");
      }
      await this.createIllustratedGuideTable(conn, newId);
      await conn.commit(); //提交事务
    } catch (error) {
      await conn.rollback();
      throw error;
    }
  }

  async createIllustratedGuideTable(ctx, id) {
    const tableName = `mining_illustrated_guide_${id}`;
  
      // SQL 创建表语句
      const sql = `
    CREATE TABLE  if not exists ${tableName} (
      id int(11) NOT NULL AUTO_INCREMENT,
      s_no varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '序号',
      level int(11) DEFAULT NULL COMMENT '稀有度:6:SS 5:S 4:A 3:B 2:C 1:D',
      weight int(11) DEFAULT NULL COMMENT '框石开权重',
      name varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '名称',
      \`desc\` text COLLATE utf8mb4_unicode_ci COMMENT '描述',
      img varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      detail_img varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '详细图片',
      delsign int(11) DEFAULT NULL COMMENT '删除标记',
      PRIMARY KEY (id),
      KEY level_idx (level) USING BTREE
    ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
  `;

      await ctx.query(sql); // 使用 query 方法执行 SQL 语句
    
  }
  /*更新图鉴赛季列表*/
  public async updateMiningSeasonList(req: ISeasonGuideRequest) {
    const { app, logger } = this;
    const db = app.mysql.get("werewolf");
    const conn = await db.beginTransaction();
    try {
      let sql =
        "UPDATE `mining_season` SET `name` = ?, `start_time` = ?, `end_time` = ? WHERE `id` = ?;";
      const result = await conn.query(sql, [
        req.name,
        req.start_time,
        req.end_time,
        req.id,
      ]);
      await conn.commit(); //提交事务
    } catch (error) {
      await conn.rollback();
      throw error;
    }
  }
  /*查询赛季内容列表*/
  public async getMiningIllustratedList(req: { id: number }) {
    const { app, logger } = this;
    let sqlStr = ''
    try {

        if(req.id>5){
            sqlStr = `SELECT \`id\`,\`s_no\`, \`level\`,\`name\`,\`weight\`, \`desc\`, \`img\`,\`detail_img\`, \`delsign\` FROM mining_illustrated_guide_${req.id} WHERE delsign = 0 ORDER BY s_no;`;
        }else if(req.id == 0){
          sqlStr = `SELECT \`id\`,\`s_no\`, \`level\`,\`name\`, \`desc\`, \`img\`, \`delsign\` FROM mining_illustrated_guide WHERE delsign = 0 ORDER BY id DESC;`;
        }else{
            sqlStr = `SELECT \`id\`,\`s_no\`, \`level\`,\`name\`, \`desc\`, \`img\`, \`delsign\` FROM mining_illustrated_guide_${req.id} WHERE delsign = 0 ORDER BY id DESC;`;
        }
      return await this.selectList(sqlStr, []);
    } catch (error) {
      logger.error(error);
      throw error;
    }
  }

  /*新增赛季内容列表*/
  public async createMiningIllustratedList(req: ISeasonGuideContentResponse) {
    const { app, logger } = this;
    const db = app.mysql.get("werewolf");
    const conn = await db.beginTransaction();
    try {
      let sql = `INSERT INTO \`mining_illustrated_guide_${req.seasonNum}\` (\`s_no\`,\`level\`, \`name\`, \`desc\`, \`delsign\`) VALUES (?,?,?,?,0);`;
      const result = await conn.query(sql, [req.s_no,req.level, req.name, req.desc]);
      await conn.commit(); //提交事务
    } catch (error) {
      await conn.rollback();
      throw error;
    }
  }
  /*修改赛季内容列表*/
  public async updateMiningIllustratedList(req: ISeasonGuideContentResponse) {
    const { app, logger } = this;
    const db = app.mysql.get("werewolf");
    const conn = await db.beginTransaction();
    try {
      let sql =
        "UPDATE `mining_illustrated_guide_"+ req.seasonNum + "` SET `s_no` = ?,`level` = ?,`weight` = ?, `name` = ?, `desc` = ?, `img` = ?,`detail_img` = ? WHERE `id` = ?;";
      const result = await conn.query(sql, [
        req.s_no,
        req.level,
        req.weight,
        req.name,
        req.desc,
        req.img,
        req.detail_img,
        req.id,

      ]);
      await conn.commit(); //提交事务
    } catch (error) {
      await conn.rollback();
      throw error;
    }
  }
}
