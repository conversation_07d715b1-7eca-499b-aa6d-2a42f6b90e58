/*
 * @Description: 下载模块-服务类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2019-06-19 13:58:30
 * @LastEditors: hammercui
 * @LastEditTime: 2019-06-20 11:49:46
 */

import { Service } from 'egg';
import * as fs from "fs";
import * as path from "path";
import { stringify } from 'querystring';

export default class DownloadService extends Service {
    //下载oss图片
    async ossPng(ossPath: string): Promise<boolean> {
        const { ctx, app, logger } = this;
        try {
            //方案1
            // const dirArray = ossPath.split('/');
            // let saveDir  = process.cwd() + "/headicon/"+dirArray[1];
            // let savePath = process.cwd() + "/"+ossPath;
            // let isExists = await this.getStat(savePath);
            // //存在，并且是文件
            // if (isExists && isExists.isFile()) {
            //     return true;
            // }else{
            //     isExists = await this.getStat(saveDir);
            //     if(!isExists){
            //         //新建目录
            //         await this.mkdir(saveDir);
            //     }
            // }
        
            // let result = await ctx.oss.getStream(ossPath);
            // let writeStream = fs.createWriteStream(savePath);
            // result.stream.pipe(writeStream);
            //方案2
            const result = await ctx.curl(app.config.headIconDownUrl,{
                method:'POST',
                data:{img:ossPath},
                contentType:'json',
                dataType: 'json',
                timeout: 3000
            });
            const { status, headers, data } = result
            if(status == 200){
                logger.debug("下载图片成功",data);
            }else{
                logger.error("下载图片失败",status,data);
            }
            return true;
        } catch (e) {
            logger.error("下载oss图片失败", e);
            return false;
        }
    }

    /**
 * 读取路径信息
 * @param {string} path 路径
 */
    getStat(path): Promise<fs.Stats> {
        const { ctx, app, logger } = this;
        return new Promise((resolve, reject) => {
            fs.stat(path, (err, stats) => {
                if (err) {
                    // logger.error("getStat err:",err);
                    // logger.error("getStat err,path:",path);
                    resolve(undefined);
                } else {
                    resolve(stats);
                }
            })
        })

    }

    /**
 * 创建路径
 * @param {string} dir 路径
 */
    mkdir(dir) {
        const { ctx, app, logger } = this;
        return new Promise((resolve, reject) => {
            fs.mkdir(dir, (err) => {
                if (err) {
                    // logger.error("mkdir err:",err);
                    // logger.error("mkdir err dir:",err,dir);
                    resolve(false);
                } else {
                    resolve(true);
                }
            })
        })
    }
}
