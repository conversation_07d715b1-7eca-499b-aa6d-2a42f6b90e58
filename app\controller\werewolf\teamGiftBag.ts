
import BaseMegaController from './BaseMegaController';
import { IerrorMsg, HttpErr } from '../../model/common';

export default class TeamGiftBagController extends BaseMegaController {

    public async getTeamGiftBagList(){
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.teamGiftBag.getTeamGiftBagList(requestBody);
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
            logger.error(err)
        }
    }

    public async insertTeamGiftBag() {
        const { ctx, logger } = this;
        try {

            const rule = {
                name: { type: "string" },
                remark: { type: "string" },
                max_num: { type: "number" },
                coin_num: { type: "number" },
                add_exp: {type: "number"},
                sort: {type: "number"},
                delsign: {type:"number"}
            };

            ctx.validate(rule)
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.teamGiftBag.insertTeamGiftBag(requestBody);
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
            logger.error(err)
        }
    }

    public async updateTeamGiftBag() {
        const { ctx, logger } = this;
        try {

            const rule = {
                id: {type: "number"},
                name: {type: "string"},
                remark: {type: "string"},
                max_num: {type: "number"},
                coin_num: {type: "number"},
                add_exp: {type: "number"},
                sort: {type: "number"},
                delsign: {type: "number"}
            };

            ctx.validate(rule)
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.teamGiftBag.updateTeamGiftBag(requestBody);
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
            logger.error(err)
        }
    }

    public async getTeamGiftBagItemList(){
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.teamGiftBag.getTeamGiftBagItemList(requestBody);
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
            logger.error(err)
        }
    }

    public async insertTeamGiftBagItem() {
        const { ctx, logger } = this;
        try {

            const rule = {
                gift_bag_id: { type: "number" },
                item_dic_id: { type: "number" },
                name: { type: "string" },
                num: { type: "number" },
                weight: { type: "number" },
                percent: {type: "string"},
                sort: {type: "number"},
                delsign: {type:"number"}
            };

            ctx.validate(rule)
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.teamGiftBag.insertTeamGiftBagItem(requestBody);
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
            logger.error(err)
        }
    }

    public async updateTeamGiftBagItem() {
        const { ctx, logger } = this;
        try {
            const rule = {
                id: {type: "number"},
                gift_bag_id: {type: "number"},
                item_dic_id: {type: "number"},
                name: {type: "string"},
                num: {type: "number"},
                weight: {type: "number"},
                percent: {type: "string"},
                sort: {type: "number"},
                delsign: {type: "number"}
            };

            ctx.validate(rule)
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.teamGiftBag.updateTeamGiftBagItem(requestBody);
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
            logger.error(err)
        }
    }

    public async getItemDicList(){
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.teamGiftBag.getItemDicList();
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
            logger.error(err)
        }
    }

}