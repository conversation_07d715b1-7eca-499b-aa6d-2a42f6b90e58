/*
 * @Description: 新建推送
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-03-16 11:56:23
 * @LastEditors: zhang<PERSON>
 * @LastEditTime: 2020-12-09 14:01:48
 */
import { Service } from 'egg';
import { PushMessageResquest } from '../../model/werewolf';
import { Ipayload } from '../../model/common';
import { IpushRecord, PushReceiveType, IpushShortInfo, PushState, IpushPhpReq, Iresp, IdeviceToken } from '../../model/werewolfPush';
import BaseMegaService from './BaseMegaService';
const qs = require("qs");

export default class PushCreateService extends BaseMegaService {

    private pushHashKey = "console_push_list"; //推送消息hashtable key

    //新建推送
    public async create(pushRecord: IpushRecord) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
            INSERT INTO push_record (admin_id,title,content,receive_type,push_time,receive)
            VALUES (?,?,?,?,?,?);
            `;
            const result = await this.execSql(sqlStr, [pushRecord.admin_id, pushRecord.title,
            pushRecord.content, pushRecord.receive_type, pushRecord.push_time, pushRecord.receive]);
            let lastId = result.insertId
            logger.info("插入pushId", result.insertId);
            // await this.setRedisRecord(lastId, new Date(pushRecord.push_time).getTime() / 1000) //精确到秒
            await this.setRedisRecord(lastId, this.getCurTimeStamp(pushRecord.push_time)) //精确到秒

        } catch (error) { throw error }
    }

    //推送列表
    public async list() {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `SELECT * FROM push_record WHERE del_sign = 0 ORDER BY id DESC;`;
            const result = await this.selectList(sqlStr);
            return result;
        } catch (error) { throw error }
    }

    // redis新增推送
    public async setRedisRecord(pushId: number, timestamp: number) {
        const { app, ctx, logger } = this;
        try {
            //写入redis
            let pushShortInfo: IpushShortInfo = { state: PushState.Ready, timestamp: timestamp };
            await app.redis.get("tokenRedis").hset(this.pushHashKey, pushId, JSON.stringify(pushShortInfo));
        } catch (error) { throw error }
    }

    //编辑推送消息
    public async edit(newPushRecord: IpushRecord) {
        const { app, ctx, logger } = this;
        try {
            logger.info("待编辑内容", newPushRecord);
            const redisResult = await app.redis.get("tokenRedis").hget(this.pushHashKey, newPushRecord.id);
            if (!redisResult) {
                throw new Error("推送记录不存在，请联系管理员");
            }
            const shortInfo: IpushShortInfo = JSON.parse(redisResult);
            if (shortInfo.state != PushState.Ready) {
                throw new Error("改推送进行中，不能进行编辑");
            }

            let sqlStr = `UPDATE push_record SET 
            title = '${newPushRecord.title}',
            content= '${newPushRecord.content}', 
            push_time = '${newPushRecord.push_time}',
            receive= '${newPushRecord.receive}' 
            WHERE id = ${ newPushRecord.id};`;
            logger.info(sqlStr);
            await this.execSql(sqlStr, []);

            //更新redis记录时间
            //await this.setRedisRecord(newPushRecord.id, new Date(newPushRecord.push_time).getTime() / 1000) //精确到秒
            await this.setRedisRecord(newPushRecord.id, this.getCurTimeStamp(newPushRecord.push_time)) //精确到秒

        } catch (error) { throw error }
    }

    //获得当前秒级时间戳
    private getCurTimeStamp(push_time): number {
        let tmp = (new Date(push_time).getTime() / 1000).toString();
        tmp = tmp.substr(0, 10);
        return parseInt(tmp, 10);
    }

    /**
     * @name: delete push record
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async delete(pushId: number) {
        const { app, ctx, logger } = this;
        try {
            //mysql
            let sqlStr = `DELETE FROM push_record WHERE id = ?;`;
            await this.execSql(sqlStr, [pushId]);
            //redis 
            await app.redis.get("tokenRedis").hdel(this.pushHashKey, pushId)
        } catch (error) { throw error }
    }

    /**
     * @name: 测试golang推送服务连通性
     * @msg: 
     * @return: 
     */
    public async test() {
        const { app, ctx, logger } = this;
        try {
            let url = app.config.pushHealthCheckUrl;
            const result = await ctx.curl(url, {
                method: 'GET',
                dataType: 'json',
                timeout: 3000
            });
            const { status, headers, data } = result
            if (status == 200) {
                return data;
            } else {
                logger.error("推送服务goExe故障", status);
                throw new Error("推送服务goExe故障");
            }
        } catch (error) { throw error }
    }
}
