/*
 * @Description: 商城道具路由
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: z<PERSON><PERSON>
 * @Date: 2020-04-24 11:50:11
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-01-14 09:36:06
 */
import { Router, Application } from "egg";
import { AccessRouteId, AccessRouteName } from "./model/accessRouteCof";
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

    const { controller, router } = app;
    //【商城道具管理】获取基础分类accCtr(AccessRouteId.wolf_mall_gift),
    router.post(`${API_VERSION}/werewolf/mallItem/getBaseTypeList`, controller.werewolf.mallItem.getBaseTypeList)
    //【商城道具管理】获取分类道具列表accCtr(AccessRouteId.wolf_mall_gift),
    router.post(`${API_VERSION}/werewolf/mallItem/getPropList`, controller.werewolf.mallItem.getPropList)
    //【商城道具管理】获取标签列表accCtr(AccessRouteId.wolf_mall_gift),
    router.post(`${API_VERSION}/werewolf/mallItem/getMallTagList`, controller.werewolf.mallItem.getMallTagList)
    //【商城道具管理】获取限制条件列表accCtr(AccessRouteId.wolf_mall_gift),
    router.post(`${API_VERSION}/werewolf/mallItem/getMallConditionList`, controller.werewolf.mallItem.getMallConditionList)
    //【商城道具管理】增加商品accCtr(AccessRouteId.wolf_mall_gift),
    router.post(`${API_VERSION}/werewolf/mallItem/insertMallItem`, controller.werewolf.mallItem.insertMallItem)
    //【商城道具管理】编辑商品accCtr(AccessRouteId.wolf_mall_gift),
    router.post(`${API_VERSION}/werewolf/mallItem/updateMallItem`, controller.werewolf.mallItem.updateMallItem)
    //【商城道具管理】获取跳转类型accCtr(AccessRouteId.wolf_mall_gift),
    router.post(`${API_VERSION}/werewolf/mallItem/getJumpTypeList`, controller.werewolf.mallItem.getJumpTypeList)
    //【商城道具管理】获取操作记录accCtr(AccessRouteId.wolf_mall_gift),
    router.post(`${API_VERSION}/werewolf/mallItem/getOpreateList`, controller.werewolf.mallItem.getOpreateList)
    //【商城道具管理】上传标签信息accCtr(AccessRouteId.wolf_mall_gift),
    router.post(`${API_VERSION}/werewolf/mallItem/uploadTagInfo`, controller.werewolf.mallItem.uploadTagInfo)
    //【商城道具管理】删除标签accCtr(AccessRouteId.wolf_mall_gift),
    router.post(`${API_VERSION}/werewolf/mallItem/delMallTag`, controller.werewolf.mallItem.delMallTag)
    //【商城道具管理】更改商品上架下架状态accCtr(AccessRouteId.wolf_mall_gift),
    router.post(`${API_VERSION}/werewolf/mallItem/changeMallItemDelsign`, controller.werewolf.mallItem.changeMallItemDelsign)
    router.post(`${API_VERSION}/werewolf/mallItem/changeMallItemDelsignConsole`, controller.werewolf.mallItem.changeMallItemDelsignConsole)
    router.post(`${API_VERSION}/werewolf/avatarperiod/changeEditManagerNum`, controller.werewolf.mallItem.changeEditManagerNum);
    router.post(`${API_VERSION}/werewolf/mallItem/getCoinList`, controller.werewolf.mallItem.getCoinList);


    router.post(`${API_VERSION}/werewolf/mallItem/addRealityLotteryBox`, controller.werewolf.mallItem.addRealityLotteryBox)
    router.post(`${API_VERSION}/werewolf/mallItem/updateRealityLotteryBox`, controller.werewolf.mallItem.updateRealityLotteryBox)
    router.post(`${API_VERSION}/werewolf/mallItem/getRealityLotteryBoxRecord`, controller.werewolf.mallItem.getRealityLotteryBoxRecord)
    router.post(`${API_VERSION}/werewolf/mallItem/getLotteryBoxLevel`, controller.werewolf.mallItem.getLotteryBoxLevel)
    router.post(`${API_VERSION}/werewolf/mallItem/deleteRealityLotteryBox`, controller.werewolf.mallItem.deleteRealityLotteryBox)

    


    
}

export default load
