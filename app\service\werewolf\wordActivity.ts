/*
 * @Description: 商城道具服务类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: z<PERSON><PERSON>
 * @Date: 2020-04-24 11:35:39
 * @LastEditors: zhang<PERSON>
 * @LastEditTime: 2022-05-05 15:23:52
 */
import BaseMegaService from './BaseMegaService';
import { MallItemBaseType, MallItemBaseInfo, CoinOperation, UploadTagInfoReq } from './../../model/mallItemManager';
export default class WordActivityService extends BaseMegaService {

    public async getWordActivityList(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
            SELECT w.*, a.\`name\` FROM word_activity w 
            LEFT JOIN word_box_award a ON a.id = w.award_id
            ORDER BY \`no\` DESC
            LIMIT ?, ?
            `;
            let data = await this.selectList(sqlStr, [(req.current - 1) * req.pageCount, req.pageCount])

            return data;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getWordActivityListCount() {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
            SELECT COUNT(*) AS num FROM word_activity w 
            LEFT JOIN word_box_award a ON a.id = w.award_id
            `;
            let data = await this.selectOne(sqlStr, [])

            return data;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getWordAwardList(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
                SELECT * FROM word_box_award
                order by id DESC
            `;
            let data = await this.selectList(sqlStr, [])

            return data;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateWordActivity(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
                UPDATE word_activity 
                SET num = ${req.num},
                word = '${req.word}',
                start_time = '${req.start_time}',
                end_time = '${req.end_time}',
                open_start_time = '${req.start_time}',
                open_end_time = '${req.end_time}',
                remark = '${req.remark}',
                remark_done = '${req.remark_done}',
                type = '${req.type}',
                award_id = ${req.award_id} 
                WHERE
                    no = ${req.no};
            `;
            await this.execSql(sqlStr, [])
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateWordActivityState(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ``;
            if (req.type == 0) {
                sqlStr = ` 
                UPDATE word_activity 
                SET
                state = ${req.state}
                WHERE
                no = ${req.no};
            `;
            } else {
                if (req.state == 1) {
                    sqlStr = ` 
                        UPDATE word_activity 
                        SET
                        state = ${req.state},
                        open_start_time = DATE_SUB(NOW(),INTERVAL 1 HOUR),
                        start_time = DATE_SUB(NOW(),INTERVAL 1 HOUR),
                        open_end_time = DATE_ADD(NOW(),INTERVAL 1 DAY),
                        end_time = DATE_ADD(NOW(),INTERVAL 1 DAY)
                        WHERE
                        no = ${req.no};
                    `;
                } else {
                    sqlStr = ` 
                        UPDATE word_activity 
                        SET
                        state = ${req.state},
                        open_end_time = DATE_SUB(NOW(),INTERVAL 1 DAY),
                        end_time = DATE_SUB(NOW(),INTERVAL 1 DAY)
                        WHERE
                        no = ${req.no};
                    `;
                }
            }

            await this.execSql(sqlStr, [])
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async insertWordActivity(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
            INSERT INTO word_activity (
                num,
                type,
                word,
                state,
                start_time,
                end_time,
                open_start_time,
                open_end_time,
                remark,
                award_id 
            )
            VALUES
                (
                    ${req.num},
                    ${req.type},
                    '${req.word}',
                    0,
                    '${req.start_time}',
                    '${req.end_time}',
                    '${req.start_time}',
                    '${req.end_time}',
                    '${req.remark}',
                    ${req.award_id}  
                );
            `;
            await this.execSql(sqlStr, [])
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getWordBoxList(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
            SELECT 
                i.item_id,
                b.*,
                IFNULL(ic.id, 10000) AS cate_id,
                ni.img_name AS normalImg,
                c.img_name AS coinImg,
                m.prive_remark AS maskshowImg
            FROM
                word_box b
            LEFT JOIN item_dic i ON i.id = b.item_dic_id
            LEFT JOIN item_cate ic ON ic.id = i.item_cate_id
            LEFT JOIN maskshow m ON m.\`no\` = i.item_id AND i.item_cate_id = 1040
            LEFT JOIN normal_item ni ON ni.\`no\` = i.item_id
            LEFT JOIN coin c ON c.id = i.item_id
            WHERE award_id = ${req.award_id}
            order by b.sort ASC
            `;
            return await this.selectList(sqlStr, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async insertWordAward(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
                INSERT INTO word_box_award (\`name\`, remark)
                VALUES
                    ('${req.name}', '${req.remark}');
            `;
            let data = await this.selectList(sqlStr, [])

            return data;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateWordAward(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
            UPDATE word_box_award 
            SET \`name\` = '${req.name}',
            remark = '${req.remark}' 
            WHERE
                id = ${req.id}
            `;
            let data = await this.selectList(sqlStr, [])

            return data;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async insertWordBox(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
            INSERT INTO word_box (
                name,
                item_dic_id,
                weight,
                num_min,
                num_max,
                \`desc\`,
                award_id,
                delsign,
                level, 
                num_random,
                price,
                sort
            )
            VALUES (?,?,?,?,?,?,?, 0,1,0,0,?)
            `;
            await this.execSql(sqlStr, [
                req.name,
                req.item_dic_id,
                req.weight,
                req.num,
                req.num,
                req.desc,
                req.award_id,
                req.sort,
            ])
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateWordBox(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
            UPDATE word_box 
                SET name = ?,
                item_dic_id = ?,
                weight = ?,
                num_min = ?,
                num_max = ?,
                \`desc\` = ?,
                award_id = ?,
                sort = ?
                WHERE
                    no = ?;
            `;
            await this.execSql(sqlStr, [
                req.name,
                req.item_dic_id,
                req.weight,
                req.num,
                req.num,
                req.desc,
                req.award_id,
                req.sort,
                req.no])
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateWordBoxDelsign(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
            UPDATE word_box 
                SET delsign = ?
                WHERE
                    no = ?;
            `;
            let data = await this.execSql(sqlStr, [
                req.delsign,
                req.no])

            return data;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getWordJumpList(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
            SELECT 
               *
            FROM
            word_activity_url
            LIMIT 1
            `;
            return await this.selectList(sqlStr, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateWordJump(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
            UPDATE word_activity_url 
            SET url = ?
            WHERE
                id = 1;
            `;
            return await this.selectList(sqlStr, [req.url]);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateWordAcitvityImg(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
            UPDATE word_activity 
            SET 
            img_icon = ?,
            img_done = ?,
            img_not_start = ?
            WHERE
                \`no\` = ?;
            `;
            return await this.selectList(sqlStr, [req.img_icon, req.img_done, req.img_not_start, req.id]);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getWordAcitvityImgList(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
            SELECT
                img_icon,
                img_done,
                img_not_start
            FROM
                word_activity
            WHERE
                img_icon IS NOT NULL
                AND img_done IS NOT NULL
                AND img_not_start IS NOT NULL
            GROUP BY img_icon, img_done, img_not_start
	
            `;
            return await this.selectList(sqlStr, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getWordInfo(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
                SELECT * FROM word_activity_info limit 1
            `;
            let data = await this.selectList(sqlStr, [])

            return data;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateWordInfo(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
            UPDATE word_activity_info 
            SET 
            state = ?,
            start_time = ?,
            end_time = ?
            WHERE
                \`no\` = 1;
            `;
            return await this.selectList(sqlStr, [req.state, req.start_time, req.end_time]);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

}
