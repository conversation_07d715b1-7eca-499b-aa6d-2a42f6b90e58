/*
 * @Description: 甜蜜赠礼
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: wb
 * @LastEditors: wangbo
 * @LastEditTime: 2021-07-21 10:54:23
 */
import BaseMegaService from './BaseMegaService';
import {
    ISweetnessGiftInsertParams,
    ISweetnessGiftDelParams
} from "../../model/sweetnessGiftDto";

const managerDb = "manager";
const werewolfDb = "werewolf";

export default class SweetnessGiftService extends BaseMegaService {

    public async getSweetnesskGiftList() {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `
                SELECT * FROM rank_award WHERE type = 40 AND del_flag = 0  GROUP BY y,m
            `;

            let list = await this.selectList(sqlStr, [], werewolfDb);

            if (list == null) {
                return [];
            }

            for (let i = 0; i < list.length; i++) {
                let item = list[i];
                let select_aql = `
                SELECT * FROM rank_award WHERE type = 40 AND del_flag = 0  AND y = ${item.y} AND m = ${item.m}
                `;
                let selectList = await this.selectList(select_aql, [], werewolfDb);
                for (let j = 0; j < selectList.length; j++) {
                    let selectItem = selectList[j];

                    if (j == 0) {
                        item.id1 = selectItem.id;
                        item.award_img1 = selectItem.award_img;
                    } else if (j == 1) {
                        item.id2 = selectItem.id;
                        item.award_img2 = selectItem.award_img;

                    } else if (j == 2) {
                        item.id3 = selectItem.id;
                        item.award_img3 = selectItem.award_img;

                    } else if (j == 3) {
                        item.id4 = selectItem.id;
                        item.award_img4 = selectItem.award_img;

                    }

                }
            }



            return list;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async insert(req: ISweetnessGiftInsertParams) {
        const { app, ctx, logger } = this;
        const werewolf = app.mysql.get(werewolfDb);
        const werewolfConn = await werewolf.beginTransaction();

        try {

            let time = req.time.split('-');

            let y = time[0];
            let m = time[1];


            let sqlStr = `
            SELECT * FROM rank_award WHERE type = 40 AND del_flag = 0  AND y = ${y} AND m = ${m}
            `;
            const list = await werewolfConn.query(sqlStr, []);

            if (list.length == 0) {
                let goodsMap = req.goodsMap;

                for (let key in goodsMap) {
                    if (goodsMap[key]["item_dic_id"] != undefined) {


                        let award_img = '';


                        if (goodsMap[key]["item_cate_id"] == 2010 || goodsMap[key]["item_cate_id"] == 2020) {
                            //http://img.53site.com/Werewolf/Frame/3297_player.png 头像框  item_cate_id 2010,2020

                            award_img = 'http://img.53site.com/Werewolf/Frame/' + goodsMap[key]["item_id"] + '_player.png';
                        } else if (goodsMap[key]["item_cate_id"] == 11000) {
                            //http://werewolf-resource.53site.com/w/c/clothes_icon_745.png  装扮 item_cate_id 11000
                            award_img = 'http://werewolf-resource.53site.com/w/c/clothes_icon_' + goodsMap[key]["item_id"] + '.png';

                        } else {
                            // http://werewolf-resource.53site.com/s/hallWebp/animation_icon_501478.png   特效  else
                            award_img = 'http://werewolf-resource.53site.com/s/hallWebp/animation_icon_' + goodsMap[key]["item_id"] + '.png';

                        }


                        let insertSql = `
                        INSERT INTO rank_award (type,time,num,y,m,del_flag,item_dic_id,award_img)
                        VALUES (40,30,1,${y},${m},0,${goodsMap[key]["item_dic_id"]},'${award_img}');
                    `;

                        await werewolfConn.query(insertSql, []);

                    }
                }
            }

            await werewolfConn.commit();

        } catch (error) {
            await werewolfConn.rollback();
            logger.error(error);
            throw error
        }
    }

    public async update(req: any) {
        const { app, ctx, logger } = this;
        const werewolf = app.mysql.get(werewolfDb);
        const werewolfConn = await werewolf.beginTransaction();

        try {

            let time = req.time.split('-');

            let y = time[0];
            let m = time[1];




            let sql = `
                UPDATE rank_award
                SET y = ${y},
                    m = ${m}
                WHERE id in (${req.id1},${req.id2 == undefined ? 0 : req.id2},${req.id3 == undefined ? 0 : req.id3},${req.id4 == undefined ? 0 : req.id4});
                `

            await werewolfConn.query(sql, []);
            let goodsMap = req.goodsMap;

            for (let key in goodsMap) {

                if (goodsMap[key]["item_dic_id"] != undefined) {


                    let award_img = '';


                    if (goodsMap[key]["item_cate_id"] == 2010 || goodsMap[key]["item_cate_id"] == 2020) {
                        //http://img.53site.com/Werewolf/Frame/3297_player.png 头像框  item_cate_id 2010,2020

                        award_img = 'http://img.53site.com/Werewolf/Frame/' + goodsMap[key]["item_id"] + '_player.png';
                    } else if (goodsMap[key]["item_cate_id"] == 11000) {
                        //http://werewolf-resource.53site.com/w/c/clothes_icon_745.png  装扮 item_cate_id 11000
                        award_img = 'http://werewolf-resource.53site.com/w/c/clothes_icon_' + goodsMap[key]["item_id"] + '.png';

                    } else {
                        // http://werewolf-resource.53site.com/s/hallWebp/animation_icon_501478.png   特效  else
                        award_img = 'http://werewolf-resource.53site.com/s/hallWebp/animation_icon_' + goodsMap[key]["item_id"] + '.png';

                    }


                    let insertSql = `
                        INSERT INTO rank_award (type,time,num,y,m,del_flag,item_dic_id,award_img)
                        VALUES (40,30,1,${y},${m},0,${goodsMap[key]["item_dic_id"]},'${award_img}');
                    `;

                    await werewolfConn.query(insertSql, []);

                }

            }


            await werewolfConn.commit();
        } catch (error) {
            await werewolfConn.rollback();
            logger.error(error);
            throw error
        }
    }

    public async updateAwardBg(req: any) {
        const { app, ctx, logger } = this;
        const werewolf = app.mysql.get(werewolfDb);
        const werewolfConn = await werewolf.beginTransaction();

        try {

            // let time = req.time.split('-');
            // let y = time[0];
            // let m = time[1];

            let sql = `
            UPDATE rank_award
            SET cp_award_bg = '${req.cp_award_bg}'
            WHERE y = ${req.y} AND m = ${req.m};
            `

            logger.error("********************************" + sql);
            await werewolfConn.query(sql, []);


            await werewolfConn.commit();
        } catch (error) {
            await werewolfConn.rollback();
            logger.error(error);
            throw error
        }
    }

    public async updateItemDelSign(req: ISweetnessGiftDelParams) {
        const { app, ctx, logger } = this;
        const werewolf = app.mysql.get(werewolfDb);
        const werewolfConn = await werewolf.beginTransaction();

        try {

            let sql = `UPDATE rank_award SET del_flag  = 1 WHERE id = ${req.id};`;

            await werewolfConn.query(sql, []);

            await werewolfConn.commit();
        } catch (error) {
            await werewolfConn.rollback();
            logger.error(error);
            throw error
        }
    }



}
