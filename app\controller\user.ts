import { Controller } from 'egg';
import {
	IloginRequest,
	IuserModel,
	IloginResponse,
	IuserResponse,
	IadminToken,
	IuserListResponse,
	ItokenIdList, IresetPwdRequest, IlogoutAllRequest, IuserCreateResponse
} from '../model/manager';
import { IerrorMsg, HttpErr, Ipayload } from '../model/common';
import * as moment from 'moment';
import { decryptedStr, publicKey } from '../util/safety';
import { UserDeleteRequest, UserCreateRequest } from '../model/werewolf';
const md5 = require("md5");

export default class UserController extends Controller {

	//服务器当前的环境
	public async env(){
		const { ctx,app } = this;
		ctx.body = { env: app.env };
	}

	/**
  * @name: 获得认证的公钥
  * @msg: 
  * @param {type} 
  * @return: 
  */
	public async publicKey() {
		const { ctx } = this;
		ctx.body = { publicKey: publicKey };
	}

	//测试非对称加密
	public async test() {
		const { ctx, logger, app } = this;
		// 校验规则
		const loginRule = {
			email: { type: 'string' },
			password: { type: 'string' }
		};
		try {
			//解密
			const request: { password: string } = ctx.request.body;
			const result = decryptedStr(request.password);
			//console.log('解密后', result);
			ctx.body = { result };
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest;
		}
	}

	private decodePass(loginRequest: IloginRequest){
		let pass = ""
		try{
			pass = decryptedStr(loginRequest.password)
		}catch(err){
			this.logger.error("非对称解谜失败，使用md5",err);
			pass = loginRequest.ps2;
		}
		this.logger.info("md5通过 pass:",pass);
		return pass;
	}

	/**
	 * @name: 密码经过加密的认证 密码格式=密码|hm|时间戳
	 * @msg: 
	 * @param {type} 
	 * @return: 
	 */
	public async auth() {
		const { ctx, logger, app } = this;
		// 校验规则
		const loginRule = {
			email: { type: 'string' },
			// password: { type: 'string' }
		};
		try {
			//1 校验
			ctx.validate(loginRule);
			const loginRequest: IloginRequest = ctx.request.body;
			const theUser: IuserModel = await ctx.service.user.getInfo(loginRequest);
			//存在账号
			if (!!theUser) {
				//解码密码
				const passwordDec: string = this.decodePass(loginRequest) ;
				const passwordArr = passwordDec.split('|hm|');
				const realPwd = passwordArr[0]; //解密后密码
				const realPwdTimestap = passwordArr[1]; //密码的时间戳
				//console.log(realPwd, realPwdTimestap);
				//登陆成功
				if (theUser.password === realPwd || md5(theUser.password) === realPwd) {
					//判断客户端请求识别码
					const reqId: [string] = await ctx.service.user.getClientReqId(realPwdTimestap);
					if (!!reqId && reqId.length > 0) {
						throw new Error(
							`发生了重放攻击, ${loginRequest.email}, ${ctx.host},${ctx.hostname},${ctx.ip},${ctx.ips},,${ctx.ref}`
						);
					}
					const clientIP = ctx.ips.length > 0 ? ctx.ips.toString() : ctx.ip;
					logger.info("登陆用户",loginRequest.email,"ip",ctx.ips,ctx.ip);
					//更新登录时间和登录ip
					const updateSuccess = await ctx.service.user.updateLogin(theUser.id, clientIP);
					if (updateSuccess.affectedRows === 1) {
						//插入adminToken表
						const tokenId: number = await ctx.service.user.insertAdminToken(
							theUser.id,
							clientIP,
							realPwdTimestap
						);
						//生成token
						const token = ctx.helper.createToken(theUser.id, tokenId,theUser.accessRoutes);
						//token存入redis缓存
						await ctx.service.user.saveRedisToken(theUser.id, tokenId, String(tokenId));
						//返回
						const reponseBody: IloginResponse = {
							token: token,
							tokenType: 'Bearer',
							uid: theUser.id,
							accessRoutes:theUser.accessRoutes
						};
						ctx.body = reponseBody;
						ctx.status = HttpErr.Success;
					} else {
						const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '更新登录信息错误' };
						ctx.body = err;
						ctx.status = HttpErr.BadRequest;
					}
				} else {
					//密码错误
					const err: IerrorMsg = { err_code: HttpErr.PasswordError, err_msg: '密码错误' };
					ctx.body = err;
					ctx.status = HttpErr.BadRequest;
				}
			} else {
				//不存在账号
				const err: IerrorMsg = { err_code: HttpErr.UserNotExist, err_msg: '用户不存在' };
				ctx.body = err;
				ctx.status = HttpErr.BadRequest;
			}
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}
	/**
   * @name: 登陆
   * @msg:
   * @param {type}
   * @return:
   */
	// public async login() {
	// 	const { ctx, logger, app } = this;
	// 	// 校验规则
	// 	const loginRule = {
	// 		email: { type: 'string' },
	// 		password: { type: 'string' }
	// 	};
	// 	try {
	// 		//1 校验
	// 		ctx.validate(loginRule);
	// 		const loginRequest: IloginRequest = ctx.request.body;
	// 		// logger.info(loginModel);
	// 		const userInfo: [IuserModel] = await ctx.service.user.getInfo(loginRequest);
	// 		//存在账号
	// 		if (!!userInfo && userInfo.length > 0) {
	// 			const theUser = userInfo[0];
	// 			//登陆成功
	// 			if (theUser.password === loginRequest.password) {
	// 				const clientIP = ctx.ips.length > 0 ? ctx.ips.toString() : ctx.ip;
	// 				//更新登录时间和登录ip
	// 				const updateSuccess = await ctx.service.user.updateLogin(theUser.id, clientIP);
	// 				if (updateSuccess.affectedRows === 1) {
	// 					//插入adminToken表
	// 					const tokenId: number = await ctx.service.user.insertAdminToken(theUser.id, clientIP, '');
	// 					//生成token
	// 					const token = ctx.helper.createToken(theUser.id, tokenId);
	// 					//token存入redis缓存
	// 					await ctx.service.user.saveRedisToken(theUser.id, tokenId, String(tokenId));
	// 					//返回
	// 					const reponseBody: IloginResponse = {
	// 						token: token,
	// 						tokenType: 'Bearer',
	// 						uid: theUser.id
	// 					};
	// 					ctx.body = reponseBody;
	// 					ctx.status = HttpErr.Success;
	// 				} else {
	// 					const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '更新登录信息错误' };
	// 					ctx.body = err;
	// 					ctx.status = HttpErr.BadRequest;
	// 				}
	// 			} else {
	// 				//密码错误
	// 				const err: IerrorMsg = { err_code: HttpErr.PasswordError, err_msg: '密码错误' };
	// 				ctx.body = err;
	// 				ctx.status = HttpErr.BadRequest;
	// 			}
	// 		} else {
	// 			//不存在账号
	// 			const err: IerrorMsg = { err_code: HttpErr.UserNotExist, err_msg: '用户不存在' };
	// 			ctx.body = err;
	// 			ctx.status = HttpErr.BadRequest;
	// 		}
	// 	} catch (e) {
	// 		logger.error(e);
	// 		const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
	// 		ctx.body = err;
	// 		ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
	// 	}
	// }

	// todo query使用url参数    params使用restfull接口获得参数  body使用json转换
	public async signUp() {
		const { ctx, logger } = this;
	}

	/**
   * @name: 根据uid获得userinfo
   * @msg: 
   * @param {type} 
   * @return: 
   */
	public async getInfo() {
		const { ctx, logger, app } = this;
		if (ctx.params.id) {
			try {
				const id = ctx.params.id;
				const fullInfos: [IuserModel] = await ctx.service.user.getFullInfo(id);
				//为空
				if (!fullInfos || fullInfos.length < 1) {
					ctx.body = {};
					ctx.status = HttpErr.Success;
					return;
				}

				const fullInfo = fullInfos[0];
				fullInfo.logintime = moment(fullInfo.logintime).format('YYYY-MM-DD HH:mm:ss');
				const body = {};
				for (const key in fullInfo) {
					if (fullInfo.hasOwnProperty(key) && key !== 'password') {
						const element = fullInfo[key];
						body[key] = element;
					}
				}
				ctx.body = body;
				ctx.status = HttpErr.Success;
			} catch (error) {
				logger.error(error);
				const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
				ctx.body = err;
				ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
			}
		} else {
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}

	/**
  * @name: 用户全部设备都登出
  * @msg: 
  * @param {type} 
  * @return: 
  */
	public async logOutAll() {
		const { ctx, logger, app } = this;
		// 校验规则
		const loginRule = {
			uid: { type: 'number' }
		};
		try {
			//1 校验
			ctx.validate(loginRule);
			//判断权限
			//console.info(ctx.request.body);
			const request: IlogoutAllRequest = ctx.request.body;
			const payload: Ipayload = ctx.helper.exPayload;
			if (request.uid !== payload.uid) {
				logger.error('body uid与token uid不一致');
				const err: IerrorMsg = { err_code: HttpErr.Forbidden, err_msg: 'uid与token uid不一致，没有权限' };
				ctx.body = err;
				ctx.status = HttpErr.Forbidden; //请求参数错误 401Unauthorized未鉴权
				return;
			}
			//清理token
			const result: ItokenIdList[] = await ctx.service.user.clearRedisToken(request.uid);
			ctx.body = result;
			ctx.status = HttpErr.Success;
		} catch (error) {
			logger.error(error);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}

	// 获得全部管理员列表
	public async userList() {
		const { ctx, logger, app } = this;
		try {
			const result = await ctx.service.user.userList();
			const response: IuserListResponse = { list: result };
			ctx.body = response;
			ctx.status = HttpErr.Success;
		} catch (error) {
			logger.error(error);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //
		}
	}

	public async userdelete() {
		const { ctx, logger, app } = this;
		try {
			const requestBody: UserDeleteRequest = ctx.request.body;
			const result = await ctx.service.user.userdelete(requestBody);
			const response: IuserListResponse = { list: result };
			ctx.body = response;
			ctx.status = HttpErr.Success;
		} catch (error) {
			logger.error(error);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //
		}
	}
	public async usercreate() {
		const { ctx, logger, app } = this;
		try {
			const requestBody: UserCreateRequest = ctx.request.body;
			const result = await ctx.service.user.usercreate(requestBody);
			const response: IuserCreateResponse = { list: result };
			ctx.body = response;
			ctx.status = HttpErr.Success;
		} catch (error) {
			logger.error(error);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //
		}
	}
	
	//重置密码
	public async resetPwd() {
		const { ctx, logger, app } = this;
		// 校验规则
		const loginRule = {
			uid: { type: 'number' },
			oldPwd: { type: 'string' },
			newPwd: { type: 'string', min: 6 }
		};
		try {
			const request: IresetPwdRequest = ctx.request.body;
			const result: IerrorMsg = await ctx.service.user.resetPwd(request);
			ctx.body = result;
			ctx.status = HttpErr.Success;
		} catch (error) {
			logger.error(error);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest;
		}
	}
}
