import BaseMegaService from './BaseMegaService';
import { IgamingReq } from '../../model/wfGmTool';

/*
 * @Author: your name
 * @Date: 2020-06-23 13:35:25
 * @LastEditTime: 2020-09-08 10:26:39
 * @LastEditors: hammercui
 * @Description: In User Settings Edit
 * @FilePath: /ConsoleSystemServer/app/service/werewolf/gmTool.ts
 */ 
export default class GmToolService extends BaseMegaService {

  public async gamingInfo(req: IgamingReq) {
    try {
      let roomId;
      if (req.room_id) {
        roomId = req.room_id;
      } else { 
        //查询
        const sqlStr = "SELECT cur_room_no FROM tuserstate WHERE user_no = ?;";
        const result = await this.selectOne(sqlStr, [req.player_id])
        if (result && result.cur_room_no > 0) {
          roomId = result.cur_room_no 
        } else { 
          throw new Error("玩家还没有开始游戏");
        }
      }
    } catch (e) { 
      throw e;
    }
  }
}