import BaseMegaController from './BaseMegaController';

export default class ActivityTalentController extends BaseMegaController {


    //1 
    public async stateInfo() {
        const { ctx, logger } = this;
        try {
            const retList = await ctx.service.werewolf.activityTalent.stateInfo();
            this.respSuccData(retList)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async stateUpdate() {
        const { ctx, logger } = this;
        try {
            const req = ctx.request.body;
            await ctx.service.werewolf.activityTalent.stateUpdate(req);
            this.respSucc()
        } catch (err) {
            this.respFail(err)
        }
    }

    public async playerList() {
        const { ctx, logger } = this;
        try {
            const req = ctx.request.body;
            const retList = await ctx.service.werewolf.activityTalent.playerList(req);
            this.respSuccData(retList)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async playerUpadte() {
        const { ctx, logger } = this;
        try {
            const req = ctx.request.body;
             await ctx.service.werewolf.activityTalent.playerUpadte(req);
            this.respSucc()
        } catch (err) {
            this.respFail(err)
        }
    }

}