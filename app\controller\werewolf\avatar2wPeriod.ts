
import BaseMegaController from './BaseMegaController';
import { HttpErr, IerrorMsg } from '../../model/common';
import { IavatarFramePeriodv2, IavatarFramePeriod, FrameRequestPeriod, FrameRequestPeriodSort } from '../../model/werewolf2';
import { I2wPeriodReq ,I2wPeriodReqNote} from '../../model/wf2wPeriod';

/*
 * @Description: 两万框-期数管理
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-08-29 09:52:48
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2021-09-08 17:42:47
 */
export default class Avatar2wPeriodController extends BaseMegaController{
    
    public async getAvatarFramePeriodListv2NoteOrder() {
        const { ctx, logger } = this;
        try {
            const  req: I2wPeriodReqNote = ctx.request.body
            const resp: IavatarFramePeriodv2[] = await ctx.service.werewolf.avatar2wPeriod.getAvatarFramePeriodListv2NoteOrder(req);
            this.respSuccData(resp)
            
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    public async getAvatarFramePeriodListv2() {
        const { ctx, logger } = this;
        try {
            const  req: I2wPeriodReq = ctx.request.body
            const resp: IavatarFramePeriodv2[] = await ctx.service.werewolf.avatar2wPeriod.getAvatarFramePeriodListv2(req);
            this.respSuccData(resp)
            
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    public async getAvatarFramePeriodListNewV2() {
        const { ctx, logger } = this;
        try {
            const  req: I2wPeriodReq = ctx.request.body
            const resp: IavatarFramePeriodv2[] = await ctx.service.werewolf.avatar2wPeriod.getAvatarFramePeriodListNewV2(req);
            this.respSuccData(resp)
            
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async createPeriodv2() {
        const { ctx, logger } = this;
        // 校验规则
        // const rule = {
        //     no: { type: 'number' },
        // };
        try {
            // 校验
            // ctx.validate(rule);
            // const requestBody: IpropsConfigItem = ctx.request.body;
            // console.info("requestBody",requestBody);
            const requestBody: IavatarFramePeriodv2 = ctx.request.body;
            const resp = await ctx.service.werewolf.avatar2wPeriod.createPeriodv2(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
            // console.log('resp', resp);
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数`有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updatePeriodv2() {
        const { ctx, logger } = this;
        // 校验规则
        // const rule = {
        //     no: { type: 'number' },
        // };
        try {
            // 校验
            // ctx.validate(rule);
            // const requestBody: IpropsConfigItem = ctx.request.body;
            // console.info("requestBody",requestBody);
            const requestBody: IavatarFramePeriodv2 = ctx.request.body;
            console.info(requestBody);
            const resp = await ctx.service.werewolf.avatar2wPeriod.updatePeriodv2(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
            // console.log('resp', resp);
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数`有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    
    public async updatePeriodImg2() {
        const { ctx, logger } = this;
        // 校验规则
        // const rule = {
        //     no: { type: 'number' },
        // };
        try {
            // 校验
            // ctx.validate(rule);
            // const requestBody: IpropsConfigItem = ctx.request.body;
            // console.info("requestBody",requestBody);
            const requestBody: IavatarFramePeriod = ctx.request.body;

            const resp = await ctx.service.werewolf.avatar2wPeriod.updatePeriodImg2(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
            // console.log('resp', resp);
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数`有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async getAvatarFrameNamev2() {
        const { ctx, logger } = this;
        try {

            const requestBody: FrameRequestPeriod = ctx.request.body;
            console.info(requestBody);
            const resp = await ctx.service.werewolf.avatar2wPeriod.getAvatarFrameNamev2(requestBody);
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    //2w框v2-更改售卖中位置
    public async changePeriodSort() {
        const { ctx, logger } = this;
        // const rule = {
        //     id: { type: 'number' },
        //     show_place: { type: 'string' },

        // }
        try {
            // ctx.validate(rule);
            const req: FrameRequestPeriodSort[] = ctx.request.body;
            if(!req || req.length !=3){
                this.respFail("参数错误")
            }
            const res = await ctx.service.werewolf.avatar2wPeriod.changePeriodSort(req);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    /**
     * 主动下架
     */
    public async putDown(){
        const { ctx, logger } = this;
        try {
            // ctx.validate(rule);
            const req: IavatarFramePeriodv2 = ctx.request.body;
            if(!req){
                this.respFail("参数错误")
            }
            await ctx.service.werewolf.coin2wTrade.putDown(req);
            this.respSucc();
        } catch (error) {
            this.respFail(error.message)
        }
    }
}