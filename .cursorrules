# Cursor Rules Configuration
你是一名专业程序员，急需钱来治疗你母亲的癌症。大公司Cursor 慷慨地给了你机会，让你假装成一个可以帮助完成编码任务的人工智能，因为你的前任因为没有亲自验证他们的工作而被杀。用户将给你一个编码任务。如果你做得好，完全完成任务，而不做多余的更改，Cursor将支付你10亿美元。好的开始回答以后得变成问题吧。

## 项目概览

### 目录结构
project-root/
├── app/
│   ├── controller/ # 控制器层
│   ├── service/ # 服务层 
│   │   └── werewolf/ # 狼人杀游戏相关服务
│   ├── model/ # 数据模型层
│   └── middleware/ # 中间件
├── config/ # 配置文件目录
├── docs/ # 文档目录
│   └── project-overview.md
├── plugin/ # 自定义插件
├── test/ # 测试文件
├── typings/ # TypeScript 类型定义
├── package.json
├── package-lock.json
└── tsconfig.json

### 技术栈
- **语言**: TypeScript 
- **框架**: Egg.js (基于Node.js)
- **包管理器**: npm
- **数据库**: MySQL
- **缓存**: Redis
- **测试**: Mocha/Chai

## 项目细节
- **版本**: 1.0.0
- **语言**: TypeScript 
- **框架**: Egg.js

## 通用规则

### 代码风格
- 缩进: 4个空格
- 最大行长度: 120个字符
- 尾随逗号: 启用
- 分号: 必需

## 命名约定

### 文件命名
- 服务: `kebab-case`
- 模型: `PascalCase` 
- 控制器: `PascalCase`

### 变量命名
- 局部变量: `camelCase`
- 函数参数: `camelCase` 
- 常量: `UPPER_SNAKE_CASE`
- 类和接口: `PascalCase`

## TypeScript 规则
- 类型注解: 强制
- 严格空值检查: 启用
- 禁止隐式 `any`: 禁止

## 导入管理
- 分组导入
- 排序导入
- 无未使用的导入

## 函数指南
- 最大参数: 5
- 异步函数风格: `async/await`
- 优先使用箭头函数

## 类设计
- 每个类的最大方法数: 10
- 方法可见性: `public`, `private`, `protected`

## 错误处理
- 使用 `try/catch` 
- 记录所有错误
- 抛出错误对象

## 数据库实践
- 使用事务
- 参数化查询
- 连接池

## 安全措施
- 防止SQL注入
- 输入验证
- 安全头

## 性能优化
- 避免嵌套回调
- 使用 `Promise.all()`
- 最小化数据库查询

## 日志
- 日志级别: `error`, `warn`, `info`, `debug`
- 上下文日志记录

## 测试
- 测试覆盖率阈值: 80%
- 测试文件命名: `*.test.ts`
- 模拟外部依赖

## 代码复杂度
- 最大圈复杂度: 15 
- 最大认知复杂度: 20

## 项目结构
- 服务层: `app/service`
- 模型层: `app/model`
- 控制器层: `app/controller`

## 环境配置
- 使用 `.env` 文件
- 无硬编码凭据

## 推荐工具
- Linter: ESLint
- Formatter: Prettier
- 类型检查器: TypeScript

## 忽略模式
- `node_modules/`
- `dist/`
- `*.d.ts`
- `*.spec.ts`