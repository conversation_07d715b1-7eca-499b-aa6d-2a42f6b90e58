/*
 * @Description: 2w框-期数管理
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-09-02 11:14:34
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2021-09-22 15:37:21
 */

import BaseMegaService from './BaseMegaService';
import { IavatarFramePeriodv2, FrameRequestPeriod, FrameRequestPeriodid, IavatarFramePeriod, FrameRequestPeriodSort } from '../../model/werewolf2';
import { I2wPeriodReq, Period2wStatus,I2wPeriodReqNote } from '../../model/wf2wPeriod';

export default class Avatar2wPeriodService extends BaseMegaService {

    public async getAvatarFramePeriodListv2NoteOrder(req: I2wPeriodReqNote): Promise<IavatarFramePeriodv2[]> {
        const { app, ctx, logger } = this;
        try {
            /*
            SELECT
            a.avatarframe_id,b.`name` FROM user_avatarframe a LEFT JOIN 
            avatarframe b ON a.avatarframe_id = b.id 
            WHERE user_id = 254 AND b.is_dynamic = 0
            */ 
            let retArray = [];
            const sqlStr = 
            `SELECT
            a.*,b.\`name\` FROM user_avatarframe a LEFT JOIN 
            avatarframe b ON a.avatarframe_id = b.id 
            WHERE user_id = ${req.userId} AND b.is_dynamic = 0 AND (a.note_id = 0 OR ISNULL(a.note_id))`
            let ret = await this.selectList(sqlStr)
            if (ret && ret.length > 0) {
                retArray = retArray.concat(ret)
            }
            return retArray;
        }
        catch (error) {
            throw error;
        }
    }

    public async getAvatarFramePeriodListv2(req: I2wPeriodReq): Promise<IavatarFramePeriodv2[]> {
        const { app, ctx, logger } = this;
        try {
            let retArray = [];
            if (!req || req.period_status == Period2wStatus.ALL || req.period_status == Period2wStatus.SHOW_ON) {
                const sqlStr = `SELECT a.*,
                IFNULL(b.id,0) as daymic_avaframe_id
                FROM base20000_avatar_frame_period_v2 AS a
                LEFT JOIN avatarframe as b ON b.pid  = a.avatar_frame_id
                WHERE a.period_status = 1
                order by show_place desc,id desc`
                let ret = await this.selectList(sqlStr)
                if (ret && ret.length > 0) {
                    retArray = retArray.concat(ret)
                }
            }
            if (req.period_status != Period2wStatus.SHOW_ON) {
                let sqlStr = ""
                if (req.period_status == Period2wStatus.ALL) {
                    sqlStr = `SELECT a.*,
                    IFNULL(b.id,0) as daymic_avaframe_id
                    FROM base20000_avatar_frame_period_v2 AS a
                    LEFT JOIN avatarframe as b ON b.pid  = a.avatar_frame_id
                    WHERE a.period_status != 1
                    order by a.period_status asc,a.puton_sort asc,id asc`
                }
                if (req.period_status == Period2wStatus.READY) {
                    sqlStr = `SELECT a.*,
                    IFNULL(b.id,0) as daymic_avaframe_id
                    FROM base20000_avatar_frame_period_v2 AS a
                    LEFT JOIN avatarframe as b ON b.pid  = a.avatar_frame_id
                    WHERE a.period_status = 0
                    order by puton_sort asc,id asc`
                }
                if (req.period_status == Period2wStatus.SHOW_DOWN) {
                    sqlStr = `SELECT a.*,
                    IFNULL(b.id,0) as daymic_avaframe_id
                    FROM base20000_avatar_frame_period_v2 AS a
                    LEFT JOIN avatarframe as b ON b.pid  = a.avatar_frame_id
                    WHERE a.period_status = 2
                    order by puton_sort asc,id asc`
                }

                const ret = await this.selectList(sqlStr)
                if (ret && ret.length > 0) {
                    retArray = retArray.concat(ret)
                }
            }

            return retArray;
        }
        catch (error) {
            throw error;
        }
    }

    public async getAvatarFramePeriodListNewV2(req: I2wPeriodReq): Promise<IavatarFramePeriodv2[]> {
        const { app, ctx, logger } = this;
        try {
            let retArray = [];
            if (!req || req.period_status == Period2wStatus.ALL || req.period_status == Period2wStatus.SHOW_ON) {
                const sqlStr = `SELECT DISTINCT a.*,
                IFNULL(b.id,0) as daymic_avaframe_id
                FROM item_dic AS a
                LEFT JOIN avatarframe as b ON b.id  = a.item_id 
                WHERE  a.item_cate_id IN (2010,2020)
                order by id asc`
                let ret = await this.selectList(sqlStr)
                if (ret && ret.length > 0) {
                    retArray = retArray.concat(ret)
                }
            }
            if (req.period_status != Period2wStatus.SHOW_ON) {
                let sqlStr = ""
                if (req.period_status == Period2wStatus.ALL) {
                    sqlStr = `SELECT DISTINCT a.*,
                    IFNULL(b.id,0) as daymic_avaframe_id
                    FROM item_dic AS a
                    LEFT JOIN avatarframe as b ON b.id  = a.item_id 
                    WHERE  a.item_cate_id IN (2010,2020)
                    order by id asc`
                }
                if (req.period_status == Period2wStatus.READY) {
                    sqlStr = `SELECT DISTINCT a.*,
                    IFNULL(b.id,0) as daymic_avaframe_id
                    FROM item_dic AS a
                    LEFT JOIN avatarframe as b ON b.id  = a.item_id 
                    WHERE  a.item_cate_id IN (2010,2020)
                    order by id asc`
                }
                if (req.period_status == Period2wStatus.SHOW_DOWN) {
                    sqlStr = `SELECT DISTINCT a.*,
                    IFNULL(b.id,0) as daymic_avaframe_id
                    FROM item_dic AS a
                    LEFT JOIN avatarframe as b ON b.id  = a.item_id 
                    WHERE  a.item_cate_id IN (2010,2020)
                    order by id asc`
                }

                const ret = await this.selectList(sqlStr)
                if (ret && ret.length > 0) {
                    retArray = retArray.concat(ret)
                }
            }

            return retArray;
        }
        catch (error) {
            throw error;
        }
    }
    
    public async createPeriodv2(req: IavatarFramePeriodv2) {
        const { app, logger } = this;
        const db = app.mysql.get('werewolf');
        if(!req.selled_show_num){
            req.selled_show_num = 0
        }
        // const conn = await db.beginTransaction();
        try {
            let sqlStr = `INSERT INTO base20000_avatar_frame_period_v2 (name,sell_num,show_period,price,period_status,author,avatar_frame_id,puton_sort,selled_show_num) 
            VALUES(?,?,?,?,?,?,?,?,?);`;
            const result = await db.query(sqlStr, [
                req.name,
                req.sell_num,
                req.show_period,
                req.price,
                req.period_status,
                req.author,
                req.avatar_frame_id,
                req.puton_sort,
                req.selled_show_num]);
            logger.info("新建2w框v2结果", result);
            logger.info("新建2w框v2 id", result.insertId);
            req.id = result.insertId;
            // await conn.commit();
        } catch (error) {
            // await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async updatePeriodv2(req: IavatarFramePeriodv2) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        if(!req.selled_show_num){
            req.selled_show_num = 0
        }
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` UPDATE  base20000_avatar_frame_period_v2
            SET 
            name = ?,
          show_period = ?,
          price = ?,
          sell_num = ?,
          period_status = ?,
          author = ? ,
          puton_sort = ?,
          selled_show_num = ?,
          endtime = ?
          WHERE
                id = ? `;
            await conn.query(sql, [
                req.name,
                req.show_period,
                req.price,
                req.sell_num,
                req.period_status,
                req.author,
                req.puton_sort,
                req.selled_show_num,
                req.endtime,
                req.id]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async updatePeriodImg2(req: IavatarFramePeriod) {
        // const { newFlag, gameConfigId, hot, desc } = req;
        const { app } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            if (req.pic_mob) {
                let sql = ` UPDATE  base20000_avatar_frame_period_v2 
                SET pic_mob = ?
                WHERE
                    id = ? `;

                await conn.query(sql, [
                    req.pic_mob,
                    req.id]);
                await conn.commit(); // 提交事务
            } else if (req.pic_pc) {
                let sql = ` UPDATE  base20000_avatar_frame_period_v2 
                SET pic_pc = ?
                WHERE
                    id = ? `;

                await conn.query(sql, [
                    req.pic_pc,
                    req.id]);
                await conn.commit(); // 提交事务
            } else {
                let sql = ` UPDATE  base20000_avatar_frame_period_v2 
                SET pic_word = ?
                WHERE
                    id = ? `;

                await conn.query(sql, [
                    req.pic_word,
                    req.id]);
                await conn.commit(); // 提交事务
            }
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

    public async getAvatarFrameNamev2(req: FrameRequestPeriod) {
        const { app, ctx, logger } = this;

        try {
            const afName = req.frameName;
            const sqlStr = `SELECT id FROM avatarframe WHERE name= ? order by id desc limit 1`
            const resp: FrameRequestPeriodid = await this.selectOne(sqlStr, [afName])
            return resp
        }
        catch (error) {
            throw error;
        }
    }

    /**
     * 2w框-更改售卖中展示顺序
     * @param req 
     */
    public async changePeriodSort(req: FrameRequestPeriodSort[]) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        const conn = await db.beginTransaction();

        try {
            // tslint:disable-next-line:prefer-for-of
            for (let i = 0; i < req.length; i++) {
                const item = req[i];
                const id = db.escape(item.id);
                const show_place = db.escape(item.show_place);
                let sqlStr = `UPDATE 
                base20000_avatar_frame_period_v2 
            SET 
                show_place=${show_place}
            WHERE 
                id=${id} `
                await conn.query(sqlStr);
            }

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }

}