/*
 * @Description: 无
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2018-12-25 17:45:33
 * @LastEditors: jiawen.wang
 * @LastEditTime: 2021-05-24 10:23:57
 */
import { Service } from "egg";
import { HttpErr, IerrorMsg } from "../../model/common";
import {
  IplayerStatusRequest,
  IplayerStatusResponse,
  IbrushScoreRequest,
  IbrushScoreResponse,
  IescapeRequest,
  IescapeResponse,
  Iescape,
  IgameSpeakRequest,
  IgameSpeakResponse,
  IplayerBgRequest,
  IplayerBgResponse,
  IbannedPlayerRequest,
  IshutterPlayerRequest,
  IbannedBgRequest,
  IbannedBgResponse,
  IremoveBannedRequest,
  IremoveBannedResponse,
  IreportRequest,
  IreportResponse,
  Ireport,
  IillegalImagesRequest,
  IillegalImagesResponse,
  IshutterRequest,
  IshutterResponse,
  IliftShutterRequest,
  IreportVideoListResponse,
  IreportVideoListRequest,
  IreportVideoList,
  IliftShutterResponse,
  IillegalImages,
  InewBrushScoreResponse,
  InewBrushScoreRequest,
  InewBrushScore,
  ImemberChangeSearch,
  ImemberChange,
  IbarragePlayerRequest,
  SendEmailsToUsersRequest,
  Ishutter,
  IbanEntertainmentRequest,
  IentertainmentSearchUserDetailRequest,
  IentertainmentSearchUserRequest,
  IentertainmentSearchRoomRequest,
  IentertainmentSearchRoomDetailRequest,
  IentertainmentSearchRoomUsersRequest,
  IentertainmentSearchRoomReportsRequest,
  IentertainmentSearchRoomChatsRequest,
  IentertainmentSearchGroupRoomsRequest,
  IentertainmentKickOutRoomRequest,
} from "../../model/werewolf";
import * as moment from "moment";
import { playerStatusEnum } from "../../model/staticEnum";
import { wrapHeadIcon } from "../../util/utils";
import { type } from 'os';
import { GetCampName, GetGameLifeDesc, GetGameWinDesc } from '../../util/gameUtils';
import BaseMegaService from './BaseMegaService';
import SendEmailService from "./sendEmail";
import ExchangeRpcService from "./exchangeRpc";
const OSS = require('ali-oss');

export default class PlayerStatusService extends BaseMegaService {

  /**
   * @name: 充值头像框为1
   * @msg:
   * @param {type}
   * @return:
   */
  public async resetAvatar(playerId: number) {
    const { app } = this;
    try {
      const werewolf = app.mysql.get("werewolf");
      playerId = werewolf.escape(playerId);
      await werewolf.update("tuser", { headicon: 1 }, {
        where: {
          no: playerId,
          delsign: 0
        }
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * @name: 更新玩家昵称
   * @msg:
   * @param {type}
   * @return:
   */
  public async updateNickname(playerId: number, nickname: string) {
    const { app } = this;
    try {
      const werewolf = app.mysql.get("werewolf");
      playerId = werewolf.escape(playerId);
      await werewolf.update("tuser", { nickname }, {
        where: {
          no: playerId,
          delsign: 0
        }
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * @name: 更新经验值
   * @msg:
   * @param {type}
   * @return:
   */
  public async updateScore(playerId: number, score: number) {
    const { app } = this;
    try {
      const werewolf = app.mysql.get("werewolf");
      playerId = werewolf.escape(playerId);
      score = werewolf.escape(score);
      await werewolf.update("tuser", { scorce: score }, {
        where: {
          no: playerId,
          delsign: 0
        }
      });
    } catch (error) {
      throw error;
    }
  }

  public async getPlayerId(req: any) {
    const { app, ctx, logger } = this;
    try {
      let sqlStr = ` 
            SELECT no AS userId FROM tuser WHERE no = ? or username = '?' LIMIT 1
              `;
      // let sqlStr = `
      //         SELECT * FROM tuser WHERE no = ? or username = '?'
      //           `;
      let data = await this.selectOne(sqlStr, [req.userId]);

      return data;
    } catch (error) {
      logger.error(error);
      throw error
    }
  }

  public async getPhoneNumber(req) {
    const { app, ctx, logger } = this;
    try {
      let sqlStr = `SELECT no FROM tuser WHERE username = ?`;
      let data = await this.selectOne(sqlStr, [req.userId]);
      return data;
    } catch (error) {
      logger.error(error);
      throw error
    }
  }
  public async getIdCardList(req) {
    const { app, ctx, logger } = this;
    try {
      let sqlStr = `SELECT a.user_id,b.udid,b.delsign FROM describe_verify a LEFT JOIN tuser b ON a.user_id = b.no WHERE idcard = ? AND LENGTH(a.user_id) > 0`;
      let data = await this.selectList(sqlStr, [req.idNumber]);

      return data;
    } catch (error) {
      logger.error(error);
      throw error
    }
  }
  public async setIdCardBan(req) {
    const { app, ctx, logger } = this;
    try {
      const db = app.mysql.get('werewolf');
      const idNumber = db.escape(req.idNumber);
      let selSql = `SELECT * FROM idcard_ban WHERE idcard = ${idNumber}`
      let selResult = await db.query(selSql);
      if (selResult.length !== 0) {
        return selResult;
      } else {
        let sqlStr = `INSERT INTO idcard_ban (idcard, is_ban) VALUES (${idNumber}, 1)`;
        await db.query(sqlStr);
        return 1
      }
    } catch (error) {
      logger.error(error);
      throw error
    }
  }
  public async delIdCardBan(req) {
    const { app, ctx, logger } = this;
    const db = app.mysql.get('werewolf');
    // const werewolfConn = await db.beginTransaction();
    try {
      const idNumber = db.escape(req.idNumber);
      let sqlStr = `DELETE FROM idcard_ban WHERE idcard = ${idNumber};`
      // await werewolfConn.query(sqlStr);
      // await werewolfConn.commit();
      await this.execSql(sqlStr, []);
    } catch (error) {
      logger.error(error);
      // await werewolfConn.rollback();
      throw error
    }
  }

  /**
   * 查询用户充值总金额及用户得基本信息
   * @param playerId 用户ID
   */
  private async playerInfo(playerId: number) {
    const { app, logger } = this;
    const werewolf = app.mysql.get("werewolf");
    const uid = werewolf.escape(playerId);
    const sql =
      "SELECT SUM(product.price) AS total FROM `transaction` LEFT JOIN product ON `transaction`.productId = product.`name` WHERE `transaction`.`user` = " +
      uid;
    const result = await werewolf.query(sql);
    let rechargeAmount = "";
    if (!result || result[0].total == null) {
      rechargeAmount = "￥0";
    } else {
      rechargeAmount = `￥${result[0].total}`;
    }
    //其他基本信息
    const baseResult = await werewolf.get("tuser", { no: uid });
    if (!baseResult) return null;

    const groupSql = `SELECT g.group_name,gu.createtime FROM group_user gu LEFT JOIN \`group\` g ON  gu.group_id = g.id WHERE gu.user_id = ${uid} AND gu.delsign = 0 `
    const groupResult = await werewolf.query(groupSql);
    let groupName = '暂无公会';
    let groupTime = '';
    if (groupResult[0]) {
      groupName = groupResult[0]['group_name'];
      groupTime = groupResult[0]['createtime'];
    }

    const giftSql = `SELECT IFNULL(end_time, NOW() - INTERVAL 1 DAY) AS end_time FROM gift_delay_buff WHERE user_id = ${uid} `
    const giftResult = await werewolf.query(giftSql);
    let giftBuffTime = '';
    if (giftResult[0]) {
      giftBuffTime = giftResult[0]['end_time'];
    }
    //查询登记信息
    const tlevelSq = "	SELECT `level`,scorce as score  ,upscorce as upscore FROM tlevel WHERE `level` > 0 ;";
    const levelResult = await werewolf.query(tlevelSq);

    //正则校验手机号
    const pattern = /0?(13|14|15|18|17)[0-9]{9}/;
    logger.debug("username", baseResult.username);
    let phone = "";
    if (pattern.test(baseResult.username)) {
      phone = baseResult.username;
    }
    // 查询手机号变化
    const phoneRecordSql = "SELECT id AS id,old_phone AS oldPhone, new_phone AS newPhone, update_time AS updateTime " +
      "                   FROM user_phone_record " +
      "                   WHERE user_no = ? " +
      "                   ORDER BY id DESC;";
    const changePhone = await werewolf.query(phoneRecordSql, [playerId]);
    let udidSql = `SELECT * FROM user_ban_udid WHERE udid = ?;`
    let udidBan = await werewolf.query(udidSql, [baseResult.udid]);

    //查询是否内部测试号
    let isInnerUser = 0
    let innerResult = await this.selectOne(`SELECT * FROM tuser_inner_total WHERE user_id = ?;`, [playerId])
    if (innerResult) {
      isInnerUser = 1;
    }

    let overseaWhiteListSql = `SELECT * FROM user_header_white WHERE user_id = ?`
    let overseaWhiteList = await werewolf.query(overseaWhiteListSql, [playerId]);
    //拼接headIcon


    //验证码发送次数
    const sqlReg = 'SELECT countrycode,username FROM tuser WHERE `no` = ?;';
    const resultReg = await werewolf.query(sqlReg,[playerId]);

    let phoneNum = '';
    
    if(resultReg[0].countrycode  != 86){
      phoneNum = resultReg[0].countrycode + resultReg[0].username;
    }else{
      phoneNum = resultReg[0].username;
    }
    phoneNum = phoneNum + '_t';


    let resignCount = await this.app.redis.get("registRedis").get(phoneNum);

    return {
      playerId: uid,
      headIcon: wrapHeadIcon(baseResult.headicon),
      nickName: baseResult.nickname,
      createTime: moment(baseResult.createtime).format("YYYY-MM-DD HH:mm:ss"),
      loginTime: moment(baseResult.logintime).format("YYYY-MM-DD HH:mm:ss"),
      scorce: baseResult.scorce,
      win: baseResult.win,
      lose: baseResult.lose,
      money: rechargeAmount,
      wolfWinRate: `${baseResult.wolfWinRate}%`,
      goodWinRate: `${baseResult.goodWinRate}%`,
      groupName: groupName,
      groupTime: groupTime,
      giftBuffTime,
      tlevel: levelResult,
      phone: phone,
      gameTotal: baseResult.wins + baseResult.loss,
      udid: baseResult.udid,
      changePhone: changePhone,
      udidBan: udidBan,
      isInnerUser: isInnerUser,
      overseaWhiteList: overseaWhiteList.length == 0 ? 0 : 1,
      resignCount:resignCount
    };
  }

  /**
   * 查询用户是否被封禁及原因
   * @param playerId 用户ID
   */
  private async playerState(playerId: number) {
    const { app } = this;
    const werewolf = app.mysql.get("werewolf");
    const uid = werewolf.escape(playerId);

    //1.是否被封禁及原因
    const banSql =
      "SELECT tuser_ban.`desc` FROM " +
      "tuser LEFT JOIN tuser_ban ON tuser.`no` = tuser_ban.user_no " +
      "WHERE tuser.`no` = " +
      uid +
      " AND tuser_ban.count = 0 AND tuser.delsign = 1 ORDER BY id DESC LIMIT 1";
    const banResult = await werewolf.query(banSql);
    if (!!banResult && banResult.length > 0) {
      return {
        state: playerStatusEnum.banned,
        stateMsg: "封号中",
        reason: banResult[0].desc
      };
    }

    const banSql1 = "SELECT delsign FROM tuser WHERE `no` = " + uid;
    const banResult1 = await werewolf.query(banSql1);
    if (!!banResult1 && banResult1.length > 0) {
      if (banResult1[0].delsign == 1) {
        return {
          state: playerStatusEnum.banned,
          stateMsg: "封号中",
          reason: "原因未知，请联系程序"
        };
      }
    }

    //2.被举报关禁闭
    let shutterSql =
      "SELECT * FROM tuser_imprison WHERE user_no = " +
      uid +
      " AND state = 1 AND videoreport_id < 0 AND time >= DATE_SUB(NOW(), INTERVAL 1 DAY) AND type NOT IN (11,12,13,21,22,23)";
    let shutterResult = await werewolf.query(shutterSql);
    if (!!shutterResult && shutterResult.length >= 2) {
      //首先判断是否有客服手动关禁闭
      const manager = app.mysql.get("manager");
      const desc = await manager.select("wf_user_ban", {
        where: { user_id: uid, type: 0, operation: 1, delsign: 0 },
        columns: ["desc"],
        limit: 1
      });
      if (!!desc && desc.length > 0) {
        return {
          state: playerStatusEnum.shutter,
          stateMsg: "禁闭中",
          reason: desc[0].desc
        };
      } else {
        return {
          state: playerStatusEnum.shutter,
          stateMsg: "禁闭中",
          reason: "其他渠道关禁闭，具体原因请联系程序"
        };
      }
    }

    //修改判定逻辑
    const dealShutterSql = "SELECT type,release_time FROM tuser_imprison_deal WHERE user_no = " + uid + " AND release_time > NOW() ORDER BY release_time DESC;";
    const dealShutterResult = await werewolf.query(dealShutterSql);
    if (!!dealShutterResult && dealShutterResult.length > 0) {
      let releaseTime = moment(dealShutterResult[0].release_time).format("YYYY-MM-DD HH:mm:ss");
      let nowTime = moment(new Date()).format("YYYY-MM-DD HH:mm:ss");
      let diff = moment(releaseTime).diff(moment(nowTime), 'hours');
      let unit = "小时";
      if (diff >= 24) {
        diff = moment(releaseTime).diff(moment(nowTime), 'days');
        unit = "天"
      }
      let stateMsg = "禁闭中"
      if (dealShutterResult[0].type == 12 || dealShutterResult[0].type == 15 || dealShutterResult[0].type == 16) {
        stateMsg = "封禁功能中"
      }

      return {
        state: playerStatusEnum.shutterNew,
        stateMsg: stateMsg,
        reason: "距离解封还有" + diff + unit
      }
    }
    return {
      state: playerStatusEnum.normal,
      stateMsg: "正常",
      reason: ""
    };
  }

  /**
   * 用户状态查询基础信息
   * @param request
   */
  public async default(
    request: IplayerStatusRequest
  ): Promise<IplayerStatusResponse> {
    const { app } = this;
    const playerInfo = await this.playerInfo(request.playerId);
    //不存在该玩家
    if (!playerInfo) return null as any;
    const playerState = await this.playerState(request.playerId);
    return {
      ...playerInfo,
      ...playerState
    };
  }

  /**
   * 查询用户刷分记录
   * @param request
   */
  public async brushScore(
    request: IbrushScoreRequest
  ): Promise<IbrushScoreResponse> {
    const { app } = this;
    const werewolf = app.mysql.get("werewolf");
    const uid = werewolf.escape(request.playerId);
    const start = werewolf.escape(request.start);
    const offset = werewolf.escape(request.offset);
    let count = 0;
    const countSql =
      "SELECT COUNT(*) AS total FROM tuser_ban WHERE user_no = " +
      uid +
      " AND count != 0";
    const countRusult = await werewolf.query(countSql);
    if (!!countRusult && countRusult.length > 0) {
      count = countRusult[0].total;
    }
    const sql =
      "SELECT * FROM tuser_ban WHERE count != 0 AND user_no = " +
      uid +
      " LIMIT " +
      start +
      "," +
      offset;
    const result = await werewolf.query(sql);
    if (!!result && result.length > 0) {
      let array = new Array();
      for (let item of result) {
        array.push({
          count: item.count,
          desc: item.desc,
          time: moment(item.time).format("YYYY-MM-DD HH:mm:ss")
        });
      }
      return {
        ...request,
        count: count,
        dataArray: array
      };
    }
    return {
      ...request,
      count: 0,
      dataArray: []
    };
  }

  /**
   * @name: 查询新的刷分
   * @msg:
   * @param {type}
   * @return:
   */
  public async newBrushScore(request: InewBrushScoreRequest): Promise<InewBrushScoreResponse> {
    const { app } = this;
    const werewolf = app.mysql.get("werewolf");
    const uid = werewolf.escape(request.playerId);
    let array: InewBrushScore[] = new Array();
    let gameIds = "";
    const sql = `SELECT game_list, createtime,user_little FROM user_cheat_game WHERE user_no = ${uid} AND createtime > DATE_SUB(NOW(),INTERVAL 30 DAY);`;
    const result = await werewolf.query(sql);
    if (!!result && result.length > 0) {
      for (let item of result) {
        if (!item.game_list) {
          continue;
        }
        const gameSql = `SELECT tugr.game_no, tugr.seat_index, tgr.starttime, tgr.endtime, r.name 
          FROM tusergamerecord tugr, tgamerecord tgr, animation_role r 
          WHERE tugr.user_no = ${uid}  AND game_no IN (${item.game_list}) AND tugr.role_no = r.role AND tgr.no = tugr.game_no;`;
        const gameResult = await werewolf.query(gameSql);
        if (!!gameResult && gameResult.length > 0) {
          for (let value of gameResult) {
            array.push({
              gameNo: value.game_no,
              seat: value.seat_index,
              role: value.name,
              starttime: moment(value.starttime).format("YYYY-MM-DD HH:mm:ss"),
              endtime: moment(value.endtime).format("YYYY-MM-DD HH:mm:ss"),
              reviewtime: moment(item.createtime).format("YYYY-MM-DD HH:mm:ss"),
              user_little: item.user_little,
            });
          }
        }
      }
    }
    return {
      ...request,
      dataArray: array
    }
  }

  /**
   * 用户状态逃跑记录
   * @param request
   */
  public async escape(request: IescapeRequest): Promise<IescapeResponse> {
    const { app } = this;
    const werewolf = app.mysql.get("werewolf");
    const uid = werewolf.escape(request.playerId);
    const escapeSql =
      "SELECT t.*,r.`name`,g.starttime,g.endtime FROM tusergamerecord AS t " +
      "LEFT JOIN tgamerecord AS g ON t.game_no = g.`no` " +
      "LEFT JOIN animation_role r ON t.role_no = r.role " +
      "WHERE t.user_no = " +
      uid +
      " AND t.`escape` = 1 AND g.endtime > DATE_SUB(NOW(), INTERVAL 6 DAY)";
    const escapeResult = await werewolf.query(escapeSql);
    let camp = "暂无";
    let life = "";
    let win = "";
    let award = "";
    if (!!escapeResult && escapeResult.length > 0) {
      let array: Iescape[] = new Array();
      for (let item of escapeResult) {
        camp = GetCampName(item.camp_no);
        life = GetGameLifeDesc(item.life);
        win = GetGameWinDesc(item.win);
        if (item.award == 1) {
          award = "是";
        } else {
          award = "否";
        }

        array.push({
          seat: item.seat_index,
          camp: camp,
          role: item.name,
          life: life,
          win: win,
          award: award,
          starttime: moment(item.starttime).format("YYYY-MM-DD HH:mm:ss"),
          endtime: moment(item.endtime).format("YYYY-MM-DD HH:mm:ss")
        });
      }
      return {
        count: escapeResult.length,
        start: request.start,
        playerId: request.playerId,
        uid: request.uid,
        offset: request.offset,
        dataArray: array
      };
    }
    return {
      count: 0,
      start: request.start,
      playerId: request.playerId,
      uid: request.uid,
      offset: request.offset,
      dataArray: []
    };
  }

  /**
   * 获取被封禁详细信息
   * @param type 举报类型
   * @param time 封禁时间
   */
  private async getSpeak(uid: number, type: number, time: number) {
    const { app } = this;
    const werewolf = app.mysql.get("werewolf");
    const shutterSql =
      "SELECT realtime.time AS REPORT_TIME, type.content AS `REPORT_TYPE`,audio.detail AS REASON,report.createtime AS EFFECT_TIME,audio.content AS CONTENT,video.url AS VIDEO_URL " +
      "FROM werewolf.tuser_imprison AS report " +
      "LEFT JOIN werewolf.treport_type AS type ON (report.type = type.`level`) " +
      "LEFT JOIN werewolf.treportvideo AS video ON (report.videoreport_id = video.id) " +
      "LEFT JOIN werewolf.taudiotransfer AS audio ON (report.videoreport_id = audio.reportvideo_id AND report.user_no = audio.user_no) " +
      "LEFT JOIN werewolf.trealtime_report AS realtime ON (report.user_no = realtime.user_no AND video.gameno = realtime.gamerecord_no) " +
      "WHERE report.user_no = " +
      uid +
      " AND report.state = 1 AND report.videoreport_id > 0 AND report.type = " +
      type +
      " AND report.time >= DATE_SUB(NOW(), INTERVAL " +
      time +
      " DAY) " +
      "GROUP BY report.videoreport_id ORDER BY REPORT_TIME DESC";
    const shutterResult = await werewolf.query(shutterSql);
    if (!!shutterResult && shutterResult.length > 0) {
      let array = new Array();
      for (let item of shutterResult) {
        let ossUrl = ""
        if (item.VIDEO_URL) {
          const url = item.VIDEO_URL.replace("zermatt", "");
          ossUrl = `http://video.53site.com${url}`
        }

        array.push({
          reportTime: moment(item.REPORT_TIME).format("YYYY-MM-DD HH:mm:ss"),
          reportType: item.REPORT_TYPE,
          shutterReason: item.REASON,
          effectStartTime: moment(item.EFFECT_TIME).format(
            "YYYY-MM-DD HH:mm:ss"
          ),
          effectEndTime: '请看禁闭详情',
          shutterDay: 0,
          content: item.CONTENT,
          ossUrl: ossUrl
        });
      }
      return array;
    }
    return [];
  }

  /**
   * 用户状态游戏中发言记录
   * @param request
   */
  public async gameSpeak(
    request: IgameSpeakRequest
  ): Promise<IgameSpeakResponse> {
    const { app } = this;
    const werewolf = app.mysql.get("werewolf");
    const uid = werewolf.escape(request.playerId);
    //3天内被多人举报,超过6次发言为场外,贴脸,互通身份,封禁3天
    let shutterSql =
      "SELECT MIN(r.time) AS time, COUNT(*) AS total FROM " +
      "( SELECT a.time FROM `tuser_imprison` a, `treport_type` b WHERE a.type = b.id AND b.`level` = 2 AND a.user_no = " +
      uid +
      " AND a.state = 1 AND a.videoreport_id > 0 AND a.time >= DATE_SUB(NOW(), INTERVAL 3 DAY) ORDER BY time DESC LIMIT 6) r";
    let shutterResult = await werewolf.query(shutterSql);
    if (
      !!shutterResult &&
      shutterResult[0].total > 0 &&
      shutterResult[0].time > 0
    ) {
      const result = await this.getSpeak(uid, 2, 3);
      return {
        count: shutterResult[0].total,
        start: request.start,
        playerId: request.playerId,
        uid: request.uid,
        offset: request.offset,
        dataArray: result
      };
    }
    //2天内被多人举报,超过4次发言为场外,贴脸,互通身份,封禁2天
    shutterSql =
      "SELECT MIN(r.time) AS time, COUNT(*) AS total FROM " +
      "( SELECT a.time FROM `tuser_imprison` a, `treport_type` b WHERE a.type = b.id AND b.`level` = 2 AND a.user_no = " +
      uid +
      " AND a.state = 1 AND a.videoreport_id > 0 AND a.time >= DATE_SUB(NOW(), INTERVAL 2 DAY) ORDER BY time DESC LIMIT 4) r";
    shutterResult = await werewolf.query(shutterSql);
    if (
      !!shutterResult &&
      shutterResult[0].total > 0 &&
      shutterResult[0].time > 0
    ) {
      const result = await this.getSpeak(uid, 2, 2);
      return {
        count: shutterResult[0].total,
        start: request.start,
        playerId: request.playerId,
        uid: request.uid,
        offset: request.offset,
        dataArray: result
      };
    }
    //2天内被多人举报,原因为辱骂,人身攻击,言语轻浮,裸露,照镜子,封禁1天
    shutterSql =
      "SELECT MIN(r.time) AS time, COUNT(*) AS total FROM " +
      "( SELECT a.time FROM `tuser_imprison` a, `treport_type` b WHERE a.type = b.id AND b.`level` = 3 AND a.user_no = " +
      uid +
      " AND a.state = 1 AND a.videoreport_id > 0 AND a.time >= DATE_SUB(NOW(), INTERVAL 1 DAY) ORDER BY time DESC LIMIT 2) r";
    shutterResult = await werewolf.query(shutterSql);
    if (
      !!shutterResult &&
      shutterResult[0].total > 0 &&
      shutterResult[0].time > 0
    ) {
      const result = await this.getSpeak(uid, 3, 1);
      return {
        count: shutterResult[0].total,
        start: request.start,
        playerId: request.playerId,
        uid: request.uid,
        offset: request.offset,
        dataArray: result
      };
    }
    //2天内被多人举报,超过2次发言为场外,贴脸,互通身份,封禁1天
    shutterSql =
      "SELECT MIN(r.time) AS time, COUNT(*) AS total FROM " +
      "( SELECT a.time FROM `tuser_imprison` a, `treport_type` b WHERE a.type = b.id AND b.`level` = 2 AND a.user_no = " +
      uid +
      " AND a.state = 1 AND a.videoreport_id > 0 AND a.time >= DATE_SUB(NOW(), INTERVAL 1 DAY) ORDER BY time DESC LIMIT 2) r";
    shutterResult = await werewolf.query(shutterSql);
    if (
      !!shutterResult &&
      shutterResult[0].total > 0 &&
      shutterResult[0].time > 0
    ) {
      const result = await this.getSpeak(uid, 2, 1);
      return {
        count: shutterResult[0].total,
        start: request.start,
        playerId: request.playerId,
        uid: request.uid,
        offset: request.offset,
        dataArray: result
      };
    }

    return {
      count: 0,
      start: request.start,
      playerId: request.playerId,
      uid: request.uid,
      offset: request.offset,
      dataArray: []
    };
  }

  /**
   * 查询用户背景图
   * @param request
   */
  public async playerBg(request: IplayerBgRequest): Promise<IplayerBgResponse> {
    const { app } = this;
    const werewolf = app.mysql.get("werewolf");
    const uid = werewolf.escape(request.playerId);
    const start = werewolf.escape(request.start);
    const offset = werewolf.escape(request.offset);
    let count = 0;
    let bgSql =
      "SELECT COUNT(*) AS total " +
      "FROM animation " +
      "LEFT JOIN user_animation ON animation.id = user_animation.animation_id " +
      "LEFT JOIN user_animation_state ON animation.id = user_animation_state.animation_id AND user_animation.user_id= user_animation_state.user_id " +
      "LEFT JOIN report_bg rb ON user_animation.animation_id = rb.an_id " +
      "WHERE animation.bg = 2 AND user_animation.user_id = " +
      uid;
    let bgResult = await werewolf.query(bgSql);
    if (!!bgResult && bgResult.length > 0) {
      count = bgResult[0].total;
    }
    bgSql =
      "SELECT animation.id, " +
      "IF ( animation.datePath IS NULL, " +
      "CONCAT('http://img.53site.com/Werewolf/bg/animation_bg_',animation.id,'.jpg'), " +
      "CONCAT('http://img.53site.com/Werewolf/bg/',animation.datePath,'/animation_bg_',animation.id,'.jpg')) AS img, " +
      "user_animation_state.id AS uasid," +
      "user_animation.delsign," +
      "rb.id AS rbid " +
      "FROM animation " +
      "LEFT JOIN user_animation ON animation.id = user_animation.animation_id " +
      "LEFT JOIN user_animation_state ON animation.id = user_animation_state.animation_id AND user_animation.user_id= user_animation_state.user_id " +
      "LEFT JOIN report_bg rb ON user_animation.animation_id = rb.an_id " +
      "WHERE animation.bg = 2 AND user_animation.user_id = " +
      uid +
      " LIMIT " +
      start +
      "," +
      offset;
    bgResult = await werewolf.query(bgSql);
    if (!!bgResult && bgResult.length > 0) {
      let array = new Array();
      for (let item of bgResult) {
        array.push({
          id: item.id,
          path: item.img,
          uasId: item.uasid,
          delsign: item.delsign,
          rbId: item.rbid
        });
      }
      return {
        ...request,
        count: count,
        dataArray: array
      };
    }
    return {
      ...request,
      count: count,
      dataArray: []
    };
  }


  /**
   * 娱乐模式：查询房间id
   * @param request
   */
  public async entertainmentSearchReportRoom(request: IentertainmentSearchRoomRequest) {
    const { app } = this;

    const id = request.showId;

    try {

      const db = app.mysql.get("werewolf");
      const mongo = app['mongo'];

      const roomInfo = await mongo.findOne('room_fun_info', {
        query: {
          showId: id.toString(),
        },
      });

      if (!mongo) {
        return 'mongo not exist!'
      }

      if (roomInfo === null) {
        return `can not find showId:${request.showId}!`;
      }

      const sql = `SELECT COUNT(*) reportCount
                 FROM fun_room_report r
                 WHERE r.fun_room_id = ? AND type = 1`;
      const reportList = await db.query(sql, [roomInfo.fi]);
      const report = reportList.length > 0 ? reportList[0] : null;

      return { ...roomInfo, reportCount: report.reportCount };

    } catch (error) {
      throw error;
    }
  }


  /**
   * 娱乐模式：房间号 查聊天记录
   * @param request
   */
  public async entertainmentSearchGroupRooms(request: IentertainmentSearchGroupRoomsRequest) {
    const { app } = this;

    const db = app.mysql.get('werewolf');
    const mongo = app['mongo'];

    const id = request.groupId;


    try {

      const rooms = await mongo.find('room_fun_info', {
        query: {
          groupId: id,
        },
        sort: {
          createDate: -1,
        },
        projection: {
          _id: 0,
          fi: 1,
          name: 1,
          showId: 1,
          pwd: 1,
          desc: 1,
        },
      });

      if (rooms.length < 1) {
        return null;
      }

      const ids = rooms.map((v) => v.fi);
      const idStr = ids.join(',');

      const sql = `SELECT r.fun_room_id,
                          COUNT(*) reportCount
                   FROM fun_room_report r
                   WHERE r.fun_room_id in (${idStr})
                   GROUP BY r.fun_room_id`;

      const reportList = await db.query(sql);
      const map = {};
      for (const { fun_room_id, reportCount } of reportList) {
        map[fun_room_id] = reportCount;
      }

      for (const v of rooms) {
        const key = v.fi;
        v['reportCount'] = map[key];
      }

      return rooms;

    } catch (error) {
      throw error;
    }
  }

  /**
   * 娱乐模式：房间号 查聊天记录
   * @param request
   */
  public async entertainmentSearchRoomChats(request: IentertainmentSearchRoomChatsRequest) {
    const { app } = this;

    const db = app.mysql.get("werewolf");

    const id = request.roomId;

    const sql = `SELECT u.\`no\`,
                        u.nickname,
                        r.content,
                        DATE_FORMAT(r.createtime, '%Y-%m-%d %H:%i:%s') createTime
                 FROM tuser u,
                      fun_room_user_chat_log r
                 WHERE r.room_id = ?
                   AND r.\`user_id\` = u.\`no\`
                 ORDER BY r.createtime DESC`;

    try {

      return await db.query(sql, [id]);

    } catch (error) {
      throw error;
    }
  }

  /**
   * 娱乐模式：房间号 查举报记录
   * @param request
   */
  public async entertainmentSearchRoomReports(request: IentertainmentSearchRoomReportsRequest) {
    const { app } = this;

    const db = app.mysql.get("werewolf");

    const id = request.roomId;

    const sql = `SELECT u.\`no\`,
                        u.nickname,
                        r.note                                          reason,
                        DATE_FORMAT(r.create_time, '%Y-%m-%d %H:%i:%s') createTime
                 FROM tuser u,
                      fun_room_report r
                 WHERE r.fun_room_id = ?
                   AND r.\`report_user_no\` = u.\`no\`
                   AND r.type = 1
                 ORDER BY r.create_time DESC`;

    try {

      return await db.query(sql, [id]);

    } catch (error) {
      throw error;
    }
  }

  /**
   * 娱乐模式：房间号 查users
   * @param request
   */
  public async entertainmentSearchRoomUsers(request: IentertainmentSearchRoomUsersRequest) {
    const { app } = this;

    const id = request.roomId;
    // const uid = request.uid;

    const db = app.mysql.get("werewolf");
    const mongo = app['mongo'];

    try {

      const users = await mongo.aggregate('room_fun_user', {
        pipeline: [
          {
            $match: {
              fi: id,
            }
          },
          {
            $project: {
              _id: 0,
              no: '$ui',
              nickname: '$nickname',
            }
          }
        ]
      });

      const ids = users.map((v) => v.no);
      if (ids.length < 1) {
        return [];
      }
      const v0 = ids.join(',');
      const sql = `SELECT r.user_id,
                          COUNT(*) reportCount
                   FROM fun_room_report r
                   WHERE r.user_id IN (${v0})
                   GROUP BY r.user_id
                   /*ORDER BY FIELD(r.user_id,${v0})*/`
      const reports = await db.query(sql);

      let map = {}
      for (const { user_id, reportCount } of reports) {
        map[user_id] = reportCount;
      }

      for (let u of users) {
        const key = u['no'];
        u['reportCount'] = map[key]
      }

      return users;

    } catch (error) {
      throw error;
    }
  }

  /**
   * 娱乐模式：查询用户id
   * @param request
   */
  public async entertainmentSearchReportUser(request: IentertainmentSearchUserRequest) {
    const { app } = this;

    const db = app.mysql.get("werewolf");
    // const managerDb = app.mysql.get("manager");

    const playerId = request.playerId;
    // const uid = request.uid;

    const sql = `SELECT u.\`no\`,
                        u.nickname,
                        COUNT(*) reportCount
                 FROM tuser u,
                      fun_room_report r
                 WHERE u.no = ?
                   AND u.\`no\` = r.user_id
                 GROUP BY u.\`no\``;

    try {

      return await db.query(sql, [playerId]);

    } catch (error) {
      throw error;
    }
  }

  /**
   * 娱乐模式：查询用户id 详情
   * @param request
   */
  public async entertainmentSearchReportUserDetail(request: IentertainmentSearchUserDetailRequest) {
    const { app } = this;

    const db = app.mysql.get("werewolf");
    // const managerDb = app.mysql.get("manager");

    const playerId = request.playerId;
    // const uid = request.uid;

    const sql = `SELECT u.\`no\`,
                        u.nickname,
                        r.note                                          reason,
                        DATE_FORMAT(r.create_time, '%Y-%m-%d %H:%i:%s') createTime
                 FROM tuser u,
                      fun_room_report r
                 WHERE r.user_id = ?
                   AND r.\`report_user_no\` = u.\`no\`
                 ORDER BY r.create_time DESC`;

    try {

      return await db.query(sql, [playerId]);

    } catch (error) {
      throw error;
    }
  }

  public async entertainmentKickOutRoom(request: IentertainmentKickOutRoomRequest) {
    const { app, ctx } = this;

    const fi = request.roomId;
    const uid = request.uid;


    const mongo = app['mongo'];

    try {

      // const  resp = await mongo.find('room_fun_user',{
      //   query:{
      //     fi:fi,
      //   },
      // });

      const room = await mongo.findOne('room_fun_info', {
        query: {
          fi: fi,
        },
      });


      const ownerMap = room['owner'];

      let owner = Object.keys(ownerMap)[0]
      // for (const k in ownerMap){
      //   if (ownerMap[k] === true) {
      //     owner=k;
      //     break;
      //   }
      // }

      // return {fi:fi,userId:owner};

      const resp = await ctx.service.werewolf.exchangeRpc.gatePost('room/fun/check/kick', { fi: fi, userId: owner });

      return resp;

    } catch (error) {

      throw error;
    }
  }

  /**
   * 用户状态操作封娱乐模式
   * @param request
   */
  public async doBanEntertainment(request: IbanEntertainmentRequest) {
    const { app } = this;

    const db = app.mysql.get("werewolf");
    const managerDb = app.mysql.get("manager");

    const playerId = request.playerId;
    const uid = request.uid;
    const reason = request.reason;


    const transaction = await db.beginTransaction();
    const managerTransaction = await managerDb.beginTransaction();

    try {


      const sql =
        `INSERT INTO tuser_imprison_deal (\`user_no\`, \`type\`, \`release_time\`)
           VALUES (?, ?, DATE_ADD(NOW(), INTERVAL 2 YEAR)) 
           ON DUPLICATE KEY UPDATE \`release_time\` = DATE_ADD(NOW(), INTERVAL 2 YEAR)`;

      await db.query(sql, [playerId, 21]);

      await managerDb.insert("wf_user_ban", {
        admin_id: uid,
        user_id: playerId,
        type: 1,
        category: 0,
        operation: 1,
        desc: reason
      });

      await transaction.commit();
      await managerTransaction.commit();

    } catch (error) {

      await transaction.rollback();
      await managerTransaction.rollback();

      throw error;
    }
  }

  /**
   * 用户状态操作封号
   * @param request
   */
  public async doBanned(request: IbannedPlayerRequest) {
    const { app } = this;
    const werewolf = app.mysql.get("werewolf");
    const manager = app.mysql.get("manager");

    const werewolfConn = await werewolf.beginTransaction();
    const managerConn = await manager.beginTransaction();
    try {
      //werewolf
      const wereRow = {
        delsign: 1,
        logintime: moment(new Date()).format("YYYY-MM-DD HH:mm:ss")
      };
      const wereOptions = {
        where: {
          no: request.playerId
        }
      };
      await werewolfConn.update("tuser", wereRow, wereOptions);
      await werewolfConn.insert("tuser_ban", {
        user_no: request.playerId,
        count: 0,
        desc: request.reason,
        time: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
        ban_id: request.banId,
      });
      //manager
      await managerConn.insert("wf_user_ban", {
        admin_id: request.uid,
        user_id: request.playerId,
        type: 1,
        category: 0,
        operation: 1,
        desc: request.reason
      });

      await werewolfConn.commit();
      await managerConn.commit();
      await this.app.redis.get("liveDanmaku").hset("live_user_login_time", request.playerId, moment(new Date()).format("YYYY-MM-DD HH:mm:ss"))

    } catch (error) {
      await werewolfConn.rollback();
      await managerConn.rollback();
      throw error;
    }
  }

  public async getUserBanId(request: IliftShutterRequest): Promise<any> {

    const { app } = this;
    const db = app.mysql.get('werewolf');
    try {
      let sql = ` 
			SELECT * FROM tuser_ban WHERE user_no = ? ORDER BY time DESC LIMIT 1
		 ` ;
      const results = await db.query(sql, [request.playerId]);
      let banId: number = -1;
      if (results.length > 0 && results[0] != undefined && results[0] != null && results[0].ban_id != undefined) {
        banId = results[0].ban_id;
      }

      return { banId: banId };

    } catch (error) {
      throw error;
    }
  }

  public async dobarrage(request: IshutterPlayerRequest) {
    const { app } = this;

    //type : 1 => 30分钟 2 => 24小时 3 => 永久
    const wfDb = app.mysql.get("werewolf");
    const playerId = wfDb.escape(request.playerId);
    const type = wfDb.escape(request.type);
    const reason = wfDb.escape(request.reason);
    let sqlstr = "";
    if (type == 1) {
      sqlstr = `INSERT INTO danmaku_ban (user_id, endtime,reason,ban_type)
        VALUES
          (
            ${playerId},
            DATE_SUB(NOW(), INTERVAL -30 MINUTE),
            ${reason},
            ${type}
          ) ON DUPLICATE KEY UPDATE endtime = DATE_SUB(NOW(), INTERVAL -30 MINUTE),forever = 0,delsign = 0,reason=${reason},ban_type=${type};`;
    } else if (type == 2) {
      sqlstr = `INSERT INTO danmaku_ban (user_id, endtime,reason,ban_type)
      VALUES
        (
          ${playerId},
          DATE_SUB(NOW(), INTERVAL 1 DAY),
          ${reason},
          ${type}
        ) ON DUPLICATE KEY UPDATE endtime = DATE_SUB(NOW(), INTERVAL -1 DAY),forever = 0,delsign = 0,reason=${reason},ban_type=${type};`;
    } else {
      sqlstr = `INSERT INTO danmaku_ban (user_id, forever,reason,ban_type)
      VALUES
        (
          ${playerId},
          1,
          ${reason},
          ${type}
        ) ON DUPLICATE KEY UPDATE forever = 1,reason=${reason},ban_type=${type};`;
    }
    try {
      await wfDb.query(sqlstr);
    } catch (error) {
      throw error;
    }
  }
  public async selUserUdid(request: IbarragePlayerRequest) {
    const { app } = this;
    const db = app.mysql.get("werewolf");
    const playerId = db.escape(request.playerId);
    try {
      const sqlstr = `SELECT udid FROM tuser WHERE no = ${playerId}`
      const results = await db.query(sqlstr, []);
      return results[0]['udid'];
    } catch (error) {

      throw error;
    }
  }
  public async selUdidState(udid: any) {
    const { app } = this;
    const db = app.mysql.get("werewolf");

    try {
      //SELECT udid FROM tuser WHERE no = ${playerId}
      const sqlstr = `SELECT COUNT(*) AS total FROM user_ban_udid WHERE udid = '${udid}'`
      const results = await db.query(sqlstr, []);
      return results[0]['total'];
    } catch (error) {

      throw error;
    }
  }
  public async doMobile(udid: any) {
    const { app } = this;
    const db = app.mysql.get("werewolf");
    try {
      // const sqlstr = `SELECT udid FROM tuser WHERE no = ${playerId}`
      const sqlstr = ` insert into user_ban_udid(udid) VALUES ('${udid}')`
      await db.query(sqlstr, []);

    } catch (error) {
      throw error;
    }
  }

  public async doShutter(request: IshutterPlayerRequest) {
    const { app } = this;
    const werewolf = app.mysql.get("werewolf");
    const manager = app.mysql.get("manager");

    const playerId = werewolf.escape(request.playerId);
    const type = werewolf.escape(request.type);
    const day = werewolf.escape(request.day);

    const werewolfConn = await werewolf.beginTransaction();
    const managerConn = await manager.beginTransaction();
    try {
      const sql =
        "INSERT INTO tuser_imprison_deal (`user_no`,`type`,`release_time`) VALUES (" +
        playerId +
        "," +
        type +
        ", DATE_ADD(NOW(),INTERVAL " +
        day +
        " DAY)) ON DUPLICATE KEY UPDATE release_time = DATE_ADD(NOW(),INTERVAL " +
        day +
        " DAY)";
      await werewolfConn.query(sql);
      await managerConn.insert("wf_user_ban", {
        admin_id: request.uid,
        user_id: request.playerId,
        type: 0,//紧闭区
        category: request.type,//1:发言贴脸2天2条封1天;2:发言贴脸2天4条封2天;
        operation: 1,
        desc: request.reason
      });

      await werewolfConn.commit();
      await managerConn.commit();
    } catch (error) {
      await werewolfConn.rollback();
      await managerConn.rollback();
      throw error;
    }
  }

  public async bannedBg(request: IbannedBgRequest): Promise<IbannedBgResponse> {
    const { app } = this;
    const werewolf = app.mysql.get("werewolf");
    const manager = app.mysql.get("manager");

    const animationId = werewolf.escape(request.animationId);

    const werewolfConn = await werewolf.beginTransaction();
    const managerConn = await manager.beginTransaction();
    try {
      //1. user_animation的delsign=1
      await werewolfConn.update("user_animation", { delsign: 1 }, {
        where: {
          user_id: request.playerId, animation_id: request.animationId
        }
      }
      );

      //2 查询使用中
      const uaAndUasSql =
        ` SELECT
      uas.id AS uasid,
        uas.role_id
      FROM
      user_animation ua
      LEFT JOIN user_animation_state uas ON ua.animation_id = uas.animation_id
      WHERE
      uas.user_id = ${request.playerId}
      AND	ua.animation_id = ${request.animationId}
      `;
      const uaAndUasResult = await werewolfConn.query(uaAndUasSql);
      //3 存在使用中 user_animation_state的animation_id=0
      if (!!uaAndUasResult && uaAndUasResult.length > 0 && uaAndUasResult[0].role_id == 200) {
        const uasid = uaAndUasResult[0].uasid;
        await werewolfConn.update(
          "user_animation_state", { animation_id: 0 }, {
          where: {
            id: uasid
          }
        }
        );
      }

      //4 更新举报
      await werewolfConn.insert("report_bg", {
        user_id: request.playerId,
        game_id: 0,
        an_id: request.animationId
      });
      //5 更新禁闭
      await managerConn.insert("wf_user_ban", {
        admin_id: request.uid,
        user_id: request.playerId,
        type: 2,
        category: 1,
        operation: 1,
        desc: "背景图违规"
      });

      await werewolfConn.commit();
      await managerConn.commit();
      return {
        animationId: request.animationId,
        playerId: request.playerId
      };

    } catch (error) {
      await werewolfConn.rollback();
      await managerConn.rollback();
      throw error;
    }
  }

  public async remove(
    request: IremoveBannedRequest
  ): Promise<IremoveBannedResponse> {
    const { app } = this;
    const werewolf = app.mysql.get("werewolf");
    const manager = app.mysql.get("manager");

    const playerId = werewolf.escape(request.playerId);
    let dingNo = werewolf.escape(request.dingNo) + "";
    //如果没有钉钉编号，查询是否存在解封记录
    if (!dingNo || dingNo == werewolf.escape('0')) {
      let sql =
        "SELECT COUNT(*) AS total FROM wf_user_ban WHERE user_id = " + playerId + " AND operation = 0 AND createtime > '2019-06-11'";
      let result = await manager.query(sql);
      console.info("result", result);
      let count = 0;
      if (!!result && result.length > 0) {
        count = result[0].total;
      }
      if (count > 0) {
        return {
          state: request.state,
          playerId: request.playerId,
          stateMsg: "异常",
          reason: "解封状态异常",
          err_msg: "已有解除封禁记录，请提交钉钉",
          removeStatus: 2
        };
      }
    }
    const werewolfConn = await werewolf.beginTransaction();
    const managerConn = await manager.beginTransaction();

    try {
      let playerState = await this.playerState(request.playerId);
      //console.log(playerState.state);
      if (playerState.state == request.state) {
        //清楚封禁状态
        const bannedRow = {
          delsign: 0
        };

        const bannedOptions = {
          where: {
            no: request.playerId
          }
        };
        await werewolfConn.update("tuser", bannedRow, bannedOptions);
        //保存清除记录
        await managerConn.insert("wf_user_ban", {
          admin_id: request.uid,
          user_id: request.playerId,
          type: 1,
          category: 0,
          operation: 0,
          desc: "解除封号状态" + dingNo
        });

        await werewolfConn.commit();
        await managerConn.commit();
        playerState = await this.playerState(request.playerId);

        return {
          state: playerState.state,
          playerId: request.playerId,
          stateMsg: playerState.stateMsg,
          reason: playerState.reason,
          err_msg: "",
          removeStatus: 1
        };
      } else {
        await werewolfConn.rollback();
        await managerConn.rollback();
        return {
          state: request.state,
          playerId: request.playerId,
          stateMsg: "异常",
          reason: "当前状态异常",
          err_msg: "当前用户状态异常，请联系程序",
          removeStatus: 0
        };
      }
    } catch (error) {
      await werewolfConn.rollback();
      await managerConn.rollback();
      throw error;
    }
  }

  /**
   * @name:
   * @msg:查看举报记录
   * @param {type}
   * @return:
   */
  public async report(request: IreportRequest): Promise<IreportResponse> {
    const { app, ctx, logger } = this;
    const werewolf = app.mysql.get("werewolf");
    const playerId = werewolf.escape(request.playerId);
    // const start = werewolf.escape(request.start);
    // const offset = werewolf.escape(request.offset);
    let count = 0;
    let sql =
      "SELECT COUNT(*) AS total FROM trealtime_report tr LEFT JOIN treport_type tt ON tr.type = tt.`level` WHERE tr.user_no = " +
      playerId +
      " AND tr.time > '2018-01-01' ORDER BY tr.id DESC;";
    let result = await werewolf.query(sql);
    if (!!result && result.length > 0) {
      count = result[0].total;
    }
    //查询旧版被玩家举报
    sql =
      "SELECT tr.*,tt.content FROM trealtime_report tr LEFT JOIN treport_type tt ON tr.type = tt.`level` WHERE tr.user_no = " +
      playerId +
      " AND tr.time > '2018-01-01' ORDER BY tr.id DESC ";
    result = await werewolf.query(sql);
    let array: Ireport[] = new Array();
    if (!!result && result.length > 0) {
      for (let i = 0, len = result.length; i < len; i++) {
        let reportUser = result[i].report_user_no;
        if (reportUser == -1) {
          reportUser = "观战举报验证生效";
        }
        array.push({
          datetime: moment(result[i].time).format("YYYY-MM-DD HH:mm:ss"),
          type: result[i].content,
          complete: result[i].isComplete,
          gameNo: result[i].gamerecord_no,
          reportUser: reportUser,
          content: result[i].note,
          version: 1,
          videoUrl: "",
          keyWords: "",
        });
      }
    }
    //查询新版系统举报
    let systemArray: Ireport[] = await ctx.service.werewolf.report.systemReport(request.playerId)
    // logger.debug("系统扫描",systemArray);
    array = systemArray.concat(array);
    count = array.length;
    return {
      ...request,
      count: count,
      dataArray: array
    };
  }

  /**
   * 查看违规图片
   * @param request 请求参数
   */
  public async illegalImages(request: IillegalImagesRequest): Promise<IillegalImagesResponse> {
    const { app } = this;
    const werewolf = app.mysql.get("werewolf");
    const playerId = werewolf.escape(request.playerId);
    const start = werewolf.escape(request.start);
    const offset = werewolf.escape(request.offset);
    let count = 0;
    let sql =
      "SELECT COUNT(*) AS total FROM user_oss_detect_record WHERE user_no = " +
      playerId;
    let result = await werewolf.query(sql);
    if (!!result && result.length > 0) {
      count = result[0].total;
    }
    sql =
      //"SELECT CONCAT('http://headicon.53site.com/',img) AS url,createtime FROM user_oss_detect_record WHERE user_no = " +
      "SELECT img AS url, createtime, img_type, reject, deal_type FROM user_oss_detect_record WHERE user_no = " +
      playerId +
      " ORDER BY `no` DESC LIMIT " +
      start +
      "," +
      offset;
    result = await werewolf.query(sql);
    let client = new OSS({
      // region以杭州为例（oss-cn-hangzhou），其他region按实际情况填写。
      region: 'oss-cn-hangzhou',
      // 阿里云主账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM账号进行API访问或日常运维，请登录RAM控制台创建RAM账号。
      accessKeyId: "LTAI4FrMp2q7twEfV27orLvM",
      accessKeySecret: "******************************",
      bucket: "werewolf-headicon"
    });

    let array: IillegalImages[] = new Array();
    if (!!result && result.length > 0) {
      for (let item of result) {
        if (item.img_type == 0) {
          switch (item.reject) {
            case 'porn':
              item.reason = "头像色情"
              break;
            case 'terrorism':
              item.reason = "头像涉政暴恐"
              break;
            default:
              item.reason = "联系开发"
              break;
          }
        } else if (item.img_type == 1) {

          switch (item.reject) {
            case 'porn':
              item.reason = "背景色情"
              break;
            case 'terrorism':
              item.reason = "背景涉政暴恐"
              break;
            default:
              item.reason = "联系开发"
              break;
          }
        } else if (item.img_type == 2) {

          switch (item.deal_type) {
            case 5:
              item.reason = "头像贴脸"
              break;
            case 6:
              item.reason = "头像辱骂"
              break;
            default:
              item.reason = "联系开发"
              break;
          }
        } else if (item.img_type == 3) {

          switch (item.deal_type) {
            case 5:
              item.reason = "背景贴脸"
              break;
            case 6:
              item.reason = "背景辱骂"
              break;
            default:
              item.reason = "联系开发"
              break;
          }
        }
        await this.ctx.service.download.ossPng(item['url']);
        // item['url'] = "http://headicon.53site.com/" + item['url'];
        // const filename = 'bg/20190621/'+'animation_bg_145523.jpg' // 自定义文件名。
        // const url = client.signatureUrl(item['url']).replace('werewolf-headicon.oss-cn-hangzhou.aliyuncs.com', 'headicon.53site.com');
        const url = "http://headicon.53site.com/" + item['url'];
        array.push({
          url: url,
          createtime: item.createtime,
          resason: item.reason
        })

      }
      return {
        ...request,
        count: count,
        dataArray: array
      }
    }
    return {
      ...request,
      count: 0,
      dataArray: []
    }
  }

  /**
   * 查询被封禁闭的列表
   * @param request
   */
  public async getShutterList(request: IshutterRequest): Promise<IshutterResponse> {
    const { app, logger } = this;
    const werewolf = app.mysql.get("werewolf");
    const playerId = werewolf.escape(request.playerId);

    //查询禁闭区和功能区
    let sql =
      "SELECT a.type, a.release_time, b.`desc`, b.id,"
      + `IF(b.id = 1, (SELECT GROUP_CONCAT(an_id) FROM report_bg WHERE user_id = ${request.playerId}), "") as bgs,
        IF(b.id = 2, (SELECT GROUP_CONCAT(headicon) FROM report_avatar WHERE user_id = ${request.playerId}), "") as avatars, `
      + `IFNULL(
          (
            SELECT
            wf.\`desc\`
          FROM
            mega_manager.wf_user_ban AS wf
          WHERE
            wf.user_id = ${request.playerId}
          AND wf.type IN (0, 2)
          AND wf.operation = 1
          AND a.type = wf.category
          AND DATE_FORMAT(wf.createtime,'%T') =  DATE_FORMAT(a.release_time,'%T')
          LIMIT 1
        ),
        "系统扫描"
      ) AS customDesc`
      + " FROM tuser_imprison_deal AS a LEFT JOIN imprison_type AS b ON  a.type = b.id "
      + " WHERE a.user_no = " + playerId + " AND a.release_time > NOW()"
      + " ORDER BY a.`release_time` DESC ";
    // logger.debug(sql);
    let result = await werewolf.query(sql);

    result.forEach((element) => {
      let releaseTime = moment(element.release_time).format("YYYY-MM-DD HH:mm:ss");
      let nowTime = moment(new Date()).format("YYYY-MM-DD HH:mm:ss");
      let diff = moment(releaseTime).diff(moment(nowTime), 'hours');
      let unit = "小时";
      if (diff >= 24) {
        diff = moment(releaseTime).diff(moment(nowTime), 'days');
        unit = "天"
      }
      element.desc += diff + unit;
      element.isForever = 0;
    });

    //查询永封功能
    sql = "SELECT a.type, a.create_time AS release_time , b.`desc`, b.id, 1 AS isForever,	"
      + `IF(b.id=1,(SELECT GROUP_CONCAT(an_id) FROM report_bg WHERE user_id = ${request.playerId}),"") as bgs, 
         IF(b.id=2,(SELECT GROUP_CONCAT(headicon) FROM report_avatar WHERE user_id = ${request.playerId}),"") as avatars,`
      + `IFNULL(
      (
        SELECT
          wf.\`desc\`
        FROM
          mega_manager.wf_user_ban AS wf
        WHERE
          wf.user_id = ${request.playerId}
        AND wf.type = 3
        AND wf.operation = 1
        AND a.type = wf.category
        AND ABS((SELECT TIMESTAMPDIFF (SECOND,wf.createtime,a.create_time))) <5
        LIMIT 1
      ),
      "系统扫描"
    ) AS customDesc`
      + " FROM tuser_ban_deal AS a LEFT JOIN ban_type AS b ON a.type = b.id "
      + " WHERE a.user_no =  " + playerId
      + " AND  a.is_delsign = 0"
      + " ORDER BY a.`create_time` DESC";
    //  logger.debug(sql);
    let foreverResult = await werewolf.query(sql);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    let dataArray = [...result, ...foreverResult]
    //查询弹幕封禁
    let danSql = `SELECT * FROM danmaku_ban WHERE user_id = ${request.playerId};`;
    let danmuResult = await this.selectOne(danSql);

    if (danmuResult) {
      let endTime = moment(danmuResult.endtime);
      let curTime = moment(moment.now());
      let createTime = moment(danmuResult.createtime);
      //判断当前是否属于被封禁状态
      if (danmuResult.forever == 1 || endTime > curTime) {
        let desc = [`封禁弹幕功能30分钟：您所发的弹幕存在违规，距解封还有${endTime.diff(curTime, 'minute')}分钟`,
        `封禁弹幕功能24小时：您所发的弹幕存在违规，距解封还有${endTime.diff(curTime, 'hour')}小时`,
          "封禁弹幕功能永久：您所发的弹幕存在违规，弹幕功能已被永久封禁"]
        dataArray.push({
          type: 100,//封禁弹幕功能
          release_time: moment(danmuResult.createtime).format("YYYY-MM-DD HH:mm:ss"),
          desc: desc[danmuResult.ban_type - 1],
          id: danmuResult.id,
          bgs: "",
          avatars: "",
          customDesc: danmuResult.reason,
          isForever: danmuResult.forever
        })
      }
    }

    return {
      ...request,
      dataArray: dataArray
    }
  }

  /**
   * 解除新的禁闭
   * @param request
   */
  public async liftShutter(request: IliftShutterRequest): Promise<IliftShutterResponse> {
    const { app } = this;
    const werewolf = app.mysql.get("werewolf");
    const manager = app.mysql.get("manager");

    const playerId = werewolf.escape(request.playerId);
    const dingNo = werewolf.escape(request.dingNo);
    //const dingNo = request.dingNo;

    // if(!dingNo || dingNo == werewolf.escape('0')){
    //   let sql =
    //     "SELECT COUNT(*) AS total FROM wf_user_ban WHERE user_id = "+playerId+" AND operation = 0";
    //   let result = await manager.query(sql);
    //   console.info("result",result);
    //   let count = 0;
    //   if (!!result && result.length > 0) {
    //     count = result[0].total;
    //   }
    //   if(count>0){
    //     return {
    //       status:2
    //     };
    //   }
    // }

    const werewolfConn = await werewolf.beginTransaction();
    const managerConn = await manager.beginTransaction();
    try {
      let wf_user_ban_type = 0; //禁闭区
      let desc = "解除紧闭状态";

      if (request.type == 100) {
        wf_user_ban_type = 100;
        desc = "解除弹幕封禁";
        let sql = `UPDATE danmaku_ban SET  forever='0' , endtime = CURRENT_TIME() WHERE user_id = ${playerId};`;
        await werewolfConn.query(sql);

      } else {
        //首先插入avatarframe表
        const wereRow = {
          release_time: moment(new Date()).subtract(2, 'm').format("YYYY-MM-DD HH:mm:ss")
        };
        const wereOptions = {
          where: {
            user_no: request.playerId,
            type: request.type
          }
        };
        await werewolfConn.update("tuser_imprison_deal", wereRow, wereOptions);
        //判断是接触紧闭，还是解除封功能
        if (request.type == 12 || request.type == 15 || request.type == 16) {
          wf_user_ban_type = 2;//功能区
          desc = "解除封功能状态";
        } else {
          wf_user_ban_type = 0; //禁闭区
        }
      }
      await managerConn.insert("wf_user_ban", {
        admin_id: request.uid,
        user_id: request.playerId,
        type: wf_user_ban_type,
        category: request.type,
        operation: 0,
        //desc: "解除封禁状态" + dingNo
        desc: desc
      });
      await werewolfConn.commit();
      await managerConn.commit();
      return {
        status: 1
      };
    } catch (error) {
      await werewolfConn.rollback();
      await managerConn.rollback();
      throw error;
    }

  }

  /*
   * 被举报视频列表
   * @param request
   */
  public async reportVideoList(request: IreportVideoListRequest): Promise<IreportVideoListResponse> {
    const { app } = this;
    // const werewolf = app.mysql.get("werewolf");
    try {
      // const results = await werewolf.select('treportvideo', {
      //   where: { gameno: request.gameId, userno: request.playerId },
      //   columns: ['timestamp', 'url'],
      //   orders: [['id', 'desc']],
      //   limit: 10,
      //   offset: 0
      // });
      const sqlStr = `SELECT DISTINCT
      url,
      completeTime
    FROM
      trealtime_report t1,
      treportvideo_chn t2
    WHERE
      gamerecord_no = t2.gameno
    AND user_no = userno
    AND userno = ${request.playerId}
    AND gameno = ${request.gameId}
    ORDER BY completeTime DESC
    LIMIT 10;
      `;
      const results = await this.selectList(sqlStr)
      if (!!results && results.length > 0) {
        let array: IreportVideoList[] = new Array();
        for (let i = 0, len = results.length; i < len; i++) {
          array.push({
            datetime: moment(results[i].completeTime).format("YYYY-MM-DD HH:mm:ss"),
            url: `http://report-stream-record.53site.com/${results[i].url}`
          });
        }
        return {
          ...request,
          dataArray: array
        }
      }
      return {
        ...request,
        dataArray: []
      }
    } catch (error) {
      throw error;
    }
  }

  /**
   * @name:
   * @msg:
   * @param {type}
   * @return:
   */
  public async getMemberChange(request: ImemberChangeSearch) {
    const { ctx, app, logger } = this;
    const db = app.mysql.get('werewolf');
    //开启事物
    try {
      let sql = ` SELECT * FROM mega_manager.wf_user_update_username `;
      if (request.timeSearch != "") {
        sql += ` WHERE DATE_SUB(CURDATE(), INTERVAL 7 DAY) <= DATE(updatetime) `;
      } else {
        sql += ` WHERE `;
        if (request.user_noSearch != null) {
          sql += ` user_no LIKE  '%` + request.user_noSearch + `%' AND `;
        }
        if (request.old_usernameSearch != null) {
          sql += ` old_username LIKE  '%` + request.old_usernameSearch + `%' AND `;
        }
        if (request.new_usernameSearch != null) {
          sql += ` new_username LIKE  '%` + request.new_usernameSearch + `%' AND `;
        }
        if (request.statusSearch != null) {
          sql += ` status = ` + request.statusSearch + ` AND `;
        }
        if (request.createtimeSearch != null) {
          sql += ` DATE_SUB( '` + request.createtimeSearch + `', INTERVAL 7 DAY) <= DATE(createtime) AND `;
          sql += ` DATE_SUB( '` + request.createtimeSearch + `', INTERVAL -7 DAY) >= DATE(createtime) AND `;
        }
        if (request.updatetimeSearch != null) {
          sql += ` DATE_SUB( '` + request.updatetimeSearch + `', INTERVAL 7 DAY) <= DATE(updatetime) AND `;
          sql += ` DATE_SUB( '` + request.updatetimeSearch + `', INTERVAL -7 DAY) >= DATE(updatetime) AND `;
        }

        if (sql.substr(-4, 4) == "AND ") {
          sql = sql.substring(0, sql.length - 4);
        }
      }
      sql += `ORDER BY updatetime DESC`
      const memberChangeList = await db.query(sql);

      sql = `
        SELECT  * 
        FROM mega_manager.wf_user_update_username
        WHERE status in (2,3) AND updatetime < CURRENT_DATE - INTERVAL 10 day
      `;
      const emailList = await db.query(sql);
      if (emailList != null && emailList.length > 0) {
        const userList3: any = [];
        for (const value of emailList) {
          if (value['status'] === 3) {
            userList3.push(value['user_no']);
          }
        }
        if (userList3 != null && userList3.length > 0) {
          let req = {
            userList: userList3,
            title: '账号变更通知',
            content: '用户您好，您提交的账号换绑申请，由于您提供资料不足审核失败，如您后期可补充提供，请您再次联系官方客服（QQ：800819777）提交申请，感谢支持。'
          };
          await ctx.service.werewolf.sendEmail.sendEmailsToUsers(req);
        }
        sql = `UPDATE mega_manager.wf_user_update_username SET status = 4 WHERE status in (2,3) AND updatetime < CURRENT_DATE - INTERVAL 10 day`;
        await db.query(sql);
      }
      return { memberChangeList };

    } catch (error) {
      throw error;
    }
  }

  /**
   * @name:
   * @msg:
   * @param {type}
   * @return:
   */
  public async insertMemberChange(request: ImemberChange) {
    const { app } = this;
    const db = app.mysql.get('werewolf');
    //开启事物
    const conn = await db.beginTransaction();

    try {
      let sql = ` INSERT INTO \`mega_manager\`.\`wf_user_update_username\` ( \`user_no\`, \`old_username\`, \`new_username\`, \`qq\`, \`card\`, \`status\`, \`content\` )
      VALUES
        ( ?, ?, ?, ?, ?, ?, ? );`;

      await conn.query(sql, [
        request.user_no,
        request.old_username,
        request.new_username,
        request.qq,
        request.card,
        request.status,
        request.content
      ]);
      await conn.commit(); // 提交事务
    } catch (error) {
      await conn.rollback(); // 一定记得捕获异常后回滚事务！
      throw error;
    }
  }

  /**
   * @name:
   * @msg:
   * @param {type}
   * @return:
   */
  public async updateMemberChange(request: ImemberChange) {
    const { ctx, app } = this;
    const db = app.mysql.get('werewolf');
    //开启事物
    const conn = await db.beginTransaction();
    try {
      let sql = ` UPDATE \`mega_manager\`.\`wf_user_update_username\` 
      SET \`user_no\` = ?,
      \`old_username\` = ?,
      \`new_username\` = ?,
      \`qq\` = ?,
      \`card\` = ?,
      \`status\` = ?,
      \`content\` = ?
      WHERE
        \`id\` = ? `;

      await conn.query(sql, [
        request.user_no,
        request.old_username,
        request.new_username,
        request.qq,
        request.card,
        request.status,
        request.content,
        request.id,
      ]);

      if (request.status === 2) {
        let userList: any = [request.user_no]
        let req: SendEmailsToUsersRequest = {
          userList: userList,
          title: '账号变更通知',
          content: '用户您好，您提交的账号换绑申请，经审核需要您进一步提交相关资料，请您在10天内联系官方客服（QQ：800819777）补充提供，感谢配合；'
        }
        await ctx.service.werewolf.sendEmail.sendEmailsToUsers(req);
      }
      await conn.commit(); // 提交事务
    } catch (error) {
      await conn.rollback(); // 一定记得捕获异常后回滚事务！
      throw error;
    }
  }

  /**
   * @name:
   * @msg:
   * @param {type}
   * @return:
   */
  public async checkUserExist(request: ImemberChange) {
    const { app } = this;
    const db = app.mysql.get('werewolf');

    try {
      let sql = ` SELECT \`no\` AS num FROM tuser WHERE username = ? `;

      const num = await db.query(sql, [
        request.old_username]);
      return { num };
    } catch (error) {
      throw error;
    }
  }

  /**
    * @name:
    * @msg:
    * @param {type}
    * @return:
    */
  public async checkUserExistName(request: ImemberChange) {
    const { app } = this;
    const db = app.mysql.get('werewolf');

    try {
      let sql = ` SELECT COUNT(*) AS num FROM tuser WHERE username = ? `;

      const num = await db.query(sql, [
        request.new_username]);
      return { num };
    } catch (error) {
      throw error;
    }
  }

  /**
   * @name:
   * @msg:
   * @param {type}
   * @return:
   */
  public async updateMemberChangeAndUserName(request: ImemberChange) {
    const { app } = this;
    const db = app.mysql.get('werewolf');
    //开启事物
    const conn = await db.beginTransaction();
    try {
      let sql = ` UPDATE \`mega_manager\`.\`wf_user_update_username\` 
        SET \`user_no\` = ?,
        \`old_username\` = ?,
        \`new_username\` = ?,
        \`qq\` = ?,
        \`card\` = ?,
        \`status\` = ?,
        \`content\` = ?
        WHERE
          \`id\` = ? `;

      await conn.query(sql, [
        request.user_no,
        request.old_username,
        request.new_username,
        request.qq,
        request.card,
        request.status,
        request.content,
        request.id,
      ]);

      let sqlUpdateUserName = ` UPDATE \`werewolf\`.\`tuser\` 
          SET \`username\` = ?
          WHERE
            \`no\` = ? `;

      await conn.query(sqlUpdateUserName, [
        request.new_username,
        request.user_no]);

      await conn.commit(); // 提交事务
    } catch (error) {
      await conn.rollback(); // 一定记得捕获异常后回滚事务！
      throw error;
    }
  }
  public async downloadRestoreOSS(request) {
    let client = new OSS({
      // region以杭州为例（oss-cn-hangzhou），其他region按实际情况填写。
      region: 'oss-cn-shenzhen',
      // 阿里云主账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM账号进行API访问或日常运维，请登录RAM控制台创建RAM账号。
      accessKeyId: "LTAI4G7Lz5GPJkb4ayWSJCeq",
      accessKeySecret: "******************************",
      bucket: "wf-history"
    });
    // let client = new OSS({
    //   // region以杭州为例（oss-cn-hangzhou），其他region按实际情况填写。
    //   region: 'oss-cn-shenzhen',
    //   // 阿里云主账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM账号进行API访问或日常运维，请登录RAM控制台创建RAM账号。
    //   accessKeyId: "LTAI4FrMp2q7twEfV27orLvM",
    //   accessKeySecret: "******************************",
    //   bucket: "stream-record"
    // });
    try {
      let object = `recordflv/record/werewolf/${request.url}/`;
      // const result = await client.listBuckets();
      let result = await client.list({
        prefix: object
      });
      let newArr: any = []
      let obj = result.objects.forEach((item, index) => {
        let nameObj = { id: null, url: '' }
        let url: string = client.signatureUrl(item.name);
        nameObj.id = index
        nameObj.url = url
        newArr.push(nameObj)
      })
      return newArr;
    } catch (error) {
      throw error;
    }
  }
}
