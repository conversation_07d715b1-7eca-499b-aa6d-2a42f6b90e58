/*
 * @Description: 无
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-04-26 09:07:28
 * @LastEditors: zhanglu
 * @LastEditTime: 2020-10-22 10:43:10
 */
/**
 * @name: manager数据库建模
 * @msg:
 * @param {type}
 * @return:
 */
export interface MallItemBaseType {
    id: number;
    pid: number;
    type: number;
    name: string;
    level: number;
    sort: number;
    delsign: number;
}

export interface MallItemBaseInfo {
    buy: number;//是否可购
    buy_jump: string;//跳转地址
    buy_jump_type: number;//跳转类型
    buy_remark: string;//不可购买描述
    cate_id: number;
    coinName: string;//货币名称
    coin_id: number;//货币id
    conditionId: null;
    conditionLevel: null;
    conditionType: null;
    current_price: number;//现价
    delsign: number;//是否展示
    discount_end_time: string;
    discount_start_time: string;
    end_time: string;//下架时间
    give: number;//是否可赠
    icon: string;//icon
    id: number;//id
    item_catalog_id: number;
    item_cate_id: number;
    item_dic_id: number;
    item_id: number;
    name: string;//名称
    original_price: number;//原价
    pic: string;//图片
    remark: string;//描述
    start_time: string;//上架时间
    tag_end_time: string;//tag结束时间
    tag_pic: string;//tag图片
    tag_start_time: string;//tag开始时间
    mall_tag_id: number; //标签ID
    sort: number;

    firstPid: number;
    secondPid: number;
    thirdPid: number;
    mall_condition_id: number;
    coinState: [];//货币
    coinOriState: [];//货币
    oriItem: any;
    operateInfo: any;
    type: number;
}

export enum CoinOperation {
    unchange = 0,
    insert = 1,
    update = 2,
    delete = 3,
}

export interface MallItemOperateInfo {
    id: number;
    nickname: string;
    name: string;
    createtime: string;
    operate_user_id: number;
    operate_type: number;
    item_catalog_id: string;
    item_dic_id: string;
    mall_condition_id: string;
    mall_tag_id: string;
    remark: string;
    give: string;
    buy: string;
    buy_jump_type: string;
    buy_jump: string;
    buy_remark: string;
    start_time: string;
    end_time: string;
    sort: string;
    display: string;
    delsign: string;
    dimaond: string;
    box_coin: string;
    box_key: string;
    activity_coin: string;
    top_coin: string;
    group_coin: string;
    tag_end_time: string;//tag结束时间
    tag_start_time: string;//tag开始时间
    discount_end_time: string;
    discount_start_time: string;
    current_price: string;//现价
    type: string;
}
export interface UploadTagInfoReq{
    pic: string;
    remark: string;
}
