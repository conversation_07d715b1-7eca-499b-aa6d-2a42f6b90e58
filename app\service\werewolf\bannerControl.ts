/*
 * @Description: banner管理
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: 张宇
 * @Date: 2019-08-22 15:41:18
 * @LastEditors: jiawen.wang
 * @LastEditTime: 2021-02-27 11:13:25
 */
import { Service } from 'egg';
import { IbannerListRequest, IbannerListResponse, IuploadBannerRequest, BannerIsShowRequest, DelBannerInfoRequest } from '../../model/werewolf';
import { IbannerBaseInfo, IbannerImageInfo, IUPbannerBaseInfo } from '../../model/wfNewBanner'
import BaseMegaService from './BaseMegaService';
import * as moment from "moment"
export default class BannerControl extends BaseMegaService {
    /**
       * @name:
       * @msg:查询banner列表
       * @param 
       * @return:
       */
    public async bannerList(request: IbannerListRequest): Promise<IbannerListResponse> {
        const { app, logger } = this;
        try {
            const werewolf = app.mysql.get("werewolf");
            const start = werewolf.escape(request.start);
            const offset = werewolf.escape(request.offset);
            //查询总数
            let count = 0;
            let sql = `SELECT COUNT(*) AS total FROM hall_banner_config WHERE is_delsign = 0`;
            let sql1 = `SELECT * FROM hall_banner_config WHERE is_delsign = 0`;

            let result = await werewolf.query(sql);
            let result1 = await werewolf.query(sql1)

            if (!!result && result.length > 0) {
                count = result[0].total;
            }
            sql = `SELECT
            hbc.*,hdt.dialog_type_name
        FROM
            hall_banner_config hbc
        LEFT JOIN home_dialog_type hdt ON hbc.type = 0 AND hbc.page = hdt.dialog_type
        WHERE
            is_delsign = 0
        ORDER BY hbc.is_show DESC, hbc.sort_id ASC `;
            result = await werewolf.query(sql);
            result.forEach((item, index) => {
                item.start_time = moment(item.start_time).format("YYYY-MM-DD HH:mm:ss")
                item.end_time = moment(item.end_time).format("YYYY-MM-DD HH:mm:ss")
            })

            if (!result) {
                return {
                    ...request,
                    count: count,
                    dataArray: []
                };
            }
            return { ...request, count: count, dataArray: result };
        } catch (err) {
            throw err;
        }
    }
    /**
      * @name:
      * @msg:上传banner信息
      * @param 
      * @return:
      */
    public async uploadBannerInfo(request: IuploadBannerRequest) {
        const { app, logger } = this;
        try {
            const werewolf = app.mysql.get("werewolf");
            const banner_name = werewolf.escape(request.banner_name);
            const banner_img = werewolf.escape(request.banner_img);
            const url = werewolf.escape(request.url);
            const type = request.type;
            const page = request.page;
            const start_time = werewolf.escape(request.start_time);
            const end_time = werewolf.escape(request.end_time);
            let sql = `INSERT INTO \`werewolf\`.\`hall_banner_config\` (
                \`banner_name\`,
                \`banner_img\`,
                \`url\`,
                \`type\`,
                \`page\`,
                \`start_time\`,
                \`end_time\`
            )
            VALUES
                (
                    ${banner_name},
                    ${banner_img},
                    ${url},
                    ${type},
                    ${page},
                    ${start_time},
                    ${end_time}
                );`;
            await werewolf.query(sql);
        } catch (err) {
            throw new Error('上传信息失败!');
        }
    }

    public async bannerIsShow(request: BannerIsShowRequest) {
        const { app, logger } = this;
        try {
            const werewolf = app.mysql.get("werewolf");
            const id = request.id;
            const is_show = request.is_show;
            let sql = `UPDATE hall_banner_config SET is_show = ${is_show} WHERE id = ${id}`
            await werewolf.query(sql);
        } catch (err) {
            throw new Error('修改状态失败!');
        }
    }

    public async delBannerInfo(request: DelBannerInfoRequest) {
        const { app, logger } = this;
        try {
            const werewolf = app.mysql.get("werewolf");
            const id = request.id;
            let sql = ` UPDATE hall_banner_config SET is_delsign = 1 WHERE id =${id} `
            await werewolf.query(sql);
        } catch (error) {
            throw new Error('删除失败!');
        }
    }
    public async uploadNewBannerBaseInfo(request: IbannerBaseInfo) {
        const { app, logger } = this;
        try {
            const werewolf = app.mysql.get("werewolf");
            const banner_name = werewolf.escape(request.banner_name);
            const url = werewolf.escape(request.url);
            const show_type = werewolf.escape(request.show_type)
            const type = request.type;
            const page = request.page;
            const start_time = werewolf.escape(request.start_time);
            const end_time = werewolf.escape(request.end_time);
            const sort_id = werewolf.escape(request.sort_id);
            const desc = werewolf.escape(request.desc)
            const show_in_eventview = werewolf.escape(request.show_in_eventview)
            const show_in_hall = werewolf.escape(request.show_in_hall)
            const tend_type = werewolf.escape(request.tend_type)
            const activity_id = werewolf.escape(request.activity_id)
            const tab1_id = werewolf.escape(request.tab1_id)
            const tab2_id = werewolf.escape(request.tab2_id)
            let sqlStr = `
            INSERT INTO 
                \`hall_banner_config\` (banner_name,url,show_type,type,page,start_time,end_time,sort_id,\`desc\`,show_in_eventview,show_in_hall,tend_type,activity_id,tab1_id,tab2_id) 
            VALUES 
                (${banner_name},${url},${show_type},${type},${page},${start_time},${end_time},${sort_id},${desc},${show_in_eventview},${show_in_hall},${tend_type},${activity_id},${tab1_id},${tab2_id})`;

            const result = await werewolf.query(sqlStr);
            return { id: result.insertId, imgageName: `${result.insertId}_${new Date().getTime()}_banner` };
        } catch (error) {
            throw new Error('上传失败!');
        }
    }
    public async uploadNewBannerImageInfo(request: IbannerImageInfo) {
        const { app, logger } = this;
        try {
            let sqlStr = `UPDATE hall_banner_config SET banner_img = ?,is_complete = 1  WHERE id = ?`;
            let result = await this.execSql(sqlStr, [request.banner_img, request.id,]);
            return { hotImgageName: `${request.id}_${new Date().getTime()}_hot`, id: request.id }
        } catch (error) {
            throw new Error('上传失败!');
        }
    }
    public async uploadNewHotImageInfo(request: IbannerImageInfo) {
        const { app, logger } = this;
        try {
            let sqlStr = `UPDATE hall_banner_config SET hot_img = ?,is_complete = 1  WHERE id = ?`;
            await this.execSql(sqlStr, [request.banner_img, request.id]);
        } catch (error) {
            throw new Error('上传失败!');
        }
    }
    public async updateNewBannerBaseInfo(request: IUPbannerBaseInfo) {
        try {
            let sqlStr = `UPDATE
                hall_banner_config
              SET
                banner_name = ?,
                url = ?,
                show_type=?,
                type = ?,
                page = ?,
                start_time = ?,
                end_time = ?,
                sort_id = ?,
                is_show = ?,
                is_prod = ?,
                show_in_eventview = ?,
                show_in_hall = ?,
                \`desc\` = ?,
                tend_type=?,
			    tab1_id=?,
			    tab2_id=?
              WHERE
                id = ?`
            await this.execSql(sqlStr, [request.banner_name, request.url, request.show_type, request.type, request.page, request.start_time, request.end_time, request.sort_id, request.is_show, request.is_prod, request.show_in_eventview,
            request.show_in_hall, request.desc, request.tend_type, request.tab1_id, request.tab2_id, request.id]);

        } catch (error) {
            throw new Error(error);
        }
    }


    public async getBetaBannerList(request) {
        const { app, logger } = this;
        try {
            const werewolf = app.mysql.get("werewolf");
           
            let sql = `SELECT * FROM hall_banner_beta`;

            let result = await werewolf.query(sql);

            return  result;
        } catch (err) {
            throw err;
        }
    }


    public async addBetaBanner(request) {
        const { app, logger } = this;
        try {
            const werewolf = app.mysql.get("werewolf");
            let activity_id = request.activity_id;
            // werewolf.escape(request.activity_id)
            const url = werewolf.escape(request.url);
            const desc = werewolf.escape(request.desc)

            if(activity_id == '' || activity_id == undefined || activity_id == null){
                activity_id = 0;
            }

            let sqlStr = `
            INSERT INTO 
                \`hall_banner_beta\` (url,\`desc\`,activity_id) 
            VALUES 
                (${url},${desc},${activity_id})`;


            const result = await werewolf.query(sqlStr);
            return { id: result.insertId};
        } catch (error) {
            throw new Error('上传失败!');
        }
    }

    public async updateBetaBannerInfo(request) {
        const { app, logger } = this;
        try {

            const werewolf = app.mysql.get("werewolf");
            let id = request.id;
            let activity_id = request.activity_id;
            // werewolf.escape(request.activity_id)
            const url = werewolf.escape(request.url);
            const desc = werewolf.escape(request.desc)


            if(activity_id == '' || activity_id == undefined || activity_id == null){
                activity_id = 0;
            }
            
            let sqlStr = `UPDATE hall_banner_beta SET url = ${url} , ` + 
                        `\``  +
                        `desc`      + `\`` +
                        ` = ${desc} , activity_id = ${activity_id}  WHERE id = ${id};`;


            logger.error("---------------------\n\n"+sqlStr+'\n\n');

            return await this.execSql(sqlStr, []);

        } catch (error) {
            throw new Error('上传失败!');
        }
    }

}
