import { IanchorShowDayReq, IanchorShow<PERSON>ay<PERSON>esp, IchangeKingReq, IchangeReadyReq, IchangeScoreReq, IchangeVotableReq,IchangeWinCampReq } from "../../model/wfAnchorShow";
import BaseMegaController from "./BaseMegaController";

export default class AnchorShownService extends BaseMegaController {

    /**
   * 第几季
   */
    public async getInfo() {
        const { ctx, logger } = this
        try {
            const resp = await ctx.service.werewolf.anchorShow.getInfo();
            this.respSuccData({ season: resp })
        } catch (error) {
            logger.error(error);
            this.respFail(error);
        }
    }

    public async getInfoByDay() {
        const { ctx, logger } = this
        try {
            const req: IanchorShowDayReq = ctx.request.body;
            const resp: IanchorShowDayResp = await ctx.service.werewolf.anchorShow.getInfoByDay(req);
            this.respSuccData(resp)
        } catch (error) {
            logger.error(error);
            this.respFail(error);
        }
    }

    public async changeAnchorReady() {
        const { ctx, logger } = this
        try {
            const req: IchangeReadyReq = ctx.request.body;
            await ctx.service.werewolf.anchorShow.changeAnchorReady(req);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error);
        }
    }

    public async changeVotable() {
        const { ctx, logger } = this
        try {
            const req: IchangeVotableReq = ctx.request.body;
            await ctx.service.werewolf.anchorShow.changeVotable(req);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error);
        }
    }

    public async changeKing() {
        const { ctx, logger } = this
        try {
            const req: IchangeKingReq = ctx.request.body;
            await ctx.service.werewolf.anchorShow.changeKing(req);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error);
        }
    }

    public async incScore() {
        const { ctx, logger } = this
        try {
            const req: IchangeScoreReq = ctx.request.body;
            await ctx.service.werewolf.anchorShow.incScore(req);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error);
        }
    }

    public async decScore() {
        const { ctx, logger } = this
        try {
            const req: IchangeScoreReq = ctx.request.body;
            await ctx.service.werewolf.anchorShow.decScore(req);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error);
        }
    }

    public async changeWinCamp() {
        const { ctx, logger } = this
        try {
            const req: IchangeWinCampReq = ctx.request.body;
            await ctx.service.werewolf.anchorShow.changeWinCamp(req);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error);
        }
    }
}