import {IbroadcastItem} from "./werewolf";

/**
 * <AUTHOR>
 *
 */

export interface UserCollectionBean{
    dataArray: UserCollectionItem[];
}

export interface UserCollectionItem {
    i: number;
    pic: string;
}

export interface UserCollectionResult{
    code: number
}

export interface IuserCollectionCleanRecordsParams {
    uid: number;
    user_id: number;
    index: number;
    url: string;
}
