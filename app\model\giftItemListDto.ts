export interface IgiftItemParams {
    gift_id: number,
    item_dic_id: number,
    item_cate_id: number, //头像框 2020 2010
    num: number,
    weight: number,
    sex: number,
    repeat: number,
    rate: string,
    delsign: number,
}

export interface IupdateGiftItemParams {
    id: number,
    gift_id: number,
    item_dic_id: number,
    item_cate_id: number, //头像框 2020 2010
    num: number,
    weight: number,
    sex: number,
    repeat: number,
    rate: string,
    delsign: number,
}