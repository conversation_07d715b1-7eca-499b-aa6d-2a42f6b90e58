/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-10-15 15:38:23
 * @LastEditTime: 2020-10-30 16:36:05
 * @LastEditors: jiawen.wang
 */
import BaseMegaService from './BaseMegaService';
import { IcreateupdateContentReq } from "../../model/updateContent"
export default class UpdateContent extends BaseMegaService {
    public async getUpdateContentList() {
        const { app, logger } = this;
        const db = app.mysql.get('werewolf');
        try {
            const sqlStr = `SELECT * FROM tsys_bvrs WHERE delsign = 0 ORDER BY create_time DESC`
            const result = await this.selectList(sqlStr)
            return result
        } catch (error) {
            logger.error(error);
            throw error;
        }
    }
    public async createUpdateContent(req: IcreateupdateContentReq) {
        const { app, logger } = this;
        const db = app.mysql.get('werewolf');

        try {
            const sqlStr = `INSERT INTO 
            tsys_bvrs (content,start_time,end_time)
        VALUES
            (?, ? ,?)`;
            await this.execSql(sqlStr, [req.content, req.start_time, req.end_time])
        } catch (error) {
            logger.error(error);
            throw error;
        }
    }
    public async editUpdateContent(req: IcreateupdateContentReq) {
        const { app, logger } = this;
        const db = app.mysql.get('werewolf');
        try {
            const sqlStr = `UPDATE 
            tsys_bvrs SET content = ?,start_time = ?,end_time = ? WHERE id= ?`;
            await this.execSql(sqlStr, [req.content, req.start_time, req.end_time, req.id])
        } catch (error) {
            logger.error(error);
            throw error;
        }
    }
    public async delUpdateContent(req) {
        const { app, logger } = this;
        const db = app.mysql.get('werewolf');
        try {
            const sqlStr = `UPDATE tsys_bvrs SET delsign = 1 WHERE id= ? `;
            await this.execSql(sqlStr, [req.id])
        } catch (error) {
            logger.error(error);
            throw error;
        }
    }
}