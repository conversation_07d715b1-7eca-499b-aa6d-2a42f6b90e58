/*
 * @Description: 公会战路由
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: z<PERSON>yi
 * @Date: 2020-10-28 11:50:11
 */
import { Router, Application } from "egg";
import { AccessRouteId, AccessRouteName } from "./model/accessRouteCof";
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

    const { controller, router } = app;
    //【公会战】获取赛季列表,
    router.post(`${API_VERSION}/werewolf/groupVersus/getSeasonList`, controller.werewolf.groupVersus.getSeasonList)
    //【公会战】获取比赛日期列表,
    router.post(`${API_VERSION}/werewolf/groupVersus/getGroupList`, controller.werewolf.groupVersus.getGroupList)
    //【公会战】获取公会对战列表,
    router.post(`${API_VERSION}/werewolf/groupVersus/getVersusList`, controller.werewolf.groupVersus.getVersusList)
    //【公会战】获取公会详情,
    router.post(`${API_VERSION}/werewolf/groupVersus/getVersusDetail`, controller.werewolf.groupVersus.getVersusDetail)

}

export default load