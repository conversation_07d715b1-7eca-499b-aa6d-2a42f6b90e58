/*
import { BaseMegaService } from "./werewolf/BaseMegaService";
import { BaseMegaController } from "./../controller/werewolf/BaseMegaController";
 * @Description: 无
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2018-11-20 18:00:10
 * @LastEditors: zhanglu
 * @LastEditTime: 2020-12-22 11:01:22
 */
import { Service } from 'egg';
import { IuserModel, IloginRequest, IadminToken, ItokenIdList, IresetPwdRequest } from '../model/manager';
import { HttpErr, IerrorMsg } from '../model/common';
import * as moment from 'moment';
import { UserDeleteRequest, UserCreateRequest } from '../model/werewolf';
import BaseMegaService from './werewolf/BaseMegaService';

//redis中token前缀
const tokenPre: string = 'token';

/**
 * Test Service
 */
export default class UserService extends BaseMegaService {
	/**
     * @name: 获得用户信息
     * @msg: 
     * @param {type} 
     * @return: 
     */
	async getInfo(loginModel: IloginRequest) {
		const { app, ctx, logger } = this;
		try {
			const db = app.mysql.get('manager');
			//查询用户信息
			const results = await db.select('wf_admin_user', {
				where: { email: loginModel.email,delsign:0 }
			});
			// 存在用户
			if(!!results && results.length >0){
				let theUser = results[0];
				//查询管理员
				let accessRoutes: Array<{route_id: number}> = await db.query(`
				select ru.route_id from wf_admin_route_user AS ru
LEFT JOIN wf_admin_route AS r ON r.id = ru.route_id 
where ru.admin_id = ${theUser.id} and ru.access_enable = 1
AND r.\`status\` = 1
`);
				// 转化为纯数组
				let accessRoutesN: number[]  = new Array<number>();;
				for (const key in accessRoutes) {
					if (accessRoutes.hasOwnProperty(key)) {
						const element = accessRoutes[key];
						accessRoutesN.push(element.route_id);
					}
				}
				//todo 暂时权限全部开启
				if(accessRoutesN.length==0){
					accessRoutesN = [1,2,3,4,5,6];
				}

				theUser.accessRoutes = accessRoutesN;
				return theUser;
			}else{
				//不存在用户
				return null;
			}
			
		} catch (err) {
			throw err;
		}
	}

	// 获得客户端的请求码 防止重放攻击
	async getClientReqId(reqId: string) {
		const { app, ctx, logger } = this;
		try {
			const db = app.mysql.get('manager');
			const results = await db.select('wf_admin_token', {
				where: { client_reqid: reqId }
			});
			return results;
		} catch (err) {
			throw err;
		}
	}

	async getFullInfo(id: number) {
		const { app, ctx, logger } = this;
		try {
			const db = app.mysql.get('manager');
			const results = await db.select('wf_admin_user', {
				where: { id: id }
			});
			return results;
		} catch (err) {
			throw err;
		}
	}

	async updateLogin(uid: number, ip: string) {
		const { app, ctx, logger } = this;
		try {
			const db = app.mysql.get('manager');
			uid = db.escape(uid);
			ip = db.escape(ip);
			const results = await db.query(
				'UPDATE wf_admin_user SET logintime = NOW(), loginip = ' + ip + ' WHERE id = ' + uid
			);
			return results;
		} catch (err) {
			throw err;
		}
	}

	// 插入wf_admin_token表
	async insertAdminToken(uid: number, ip: string, reqId: string): Promise<number> {
		const { app, ctx, logger } = this;
		try {
			const device = ctx.request.get('User-Agent');
			const db = app.mysql.get('manager');
			const result = await db.insert('wf_admin_token', {
				admin_id: uid,
				login_device: device,
				create_time: moment().format('YYYY-MM-DD HH:mm:ss'),
				expire_time: moment().add(app.config.jwt['exTime'], 'seconds').format('YYYY-MM-DD HH:mm:ss'),
				login_ip: ip,
				client_reqid: reqId
			});
			//console.info("insertAdminToken result:",result);
			//success
			if (result.affectedRows === 1) {
				return Number(result.insertId);
			} else {
				throw new Error('插入wf_admin_token表错误');
			}
		} catch (err) {
			throw err;
		}
	}

	async create(user: IuserModel): Promise<IerrorMsg> {
		const result: IerrorMsg = { err_code: HttpErr.Success, err_msg: '' };
		return result;
	}

	//从redis获得token
	async getRedisToken(uid: number, tokenId: number) {
		const { app, ctx, logger } = this;
		try {
			const key = tokenPre + uid + '_' + tokenId;
			//console.info("getToken key",key);
			const result = await app.redis.get("tokenRedis").get(key);
			return result;
		} catch (err) {
			logger.warn('redis出错：' + err);
			throw err;
		}
	}

	//token存储到redis
	async saveRedisToken(uid: number, tokenId: number, value: string) {
		const { app, ctx, logger } = this;
		try {
			const key = tokenPre + uid + '_' + tokenId;
			//const value = String(Date.now());
			//console.info(`setToken key:${key} value:${value}`);
			const result = await app.redis.get("tokenRedis").set(key, value, 'EX', app.config.jwt['exTime']);
			return result;
		} catch (err) {
			logger.warn('redis出错：' + err);
			throw err;
		}
	}

	//清理玩家所有token
	async clearRedisToken(uid: number): Promise<ItokenIdList[]> {
		const { app, ctx, logger } = this;
		try {
			//查询指定用户的token未过期列表，
			const db = app.mysql.get('manager');
			const escapeuid = db.escape(uid);
			const sql = `select id from wf_admin_token WHERE admin_id = ${escapeuid} and expire_time > NOW()`;
			const sqlResult: ItokenIdList[] = await db.query(sql);

			//存在token
			if (sqlResult.length > 0) {
				const pipeline = await app.redis.get("tokenRedis").pipeline();
				for (const value of sqlResult) {
					const tokenId = value.id;
					const key = tokenPre + uid + '_' + tokenId;
					await pipeline.del(key);
				}
				const result = await pipeline.exec();
				//console.info('clear redis result:', result);
			}
			return sqlResult;
		} catch (err) {
			logger.warn('redis出错：' + err);
			throw err;
		}
	}

	async userdelete(req: UserDeleteRequest) {
		const uid = req.uid;
		const { app, ctx, logger } = this;

		try {
			//查询指定用户的token未过期列表，
			// const db = app.mysql.get('manager');
			const sql = 'update mega_manager.wf_admin_user SET delsign = 1 WHERE id=?;';
			const sql2 = 'select id as uid,nickname from mega_manager.wf_admin_user WHERE delsign = 0;';
			const results1 = await this.execSql(sql, [uid]);
			const results = await this.execSql(sql2, []);
			return results;
		} catch (err) {
			throw err;
		}
	}
	async usercreate(req: UserCreateRequest) {
		const uid = req.uid;
		const nickname=req.nickname;
		const password = req.password;
		const email =req.email;
		const { app, ctx, logger } = this;

		try {
			//查询指定用户的token未过期列表，
			// const db = app.mysql.get('manager');
			const sql = 'INSERT INTO mega_manager.wf_admin_user SET '+ 'id='+uid+','+'email='+"'"+email+"'"+','+'password='+"'"+password+"'"+','+'nickname='+"'"+nickname+"'"+';';			
			const sql2 = 'select id as uid,nickname from mega_manager.wf_admin_user WHERE delsign = 0;';
			const results1 = await this.execSql(sql,[]);
			const results2 = await this.execSql(sql2,[]);
			
			return results2;
		} catch (err) {
			throw err;
		}
	}

	// 获得全部管理员列表
	async userList() {
		const { app, ctx, logger } = this;
		try {
			//查询指定用户的token未过期列表，
			const db = app.mysql.get('manager');
			const sql = 'select id as uid,nickname from wf_admin_user WHERE delsign = 0';
			const results = await db.query(sql);
			return results;
		} catch (err) {
			throw err;
		}
	}

	//重置密码
	public async resetPwd(request: IresetPwdRequest): Promise<IerrorMsg> {
		const { app, ctx, logger } = this;
		try {
			const db = app.mysql.get('manager');
			//判断是否密码是否正确
			const oldPwd = db.escape(request.oldPwd);
			const uid = db.escape(request.uid);
			const sqlUser = 'select * from wf_admin_user WHERE' + '`password`=' + oldPwd + 'and id=' + uid;
			const user = await db.query(sqlUser);
			//密码不正确
			if (!user || user.length <= 0) {
				return { err_code: HttpErr.PasswordError, err_msg: '密码不正确' };
			}
			// 初始化事务
			const conn = await db.beginTransaction();
			try {
				// 更新
				await conn.update('wf_admin_user', { password: request.newPwd }, { where: { id: request.uid } });
				await conn.commit(); // 提交事务
				return { err_code: HttpErr.Success, err_msg: '更新密码成功' };
			} catch (err) {
				await conn.rollback(); // 一定记得捕获异常后回滚事务！
				throw err;
			}
		} catch (err) {
			throw err;
		}
	}
}
