## 注意事项


### 事务的正确开启

>事务，即transaction，一般情况单个查询，更新，插入不需要开启；批量的更新或插入操作，才需要开启。

正确的开启流程：
1. 获得db连接实例
2. 通过db实例获得事务连接实例werewolfConn 
3. 通过werewolfConn执行批量操作
4. commit()提交事务
5. 捕捉事务异常，并rollback()


```
const db = app.mysql.get('werewolf');
        const activityId = db.escape(request.activityId);
        const werewolfConn = await db.beginTransaction();
        const sql1 = 'UPDATE `word_activity` SET `state` = 0,`end_time` = NOW(),`open_end_time`=NOW() ';
        const sql2 = `UPDATE word_activity SET state = 1, start_time  = DATE_SUB(NOW(),INTERVAL 1 DAY),
        end_time = DATE_ADD(NOW(),INTERVAL 2 DAY), open_start_time = DATE_SUB(NOW(),INTERVAL 1 DAY), 
        open_end_time  = DATE_ADD(NOW(),INTERVAL 2 DAY) WHERE  no  = ${activityId}`;

        try {
            await werewolfConn.query(sql1);
            await werewolfConn.query(sql2);
            await werewolfConn.commit(); // 提交事务

        } catch (error) {
            await werewolfConn.rollback();
            throw error;
        }
```

### 如何调用exchange服务?

>后端不用做任何操作，只需要再前端配置反代，直接调用请求即可。

比如调用下发物品接口,路由为`/WerewolfExchange/{业务路径}`

```
export async function itemObtain(params) {
  return request(`/WerewolfExchange/item/obtain`, {
    method: `POST`,
    body: params,
  });
}

```
