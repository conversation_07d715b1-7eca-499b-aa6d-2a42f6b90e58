import { Subscription, Context } from "egg";


/**
 * 团队礼包定时上下架任务
 * 每分钟检查一次礼包的上下架状态
 */

module.exports = (app) => {
    return {
        schedule: {
            interval: "20s", // 20s间隔
            type: 'worker', // 指定所有的 worker 都需要执行
            immediate: true, // 项目启动就执行一次
        },

        async task(ctx: Context) {
            try {
                ctx.logger.info('Start checking team gift bag status...');
                await ctx.service.werewolf.teamGiftBag.checkAndUpdateGiftBagStatus();
            } catch (error) {
                ctx.logger.error('Failed to execute team gift bag schedule:', error);
            }
            ctx.logger.info('Start checking team gift bag success!');
        }
    }
};
