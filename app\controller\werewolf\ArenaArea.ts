/**
 * <AUTHOR>
 */

import { Controller } from 'egg';
import { IerrorMsg, HttpErr } from '../../model/common';
import {AreneAreaData, IareneAreaListRes} from "../../model/ArenaArea";

export default class ArenaAreaContorller extends Controller {

    public async getArenaAreaList() {
       // console.info('****************************************66666666666666');
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;

             console.info('requestBody='+ JSON.stringify(requestBody));
            const resp: AreneAreaData = await ctx.service.werewolf.arenaArea.getArenaAreaList(requestBody);
            ctx.body = resp;
            ctx.status = HttpErr.Success;


        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
}
