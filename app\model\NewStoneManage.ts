export interface IStoneListReq{
    id?: number
    name: string
    level: string
    season_id:number
    pic?: string
}export interface ISeasonPoolReq{
    id?: number
    frame_id: number
    card_level: number
    weight: number
}
export interface IRuleListReq{
    id?: number,
    desc:string,
    rate:number,
    type:number,
}

export interface IStoneRuleListReq{
    id?: number,
    frame_id:number,
    rule_id:number,
    delsign?:number,
}