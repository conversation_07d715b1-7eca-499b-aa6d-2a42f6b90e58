/*
 * @Description:游戏记录
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2019-07-10 13:43:50
 * @LastEditors: jiawen.wang
 * @LastEditTime: 2021-03-12 17:21:27
 */

import { Service } from 'egg';
import { IoperationListRequest, IbannedListRequest, IbannedListResponse, IopeAvaRecordListRequest, IopeAvaRecordListResponse, IuserAvatarFrame, IopeAvaRecord, IopeAchRecordRequest, IopeAchRecordResponse, IopeAchRecord, IGameRecordListResponse, IGameRecordListRequest, IgameReplayRequest, IgameReplayResponse, IgameRecordDetailReq, IgameRecordDetailResp } from './../../model/werewolf';
import * as moment from 'moment';
import { GetCampName, GetGameLifeDesc, GetGameWinDesc } from '../../util/gameUtils';
import { IerrorMsg, HttpErr } from '../../model/common';
const OSS = require('ali-oss');
let fs = require('fs');

export default class GameRecordService extends Service {

    /**
     * @name: 查询游戏记录列表
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async list(request: IGameRecordListRequest): Promise<IGameRecordListResponse> {
        const { app, ctx, logger } = this;
        const werewolf = app.mysql.get("werewolf");
        const uid = werewolf.escape(request.playerId);

        let count = 0;
        try {
            //查询总数
            let countSql = `SELECT
        COUNT(*) AS total
    FROM
        tusergamerecord
    LEFT JOIN tgamerecord ON tusergamerecord.game_no = tgamerecord.\`no\`
    WHERE
        tusergamerecord.user_no = ${request.playerId}
    AND tgamerecord.no >= 34614662;
        `;
            let countResult = await werewolf.query(countSql);
            if (!!countResult && countResult.length > 0) {
                count = countResult[0].total;
            }
            if (count <= 0) {
                return { ...request, count: count, dataArray: [] }
            }
            //查询分页
            let listSql = `SELECT
            tgr.game_no,
            tgr.seat_index,
            tgr.camp_no,
            tgr.life,
            tgr.win,
            tgr.\`escape\`,
            tgr.award,
           
           IF (
            tgr.role_no >= 9000,
            tgr.role_no % 9000,
            tgr.role_no
           ) AS role_no,
            tgcf.\`desc\` AS game_desc,
            tscf.\`name\` AS server_name,
            ti.minVersion,
            ti. NAME AS jvm_name,
            tg.starttime,
            tg.endtime,
           
            IF(tgr.valid = 1,'是','否') AS rated,
            tscore.scorce,
            tscore.scorce_d,
            tscore.mvp,
            rl.\`name\` AS role_name
           FROM
            tusergamerecord AS tgr
           LEFT JOIN tgamerecord AS tg ON tgr.game_no = tg.\`no\`
           LEFT JOIN tgameconfig AS tgcf ON tg.game_type = tgcf.id
           LEFT JOIN tserver_info AS ti ON ti.id = tg.server_no
           LEFT JOIN tserver_config AS tscf ON tg.server_type = tscf.id
           LEFT JOIN tusergamerecordscorce AS tscore ON tscore.user_no = tgr.user_no
           AND tscore.game_no = tgr.game_no
           LEFT JOIN role AS rl ON IF(tgr.role_no >= 9000, tgr.role_no % 9000,tgr.role_no) = rl.id
           WHERE
           tgr.user_no = ${uid}    
           AND tg.no >= 34614662
           ORDER BY tgr.game_no DESC 
           LIMIT ${request.start} , ${request.offset};`;

            logger.debug(listSql);
            let listResult = await werewolf.query(listSql);

            for (let item of listResult) {
                item.camp_no = GetCampName(item.camp_no);
                item.life = GetGameLifeDesc(item.life);
                item.win = GetGameWinDesc(item.win);
                item.award = item.award == 1 ? "是" : "否";
                item.escape = item.escape == 1 ? "是" : "否";
                item.starttime = moment(item.starttime).format('YYYY-MM-DD HH:mm:ss');
                item.endtime = moment(item.endtime).format('YYYY-MM-DD HH:mm:ss');
            }
            return { ...request, count: count, dataArray: listResult };
        } catch (e) {
            logger.error(e);
            throw e;
        }
    }

    public async gameInfoList(req) {
        const { app, ctx, logger } = this;
        const werewolf = app.mysql.get("werewolf");
        try {
            //查询板子列表
            let sql = `SELECT 
            address,result,completeTime,userNo,keywords 
            FROM 
            AudioConvertToCHN
            WHERE 
            gameNo =  ${req.gameNo}`;
            const gameInfoList = await werewolf.query(sql);
            return gameInfoList;
        } catch (error) {
            throw error;
        }
    }
    /**
         * @name: 下载某局游戏详情
         * @msg: 
         * @param {type} 
         * @return: 
         */
    public async downloadGameInfo(req) {
        let client = new OSS({
            // region以杭州为例（oss-cn-hangzhou），其他region按实际情况填写。
            region: 'oss-cn-shenzhen',
            // 阿里云主账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM账号进行API访问或日常运维，请登录RAM控制台创建RAM账号。
            accessKeyId: "LTAI4FrMp2q7twEfV27orLvM",
            accessKeySecret: "******************************",
            bucket: "stream-record"
        });
        try {
            // 'object'表示上传到OSS的object名称，'localfile'表示本地文件或者文件路径。
            let object = `recordflv/record/werewolf/${req.address}/`;
            // const result = await client.listBuckets();
            let result = await client.list({
                prefix: object
            });
            // let url = client.get(result);
            let newArr: any = []
            let obj = result.objects.forEach((item, index) => {
                let nameObj = { id: null, url: '' }
                let url: string = client.signatureUrl(item.name);
                nameObj.id = index
                nameObj.url = url
                newArr.push(nameObj)
            })
            return newArr;

        } catch (error) {
            throw error;
        }
    }
    /**
     * @name: 查询某局游戏详情
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async gameDetail(request: IgameRecordDetailReq): Promise<IgameRecordDetailResp> {
        const { app, ctx, logger } = this;
        const werewolf = app.mysql.get("werewolf");
        const gameN0 = werewolf.escape(request.gameNo);

        let count = 0;
        try {
            //查询发言
            let speakTimeSql = `SELECT user_speak_time  FROM game_user_record WHERE game_no = ${gameN0}`
            let speakTimeResult = await werewolf.query(speakTimeSql);
            if (!speakTimeResult) {
                throw new Error('获取发言失败')
            }
            let speakObj = {};
            if (speakTimeResult[0]) {
                let speakTimeArray = String(speakTimeResult[0]['user_speak_time']).split('#');
                for (const item of speakTimeArray) {
                    speakObj[item.split(':')[0]] = item.split(':')[1]
                }
            }

            let sqlStr = `SELECT * FROM tuser WHERE \`no\` in (
                SELECT user_no FROM tusergamerecord WHERE game_no = ${request.gameNo})`;
                
            let userList = await werewolf.query(sqlStr);

            let m = new Map();

            for (const v of userList) {
                let no = v.no;
                m.set(no,v.nickname);
            }

            //查询分页
            let listSql = `SELECT
            tgr.game_no,
            tgr.seat_index,
            tgr.camp_no,
            tgr.life,
            tgr.win,
            tgr.\`escape\`,
            tgr.award,
            tgr.user_no AS userNo,
           
           IF (
            tgr.role_no >= 9000,
            tgr.role_no % 9000,
            tgr.role_no
           ) AS role_no,
            tgcf.\`desc\` AS game_desc,
            tscf.\`name\` AS server_name,
            ti.minVersion,
            ti. NAME AS jvm_name,
            tg.starttime,
            tg.endtime,
           
           IF (tgr.valid = 1, '是', '否') AS rated,
            tscore.scorce,
            tscore.scorce_d,
            tscore.mvp,
            rl.\`name\` AS role_name
           FROM
            tusergamerecord AS tgr
           LEFT JOIN tgamerecord AS tg ON tgr.game_no = tg.\`no\`
           LEFT JOIN tgameconfig AS tgcf ON tg.game_type = tgcf.id
           LEFT JOIN tserver_info AS ti ON ti.id = tg.server_no
           LEFT JOIN tserver_config AS tscf ON tg.server_type = tscf.id
           LEFT JOIN tusergamerecordscorce AS tscore ON tscore.user_no = tgr.user_no
           AND tscore.game_no = tgr.game_no
           LEFT JOIN role AS rl ON IF(tgr.role_no >= 9000, tgr.role_no % 9000,tgr.role_no) = rl.id
           WHERE
              tgr.game_no = ${gameN0}`;
            // AND tg.endtime > DATE_SUB(NOW(), INTERVAL 6 DAY)
            // ) a LEFT JOIN animation_role r ON a.role_no = r.role`;
            let listResult = await werewolf.query(listSql);
            for (let item of listResult) {
                item.camp_no = GetCampName(item.camp_no);
                item.life = GetGameLifeDesc(item.life);
                item.win = GetGameWinDesc(item.win);
                item.award = item.award == 1 ? "是" : "否";
                item.escape = item.escape == 1 ? "是" : "否";
                item.starttime = moment(item.starttime).format('YYYY-MM-DD HH:mm:ss');
                item.endtime = moment(item.endtime).format('YYYY-MM-DD HH:mm:ss');
                item.userNo = item.userNo;
                item['speak_time'] = speakObj[item.userNo] ? speakObj[item.userNo] : '';
                item.nickname = m.get(item.userNo);
            }


          

            

            // console.info(listResult);
            return {
                dataArray: listResult
            };
        } catch (e) {
            logger.error(e);
            throw e;
        }
    }

    /**
     * @name: 游戏复盘
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async replay(request: IgameReplayRequest): Promise<IgameReplayResponse> {
        const { app, ctx, logger } = this;
        const werewolf = app.mysql.get("werewolf");
        const gameNo = werewolf.escape(request.gameNo);
        try {
            //
            let sql = `select tu.headicon,r.\`name\` AS role_name,r.role AS role_id,tgr.seat_index
        from tusergamerecord AS tgr
        LEFT JOIN tgamerecord AS tg ON tgr.game_no = tg.\`no\`
        LEFT JOIN animation_role r ON tgr.role_no = r.role
        LEFT JOIN tuser AS tu ON tu.no = tgr.user_no 
        WHERE tgr.game_no = ${gameNo}
        ORDER BY tgr.seat_index `;
            let peoplesResult = await werewolf.query(sql);

            //
            sql = `select note from tgamerecord WHERE tgamerecord.\`no\` = ${gameNo}`;
            let noteResult = await werewolf.query(sql);

            if (noteResult && noteResult.length > 0) {
                return { note: noteResult[0].note, peopleList: peoplesResult }
            } else {
                return { note: "", peopleList: [] };
            }
        } catch (e) {
            logger.error(e);
            throw e;
        }
    }
}
