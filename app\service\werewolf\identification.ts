/*
 * @Author: wy
 * @Date: 2021-01-25 13:33:20
 * @LastEditTime: 2021-02-18 16:30:14
 * @LastEditors: jiawen.wang
 * @Description: 用户认证
 * @FilePath: \ConsoleSystemServer\app\service\werewolf\identification.ts
 */
import { Service } from 'egg';

export default class IdentificationService extends Service {
    public async identify(req) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        const userno = db.escape(req.userno);
        try {
            //查询板子列表
            let sql = `INSERT INTO user_credit_score(id, user_id, score, current_level, max_level, max_add, current_add, update_time, delsign, down_time ) VALUES (NULL, ${userno}, 100, 0, 0, 0, 0, NOW(), 0 , 1) ON DUPLICATE KEY UPDATE down_time = 1`;
            const result = await db.query(sql);
            return result;
        } catch (error) {
            throw error;
        }
    }
    public async cancelIdentify(req) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        const userno = db.escape(req.userno);
        try {
            //查询板子列表
            let sql = `UPDATE user_credit_score SET down_time = 0 WHERE user_id= ${userno}`;
            const result = await db.query(sql);
            return result;
        } catch (error) {
            throw error;
        }
    }
    public async identifyByUdid(req) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        const udid = db.escape(req.udid);
        try {
            //查询板子列表
            let sql = `INSERT INTO user_ban_udid_verify (udid) VALUES (${udid}) ON DUPLICATE KEY UPDATE createtime = NOW()`;
            const result = await db.query(sql);
            return result;
        } catch (error) {
            throw error;
        }
    }
}