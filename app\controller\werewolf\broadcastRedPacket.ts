import BaseMegaController from "./BaseMegaController";
import {IbroadcastListReq, IbroadcastListResp} from "../../model/werewolf";
import {HttpErr, IerrorMsg} from "../../model/common";
import {
    IbroadcastRedPacketInsertParams,
    IbroadcastRedPacketUpdateParams,
    IbroadcastRedPacketUpdateTypeParams
} from "../../model/broadcastRedPacketDto";

export default class BroadcastRedPacketController extends BaseMegaController{
    public async getList(){
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: IbroadcastListReq = ctx.request.body;
            const responseBody: IbroadcastListResp = await ctx.service.werewolf.broadcastRedPacket.getList();
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async insert(){
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: IbroadcastRedPacketInsertParams = ctx.request.body;
            const responseBody = await ctx.service.werewolf.broadcastRedPacket.insert(requestBody);
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async update(){
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: IbroadcastRedPacketUpdateParams = ctx.request.body;
            const responseBody = await ctx.service.werewolf.broadcastRedPacket.update(requestBody);
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateType(){
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: IbroadcastRedPacketUpdateTypeParams = ctx.request.body;
            const responseBody = await ctx.service.werewolf.broadcastRedPacket.updateType(requestBody);
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
}