/*
 * @Description: 推送系统
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-03-16 14:30:53
 * @LastEditors: hammercui
 * @LastEditTime: 2020-03-18 13:27:42
 */
import { Controller } from 'egg';
import { PushMessageResquest } from '../../model/werewolf';
import { HttpErr, IerrorMsg } from '../../model/common';
import BaseMegaController from './BaseMegaController';
export default class PushController extends BaseMegaController {

    //查询三天内新注册用户一直没开局的
    public async searchNewUserList() {
        const { ctx, logger } = this;
        const rule = {
            content: { type: 'string' }
        }
        try {
            ctx.validate(rule);
            const request: PushMessageResquest = ctx.request.body;
            await ctx.service.werewolf.push.searchNewUserList(request);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }

    //老用户沉默15天未登录的（老用户标准：开局10局以上）
    public async searchOldUserFifthDays() {
        const { ctx, logger } = this;
        const rule = {
            content: { type: 'string' }
        }
        try {
            ctx.validate(rule);
            const request: PushMessageResquest = ctx.request.body;
            await ctx.service.werewolf.push.searchOldUserFifthDays(request);
            this.respSucc();
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }

    //老用户沉默30天未登录的（老用户标准：开局10局以上）
    public async searchOldUserThirtyDays() {
        const { ctx, logger } = this;
        const rule = {
            content: { type: 'string' }
        }
        try {
            ctx.validate(rule);
            const request: PushMessageResquest = ctx.request.body;
            await ctx.service.werewolf.push.searchOldUserThirtyDays(request);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }
    //给指定用户发推送
    public async sendMsgToSomebody() {
        const { ctx, logger } = this;
        const rule = {
            userId: { type: 'array' },
            content: { type: 'string' }
        }
        try {
            ctx.validate(rule);
            const request: PushMessageResquest = ctx.request.body;
            //wait ctx.service.werewolf.push.sendMsgToSomebody(request);
            this.respSucc()

        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }
}
