/*
 * @Description: Mega路由权限配置类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2019-01-30 10:06:45
 * @LastEditors: zhanglu
 * @LastEditTime: 2022-09-26 13:24:17
 */

/**
 * @name: 需要权限管理的路由id
 */
export const AccessRouteId = {
  /**
     * app狼人杀
     */
  app_wolf: 1,
  /**
   * app了了
   */
  app_liaoliao: 2,
  /**
   * 狼人杀 资产详情
   */
  wolf_treasure_detail: 3,
  /**
   * 狼人杀 状态操作
   */
  wolf_status_update: 4,
  /**
   * 狼人杀 广告位
   */
  wolf_ad: 5,
  /**
   * 了了 提款
   */
  liao_drawings: 6,
  /**
   * 管理路由访问权限
   */
  admin_edit_route: 7,
  /**
   * 狼人杀 头像框
   */
  wolf_avatar_frame: 8,
  /**
   * 狼人杀 成就
   */
  wolf_achievement: 9,
  /**
   * 狼人杀 口令红包
   */
  wolf_redbag: 10,

  /**
   * 狼人杀 新手辅助机器人
   */
  wolf_newPlayerRobot: 83,

  // 【狼人杀】 应用管理-推送
  wolf_push: 11,
  // 【狼人杀】操作流水
  wolf_operation: 12,
  // 【狼人杀】应用管理-开屏
  wolf_splash: 13,
  // 【狼人杀】发送邮件
  wolf_email: 14,
  // 【狼人杀】 玩家记录-世界频道
  wolf_record_broardcast: 15,

  // 【狼人杀】 玩家记录-聊天记录
  wolf_record_im: 16,
  // 【狼人杀】 玩家记录-游戏记录
  wolf_record_game: 17,
  // 【狼人杀】 玩家资产-全部资产
  wolf_treasure_all: 18,
  // app 【开发】
  app_dev: 19,
  // 【开发】 接口文档列表
  dev_swagger: 20,
  // 【狼人杀】 玩家状态-状态查询
  wolf_status_query: 21,
  // 【狼人杀】 玩家状态-清逃怕
  wolf_clear_escape: 22,
  // 【狼人杀】 应用管理-宝箱管理
  wolf_box_manager: 23,
  // 【狼人杀】 应用管理-道具管理
  wolf_props_manager: 24,
  // 【狼人杀】 玩家状态-账号变更
  wolf_member_change: 25,
  // 【狼人杀】 玩家状态-账号变更确认
  wolf_member_change_confirm: 26,
  // 版型管理
  wolf_game_config: 27,
  // 【狼人杀】 应用管理-首页弹窗
  wolf_home_dialog: 28,
  // 【狼人杀】应用管理-2w框
  wolf_2w_avatar: 29,
  //【狼人杀】运营活动-配置管理
  wolf_aty_conf: 30,
  // 【狼人杀】商城管理
  wolf_mall: 31,
  // 【狼人杀】商城管理-礼包管理
  wolf_gift_bag_manager: 32,
  // 【狼人杀】商城管理-道具管理
  wolf_mall_item: 33,
  // 【狼人杀】商城管理-道具库管理
  wolf_mall_item_dic: 34,
  // 【狼人杀】应用管理-系统公告
  wolf_announce: 35,
  // 【狼人杀】巅峰狼王
  wolf_wolf_king: 36,
  // 【狼人杀】商城管理-公会道具管理
  wolf_group_props: 37,
  // 【狼人杀】 玩家资产-冲值2w
  wolf_treasure_20000: 38,
  // 【狼人杀】运营活动-奖励管理
  wolf_aty_award: 39,
  // 【狼人杀】融云控制
  wolf_rong_cloud_control: 40,
  // 【狼人杀】主播板块banner控制
  wolf_anchor_banner_control: 41,
  // 【狼人杀】版本更新弹窗h5编辑后台
  wolf_tsys_bvrs: 42,

  //【狼人杀】刷分违规处理
  wolf_group_controller: 46,

  //【狼人杀】违规刷分-小号黑名单
  wolf_group_blacklist: 48,

  //【狼人杀】违规刷分-账户异常查询
  wolf_group_abnormal: 50,
  // 运营活动-任务配置
  wolf_aty_task: 47,
  //狼人杀】违规刷分-根据udid查询用户信息
  wolf_group_udidInquire: 51,
  //【狼人杀】违规刷分-团队刷分查询
  wolf_group_scoreGroup: 52,

  //剧本杀 app
  app_script: 49,
  //【剧本杀】查看审核流水
  script_audit_list: 53,

  wolf_credit_manager: 54,
  //【剧本杀】用户管理
  script_user_info_manager: 55,
  //【剧本杀】剧本圈
  script_moment_list: 59,
  // 【狼人杀】 玩家状态-udid查询
  wolf_udid_query: 56,
  //【剧本杀】热门剧本
  script_hot_script: 62,
  //【剧本杀】 首页banner图管理
  script_banner_main_page: 64,
  // 【狼人杀】信誉分统计
  wolf_credit_statistics: 65,
  //【剧本杀】提现
  script_money_manager: 66,
  // 【狼人杀】比赛区
  wolf_game_area: 67,
  // 【狼人杀】身份证状态查询
  wolf_idCard_query: 68,
  // 【狼人杀】神皇贵族
  wolf_noble: 69,
  // 【狼人杀】运营活动-才艺后台
  wolf_aty_talent: 70,
  // 【狼人杀】查弹幕
  wolf_ban_danmu: 71,
  // 【狼人杀】世界频道
  wolf_broadcast: 72,
  // 【狼人杀】竞技场活动
  wolf_arenaActivity: 73,
  // 竞技场
  wolf_arena: 74,
  //收藏室
  wolf_collect_room: 75,
  //【狼人杀】集市
  wolf_makert: 76,
  //集市列表
  wolf_makert_list: 77,
  //我的
  wolf_market_mine: 78,
  //市场监督
  wolf_market_observer: 79,
  //交易记录
  wolf_market_transaction: 80,
  //框石管理
  wolf_market_stonemanage: 81,
  //服务监控
  dev_server_monitor: 82,

  //【AI】mst
  app_mst: 107,
  mst_rawPicture: 108,
  mst_modelManager: 109,

  //【显卡租赁】
  app_lease: 200,
  lease_history: 201,


}
// 需要权限管理的路由名称
export const AccessRouteName = {
  app_wolf: 'app_wolf', // app狼人杀
  app_liaoliao: 'app_liaoliao', // app了了
  wolf_treasure_detail: 'wolf_treasure_detail', // 狼人杀 资产详情
  wolf_detail_update: 'wolf_detail_update', // 狼人杀 状态操作
  wolf_ad: 'wolf_ad', // 狼人杀 广告位
  liao_drawings: 'liao_drawings',// 了了 提款
  admin_edit_route: 'admin_edit_route' // 管理路由访问权限
};

// export default {
//   AccessRouteId,
//   AccessRouteName
// }
