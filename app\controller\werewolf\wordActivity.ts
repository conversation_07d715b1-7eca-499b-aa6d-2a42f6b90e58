/*
 * @Description: 商城道具管理-控制类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: z<PERSON>yu
 * @Date: 2020-04-24 13:00:00
 * @LastEditors: zhang<PERSON>
 * @LastEditTime: 2022-05-05 15:25:08
 */
import { Controller } from 'egg';
import BaseMegaController from './BaseMegaController';
import { IerrorMsg, HttpErr } from '../../model/common';

export default class MallItemController extends BaseMegaController {

    public async getWordActivityList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.wordActivity.getWordActivityList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getWordActivityListCount() {
        const { ctx, logger } = this;
        try {
            let res = await ctx.service.werewolf.wordActivity.getWordActivityListCount();
            this.respSuccData(res)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async getWordAwardList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.wordActivity.getWordAwardList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async updateWordActivity() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.wordActivity.updateWordActivity(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }

    public async updateWordActivityState() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.wordActivity.updateWordActivityState(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }

    public async insertWordActivity() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.wordActivity.insertWordActivity(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }
    
    public async getWordBoxList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.wordActivity.getWordBoxList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getWordAcitvityImgList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.wordActivity.getWordAcitvityImgList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async insertWordAward() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.wordActivity.insertWordAward(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }

    public async updateWordAward() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.wordActivity.updateWordAward(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }

    public async insertWordBox() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.wordActivity.insertWordBox(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }

    public async updateWordBox() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.wordActivity.updateWordBox(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }

    public async updateWordBoxDelsign() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.wordActivity.updateWordBoxDelsign(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }
    
    public async getWordJumpList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.wordActivity.getWordJumpList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async updateWordJump() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.wordActivity.updateWordJump(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }

    public async updateWordAcitvityImg() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.wordActivity.updateWordAcitvityImg(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }

    public async getWordInfo() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.wordActivity.getWordInfo(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async updateWordInfo() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.wordActivity.updateWordInfo(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err) }
    }
}
