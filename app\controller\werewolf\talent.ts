/*
 * @Description: 商城道具管理-控制类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: z<PERSON><PERSON>
 * @Date: 2020-04-24 13:00:00
 * @LastEditors: zhang<PERSON>
 * @LastEditTime: 2021-09-07 14:34:08
 */
import { Controller } from 'egg';
import BaseMegaController from './BaseMegaController';
import { IerrorMsg, HttpErr } from '../../model/common';

export default class TalentController extends BaseMegaController {

    public async getVideoList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.talent.getVideoList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async updateVideoDelsign() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.talent.updateVideoDelsign(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async updateVideo() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.talent.updateVideo(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async insertVideo() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.talent.insertVideo(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async updateVideoUrl() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.talent.updateVideoUrl(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async getSeasonList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.talent.getSeasonList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async insertVideoSeason() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.talent.insertVideoSeason(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async updateVideoSeason() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.talent.updateVideoSeason(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async updateVideoSeasonDelsign() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.talent.updateVideoSeasonDelsign(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async updateVideoSeasonCurrent() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.talent.updateVideoSeasonCurrent(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }
}
