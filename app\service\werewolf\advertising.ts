/*
 * @Description: 玩家资产
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2018-11-26 16:30:09
 * @LastEditors: hammercui
 * @LastEditTime: 2020-04-29 14:31:16
 */

import { Service } from "egg";
import {
  IadListRequest,
  IadListResponse,
  IadOperationRequest,
  IadOperationResponse,
  Iadvertising,
  AddScreenAdImgRequest,
  SaveAdInfoRequest
} from "../../model/werewolf";
import { adTypeEnum, adTypeName } from "../../model/staticEnum";
import BaseMegaService from './BaseMegaService';

export default class AdvertisingService extends BaseMegaService {

  /**
   * 增加开屏广告
   * @param 添加开屏图
   */
  public async addScreenAdImg(request: AddScreenAdImgRequest) {
    const { logger } = this;
    const name = request.name
    const icon = request.icon
    const loading = request.loading
    const loadimgname = request.loadimgname
    const tend = request.tend
    const ad_tend = request.ad_tend
    const state = request.state;
    const anchor = request.anchor;
    try {
      const sqlStr = `INSERT INTO ad_info 
      ( name, icon, loading, loadimgname, tend, ad_tend, createtime, state, left_1, right_1, left_2, right_2, ad, anchor,ad_type,ad_time,delsign) 
      VALUES(?,?,?,?,?,?,NOW(),?,0,0,0,0,0,?,?,?,0)
      `;
      // await werewolf.query(sql);
      await this.execSql(sqlStr, [name, icon, loading, loadimgname, tend, ad_tend, state, anchor, request.fileType, request.duration]);
    } catch (error) {
      logger.error("sql err", error);
      throw new Error('sql err:' + error.message)
    }
  }

  /**
   * 
   * @param 删除开屏图
   */
  public async delScreenAdImg(id: number) {
    const { app } = this;
    const werewolf = app.mysql.get("werewolf");
    try {
      const sql = "UPDATE ad_info SET delsign = 1 WHERE id = " + id;
      await werewolf.query(sql);
    } catch (error) {
      throw new Error('删除失败')
    }
  }

  /**
   * 
   * @param 保存开屏图信息
   */
  public async saveAdInfo(request: SaveAdInfoRequest) {
    const { app } = this;
    const werewolf = app.mysql.get("werewolf");
    const id = request.id;
    const open = request.open;
    try {
      const sql = "UPDATE ad_info SET ad = " + open + " WHERE id = " + id;
      await werewolf.query(sql);
    } catch (error) {
      throw new Error('保存失败')
    }
  }

  /**
   * @name:
   * @msg:查询广告位列表
   * @param playerId{number}
   * @return:
   */
  public async list(request: IadListRequest): Promise<IadListResponse> {
    const { app } = this;
    try {
      const werewolf = app.mysql.get("werewolf");
      //查询总数
      const countResult = await werewolf.query(
        `SELECT COUNT(id) AS count FROM ad_info WHERE delsign = 0 AND id != 39`
      );
      const count: number = countResult[0].count;
      if (!count) {
        return { ...request, count: 0, dataArray: [] };
      }
      const start = werewolf.escape(request.start);
      const offset = werewolf.escape(request.offset);
      const sql = `
            SELECT * FROM ad_info
            WHERE delsign = 0 AND id != 39
            ORDER BY createtime
            DESC
            LIMIT ${start},${offset}`;
      const results = await werewolf.query(sql);
      let array: Iadvertising[] = new Array();
      if (!!results && results.length > 0) {
        const len = results.length;
        for (let i = 0; i < len; i++) {
          //id,icon,loading,tend AS iconLink, ad_tend AS loadingLink,name,left_1,left_2,right_1,right_2,ad
          array.push({
            id: results[i].id,
            icon: results[i].icon,
            loading: results[i].loading,
            iconLink: results[i].tend,
            loadingLink: results[i].ad_tend,
            name: results[i].name,
            left_1: results[i].left_1,
            left_2: results[i].left_2,
            right_1: results[i].right_1,
            right_2: results[i].right_2,
            ad: results[i].ad
          });
        }
      }

      return { ...request, count: count, dataArray: array };
    } catch (err) {
      throw err;
    }
  }
  /**
   * @name: 操作广告位
   * @msg:
   * @param {type}
   * @return:
   */
  public async operation(
    request: IadOperationRequest
  ): Promise<IadOperationResponse> {
    const { app } = this;
    const werewolf = app.mysql.get("werewolf");
    const werewolfConn = await werewolf.beginTransaction();
    try {
      //除置空和开屏外
      if (request.type < adTypeEnum.splash) {
        //是否需要更新旧数据
        let oldId = 0;
        //1 首先检查是否广告位被开启
        const oldSelSql =
          "SELECT id FROM ad_info WHERE" + ` ${adTypeName[request.type]} = 1`;
        const oldIds = await werewolfConn.query(oldSelSql);
        //存在查询结果
        if (!!oldIds && oldIds.length > 0) {
          oldId = oldIds[0].id;
        }
        try {
          //2 更改新的广告位
          //先清除自己除了广告位（ad）得状态
          const ownRow = {
            left_1: 0,
            left_2: 0,
            right_1: 0,
            right_2: 0
          };
          const ownOptions = {
            where: {
              id: request.aid
            }
          };
          await werewolfConn.update("ad_info", ownRow, ownOptions);
          //再清除本次操作广告位之前得id得状态
          if (oldId != 0 && oldId != request.aid) {
            const oldSql = `UPDATE ad_info SET ${
              adTypeName[request.type]
              } = 0 WHERE id = ${oldId}`;
            await werewolfConn.query(oldSql);
          }
          //最后改变本id本广告位得状态
          const newSql = `UPDATE ad_info SET ${
            adTypeName[request.type]
            } = 1 WHERE id = ${request.aid}`;
          await werewolfConn.query(newSql);
          let array: Iadvertising[] = new Array();
          let newResult = await werewolfConn.get("ad_info", {
            id: request.aid
          });
          if (!!newResult && newResult.id != null) {
            array.push({
              id: newResult.id,
              icon: newResult.icon,
              loading: newResult.loading,
              iconLink: newResult.tend,
              loadingLink: newResult.ad_tend,
              name: newResult.name,
              left_1: newResult.left_1,
              left_2: newResult.left_2,
              right_1: newResult.right_1,
              right_2: newResult.right_2,
              ad: newResult.ad
            });
          }
          if (oldId != 0 && oldId != request.aid) {
            const oldResult = await werewolfConn.get("ad_info", { id: oldId });
            if (!!oldResult && oldResult.id != null) {
              array.push({
                id: oldResult.id,
                icon: oldResult.icon,
                loading: oldResult.loading,
                iconLink: oldResult.tend,
                loadingLink: oldResult.ad_tend,
                name: oldResult.name,
                left_1: oldResult.left_1,
                left_2: oldResult.left_2,
                right_1: oldResult.right_1,
                right_2: oldResult.right_2,
                ad: oldResult.ad
              });
            }
          }
          werewolfConn.commit();
          return { dataArray: array };
        } catch (e) {
          werewolfConn.rollback();
          throw e;
        }
      } else {
        //置空和开屏
        if ((request.type == adTypeEnum.splash)) {
          let oldId = 0;
          const oldIds = await werewolfConn.get("ad_info", { ad: 1 });
          if (!!oldIds && oldIds.id != null) {
            oldId = oldIds.id;
          }
          if (oldId != 0 && oldId != request.aid) {
            const oldRow = {
              ad: 0
            };
            const oldOptions = {
              where: {
                id: oldId
              }
            };
            await werewolfConn.update("ad_info", oldRow, oldOptions);
          }
          const newRow = {
            ad: 1
          };
          const newOptions = {
            where: {
              id: request.aid
            }
          };
          await werewolfConn.update("ad_info", newRow, newOptions);
          let array: Iadvertising[] = new Array();
          const result = await werewolfConn.get("ad_info", { id: request.aid });
          if (!!result && result.id != null) {
            array.push({
              id: result.id,
              icon: result.icon,
              loading: result.loading,
              iconLink: result.tend,
              loadingLink: result.ad_tend,
              name: result.name,
              left_1: result.left_1,
              left_2: result.left_2,
              right_1: result.right_1,
              right_2: result.right_2,
              ad: result.ad
            });
          }
          if (oldId != 0 && oldId != request.aid) {
            const oldResult = await werewolfConn.get("ad_info", {
              id: oldId
            });
            if (!!oldResult && oldResult.id != null) {
              array.push({
                id: oldResult.id,
                icon: oldResult.icon,
                loading: oldResult.loading,
                iconLink: oldResult.tend,
                loadingLink: oldResult.ad_tend,
                name: oldResult.name,
                left_1: oldResult.left_1,
                left_2: oldResult.left_2,
                right_1: oldResult.right_1,
                right_2: oldResult.right_2,
                ad: oldResult.ad
              });
            }
          }
          werewolfConn.commit();
          return { dataArray: array };
        } else {
          const result = await this.operationNull(werewolfConn, request);
          return result;
        }
      }
    } catch (err) {
      werewolfConn.rollback();
      throw err;
    }
  }

  // 置空操作
  public async operationNull(
    werewolf,
    request: IadOperationRequest
  ): Promise<IadOperationResponse> {
    let newSql = `UPDATE ad_info SET left_1=0,left_2=0,right_1=0,right_2=0,ad=0 WHERE id=${
      request.aid
      }`;
    try {
      await werewolf.query(newSql);
      let array: Iadvertising[] = new Array();
      const oldResult = await werewolf.get("ad_info", { id: request.aid });
      if (!!oldResult && oldResult.id != null) {
        array.push({
          id: oldResult.id,
          icon: oldResult.icon,
          loading: oldResult.loading,
          iconLink: oldResult.tend,
          loadingLink: oldResult.ad_tend,
          name: oldResult.name,
          left_1: oldResult.left_1,
          left_2: oldResult.left_2,
          right_1: oldResult.right_1,
          right_2: oldResult.right_2,
          ad: oldResult.ad
        });
      }
      await werewolf.commit();
      return { dataArray: array };
    } catch (e) {
      werewolf.rollback();
      throw e;
    }
  }
}
