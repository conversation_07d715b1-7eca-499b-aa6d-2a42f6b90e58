/*
 * @Description: 
 * @Company: sdbean
 * @Author: z<PERSON><PERSON>
 * @Date: 2021-03-30 17:23:36
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-04-23 17:05:34
 */
import { Router, Application } from "egg";
import { AccessRouteId, AccessRouteName } from "./model/accessRouteCof";
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

    const { controller, router } = app;

    router.post(`${API_VERSION}/werewolf/noble/getUserNobleEmperor`, accCtr(AccessRouteId.wolf_noble), controller.werewolf.noble.getUserNobleEmperor)
    router.post(`${API_VERSION}/werewolf/noble/getAwardList`, accCtr(AccessRouteId.wolf_noble), controller.werewolf.noble.getAwardList)
    router.post(`${API_VERSION}/werewolf/noble/insertNoble`, accCtr(AccessRouteId.wolf_noble), controller.werewolf.noble.insertNoble)
    router.post(`${API_VERSION}/werewolf/noble/updateNoble`, accCtr(AccessRouteId.wolf_noble), controller.werewolf.noble.updateNoble)
    router.post(`${API_VERSION}/werewolf/noble/getUserNobleInfo`, accCtr(AccessRouteId.wolf_noble), controller.werewolf.noble.getUserNobleInfo)
    router.post(`${API_VERSION}/werewolf/noble/getUserOrderList`, accCtr(AccessRouteId.wolf_noble), controller.werewolf.noble.getUserOrderList)
    router.post(`${API_VERSION}/werewolf/noble/insertOrder`, accCtr(AccessRouteId.wolf_noble), controller.werewolf.noble.insertOrder)
    router.post(`${API_VERSION}/werewolf/noble/updateUploadOrder`, accCtr(AccessRouteId.wolf_noble), controller.werewolf.noble.updateUploadOrder)
    router.post(`${API_VERSION}/werewolf/noble/updateProvideOrder`, accCtr(AccessRouteId.wolf_noble), controller.werewolf.noble.updateProvideOrder)
    router.post(`${API_VERSION}/werewolf/noble/getOpreateList`, accCtr(AccessRouteId.wolf_noble), controller.werewolf.noble.getOpreateList)
    router.post(`${API_VERSION}/werewolf/noble/getUserNobleNumberList`, accCtr(AccessRouteId.wolf_noble), controller.werewolf.noble.getUserNobleNumberList)
    router.post(`${API_VERSION}/werewolf/noble/updateUserRoomNo`, accCtr(AccessRouteId.wolf_noble), controller.werewolf.noble.updateUserRoomNo)
    router.post(`${API_VERSION}/werewolf/noble/renewalNoble`, accCtr(AccessRouteId.wolf_noble), controller.werewolf.noble.renewalNoble)

}

export default load
