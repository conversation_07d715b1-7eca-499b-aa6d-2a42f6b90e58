/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-06 10:15:24
 * @LastEditTime: 2021-07-10 11:05:22
 * @LastEditors: jiawen.wang
 */
import { Service } from 'egg';
// import { IrecordList } from '../../model/randomRecordCof';
import BaseMegaService from './BaseMegaService';

export default class AliUmidService extends BaseMegaService {

    public async getAliUmidList(request) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        const umid = db.escape(request.umid);
        try {
            //查询板子列表
            let sql = `SELECT 
                    b.no,a.nickname,b.logintime,a.create_time,c.reason
                FROM 
                    account_abuse_pro_record a
                LEFT JOIN
                    tuser b
                ON 
                    a.account_id = b.no 
                LEFT JOIN 
                    user_ban_udid_ali c
                ON
                    a.umid = c.ali_udid
                WHERE 
                    a.umid = ${umid}`;
            const aliUmidList = await db.query(sql);
            return aliUmidList;
        } catch (error) {
            throw error;
        }
    }

    public async banUserAliUmid(req) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        // const werewolfConn = await db.beginTransaction();
        const umid = db.escape(req.umid);
        const reason = db.escape(req.reason);
        try {
            //查询板子列表
            let banSql = `SELECT reason FROM user_ban_udid_ali WHERE ali_udid = ${umid}`
            const banAliUmidStatus = await this.selectOne(banSql);
            if (banAliUmidStatus) {
                return banAliUmidStatus
            } else {
                let sql = `INSERT INTO user_ban_udid_ali (ali_udid,reason) VALUES (?,?)`;
                await this.execSql(sql, [req.umid, req.reason])
                // await werewolfConn.query(sql, [req.umid, req.reason]);
                // await werewolfConn.commit();
            }
        } catch (error) {
            // await werewolfConn.rollback();
            throw error;
        }
    }
}