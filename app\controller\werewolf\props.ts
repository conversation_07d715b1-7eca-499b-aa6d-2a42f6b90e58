/*
 * @Description: 玩家资产
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2018-11-26 16:25:52
 * @LastEditors: hammercui
 * @LastEditTime: 2018-12-28 11:15:30
 */
import { Controller } from 'egg';
import { HttpErr, IerrorMsg } from '../../model/common';
import { IpropsRequest, IpropsResponse } from '../../model/werewolf';
import { wrapHeadIcon } from '../../util/utils';

export default class PropsController extends Controller {
	public async all() {
		const { ctx, logger, app } = this;
		// 校验规则
		const rule = {
			playerId: { type: 'number' }
		};
		try {
			//1 校验
			ctx.validate(rule);
			const propsReq: IpropsRequest = ctx.request.body;
			const result: IpropsResponse = await ctx.service.werewolf.props.all(propsReq.playerId);
			result.headicon = wrapHeadIcon(result.headicon);
			ctx.body = result;
			ctx.status = HttpErr.Success;
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}
}
