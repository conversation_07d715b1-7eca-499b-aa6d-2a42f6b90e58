/*
 * @Description: 团队活动配置中心
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: musong
 */
import { Router, Application } from "egg";
import { AccessRouteId } from './model/accessRouteCof';
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

    const { controller, router } = app;
    //【运营活动】-团队活动
    router.post(`${API_VERSION}/werewolf/teamActivity/create`, accCtr(AccessRouteId.wolf_aty_award), controller.werewolf.teamActivity.createTeamActivity)
    router.post(`${API_VERSION}/werewolf/teamActivity/update`, accCtr(AccessRouteId.wolf_aty_award), controller.werewolf.teamActivity.updateTeamActivity)
    router.post(`${API_VERSION}/werewolf/teamActivity/activityList`, accCtr(AccessRouteId.wolf_aty_award), controller.werewolf.teamActivity.teamActivityList)
    router.post(`${API_VERSION}/werewolf/teamActivity/detail`, accCtr(AccessRouteId.wolf_aty_award), controller.werewolf.teamActivity.teamActivityDetail)

}

export default load
