/*
 * @Description: 游戏内记录
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2019-07-10 15:13:01
 * @LastEditors: hammercui
 * @LastEditTime: 2020-09-12 15:35:14
 */

import { Controller } from 'egg';
import { HttpErr, IerrorMsg } from '../../model/common';
import { IGameRecordListRequest, IgameReplayRequest, IGameRecordListResponse, IgameReplayResponse, IgameRecordDetailReq, IgameRecordDetailResp } from '../../model/werewolf';

export default class GameRecordController extends Controller {

	/**
	 * @name: 查询游戏列表
	 * @msg: 
	 * @param {type} 
	 * @return: 
	 */
	public async list() {
		const { ctx, logger } = this;
		// 校验规则
		const rule = {
			playerId: { type: 'number' },
			start: { type: 'number' },
			offset: { type: 'int', min: 1 },
		};
		try {
			// 校验
			ctx.validate(rule);
			const requestBody: IGameRecordListRequest = ctx.request.body;
			const responseBody: IGameRecordListResponse = await ctx.service.werewolf.gameRecord.list(requestBody);
			ctx.body = responseBody;
			ctx.status = HttpErr.Success;
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}
	/**
		* @name: 查询游戏信息列表
		* @msg: 
		* @param {type} 
		* @return: 
		*/
	public async gameInfoList() {
		const { ctx, logger } = this;
		// 校验规则
		const rule = {
			gameNo: { type: 'number' },
		};
		try {
			// 校验
			ctx.validate(rule);
			const requestBody = ctx.request.body;
			const responseBody = await ctx.service.werewolf.gameRecord.gameInfoList(requestBody);
			ctx.body = responseBody;
			ctx.status = HttpErr.Success;
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}
	/**
	* @name: 下载游戏信息列表
	* @msg: 
	* @param {type} 
	* @return: 
	*/
	public async downloadGameInfo() {
		const { ctx, logger, service, app } = this;
		const rule = {
			address: { type: 'string' },
		};

		try {
			ctx.validate(rule);
			const requestBody = ctx.request.body;
			const responseBody = await ctx.service.werewolf.gameRecord.downloadGameInfo(requestBody);
			ctx.body = responseBody
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}

	/**
		* @name: 查询游戏详情
		* @msg: 
		* @param {type} 
		* @return: 
		*/
	public async detail() {
		const { ctx, logger } = this;
		// 校验规则
		const rule = {
			gameNo: { type: 'number' },
		};
		try {
			// 校验
			ctx.validate(rule);
			const requestBody: IgameRecordDetailReq = ctx.request.body;
			const responseBody: IgameRecordDetailResp = await ctx.service.werewolf.gameRecord.gameDetail(requestBody);
			ctx.body = responseBody;
			ctx.status = HttpErr.Success;
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}

	/**
  * @name: 游戏复盘
  * @msg: 
  * @param {type} 
  * @return: 
  */
	public async replay() {
		const { ctx, logger } = this;
		// 校验规则
		const rule = {
			gameNo: { type: 'string' },
		};
		try {
			// 校验
			ctx.validate(rule);
			const requestBody: IgameReplayRequest = ctx.request.body;
			const responseBody: IgameReplayResponse = await ctx.service.werewolf.gameRecord.replay(requestBody);
			ctx.body = responseBody;
			ctx.status = HttpErr.Success;
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}
}
