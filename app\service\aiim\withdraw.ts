/*
 * @Description: 了了提现具体逻辑
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: leeou
 * @Date: 2019-02-02 11:26:39
 * @LastEditors: leeou
 * @LastEditTime: 2019-03-26 09:59:45
 */
import { Service } from "egg";
import * as moment from "moment";
import {
  IwithDrawListRequest,
  IwithDrawListResponse,
  IwithDrawList,
  IwithDrawOperateRequest,
  IwithDrawOperateResponse
} from "../../model/aiim";
import { imWithDrawType } from "../../model/staticEnum";

export default class WithDrawService extends Service {
  /**
   * @name:
   * @msg:
   * @param {type}
   * @return:
   */
  public async list(
    request: IwithDrawListRequest
  ): Promise<IwithDrawListResponse> {
    const { app } = this;
    const aiim = app.mysql.get("aiim");
    const start = aiim.escape(request.start);
    const offset = aiim.escape(request.offset);
    let status = "已申请";
    if (request.type == 0) {
      status = "已通过";
    } else if (request.type == 2) {
      status = "未通过";
    }
    let count = 0;
    let sql =
      "SELECT COUNT(*) AS total FROM wallet_log WHERE reason = '提现' AND `status` = '" +
      status +
      "' ORDER BY id DESC;";
    let result = await aiim.query(sql);
    if (!!result && result.length > 0) {
      count = result[0].total;
    }
    sql =
      "SELECT wl.id,wl.amount,uw.alipayAccount,uw.alipayName,wl.time FROM wallet_log wl" +
      " LEFT JOIN user_wallet uw ON wl.user_id = uw.user_id WHERE wl.reason = '提现' AND wl.`status` = '" +
      status +
      "' ORDER BY wl.id DESC LIMIT " +
      start +
      "," +
      offset;
    result = await aiim.query(sql);
    let array: IwithDrawList[] = new Array();
    if (!!result && result.length > 0) {
      for (let i = 0, len = result.length; i < len; i++) {
        array.push({
          id: result[i].id,
          alipayAccount: result[i].alipayAccount,
          alipayName: result[i].alipayName,
          money: result[i].amount,
          datetime: (result[i].time).format("YYYY-MM-DD HH:mm:ss"),
          status: request.type
        });
      }
    }
    return {
      ...request,
      count: count,
      list: array
    };
  }

  /**
   * 改变状态
   * @param request 
   */
  public async changeState(
    request: IwithDrawOperateRequest
  ): Promise<IwithDrawOperateResponse> {
    const { app } = this;
    const aiim = app.mysql.get("aiim");
    const reportId = aiim.escape(request.reportId);
    let item = new Array<IwithDrawList>();
    let lang = "";
    switch (request.type) {
      case imWithDrawType.untreated:
        lang = "未通过";
        break;
      case imWithDrawType.processed:
        lang = "已通过";
        break;
      default:
        lang = "未通过";
        break;
    }
    const aiimConn = await aiim.beginTransaction();
    try {
      let oldsql = `SELECT uw.id,wl.amount,wl.status FROM wallet_log wl LEFT JOIN user_wallet uw ON wl.user_id=uw.user_id WHERE wl.id=${reportId}`;
      let result = await aiimConn.query(oldsql);
      //查询原始数据
      if (!!result && result.length > 0) {
        let oldStatus;
        // console.log(result)
        switch (result[0].status) {
          case "已通过":
            oldStatus = imWithDrawType.processed;
            break;
          case "已完成":
            oldStatus = imWithDrawType.complete;
            break;
          case "已申请":
            oldStatus = imWithDrawType.apply;
            break;
          case "未通过":
            oldStatus = imWithDrawType.untreated;
            break;
          default:
            oldStatus = imWithDrawType.complete;
            break;
        }
        let data = {
          add: result[0].amount,
          id: result[0].id,
          status: oldStatus
        };
        // console.log(data,result)
        if (
          data.status == imWithDrawType.apply ||
          data.status == imWithDrawType.untreated
        ) {
          //更改wallet_log表
          let row = {
            id: reportId,
            status: lang
          };
          result = await aiimConn.update("wallet_log", row);
          //更改user_wallet表
          if (request.type == imWithDrawType.processed) {
            await aiimConn.query(
              `UPDATE user_wallet SET withdrawTotal= withdrawTotal+${
                data.add
              } WHERE id=${data.id}`
            );
          }
        }
      }

      //获取新数据
      result = await aiimConn.query(
        `SELECT wl.status,wl.id,wl.amount,uw.alipayAccount,uw.alipayName,wl.time FROM wallet_log wl LEFT JOIN user_wallet uw ON wl.user_id = uw.user_id WHERE wl.id=${reportId}`
      );
      if (!!result && result.length > 0) {
        let status;
        switch (result[0].status) {
          case "已通过":
            status = imWithDrawType.processed;
            break;
          case "已完成":
            status = imWithDrawType.complete;
            break;
          case "已申请":
            status = imWithDrawType.apply;
            break;
          case "未通过":
            status = imWithDrawType.untreated;
            break;
          default:
            status = imWithDrawType.untreated;
            break;
        }
        item.push({
          id: result[0].id,
          alipayAccount: result[0].alipayAccount,
          alipayName: result[0].alipayName,
          money: result[0].amount,
          datetime: moment(result[0].time).format("YYYY-MM-DD HH:mm:ss"),
          status: status
        });
      }

      await aiimConn.commit();
      return {
        item
      };
    } catch (e) {
      await aiimConn.rollback();
      throw e;
    }
  }
}
