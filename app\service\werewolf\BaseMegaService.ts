/*
 * @Description: Service基础类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-03-17 11:24:20
 * @LastEditors: zhanglu
 * @LastEditTime: 2020-11-20 14:21:57
 */
import { Service } from 'egg';
import {MySqlClientName} from "../../../typings";
export default class BaseMegaService extends Service {

    /**
     * @name: 执行insert,update，delete等操作
     * @msg:
     * @param {type}
     * @return:
     */
    public async execSql(sqlStr: string, params, dbName: MySqlClientName = 'werewolf') {
        try {
            const db = this.app.mysql.get(dbName);
            const result = await db.query(sqlStr, params);
            return result;
        } catch (error) { throw error; }
    }

    /**
     * @name: 查询一个对象
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async selectOne(sqlStr: string, params=[] as any, dbName: MySqlClientName = 'werewolf') {
        try {
            const db = this.app.mysql.get(dbName);
            const result = await db.query(sqlStr, params) as any[];
            if(result && result.length > 0){
                let dataString = JSON.stringify(result);
                let data = JSON.parse(dataString);
                return data[0];
            }else{
                return null;
            }
        } catch (error) { throw error; }
    }

    /**
     * @name: 查询一个列表
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async selectList(sqlStr: string, params=[] as any, dbName: MySqlClientName = 'werewolf') {
        try {
            const db = this.app.mysql.get(dbName);
            const result = await db.query(sqlStr, params) as any[];

            if(result && result.length > 0 && result[0]){
                let dataString = JSON.stringify(result);
                let data = JSON.parse(dataString);
                return data;
            }else{
                return null;
            }
        } catch (error) { throw error; }
    }
}
