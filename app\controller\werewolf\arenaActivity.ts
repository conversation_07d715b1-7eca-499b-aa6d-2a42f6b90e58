/*
 * @Author: your name
 * @Date: 2021-07-29 14:45:48
 * @LastEditTime: 2021-07-29 16:40:57
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /MGKFHTServer/app/controller/werewolf/arenaActivity.ts
 */
import { Controller } from 'egg';
import { IerrorMsg, HttpErr } from '../../model/common';
import {IarenaUserInfo,IarenaActivityList,IarenaResp} from "../../model/wfArenaActivity";
import BaseMegaController from './BaseMegaController';

export default class ArenaAreaContorller extends BaseMegaController {

    public async getArenaActivityList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const resp: IarenaResp = await ctx.service.werewolf.arenaActivity.getArenaActivityList(requestBody);
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
}