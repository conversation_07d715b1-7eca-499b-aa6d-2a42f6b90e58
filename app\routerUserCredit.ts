/*
 * @Description: 商城管理路由
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhang<PERSON>
 * @Date: 2020-04-22 11:50:11
 * @LastEditors: zhanglu
 * @LastEditTime: 2020-12-23 15:30:11
 */
import { Router, Application } from "egg";
import { AccessRouteId, AccessRouteName } from "./model/accessRouteCof";
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

    const { controller, router } = app;
    
    router.post(`${API_VERSION}/werewolf/credit/getUserCreditFlowList`, accCtr(AccessRouteId.wolf_credit_manager), controller.werewolf.userCredit.getUserCreditFlowList);
    router.post(`${API_VERSION}/werewolf/credit/getUserCreditFlowListCount`, accCtr(AccessRouteId.wolf_credit_manager), controller.werewolf.userCredit.getUserCreditFlowListCount);
    router.post(`${API_VERSION}/werewolf/credit/getUserCreditInfo`, accCtr(AccessRouteId.wolf_credit_manager), controller.werewolf.userCredit.getUserCreditInfo);
    router.post(`${API_VERSION}/werewolf/credit/getUserEscapeLogId`, accCtr(AccessRouteId.wolf_credit_manager), controller.werewolf.userCredit.getUserEscapeLogId);
    router.post(`${API_VERSION}/werewolf/credit/getCreditTypeList`, accCtr(AccessRouteId.wolf_credit_manager), controller.werewolf.userCredit.getCreditTypeList);
    // 信誉分统计
    router.post(`${API_VERSION}/werewolf/credit/getCreditList`, accCtr(AccessRouteId.wolf_credit_statistics), controller.werewolf.userCredit.getCreditList);
}

export default load
