/*
 * @Description: 邮件系统
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: 张宇
 * @Date: 2019-06-21 15:02:09
 * @LastEditors: hammercui
 * @LastEditTime: 2020-03-16 15:19:30
 */

import { Service } from 'egg';
import { SendEmailsToUsersRequest, SearchNewUserListResponse, PushMessageResquest } from '../../model/werewolf';
import { Ipayload } from '../../model/common';

export default class SendEmailService extends Service {

    /**
     * @name: 发送邮件
     */
    public async sendEmailsToUsers(request: SendEmailsToUsersRequest) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        // const userList =request.userList;
        const title = db.escape(request.title);
        const content = db.escape(request.content);
        let value = Array();
        request.userList.forEach((element) => {
            value.push(`( ${element}, '0', '1', '0',  NOW(),  NOW(), ${title}, ${content}, '0', '1', '0', '0', '1', NULL, '' )`);
        });
        let sql = 'INSERT INTO `tnotcie` ( `userNo`, `type`, `n_state`,  `u_state`, `n_time`, `u_time`, `title`, `content`,  `prop_num`,  `note`, `type_two`, ' +
            '`prop_two_num`,  `isnew`,  `url`, `extend` ) VALUES' + value.toString();

        try {
            await db.query(sql);
        } catch (error) {
            throw error;
        }
    }    
}
