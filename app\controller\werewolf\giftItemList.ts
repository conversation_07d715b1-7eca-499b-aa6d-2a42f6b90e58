import BaseMegaController from "./BaseMegaController";

export default class GiftItemListController extends BaseMegaController {

    public async getGiftItems(){
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.giftItemList.getGiftItems(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
            logger.error(err)
        }
    }

    public async addGiftItem(){
        const { ctx, logger } = this;
        const rule = {
            gift_id: {type: "number"},
            item_dic_id: {type: "number"},
            item_cate_id: {type: "number"},
            num: {type: "number"},
            weight: {type: "number"},
            repeat: {type: "number"},
            rate: {type: "string"},
            delsign: {type: "number"}
        };
        try {
            ctx.validate(rule)
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.giftItemList.addGiftItem(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
            logger.error(err)
        }
    }

    public async updateGiftItem(){
        const { ctx, logger } = this;
        const rule = {
            id: {type: "number"},
            gift_id: {type: "number"},
            item_dic_id: {type: "number"},
            item_cate_id: {type: "number"},
            num: {type: "number"},
            weight: {type: "number"},
            repeat: {type: "number"},
            rate: {type: "string"},
            delsign: {type: "number"}
        };
        try {
            ctx.validate(rule)
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.giftItemList.updateGiftItem(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
            logger.error(err)
        }
    }

    public async getGifts(){
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.giftItemList.getGifts(requestBody)
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
            logger.error(err)
        }
    }
}