/*
 * @Description: 新手辅助
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: wb
 * @Date: 2020-04-24 11:35:39
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-07-21 10:54:23
 */
import BaseMegaService from '../werewolf/BaseMegaService';
import {
    IsetModelAddPicParams,
    IsetModelChangeTagParams,
    IsetModelDelPicParams,
    IsetModelSaveParams,
    IsetModelSearchParams,
    IsetModelTagParams
} from "./model/setModelDto";


const chatgptDB = "chatgpt";
const resource_mst = "https://resource.mst.xyz/";

export default class SetModelService extends BaseMegaService {

    public async searchModelInfo(req: IsetModelSearchParams) {
        const { app, ctx, logger } = this;
        try {

            //is_recommend
            let sql = `
            SELECT md.model_type_id,sd1.dic_name AS model_type_name,md.model_base_id,sd2.dic_name AS model_base_name,md.is_public 
            FROM  stable_diffusion_model_dic md,stable_diffusion_system_dic sd1,stable_diffusion_system_dic sd2
            WHERE md.model_id = "${req.model_id.replace(/^\s+|\s+$/g, "")}" 
            AND md.model_type_id=sd1.system_dic_id AND sd1.type = "1" AND sd1.delsign = '0'
            AND md.model_base_id=sd2.system_dic_id AND sd2.type = "3" AND sd2.delsign = '0';
            `;

            const chatgpt = app.mysql.get(chatgptDB);
            const chatgptConn = await chatgpt.beginTransaction();
            let list = await chatgptConn.query(sql, []);

            await chatgptConn.commit();

            return list;

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async searchFromModelList(req: IsetModelSearchParams) {
        const { app, ctx, logger } = this;
        try {

            // let sql = `
            // SELECT md.model_id AS uid,md.model_type_id,md.id,md.model_name,ds.image_url AS url,ds.task_id
            // FROM stable_diffusion_model_dic md, stable_diffusion_model_dic_samples ds
            // WHERE md.model_id = "${req.model_id.replace(/^\s+|\s+$/g,"")}" AND ds.model_id = md.model_id; 
            // `;
            let sql = `
            SELECT ds.id AS uid,ds.image_url AS url
            FROM  stable_diffusion_model_dic md, stable_diffusion_model_dic_samples ds
            WHERE md.model_id = "${req.model_id.replace(/^\s+|\s+$/g, "")}" AND ds.model_id = md.model_id;  
            `;

            const chatgpt = app.mysql.get(chatgptDB);
            const chatgptConn = await chatgpt.beginTransaction();
            let list = await chatgptConn.query(sql, []);

            await chatgptConn.commit();

            return list;

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async delPic(req: IsetModelDelPicParams) {
        const { app, ctx, logger } = this;
        try {


            let sql = `
                DELETE FROM stable_diffusion_model_dic_samples WHERE id = "${req.uid}"; 
            `;

            const chatgpt = app.mysql.get(chatgptDB);
            const chatgptConn = await chatgpt.beginTransaction();
            let list = await chatgptConn.query(sql, []);

            await chatgptConn.commit();

            return list;

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async addPic(req: IsetModelAddPicParams) {
        const { app, ctx, logger } = this;
        try {

            let sql = `
            INSERT INTO stable_diffusion_model_dic_samples (model_id,image_url)
            VALUES ("${req.model_id.replace(/^\s+|\s+$/g, "")}","${resource_mst + req.image_url + '.png'}"); 
            `;

            const chatgpt = app.mysql.get(chatgptDB);
            const chatgptConn = await chatgpt.beginTransaction();
            let list = await chatgptConn.query(sql, []);

            await chatgptConn.commit();

            return list;

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async modelTypeList() {
        const { app, ctx, logger } = this;
        try {

            let sql = `
            SELECT *
            FROM  stable_diffusion_system_dic  sd
            WHERE sd.type = "1" AND sd.delsign = '0';
            `;

            const chatgpt = app.mysql.get(chatgptDB);
            const chatgptConn = await chatgpt.beginTransaction();
            let list = await chatgptConn.query(sql, []);

            await chatgptConn.commit();

            return list;

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async classificationTypeList(req: IsetModelTagParams) {
        const { app, ctx, logger } = this;
        try {
            // type:number,//1-未选的 2-选中的

            let sql = `
            SELECT *
            FROM  stable_diffusion_system_dic  sd
            WHERE sd.type = "2" AND sd.delsign = '0'
            AND id NOT IN 
            (SELECT id 
                FROM  stable_diffusion_system_dic  sd 
                WHERE sd.type = "2" AND sd.delsign = '0' AND sd.system_dic_id 
                IN ( SELECT model_cate_id FROM  stable_diffusion_model_dic_cate_ref 
                    WHERE model_id = "${req.model_id.replace(/^\s+|\s+$/g, "")}" AND delsign = '0')) 
                ORDER BY sd.dic_name;
            `;

            if (req.type == 2) {
                sql = `
                SELECT * 
                FROM  stable_diffusion_system_dic  sd 
                WHERE sd.type = "2" AND sd.delsign = '0' AND sd.system_dic_id 
                IN ( SELECT model_cate_id FROM  stable_diffusion_model_dic_cate_ref 
                    WHERE model_id = "${req.model_id.replace(/^\s+|\s+$/g, "")}" AND delsign = '0')
                ORDER BY sd.dic_name;
                `;
            }

            const chatgpt = app.mysql.get(chatgptDB);
            const chatgptConn = await chatgpt.beginTransaction();
            let list = await chatgptConn.query(sql, []);

            await chatgptConn.commit();

            return list;

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async addClassificationType(req: IsetModelChangeTagParams) {
        const { app, ctx, logger } = this;
        try {

            let select_sql = `
            SELECT * 
            FROM  stable_diffusion_system_dic  sd 
            WHERE sd.type = "2" AND sd.delsign = '0' AND sd.system_dic_id 
            IN ( SELECT model_cate_id FROM  stable_diffusion_model_dic_cate_ref WHERE model_id = "${req.model_id.replace(/^\s+|\s+$/g, "")}" AND model_cate_id = "${req.model_cate_id}"  AND delsign = '0')
            `;

            let sql = `
            INSERT INTO stable_diffusion_model_dic_cate_ref (model_id,model_cate_id,delsign) VALUES ("${req.model_id.replace(/^\s+|\s+$/g, "")}","${req.model_cate_id}","0")
            `;

            const chatgpt = app.mysql.get(chatgptDB);
            const chatgptConn = await chatgpt.beginTransaction();

            let select_list = await chatgptConn.query(select_sql, []);

            let list = [];
            if (select_list.length <= 0) {
                list = await chatgptConn.query(sql, []);
            }

            await chatgptConn.commit();

            return list;

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async delClassificationType(req: IsetModelChangeTagParams) {
        const { app, ctx, logger } = this;
        try {

            let sql = `
            DELETE FROM stable_diffusion_model_dic_cate_ref WHERE model_id="${req.model_id.replace(/^\s+|\s+$/g, "")}" AND model_cate_id = "${req.model_cate_id}"
            `;

            const chatgpt = app.mysql.get(chatgptDB);
            const chatgptConn = await chatgpt.beginTransaction();
            let list = await chatgptConn.query(sql, []);

            await chatgptConn.commit();

            return list;

        } catch (error) {
            logger.error(error);
            throw error
        }
    }


    public async baseModelList() {
        const { app, ctx, logger } = this;
        try {

            let sql = `
            SELECT *
            FROM  stable_diffusion_system_dic  sd
            WHERE sd.type = "3" AND sd.delsign = '0';
            `;

            const chatgpt = app.mysql.get(chatgptDB);
            const chatgptConn = await chatgpt.beginTransaction();
            let list = await chatgptConn.query(sql, []);

            await chatgptConn.commit();

            return list;

        } catch (error) {
            logger.error(error);
            throw error
        }
    }


    public async saveModel(req: IsetModelSaveParams) {
        const { app, ctx, logger } = this;
        try {

            let sql = ``;

            if(req.type == 1){//1-类型
                sql = `UPDATE stable_diffusion_model_dic SET model_type_id = "${req.id}" WHERE id = "${req.model_id}" ;`;
            }else if(req.type == 2){//2-基础模型
                sql = `UPDATE stable_diffusion_model_dic SET model_base_id = "${req.id}" WHERE id = "${req.model_id}" ;`;
            }else if(req.type == 3){//3-可见
                sql = `UPDATE stable_diffusion_model_dic SET is_public = "${req.id}" WHERE id = "${req.model_id}" ;`;
            }else if(req.type == 4){//4-推荐 is_recommend需要换字段名
                // sql = `UPDATE stable_diffusion_model_dic SET is_recommend = "${req.id}" WHERE id = "${req.model_id}" ;`;
            }

            const chatgpt = app.mysql.get(chatgptDB);
            const chatgptConn = await chatgpt.beginTransaction();
            let list = await chatgptConn.query(sql, []);

            await chatgptConn.commit();

            return list;

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

}
