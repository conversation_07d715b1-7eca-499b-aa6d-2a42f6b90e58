/*
 * @Description: 了了提现具体逻辑
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: leeou
 * @Date: 2019-02-02 11:26:39
 * @LastEditors: zhanglu
 * @LastEditTime: 2022-10-20 14:11:12
 */
import { Service } from "egg";
import { IapiDocItem } from "../../model/werewolf";

enum monitorDicEnum {
  Server = 1,
  Business = 2,
  Env = 3,
  Type = 4,
  User = 5,
  Format = 6,
  System = 7,
}
export default class ApiDocService extends Service {
  /**
   * @name:
   * @msg:
   * @param {type}
   * @return:
   */
  public async list(): Promise<IapiDocItem[]> {
    const { app } = this;
    const db = app.mysql.get("manager");

    let sql = `SELECT id,doc_name,doc_url,doc_desc FROM wf_admin_apidoc WHERE delsign = 0`;
    try {
      let result = await db.query(sql);
      return result;
    } catch (error) {
      throw error;
    }
  }

  public async getServerMonitorList(req): Promise<any[]> {
    const { app } = this;
    const db = app.mysql.get("manager");

    let sql = `SELECT * FROM wf_admin_server_monitor where 1=1`;
    if (req.serverSearch) {
      sql += ` AND server_name LIKE '%${req.serverSearch}%'  `;
    }
    if (req.otherSearch) {
      sql += ` AND (name LIKE '%${req.otherSearch}%' OR docker_name LIKE '%${req.otherSearch}%' OR start LIKE '%${req.otherSearch}%' OR restart LIKE '%${req.otherSearch}%' OR description LIKE '%${req.otherSearch}%' OR remark LIKE '%${req.otherSearch}%') `;
    }
    if (req.optionUserId && req.optionUserId != 0) {
      sql += ` AND user_id = ${req.optionUserId} `;
    }
    if (req.optionBusinessId && req.optionBusinessId != 0) {
      sql += ` AND business_id = ${req.optionBusinessId} `;
    }
    if (req.optionTypeId && req.optionTypeId != 0) {
      sql += ` AND type_id = ${req.optionTypeId} `;
    }
    sql += ` order by status ASC, sort ASC `;

    try {
      let result = await db.query(sql);
      return result;
    } catch (error) {
      throw error;
    }
  }

  public async getServerMonitorTypeList(req): Promise<any[]> {
    const { app } = this;
    const db = app.mysql.get("manager");

    let sql = ` SELECT * FROM wf_admin_server_monitor_type where delsign = 0 `;
    try {
      let result = await db.query(sql);
      return result;
    } catch (error) {
      throw error;
    }
  }

  public async getServerMonitorUserList(req): Promise<any[]> {
    const { app } = this;
    const db = app.mysql.get("manager");

    let sql = ` SELECT * FROM wf_admin_server_monitor_user where delsign = 0 `;
    try {
      let result = await db.query(sql);
      return result;
    } catch (error) {
      throw error;
    }
  }

  public async getServerMonitorBusinessList(req): Promise<any[]> {
    const { app } = this;
    const db = app.mysql.get("manager");

    let sql = ` SELECT * FROM wf_admin_server_monitor_business where delsign = 0 `;
    try {
      let result = await db.query(sql);
      return result;
    } catch (error) {
      throw error;
    }
  }

  public async getServerMonitorServerList(req): Promise<any[]> {
    const { app } = this;
    const db = app.mysql.get("manager");

    let sql = ` SELECT * FROM wf_admin_server_monitor_server where delsign = 0 `;
    try {
      let result = await db.query(sql);
      return result;
    } catch (error) {
      throw error;
    }
  }

  public async getServerMonitorEnvList(req): Promise<any[]> {
    const { app } = this;
    const db = app.mysql.get("manager");

    let sql = ` SELECT * FROM wf_admin_server_monitor_env where delsign = 0 `;
    try {
      let result = await db.query(sql);
      return result;
    } catch (error) {
      throw error;
    }
  }

  public async getServerMonitorFormatList(req): Promise<any[]> {
    const { app } = this;
    const db = app.mysql.get("manager");

    let sql = ` SELECT * FROM wf_admin_server_monitor_format where delsign = 0 `;
    try {
      let result = await db.query(sql);
      return result;
    } catch (error) {
      throw error;
    }
  }

  public async getServerMonitorSystemList(req): Promise<any[]> {
    const { app } = this;
    const db = app.mysql.get("manager");

    let sql = ` SELECT * FROM wf_admin_server_monitor_system where delsign = 0 `;
    try {
      let result = await db.query(sql);
      return result;
    } catch (error) {
      throw error;
    }
  }

  public async updateServerMonitor(req) {
    const { app } = this;
    const db = app.mysql.get("manager");
    //开启事物
    const conn = await db.beginTransaction();
    try {
      let sql = ` UPDATE wf_admin_server_monitor 
            SET name = ?,
            gitlab = ?,
            server_name = ?,
            server_id = ?,
            version = ?,
            git_branch = ?,
            outer_ip = ?,
            inner_ip = ?,
            jenkin_link = ?,
            port = ?,
            docker_name = ?,
            docker_volum = ?,
            language = ?,
            path = ?,
            log_path = ?,
            \`check\` = ?,
            start = ?,
            restart = ?,
            description = ?,
            remark = ?,
            type_id = ?,
            type_name = ?,
            self = ?,
            business_id = ?,
            business_name = ?,
            user_id = ?,
            user_name = ?,
            time_format = ?,
            time_pattern = ?,
            max_second = ?,
            env_id = ?,
            env_name = ?,
            time_str = ?,
            time_id = ?,
            system_id = ?,
            system_name = ?
            WHERE
                id = ?
                `;
      await conn.query(sql, [
        req.name,
        req.gitlab,
        req.server_name,
        req.server_id,
        req.version,
        req.git_branch,
        req.outer_ip,
        req.inner_ip,
        req.jenkin_link,
        req.port,
        req.docker_name,
        req.docker_volum,
        req.language,
        req.path,
        req.log_path,
        req.check,
        req.start,
        req.restart,
        req.description,
        req.remark,
        req.type_id,
        req.type_name,
        req.self,
        req.business_id,
        req.business_name,
        req.user_id,
        req.user_name,
        req.time_format,
        req.time_pattern,
        req.max_second,
        req.env_id,
        req.env_name,
        req.time_str,
        req.time_id,
        req.system_id,
        req.system_name,
        req.id,
      ]);
      await conn.commit(); // 提交事务
    } catch (error) {
      await conn.rollback(); // 一定记得捕获异常后回滚事务！
      throw error;
    }
  }

  public async updateServerMonitorSingleData(req) {
    const { app } = this;
    const db = app.mysql.get("manager");
    //开启事物
    const conn = await db.beginTransaction();
    try {
      if (req.type == 1) {
        let sql = ` UPDATE wf_admin_server_monitor 
            SET 
            version = ?
            WHERE
                id IN (?)
                `;
        await conn.query(sql, [req.version, req.ids]);
      } else if (req.type == 2) {
        let sql = ` UPDATE wf_admin_server_monitor 
            SET 
            git_branch = ?
            WHERE
                id IN (?)
            `;
        await conn.query(sql, [req.git_branch, req.ids]);
      }

      await conn.commit(); // 提交事务
    } catch (error) {
      await conn.rollback(); // 一定记得捕获异常后回滚事务！
      throw error;
    }
  }

  public async updateServerMonitorDelsign(req) {
    const { app } = this;
    const db = app.mysql.get("manager");
    //开启事物
    const conn = await db.beginTransaction();
    try {
      let sql = ` UPDATE wf_admin_server_monitor 
            SET delsign = '?'
            WHERE
                id = ?
                `;
      await conn.query(sql, [req.delsign, req.id]);
      await conn.commit(); // 提交事务
    } catch (error) {
      await conn.rollback(); // 一定记得捕获异常后回滚事务！
      throw error;
    }
  }

  public async updateServerMonitorLogDelsign(req) {
    const { app } = this;
    const db = app.mysql.get("manager");
    //开启事物
    const conn = await db.beginTransaction();
    try {
      let sql = ` UPDATE wf_admin_server_monitor 
            SET log_delsign = '?'
            WHERE
                id = ?
                `;
      await conn.query(sql, [req.delsign, req.id]);
      await conn.commit(); // 提交事务
    } catch (error) {
      await conn.rollback(); // 一定记得捕获异常后回滚事务！
      throw error;
    }
  }

  public async insertServerMonitor(req) {
    const { app } = this;
    const db = app.mysql.get("manager");
    const conn = await db.beginTransaction();
    try {
      let sql = ` INSERT INTO wf_admin_server_monitor (
                name,
                gitlab,
                server_name,
                server_id,
                version,
                git_branch,
                outer_ip,
                inner_ip,
                jenkin_link,
                port,
                docker_name,
                docker_volum,
                language,
                path,
                log_path,
                \`check\`,
                start,
                restart,
                description,
                remark,
                type_id,
                type_name,
                self,
                business_id,
                business_name,
                user_id,
                user_name,
                time_format,
                time_pattern,
                max_second,
                env_id,
                env_name,
                time_str,
                time_id,
                system_id,
                system_name,
                sort,
                log_delsign,
                delsign 
            )
            VALUES
                (
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    1,
                    1,
                    1 
                ) `;
      await conn.query(sql, [
        req.name,
        req.gitlab,
        req.server_name,
        req.server_id,
        req.version,
        req.git_branch,
        req.outer_ip,
        req.inner_ip,
        req.jenkin_link,
        req.port,
        req.docker_name,
        req.docker_volum,
        req.language,
        req.path,
        req.log_path,
        req.check,
        req.start,
        req.restart,
        req.description,
        req.remark,
        req.type_id,
        req.type_name,
        req.self,
        req.business_id,
        req.business_name,
        req.user_id,
        req.user_name,
        req.time_format,
        req.time_pattern,
        req.max_second,
        req.env_id,
        req.env_name,
        req.time_str,
        req.time_id,
        req.system_id,
        req.system_name,
      ]);
      await conn.commit(); // 提交事务
    } catch (error) {
      await conn.rollback(); // 一定记得捕获异常后回滚事务！
      throw error;
    }
  }

  public async insertServer(req) {
    const { app } = this;
    const db = app.mysql.get("manager");
    const conn = await db.beginTransaction();
    try {
      let sql = ` INSERT INTO wf_admin_server_monitor_server (
                name,outer_ip,inner_ip,system_name,system_id,
                delsign 
            )
            VALUES
                (
                  ?,?,?,?,?,0
                ) `;
      await conn.query(sql, [
        req.name,
        req.outer_ip,
        req.inner_ip,
        req.system_name,
        req.system_id,
      ]);
      await conn.commit(); // 提交事务
    } catch (error) {
      await conn.rollback(); // 一定记得捕获异常后回滚事务！
      throw error;
    }
  }

  public async insertBusiness(req) {
    const { app } = this;
    const db = app.mysql.get("manager");
    const conn = await db.beginTransaction();
    try {
      let sql = ` INSERT INTO wf_admin_server_monitor_business (
                name,
                delsign 
            )
            VALUES
                (
                    ?,0
                ) `;
      await conn.query(sql, [req.name]);
      await conn.commit(); // 提交事务
    } catch (error) {
      await conn.rollback(); // 一定记得捕获异常后回滚事务！
      throw error;
    }
  }

  public async insertEnv(req) {
    const { app } = this;
    const db = app.mysql.get("manager");
    const conn = await db.beginTransaction();
    try {
      let sql = ` INSERT INTO wf_admin_server_monitor_env (
                name,
                delsign 
            )
            VALUES
                (
                    ?,0
                ) `;
      await conn.query(sql, [req.name]);
      await conn.commit(); // 提交事务
    } catch (error) {
      await conn.rollback(); // 一定记得捕获异常后回滚事务！
      throw error;
    }
  }

  public async insertType(req) {
    const { app } = this;
    const db = app.mysql.get("manager");
    const conn = await db.beginTransaction();
    try {
      let sql = ` INSERT INTO wf_admin_server_monitor_type (
                name,
                delsign 
            )
            VALUES
                (
                    ?,0
                ) `;
      await conn.query(sql, [req.name]);
      await conn.commit(); // 提交事务
    } catch (error) {
      await conn.rollback(); // 一定记得捕获异常后回滚事务！
      throw error;
    }
  }

  public async insertUser(req) {
    const { app } = this;
    const db = app.mysql.get("manager");
    const conn = await db.beginTransaction();
    try {
      let sql = ` INSERT INTO wf_admin_server_monitor_user (
                name,
                delsign 
            )
            VALUES
                (
                    ?,0
                ) `;
      await conn.query(sql, [req.name]);
      await conn.commit(); // 提交事务
    } catch (error) {
      await conn.rollback(); // 一定记得捕获异常后回滚事务！
      throw error;
    }
  }

  public async insertFormat(req) {
    const { app } = this;
    const db = app.mysql.get("manager");
    const conn = await db.beginTransaction();
    try {
      let sql = ` INSERT INTO wf_admin_server_monitor_format (
                name, time_format, time_pattern,
                delsign 
            )
            VALUES
                (
                    ?,?,?,0
                ) `;
      await conn.query(sql, [req.name, req.time_format, req.time_pattern]);
      await conn.commit(); // 提交事务
    } catch (error) {
      await conn.rollback(); // 一定记得捕获异常后回滚事务！
      throw error;
    }
  }

  public async insertSystem(req) {
    const { app } = this;
    const db = app.mysql.get("manager");
    const conn = await db.beginTransaction();
    try {
      let sql = ` INSERT INTO wf_admin_server_monitor_system (
                name, 
                delsign 
            )
            VALUES
                (
                    ?,0
                ) `;
      await conn.query(sql, [req.name]);
      await conn.commit(); // 提交事务
    } catch (error) {
      await conn.rollback(); // 一定记得捕获异常后回滚事务！
      throw error;
    }
  }

  public async updateServer(req) {
    const { app } = this;
    const db = app.mysql.get("manager");
    const conn = await db.beginTransaction();
    try {
      let sql = ` 
        UPDATE wf_admin_server_monitor_server 
        SET name = ?,
        outer_ip = ?,
        inner_ip = ?,
        system_name = ?,
        system_id = ?,
        delsign = 0
        WHERE
          id = ?
      `;
      await conn.query(sql, [
        req.name,
        req.outer_ip,
        req.inner_ip,
        req.system_name,
        req.system_id,
        req.id,
      ]);
      this.updateMonitorWhenUpdateDict(req, conn, monitorDicEnum.Server);
      await conn.commit(); // 提交事务
    } catch (error) {
      await conn.rollback(); // 一定记得捕获异常后回滚事务！
      throw error;
    }
  }

  public async updateBusiness(req) {
    const { app } = this;
    const db = app.mysql.get("manager");
    const conn = await db.beginTransaction();
    try {
      let sql = ` 
        UPDATE wf_admin_server_monitor_business 
        SET name = ?,
        delsign = 0 
        WHERE
          id = ?
      `;
      await conn.query(sql, [req.name, req.id]);
      this.updateMonitorWhenUpdateDict(req, conn, monitorDicEnum.Business);
      await conn.commit(); // 提交事务
    } catch (error) {
      await conn.rollback(); // 一定记得捕获异常后回滚事务！
      throw error;
    }
  }

  public async updateEnv(req) {
    const { app } = this;
    const db = app.mysql.get("manager");
    const conn = await db.beginTransaction();
    try {
      let sql = ` 
        UPDATE wf_admin_server_monitor_env
        SET name = ?,
        delsign = 0 
        WHERE
          id = ?
      `;
      await conn.query(sql, [req.name, req.id]);
      this.updateMonitorWhenUpdateDict(req, conn, monitorDicEnum.Env);
      await conn.commit(); // 提交事务
    } catch (error) {
      await conn.rollback(); // 一定记得捕获异常后回滚事务！
      throw error;
    }
  }

  public async updateType(req) {
    const { app } = this;
    const db = app.mysql.get("manager");
    const conn = await db.beginTransaction();
    try {
      let sql = ` 
        UPDATE wf_admin_server_monitor_type
        SET name = ?,
        delsign = 0 
        WHERE
          id = ?
      `;
      await conn.query(sql, [req.name, req.id]);
      this.updateMonitorWhenUpdateDict(req, conn, monitorDicEnum.Type);
      await conn.commit(); // 提交事务
    } catch (error) {
      await conn.rollback(); // 一定记得捕获异常后回滚事务！
      throw error;
    }
  }

  public async updateUser(req) {
    const { app } = this;
    const db = app.mysql.get("manager");
    const conn = await db.beginTransaction();
    try {
      let sql = ` 
        UPDATE wf_admin_server_monitor_user
        SET name = ?,
        delsign = 0 
        WHERE
          id = ?
      `;
      await conn.query(sql, [req.name, req.id]);
      this.updateMonitorWhenUpdateDict(req, conn, monitorDicEnum.User);
      await conn.commit(); // 提交事务
    } catch (error) {
      await conn.rollback(); // 一定记得捕获异常后回滚事务！
      throw error;
    }
  }

  public async updateFormat(req) {
    const { app } = this;
    const db = app.mysql.get("manager");
    const conn = await db.beginTransaction();
    try {
      let sql = ` 
        UPDATE wf_admin_server_monitor_format
        SET name = ?,
        time_format = ?,
        time_pattern = ?,
        delsign = 0 
        WHERE
          id = ?
      `;
      await conn.query(sql, [
        req.name,
        req.time_format,
        req.time_pattern,
        req.id,
      ]);
      this.updateMonitorWhenUpdateDict(req, conn, monitorDicEnum.Format);
      await conn.commit(); // 提交事务
    } catch (error) {
      await conn.rollback(); // 一定记得捕获异常后回滚事务！
      throw error;
    }
  }

  public async updateSystem(req) {
    const { app } = this;
    const db = app.mysql.get("manager");
    const conn = await db.beginTransaction();
    try {
      let sql = ` 
        UPDATE wf_admin_server_monitor_system
        SET name = ?,
        delsign = 0 
        WHERE
          id = ?
      `;
      await conn.query(sql, [req.name, req.id]);
      let sqlUpdateServer = ` 
        UPDATE wf_admin_server_monitor_server 
        SET
          system_name = ?
        WHERE
          system_id = ?
      `;
      await conn.query(sqlUpdateServer, [req.name, req.id]);
      this.updateMonitorWhenUpdateDict(req, conn, monitorDicEnum.System);
      await conn.commit(); // 提交事务
    } catch (error) {
      await conn.rollback(); // 一定记得捕获异常后回滚事务！
      throw error;
    }
  }

  public async updateMonitorWhenUpdateDict(req, conn, type) {
    let sql;
    switch (type) {
      case monitorDicEnum.Server:
        sql = `
          UPDATE wf_admin_server_monitor 
          SET
            system_name = ?,
            system_id = ?,
            server_name = ?,
            outer_ip = ?,
            inner_ip = ?
          WHERE
            server_id = ?
        `;
        await conn.query(sql, [
          req.system_name,
          req.system_id,
          req.name,
          req.outer_ip,
          req.inner_ip,
          req.id,
        ]);
        break;
      case monitorDicEnum.Business:
        sql = `
        UPDATE wf_admin_server_monitor 
        SET
          business_name = ?
        WHERE
          business_id = ?
      `;
        await conn.query(sql, [req.name, req.id]);
        break;
      case monitorDicEnum.Env:
        sql = `
        UPDATE wf_admin_server_monitor 
        SET
          env_name = ?
        WHERE
          env_id = ?
      `;
        await conn.query(sql, [req.name, req.id]);
        break;
      case monitorDicEnum.Type:
        sql = `
        UPDATE wf_admin_server_monitor 
        SET
          type_name = ?
        WHERE
          type_id = ?
      `;
        await conn.query(sql, [req.name, req.id]);
        break;
      case monitorDicEnum.User:
        sql = `
        UPDATE wf_admin_server_monitor 
        SET
          user_name = ?
        WHERE
          user_id = ?
      `;
        await conn.query(sql, [req.name, req.id]);
        break;
      case monitorDicEnum.Format:
        sql = `
        UPDATE wf_admin_server_monitor 
        SET
          time_str = ?,
          time_format = ?,
          time_pattern = ?
        WHERE
          time_id = ?
      `;
        await conn.query(sql, [
          req.name,
          req.time_format,
          req.time_pattern,
          req.id
        ]);
        break;
      case monitorDicEnum.System:
        sql = `
        UPDATE wf_admin_server_monitor 
        SET
          system_name = ?
        WHERE
          system_id = ?
      `;
        await conn.query(sql, [req.name, req.id]);
        break;
      default:
        break;
    }
  }

  public async updateMonitorWhenDeleteDict(id, conn, type) {
    let sql;
    switch (type) {
      case monitorDicEnum.Server:
        sql = `
          UPDATE wf_admin_server_monitor 
          SET
            system_name = NULL,
            system_id = NULL,
            server_name = NULL,
            outer_ip = NULL,
            inner_ip = NULL,
            server_id = NULL
          WHERE
            server_id = ?
        `;
        break;
      case monitorDicEnum.Business:
        sql = `
        UPDATE wf_admin_server_monitor 
        SET
          business_name = NULL,
          business_id = NULL
        WHERE
          business_id = ?
      `;
        break;
      case monitorDicEnum.Env:
        sql = `
        UPDATE wf_admin_server_monitor 
        SET
          env_name = NULL,
          env_id = NULL
        WHERE
          env_id = ?
      `;
        break;
      case monitorDicEnum.Type:
        sql = `
        UPDATE wf_admin_server_monitor 
        SET
          type_name = NULL,
          type_id = NULL
        WHERE
          type_id = ?
      `;
        break;
      case monitorDicEnum.User:
        sql = `
        UPDATE wf_admin_server_monitor 
        SET
          user_name = NULL,
          user_id = NULL
        WHERE
          user_id = ?
      `;
        break;
      case monitorDicEnum.Format:
        sql = `
        UPDATE wf_admin_server_monitor 
        SET
          time_str = NULL,
          time_id = NULL,
          time_format = NULL,
          time_pattern = NULL
        WHERE
          time_id = ?
      `;
        break;
      case monitorDicEnum.System:
        sql = `
        UPDATE wf_admin_server_monitor 
        SET
          system_name = NULL,
          system_id = NULL
        WHERE
          system_id = ?
      `;
        break;
      default:
        sql = "";
        break;
    }
    if (sql) {
      await conn.query(sql, [id]);
    }
  }

  public async deleteServer(req) {
    const { app } = this;
    const db = app.mysql.get("manager");
    const conn = await db.beginTransaction();
    try {
      let sql = ` DELETE FROM wf_admin_server_monitor_server WHERE id = ? `;
      await conn.query(sql, [req.id]);
      this.updateMonitorWhenDeleteDict(req.id, conn, monitorDicEnum.Server);
      await conn.commit(); // 提交事务
    } catch (error) {
      await conn.rollback(); // 一定记得捕获异常后回滚事务！
      throw error;
    }
  }

  public async deleteBusiness(req) {
    const { app } = this;
    const db = app.mysql.get("manager");
    const conn = await db.beginTransaction();
    try {
      let sql = ` DELETE FROM wf_admin_server_monitor_business WHERE id = ? `;
      await conn.query(sql, [req.id]);
      this.updateMonitorWhenDeleteDict(req.id, conn, monitorDicEnum.Business);
      await conn.commit(); // 提交事务
    } catch (error) {
      await conn.rollback(); // 一定记得捕获异常后回滚事务！
      throw error;
    }
  }

  public async deleteEnv(req) {
    const { app } = this;
    const db = app.mysql.get("manager");
    const conn = await db.beginTransaction();
    try {
      let sql = ` DELETE FROM wf_admin_server_monitor_env WHERE id = ? `;
      await conn.query(sql, [req.id]);
      this.updateMonitorWhenDeleteDict(req.id, conn, monitorDicEnum.Env);
      await conn.commit(); // 提交事务
    } catch (error) {
      await conn.rollback(); // 一定记得捕获异常后回滚事务！
      throw error;
    }
  }

  public async deleteType(req) {
    const { app } = this;
    const db = app.mysql.get("manager");
    const conn = await db.beginTransaction();
    try {
      let sql = ` DELETE FROM wf_admin_server_monitor_type WHERE id = ? `;
      await conn.query(sql, [req.id]);
      this.updateMonitorWhenDeleteDict(req.id, conn, monitorDicEnum.Type);
      await conn.commit(); // 提交事务
    } catch (error) {
      await conn.rollback(); // 一定记得捕获异常后回滚事务！
      throw error;
    }
  }

  public async deleteUser(req) {
    const { app } = this;
    const db = app.mysql.get("manager");
    const conn = await db.beginTransaction();
    try {
      let sql = ` DELETE FROM wf_admin_server_monitor_user WHERE id = ? `;
      await conn.query(sql, [req.id]);
      this.updateMonitorWhenDeleteDict(req.id, conn, monitorDicEnum.User);
      await conn.commit(); // 提交事务
    } catch (error) {
      await conn.rollback(); // 一定记得捕获异常后回滚事务！
      throw error;
    }
  }

  public async deleteFormat(req) {
    const { app } = this;
    const db = app.mysql.get("manager");
    const conn = await db.beginTransaction();
    try {
      let sql = ` DELETE FROM wf_admin_server_monitor_format WHERE id = ? `;
      await conn.query(sql, [req.id]);
      this.updateMonitorWhenDeleteDict(req.id, conn, monitorDicEnum.Format);
      await conn.commit(); // 提交事务
    } catch (error) {
      await conn.rollback(); // 一定记得捕获异常后回滚事务！
      throw error;
    }
  }

  public async deleteSystem(req) {
    const { app } = this;
    const db = app.mysql.get("manager");
    const conn = await db.beginTransaction();
    try {
      let sql = ` DELETE FROM wf_admin_server_monitor_system WHERE id = ? `;
      await conn.query(sql, [req.id]);
      this.updateMonitorWhenDeleteDict(req.id, conn, monitorDicEnum.System);
      let sqlUpdateServer = ` 
        UPDATE wf_admin_server_monitor_server 
        SET
          system_name = NULL,
          system_id = NULL
        WHERE
          system_id = ?
      `;
      await conn.query(sqlUpdateServer, [req.id]);
      await conn.commit(); // 提交事务
    } catch (error) {
      await conn.rollback(); // 一定记得捕获异常后回滚事务！
      throw error;
    }
  }
}
