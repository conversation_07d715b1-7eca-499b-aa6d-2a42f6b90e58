/*
 * @Description: 玩家信息管理模块路由
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2020-03-24 11:50:11
 * @LastEditors: jiawen.wang
 * @LastEditTime: 2021-05-24 09:37:39
 */
import { Router, Application } from "egg";
import { AccessRouteId } from './model/accessRouteCof';
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

    const { controller, router } = app;
    //【狼王集结】
    // 基础信息
    router.post(`${API_VERSION}/werewolf/playerInfo/toAnchor`, controller.werewolf.playerInfo.toAnchor);
    router.post(`${API_VERSION}/werewolf/playerInfo/beAnchor`, controller.werewolf.playerInfo.beAnchor);
    router.post(`${API_VERSION}/werewolf/playerInfo/removeBan`, controller.werewolf.playerInfo.removeBan);
    router.post(`${API_VERSION}/werewolf/playerInfo/clickRemoveResign`, controller.werewolf.playerInfo.clickRemoveResign);
    router.post(`${API_VERSION}/werewolf/playerInfo/addOverseaItem`, controller.werewolf.playerInfo.addOverseaItem);
    router.post(`${API_VERSION}/werewolf/playerInfo/clearSex`, controller.werewolf.playerInfo.clearSex);
    router.post(`${API_VERSION}/werewolf/playerInfo/clearTime`, controller.werewolf.playerInfo.clearTime);
    router.post(`${API_VERSION}/werewolf/playerInfo/clearBoard`, controller.werewolf.playerInfo.clearBoard);
    router.post(`${API_VERSION}/werewolf/playerInfo/getNodleStatus`, controller.werewolf.playerInfo.getNodleStatus);
    router.post(`${API_VERSION}/werewolf/playerInfo/addUserCoe`, controller.werewolf.playerInfo.addUserCoe);
    router.post(`${API_VERSION}/werewolf/playerInfo/groupWhite`, controller.werewolf.playerInfo.groupWhite);
}

export default load