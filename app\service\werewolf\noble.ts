/*
 * @Description: 
 * @Company: sdbean
 * @Author: zhanglu
 * @Date: 2021-03-30 17:23:36
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-06-26 11:03:44
 */

import BaseMegaService from './BaseMegaService';
import { MallItemBaseType, MallItemBaseInfo, CoinOperation, UploadTagInfoReq } from './../../model/mallItemManager';
import moment = require('moment');
export default class NobleService extends BaseMegaService {

    public async getUserNobleEmperor(req: any) {
        const { app, ctx, logger } = this;
        try {
            let sqlCheck = `
                SELECT COUNT(*) as num FROM tuser
                WHERE no = ?
            `;
            let check = await this.selectOne(sqlCheck, [req.user_id]);
            if (check.num <= 0) {
                return -1;
            }
            let sql = ` SELECT * FROM user_noble_record
                    WHERE user_no = ? AND noble_no IN (6,7,8) AND delsign = 0
            `;
            let gameList = await this.selectList(sql, [req.user_id]);
            return gameList;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getAwardList() {
        const { app, ctx, logger } = this;
        try {
            let sql = ` SELECT * FROM noble_award_flow
                WHERE noble_no = 6 
            `;
            let gameList = await this.selectList(sql, []);
            return gameList;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async insertNoble(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get('werewolf');
        const manager = app.mysql.get("manager");

        //开启事物
        const conn = await db.beginTransaction();
        const managerConn = await manager.beginTransaction();

        try {
            let sqlStr = `
                INSERT INTO user_noble (
                    user_no,
                    noble_no,
                    noble_date,
                    delay_date,
                    duration_days,
                    isbroadcast
                )
                VALUES
                    (
                        ?, ?, DATE_ADD(NOW(),INTERVAL 30 DAY), DATE_ADD(NOW(),INTERVAL 120 DAY), 31, 1
                    )
                    ON DUPLICATE KEY UPDATE
                    noble_no = ?,
                    noble_date = DATE_ADD(NOW(),INTERVAL 30 DAY),
                    delay_date = DATE_ADD(NOW(),INTERVAL 120 DAY),
                    duration_days = duration_days + 31,
                    isbroadcast = 1
            `;
            await conn.query(sqlStr, [req.user_id, req.noble_no, req.noble_no]);

            let sqlStr1 = `
                INSERT INTO user_noble_record(user_no, noble_no, datatime, delsign) VALUES 
                (?, ?, NOW(), 0);
            `;
            await conn.query(sqlStr1, [req.user_id, req.noble_no]);

            if (req.insertBadge == 1) {
                let sqlStr2 = `
                    INSERT INTO user_noble_badge_record(user_no, noble_no, source, price, date, createtime, num, delsign) 
                    VALUES (?, ?, 1, 19998, NOW(), NOW(), 1, 0);
                `;
                await conn.query(sqlStr2, [req.user_id, req.noble_no]);
            }

            let sqlMaxId = `
                SELECT id FROM user_noble_godemperor_buy_record
                WHERE user_id = ?
                ORDER BY id DESC LIMIT 1
            `
            let maxR = await conn.query(sqlMaxId, [req.user_id]);

            if (maxR == null || maxR[0] == null || maxR[0].id == null) {
                let insertRec = `
                INSERT INTO werewolf.user_noble_godemperor_buy_record (
                    user_id,
                    create_time,
                    activite_time,
                    godemper_state,
                    noble_level,
                    update_time,
                    is_delsign,
                    record_source 
                )
                VALUES
                    (?, NOW(), NOW(), 2, 6, NOW(), 0, "console" )
                `;
                await conn.query(insertRec, [req.user_id]);
            } else {
                let updateRec = `
                    UPDATE user_noble_godemperor_buy_record 
                    SET 
                    godemper_state = 2,
                    noble_level = 6,
                    update_time = NOW(),
                    record_source = 'console' 
                    WHERE
                        id = ?
                `
                await conn.query(updateRec, [maxR[0].id]);
            }

            let sqlOperate = `
            INSERT INTO wf_noble_operate_record(operate_user_id, user_id, noble_no, send_item, createtime) 
            VALUES (?, ?, ?, ?, NOW());
            `;
            await managerConn.query(sqlOperate, [
                req.operate_user_id,
                req.user_id,
                req.noble_no,
                req.send_item
            ]);

            await conn.commit(); // 提交事务
            await managerConn.commit();

        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            await managerConn.rollback();

            logger.error(error);
            throw error
        }
    }

    public async updateNoble(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get('werewolf');
        const manager = app.mysql.get("manager");

        //开启事物
        const conn = await db.beginTransaction();
        const managerConn = await manager.beginTransaction();

        try {
            let sqlStr = `
                INSERT INTO user_noble (
                    user_no,
                    noble_no
                )
                VALUES
                    (
                        ?, ?
                    )
                    ON DUPLICATE KEY UPDATE
                    noble_no = ?
            `;
            await conn.query(sqlStr, [req.user_id, req.noble_no, req.noble_no]);

            let sqlStr1 = `
                INSERT INTO user_noble_record(user_no, noble_no, datatime, delsign) VALUES 
                (?, ?, NOW(), 0);
            `;
            await conn.query(sqlStr1, [req.user_id, req.noble_no]);

            let sqlOperate = `
            INSERT INTO wf_noble_operate_record(operate_user_id, user_id, noble_no, send_item, createtime) 
            VALUES (?, ?, ?, 0, NOW());
            `;
            await managerConn.query(sqlOperate, [
                req.operate_user_id,
                req.user_id,
                req.noble_no
            ]);

            await conn.commit(); // 提交事务
            await managerConn.commit();

        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            await managerConn.rollback();

            logger.error(error);
            throw error
        }
    }

    public async getUserNobleInfo(req: any) {
        const { app, ctx, logger } = this;
        try {

            let sql = ` SELECT us.nickname, u.*, n.name, n.simple_name 
            FROM user_noble u
            INNER JOIN noble n ON n.no = u.noble_no
			INNER JOIN tuser us ON us.no = u.user_no
            WHERE u.user_no = ?
            `;
            return await this.selectOne(sql, [req.user_id]);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getUserOrderList(req: any) {
        const { app, ctx, logger } = this;
        try {

            let sql = ` SELECT *
            FROM user_noble_order_record
            WHERE user_no = ?
            AND delsign = 0
            ORDER BY id DESC
            `;
            return await this.selectList(sql, [req.user_id]);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async insertOrder(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get('werewolf');
        const manager = app.mysql.get("manager");

        //开启事物
        const conn = await db.beginTransaction();
        const managerConn = await manager.beginTransaction();

        try {
            let sqlStr = `
            INSERT INTO user_noble_order_record (
                user_no,
                order_state,
                create_time,
                delsign 
            )
            VALUES
                (?, 1, NOW(), 0)
            `;
            let result = await conn.query(sqlStr, [req.user_id]);
            let sqlOperate = `
            INSERT INTO wf_noble_operate_record(operate_user_id, user_id, order_id, order_state, createtime) 
            VALUES (?, ?, ?, 1, NOW());
            `;
            await managerConn.query(sqlOperate, [
                req.operate_user_id,
                req.user_id,
                result.insertId
            ]);

            await conn.commit(); // 提交事务
            await managerConn.commit();
            //4 钉钉
            const dingReq: any = {
                msgtype: 'markdown',
                markdown: {
                    title: `2W头像框库`,
                    text: `### ID ${req.user_id} 新增了动态头像需求订单`
                }
            }
            await ctx.service.werewolf.coin2wTrade.sendDingtalk(dingReq);

            await ctx.service.werewolf.coin2wTrade.sendFeishuTextTalk(`2W头像框库`,`### ID ${req.user_id} 新增了动态头像需求订单`);


        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            await managerConn.rollback();

            logger.error(error);
            throw error
        }
    }

    public async updateUploadOrder(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get('werewolf');
        const manager = app.mysql.get("manager");

        //开启事物
        const conn = await db.beginTransaction();
        const managerConn = await manager.beginTransaction();

        try {
            let sqlStr = `
                INSERT INTO live_avatar ( user_id, headicon, isUse, delsign )
                VALUES
                    ( ?, ?, 0, 1 )
            `;
            let result = await conn.query(sqlStr, [req.user_id, req.avatar_url]);

            let sqlUpdate = `
                UPDATE user_noble_order_record 
                SET 
                order_state = 2,
                live_avatar_id = ?,
                avatar_url = ?
                WHERE
                    id = ?
                `
            await conn.query(sqlUpdate, [
                result.insertId,
                req.avatar_url,
                req.id]);

            let sqlOperate = `
                INSERT INTO wf_noble_operate_record (
                    operate_user_id,
                    user_id,
                    createtime,
                    order_id,
                    order_state,
                    avatar_url 
                )
                VALUES
                    (?, ?, NOW(), ?, 2, ?)
            `;
            await managerConn.query(sqlOperate, [
                req.operate_user_id,
                req.user_id,
                req.id,
                req.avatar_url
            ]);

            await conn.commit(); // 提交事务
            await managerConn.commit();

        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            await managerConn.rollback();

            logger.error(error);
            throw error
        }
    }

    public async updateProvideOrder(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get('werewolf');
        const manager = app.mysql.get("manager");

        //开启事物
        const conn = await db.beginTransaction();
        const managerConn = await manager.beginTransaction();

        try {
            let sqlStr = `
                UPDATE live_avatar 
                SET
                delsign = 0
                WHERE
                    id = ?
            `;
            await conn.query(sqlStr, [req.live_avatar_id]);

            let sqlInserNotice = ` 
            INSERT INTO tnotcie (
                userNo,type,n_state,u_state,n_time,u_time,title,content,prop_num,note,type_two,prop_two_num,isnew,url,extend
                )
                VALUES
                    (
                ${req.user_id},0,1,0,NOW(),NOW(),'神皇专属动态头像提醒','亲爱的天狼用户，恭喜您获得神皇专属动态头像，请前往个人主页点击头像查看。',0,1,0,0,1,NULL,''
                    )
            `;
            await conn.query(sqlInserNotice, []);

            let sqlUpdate = `
                UPDATE user_noble_order_record 
                SET 
                order_state = 3,
                provide_time = NOW()
                WHERE
                    id = ?
                `
            await conn.query(sqlUpdate, [
                req.id]);

            let sqlOperate = `
                INSERT INTO wf_noble_operate_record (
                    operate_user_id,
                    user_id,
                    createtime,
                    order_id,
                    order_state 
                )
                VALUES
                    (?, ?, NOW(), ?, 3)
            `;
            await managerConn.query(sqlOperate, [
                req.operate_user_id,
                req.user_id,
                req.id
            ]);

            await conn.commit(); // 提交事务
            await managerConn.commit();

            const dingReq: any = {
                msgtype: 'markdown',
                markdown: {
                    title: `2W头像框库`,
                    text: `### ID ${req.user_id} 动态头像已发放`
                }
            }
            await ctx.service.werewolf.coin2wTrade.sendDingtalk(dingReq);

            await ctx.service.werewolf.coin2wTrade.sendFeishuTextTalk(`2W头像框库`,`### ID ${req.user_id} 动态头像已发放`);


        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            await managerConn.rollback();

            logger.error(error);
            throw error
        }
    }

    public async getOpreateList(req: any) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
                SELECT
                r.*, 
                u.nickname,
                0 AS isPrice
                FROM
                    wf_noble_operate_record r,
                    wf_admin_user u 
                WHERE
                    r.operate_user_id = u.id 
                    AND r.user_id = ?
                ORDER BY
                    createtime DESC
            `;
            let data = await this.selectList(sqlStr, [
                req.user_id
            ]
                , 'manager');

            let sql = `
                SELECT *, 1 AS isPrice FROM user_noble_badge_record
                WHERE user_no = ? AND source = 2 AND noble_no > 5
            `
            let dataPrice = await this.selectList(sql, [
                req.user_id
            ]);

            return { opeList: data, priceList: dataPrice };
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getUserNobleNumberList(req: any) {
        const { app, ctx, logger } = this;
        try {

            let sql = `SELECT us.nickname, u.*, n.name, n.simple_name 
            FROM user_noble u
            INNER JOIN noble n ON n.no = u.noble_no
			INNER JOIN tuser us ON us.no = u.user_no
            WHERE u.noble_no > 5 
            AND u.user_no NOT IN (SELECT user_id FROM tuser_inner_total)
            `;
            if (req.roomNo) {
                sql += `
                    AND u.room_no = ${req.roomNo}
                `;
            }
            if (req.user_id) {
                sql += `
                    AND u.user_no = ${req.user_id}
                `;
            }
            sql += ` ORDER BY room_no DESC `;

            return await this.selectList(sql, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateUserRoomNo(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get('werewolf');
        const manager = app.mysql.get("manager");

        //开启事物
        const conn = await db.beginTransaction();
        const managerConn = await manager.beginTransaction();

        try {
            let sqlStr = `
                UPDATE user_noble 
                SET
                room_no = ?
                WHERE
                    user_no = ?
            `;
            await conn.query(sqlStr, [req.room_no, req.user_no]);

            let sqlOperate = `
                INSERT INTO wf_noble_operate_record (
                    operate_user_id,
                    user_id,
                    createtime,
                    room_no
                )
                VALUES
                    (?, ?, NOW(), ?)
            `;
            await managerConn.query(sqlOperate, [
                req.operate_user_id,
                req.user_no,
                req.operate_room_no
            ]);

            await conn.commit(); // 提交事务
            await managerConn.commit();

        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            await managerConn.rollback();

            logger.error(error);
            throw error
        }
    }

    public async renewalNoble(req) {
        const { app, ctx, logger } = this;

        const manager = app.mysql.get("manager");

        const db = app.mysql.get('werewolf');

        let sqlStr = `SELECT * FROM user_noble WHERE user_no = ?`;
        const result = await this.selectOne(sqlStr, [req.user_no]);
        if (!!!result || result['noble_no'] < 6) {
            //神皇已过期
            throw new Error('expired')
        }

        //开启事务
        const conn = await db.beginTransaction();
        const managerConn = await manager.beginTransaction();
        try {
            const nobleDate = moment(result['noble_date']).format("YYYY-MM-DD")
            const lastDate = moment().subtract(1, "days").format("YYYY-MM-DD"); //当前时间提前一天
            const curDate = moment().format("YYYY-MM-DD"); //当前时间
            let addDay = 30
            if (lastDate < nobleDate) {
                //未到期 +31天
                addDay = 31
                sqlStr = `UPDATE user_noble SET noble_date = DATE_ADD(?,INTERVAL 31 day) ,
                delay_date = DATE_ADD(?,INTERVAL 121 day),
                duration_days = duration_days + 31
                WHERE user_no = ?;`
                await conn.query(sqlStr, [nobleDate, nobleDate, req.user_no]);
            }else{
                //已到期，位于缓冲期， 今天+30天
                sqlStr = `UPDATE user_noble SET noble_date = DATE_ADD(?,INTERVAL 30 day) ,
                delay_date = DATE_ADD(?,INTERVAL 120 day),
                duration_days = duration_days + 31
                WHERE user_no = ?;`
                await conn.query(sqlStr, [curDate, curDate, req.user_no]);
                
            }

            //user_noble_badge_record 插入记录
            sqlStr = `INSERT INTO user_noble_badge_record ( user_no, noble_no, source, date )
            VALUES ( ?, ?, '3', ? );`
            await conn.query(sqlStr, [req.user_no, result['noble_no'], curDate]);

            //user_noble_godemperor_continue_record 插入记录
            sqlStr = `INSERT INTO user_noble_godemperor_continue_record ( user_id, noble_level, continue_day )
            VALUES ( ?, ?, ? );`
            await conn.query(sqlStr, [req.user_no, result['noble_no'], addDay]);

            //发放奖励{ itemDicId: 3627, num: 2 }, { itemDicId: 3631, num: 2 }, { itemDicId: 1506, num: 49888 }, 活动id  -2
            let awardArray = [{ itemDicId: 3627, num: 2 }, { itemDicId: 3631, num: 2 }, { itemDicId: 1506, num: 49888 }]
            //生产下发奖励请求
            let ObtainAwardReq = ctx.service.werewolf.awardService.GenObtainAwardReq(req.user_no, -2, awardArray)
            //发放奖励
            let ObtainResult = await ctx.service.werewolf.awardService.ObtainAwardByNet(ctx, ObtainAwardReq)
            if (!ObtainResult || ObtainResult.data.code != 1) {
                throw new Error('奖励发放失败')
            }

            let sqlOperate = `
            INSERT INTO wf_noble_operate_record(operate_user_id, user_id, noble_no, send_item, createtime,noble_state) 
            VALUES (?, ?, ?, 1, NOW(),1);
            `;
            await managerConn.query(sqlOperate, [
                req.operate_user_id,
                req.user_no,
                result['noble_no']
            ]);
            await conn.commit(); // 提交事务
            await managerConn.commit();

        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            await managerConn.rollback();

            logger.error(error);
            throw error
        }
    }

}
