/*
 * @Description: 无
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2019-03-19 14:58:24
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-01-19 09:24:45
 */
import { Service } from 'egg';
import {
	IaccessRoutesRequest,
	IaccessRoutesResponse,
	IaccessRoute,
	IupdateRouteRequest,
	IupdateRouteResponse
} from '../model/manager';
import BaseMegaService from './werewolf/BaseMegaService';

export default class UserService extends BaseMegaService {
	/**
	 * @name: 获得用户路由信息
	 * @msg: 
	 * @param {type} 
	 * @return: 
	 */
	async routes(request: IaccessRoutesRequest): Promise<IaccessRoutesResponse> {
		const { app, ctx, logger } = this;
		try {
			const db = app.mysql.get('manager');
			const sqlStr = `SELECT
			b.id AS routeId,
			b.desc AS routeDesc,
			IFNULL(a.access_enable, 0) AS accessEnabel
		FROM
			wf_admin_route AS b
		LEFT JOIN wf_admin_route_user AS a ON a.route_id = b.id AND a.admin_id = ?
		WHERE
			b.status = 1
			AND b.sort >= 0
		GROUP BY b.id	
		ORDER BY
			b.sort ASC,
			b.id ASC;
		`;
			// const results: IaccessRoute[] = await db.query(`select a.id as routeId,a.desc as routeDesc,b.access_enable as accessEnabel   from wf_admin_route as a
			//       LEFT JOIN (select * from wf_admin_route_user where admin_id = ${request.uid})  as b
			// on a.id = b.route_id 
			// WHERE a.\`status\` = 1
			// ORDER BY  a.desc DESC`);
			const results = await this.selectList(sqlStr, [request.uid], 'manager')
			return { uid: request.uid, accessRoutes: results };
		} catch (err) {
			throw err;
		}
	}

	/**
	 * @name: 获得用户路由信息
	 * @msg: 
	 * @param {type} 
	 * @return: 
	 */
	async getUserAccessList(request: IaccessRoutesRequest): Promise<any> {
		const { app, ctx, logger } = this;
		try {
			const sqlStr = ` 
			SELECT c.id, c.route_id, c.\`desc\` AS \`title\`, c.\`level\`,c.pid, IFNULL(a.access_enable, 0) AS access
			FROM wf_admin_route_catalog c
			LEFT JOIN wf_admin_route_user AS a ON a.route_id = c.route_id AND a.admin_id = ?
			WHERE c.status = 1
			ORDER BY \`level\` ASC , sort ASC 
		`;
			return await this.selectList(sqlStr, [request.uid], 'manager')
		} catch (err) {
			throw err;
		}
	}

	/**
	 * @name: 更新用户路由信息
	 * @msg: 
	 * @param {type} 
	 * @return: 
	 */
	async update(req: IupdateRouteRequest): Promise<IupdateRouteResponse> {
		const { app, ctx, logger } = this;
		try {
			const sqlStr = `INSERT INTO wf_admin_route_user (admin_id,route_id,access_enable,update_time)
			VALUES (?,?,?,NOW()) ON DUPLICATE KEY UPDATE access_enable = ?`;
			await this.execSql(sqlStr, [req.uid, req.routeId, req.accessEnable, req.accessEnable], 'manager')
			return { ...req };
		} catch (err) {
			throw err;
		}
	}
}
