import BaseMegaService from "./BaseMegaService";
import { IexchangeReqPer, IobtianItemReq, IselFailListReq } from '../../model/wfExchange';
import moment = require('moment');

export default class ExchangeFailHandleService extends BaseMegaService {

    public async loopPopFail() {
        const { app, ctx, logger } = this;
        try {
            //第一版
            const jsonV1 = await app.redis.get("confRedis").rpop("activity_exchange_fail");
            if (jsonV1) {
                //1
                let reqV1: IobtianItemReq = JSON.parse(jsonV1)
                // logger.debug("jsonV1",jsonV1)
                if (reqV1 && reqV1.userList) {
                    await this.insertFailRecord({
                        reqJson: jsonV1,
                        createTime: moment().format("YYYY-MM-DD HH:mm:ss"),
                        route: "/item/obtain",
                        errResult: "",
                        channel: "activity",
                        reqSign: ""
                    })
                }
            }

            const jsonV2 = await app.redis.get("confRedis").rpop("activity_exchange_v2_fail");
            if (jsonV2) {
                // logger.debug("jsonV2",jsonV2)
                //2
                let reqV2: IexchangeReqPer = JSON.parse(jsonV2)
                if (reqV2 && reqV2.reqSign) {
                    await this.insertFailRecord(reqV2)
                }
            }

        } catch (e) {
            throw e
        }
    }


    /**
     * 写入错误记录
     * @param record 
     * @returns 
     */
    public async insertFailRecord(record: IexchangeReqPer) {
        const { app, ctx, logger } = this;
        try {
            let exchangeReq: IobtianItemReq = JSON.parse(record.reqJson)
            if (!exchangeReq) {
                return
            }
            let sqlStr = `
            INSERT INTO activity_exchange_fail_record (user_id,req_sign,channel,route,err_result,req_json,create_time,activity_id)
            VALUES(?,?,?,?,?,?,?,?);`
            this.execSql(sqlStr, [
                exchangeReq.userList[0].userId,
                record.reqSign,
                record.channel,
                record.route,
                record.errResult,
                record.reqJson,
                record.createTime,
                exchangeReq.activityId,
                exchangeReq.type,
            ])
        } catch (e) {
            throw e
        }
    }

    /**
     * 查询失败列表
     * @param req 
     * @returns 
     */
    public async selectFailList(req: IselFailListReq) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `SELECT a.*,b.\`name\`as activity_name,c.nickname FROM activity_exchange_fail_record  as a
            LEFT JOIN activity_index as b on a.activity_id = b.id
            LEFT JOIN tuser as c on a.user_id = c.\`no\`
            WHERE a.state = 0`
            if (req.activity) {
                sqlStr = sqlStr + `  AND a.activity_id = ${req.activity}`
            }
            if (req.req_sign) {
                sqlStr = sqlStr + `  AND a.req_sign in (${req.req_sign}) `
            }
            if (req.type) {
                sqlStr = sqlStr + `  AND a.type = ${req.type}`
            }
            if (req.user_id) {
                sqlStr = sqlStr + `  AND a.user_id in (${req.user_id})`
            }
            logger.debug("select sql",sqlStr)
            let result = await this.selectList(sqlStr)
            return result
        } catch (e) {
            throw e
        }
    }

    /**
     * 重新发送
     * @param failId 
     */
    public async reSend(failId: number) {
        const { app, ctx, logger } = this;
        try {
            //1 重发
            let item = await this.selectOne(`SELECT * FROM activity_exchange_fail_record WHERE id = ?;`, [failId])
            //2 更新状态
            if (item && item.req_json) {
                let req: IobtianItemReq = JSON.parse(item.req_json)
                await ctx.service.werewolf.exchangeRpc.itemObtain(req)

                //3 更新为已处理
                await this.execSql(`UPDATE activity_exchange_fail_record SET state = 1 WHERE id = ?;`, [failId])
            }
        } catch (e) {
            throw e
        }
    }


}