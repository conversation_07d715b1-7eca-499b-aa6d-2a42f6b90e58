/*
 * @Description: 玩家信誉分
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhang<PERSON>
 * @Date: 2020-12-10 09:46:39
 * @LastEditors: zhanglu
 * @LastEditTime: 2020-12-23 15:29:49
 */
import BaseMegaController from './BaseMegaController';

export default class UserCreditController extends BaseMegaController {

    public async getUserCreditFlowList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.userCredit.getUserCreditFlowList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }
    
    public async getUserCreditFlowListCount() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.userCredit.getUserCreditFlowListCount(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }
	
	public async getUserCreditInfo() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.userCredit.getUserCreditInfo(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getUserEscapeLogId() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.userCredit.getUserEscapeLogId(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getCreditTypeList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.userCredit.getCreditTypeList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getCreditList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.userCredit.getCreditList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }
}
