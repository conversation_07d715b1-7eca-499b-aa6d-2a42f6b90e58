/*
 * @Description: 活动状态管理
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: 张宇
 * @Date: 2019-06-15 15:02:09
 */

import { Service } from 'egg';
import { ActivityStateOperRes, ActivityStateOperReq, ActivityChangeNameRequest, ActivityChangePrizeNumRequest } from '../../model/werewolf';

export default class ActivityStateService extends Service {

    /**
 * @name: 获取活动列表
 * @msg: 
 * @param
 * @return: 
 */
    public async getActivityList(): Promise<ActivityStateOperRes[]> {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        const results = await db.query(
            'SELECT `no`,`num`,`word`,`state`,`start_time`,`end_time`,`open_start_time`,`open_end_time`,now() as nowtime FROM `word_activity` '
        );
        if (!!results && results.length > 0) {
            return results;
        } else {
            return [];
        }
    }

    /**
     * @name:更改大厅状态-显示大厅入口
     */
    public async operHallStateShow() {

        const { app } = this;
        const db = app.mysql.get('werewolf');
        const werewolfConn = await db.beginTransaction();
        const sql1 = 'UPDATE `word_activity` SET `state` = 0,`end_time` = DATE_SUB(NOW(),INTERVAL 1 DAY),`open_end_time` = DATE_SUB(NOW(),INTERVAL 1 DAY)';
        const sql2 = 'UPDATE `word_activity` SET `state` = 1,`end_time` = DATE_SUB(NOW(),INTERVAL 1 DAY),`open_end_time` = DATE_SUB(NOW(),INTERVAL 1 DAY) WHERE `no` = 1';

        try {
            await werewolfConn.query(sql1);
            await werewolfConn.query(sql2);
            await werewolfConn.commit(); // 提交事务

        } catch (error) {
            await werewolfConn.rollback();
            throw error;
        }

    }
    /**
     * @name:更改大厅状态-隐藏大厅入口
     */
    public async operHallStateHide() {

        const { app } = this;
        const db = app.mysql.get('werewolf');
        const werewolfConn = await db.beginTransaction();
        const sql = 'UPDATE `word_activity` SET `state` = 0,`end_time` = NOW(),`open_end_time` = NOW() ';

        try {
            await werewolfConn.query(sql);
            await werewolfConn.commit(); // 提交事务
        } catch (error) {
            await werewolfConn.rollback();
            throw error;
        }

    }

    /**
     * @name:更改活动状态-开启活动
     */
    public async operActivityStateStart(request: ActivityStateOperReq) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        const activityId = db.escape(request.activityId);
        const werewolfConn = await db.beginTransaction();
        const sql1 = 'UPDATE `word_activity` SET `state` = 0,`end_time` = NOW(),`open_end_time`=NOW() ';
        const sql2 = `UPDATE word_activity SET state = 1, start_time  = DATE_SUB(NOW(),INTERVAL 1 DAY),
        end_time = DATE_ADD(NOW(),INTERVAL 2 DAY), open_start_time = DATE_SUB(NOW(),INTERVAL 1 DAY), 
        open_end_time  = DATE_ADD(NOW(),INTERVAL 2 DAY) WHERE  no  = ${activityId}`;

        try {
            await werewolfConn.query(sql1);
            await werewolfConn.query(sql2);
            await werewolfConn.commit(); // 提交事务

        } catch (error) {
            await werewolfConn.rollback();
            throw error;
        }
    }

    /**
    * @name:更改活动状态-关闭活动
    */
    public async operActivityStateEnd(request: ActivityStateOperReq) {
        
        const { app } = this;
        const db = app.mysql.get('werewolf');
        const activityId = db.escape(request.activityId);
        const werewolfConn = await db.beginTransaction();
        const sql = `UPDATE  word_activity  SET  state  = 1, open_end_time = DATE_SUB(NOW(),INTERVAL 1 DAY) WHERE  no  = ${activityId}`;

        try {
            await werewolfConn.query(sql);
            await werewolfConn.commit(); // 提交事务
        } catch (error) {
            await werewolfConn.rollback();
            throw error;
        }
    }

    /**
     * @name: 更改活动名称
     */
    public async changeActivityName(request: ActivityChangeNameRequest){
        const { app } = this;
        const db = app.mysql.get('werewolf');
        const activityId = db.escape(request.activityId);
        const name = db.escape(request.name);
        const werewolfConn = await db.beginTransaction();
        const sql = `UPDATE word_activity SET word = ${name} WHERE  no  = ${activityId}`;

        try {
            await werewolfConn.query(sql);
            await werewolfConn.commit(); // 提交事务
        } catch (error) {
            await werewolfConn.rollback();
            throw error;
        }
    }

    /**
     * @name: 更改活动奖励数量
     */
    public async changeActivityPrizeNum(request: ActivityChangePrizeNumRequest){
        const { app } = this;
        const db = app.mysql.get('werewolf');
        const activityId = db.escape(request.activityId);
        const num = db.escape(request.num);
        const werewolfConn = await db.beginTransaction();
        const sql = `UPDATE word_activity SET num = ${num} WHERE  no  = ${activityId}`;

        try {
            await werewolfConn.query(sql);
            await werewolfConn.commit(); // 提交事务
        } catch (error) {
            await werewolfConn.rollback();
            throw error;
        }
    }

}
