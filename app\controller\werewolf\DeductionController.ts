/*
 * @Description: 违规记录查询
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: 赵宝强
 * @Date: 2020-11-05 17:37
 * @LastEditors: 赵宝强
 * @LastEditTime: 2020-09-21 13:56:44
 */
import BaseMegaController from './BaseMegaController';
import {IdeductionGameMatchRequest, IdeductionRecoreRequest, LittleOpenBlackRequest} from "../../model/werewolf2";


export default class DeductionController extends BaseMegaController {

    //1 查指定用户违规记录
    public async getUserList() {

        const { ctx, logger } = this;
        const requestBody: IdeductionRecoreRequest = ctx.request.body;

        try {
           const list =  await ctx.service.werewolf.deduction.getUserList(requestBody);
           this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }
    // 查询指定玩家游戏列表
    public async getGameMatch() {
        const { ctx, logger } = this;
        const requestBody: IdeductionGameMatchRequest = ctx.request.body;
        try {
            const list = await ctx.service.werewolf.deduction.getGameMatch(requestBody);
            this.respSuccData(list);
        } catch (err) {
            this.respFail(err);
        }
    }

    //根据gameId查询小号开黑玩家
    public async getLittleOpenBlack() {
        const {ctx, logger} = this;

        const requestBody: LittleOpenBlackRequest = ctx.request.body;
        logger.info(requestBody);
        try {
            const list = await ctx.service.werewolf.deduction.getLittleOpenBlack(requestBody);
            this.respSuccData(list);
        } catch (err) {
            this.respFail(err);
        }
    }


}
