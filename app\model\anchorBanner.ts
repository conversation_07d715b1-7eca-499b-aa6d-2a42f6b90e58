/*
 * @Author: your name
 * @Date: 2020-09-29 15:25:13
 * @LastEditTime: 2020-10-12 17:08:07
 * @LastEditors: jiawen.wang
 * @Description: In User Settings Edit
 * @FilePath: \ConsoleSystemServer\app\model\anchorBanner.ts
 */
export interface IanchorListItemRes {
    id: number
    recommend_tag: number;
    show_type: number;
    content: string;
    remark: string;
    turn_to_page: number;
    page_url: string;
    img_url: string;
    sort: number;
}
export interface IcreateAnchorBannerReq {
    sort: number;
    recommend_tag: number;
    show_type: number;
    content: string;
    remark: string;
    turn_to_page: number;
    page_url: string;
}
export interface IdelAnchorBannerReq {
    id: number
}