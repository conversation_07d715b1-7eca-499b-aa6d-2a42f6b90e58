/*
 * @Description: 账户异常查询
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: 赵宝强
 * @Date: 2020-11-18 11:10
 * @LastEditors: 赵宝强
 * @LastEditTime:
 */

import BaseMegaController from './BaseMegaController';
import {AccountAbnormalRequest, DelLittleRequest, UdidRequest} from "../../model/werewolf2";


export default class AccountAbnormalController extends BaseMegaController {

    public async getCheatGameSts() {

        const { ctx, logger } = this;
        const requestBody: AccountAbnormalRequest = ctx.request.body;
        try {
            const list =  await ctx.service.werewolf.accountAbnormal.getCheatGameSts(requestBody);
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    public async getTuser() {
        const { ctx, logger } = this;
        const requestBody: UdidRequest = ctx.request.body;
        try {
            const list =  await ctx.service.werewolf.accountAbnormal.getUdid(requestBody);
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

}
