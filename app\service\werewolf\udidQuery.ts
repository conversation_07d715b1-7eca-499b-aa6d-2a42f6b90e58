/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-01-14 16:27:31
 * @LastEditTime: 2021-05-19 15:37:37
 * @LastEditors: jiawen.wang
 */
import { Service } from 'egg';
import * as moment from 'moment';
import { time } from 'console';

export default class UdidQueryService extends Service {
    public async getUdidQueryList(req) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        const udid = db.escape(req.udid);
        try {
            let sql = `SELECT no,username,password,createtime,nickname,logintime,DeviceName,model,delsign FROM tuser WHERE udid = ${udid}`;
            const result = await db.query(sql);
            for (let item of result) {
                let accountSql = `SELECT * FROM account_abuse_pro_record WHERE account_id = ${item.no} AND ((data_score >= 35 AND data_score <= 100) OR (
                 data_score < 35 AND data_tags != 'rn0316' AND data_tags != 'no_tag' AND !ISNULL(data_tags) AND data_tags != '') OR (
                 LENGTH(data_score) = 0 AND data_tags != 'no_tag' AND data_tags != '' AND !ISNULL(data_tags)) OR LENGTH(device_token) = 0)`
                let userCreditScoreSql = `SELECT * FROM user_credit_score WHERE user_id = ${item.no} AND down_time >= 1`;//有记录需要认证还没认证通过
                let verifyStatusSql = `SELECT verify_status,idcard FROM describe_verify WHERE user_id = ${item.no} ORDER BY verify_status DESC`;//有值 = 有认证记录 
                let idCardSql = `SELECT idcard FROM describe_verify WHERE user_id = ${item.no}`
                const result1 = await db.query(verifyStatusSql)
                const result2 = await db.query(userCreditScoreSql)
                const result3 = await db.query(accountSql)
                const cardIdresult = await db.query(idCardSql)
                item.certification = result1
                item.certification1 = result2
                item.certification2 = result3
                if (cardIdresult.length !== 0) {
                    item.idCard = cardIdresult[0].idcard
                }
            }
            return result;
        } catch (error) {
            throw error;
        }
    }
    public async userVerifyStatus(req) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        const playerId = db.escape(req.userId);
        try {
            let accountSql = `SELECT * FROM account_abuse_pro_record WHERE account_id = ${playerId} AND ((data_score >= 35 AND data_score <= 100) OR (
                data_score < 35 AND data_tags != 'rn0316' AND data_tags != 'no_tag' AND !ISNULL(data_tags) AND data_tags != '') OR (
                LENGTH(data_score) = 0 AND data_tags != 'no_tag' AND data_tags != '' AND !ISNULL(data_tags)) OR LENGTH(device_token) = 0)`
            let userCreditScoreSql = `SELECT * FROM user_credit_score WHERE user_id = ${playerId} AND down_time >= 1`;//有记录需要认证还没认证通过
            let verifyStatusSql = `SELECT verify_status,idcard FROM describe_verify WHERE user_id = ${playerId} ORDER BY verify_status DESC`;//有值 = 有认证记录 

            const result1 = await db.query(verifyStatusSql)
            const result2 = await db.query(userCreditScoreSql)
            const result3 = await db.query(accountSql)

            return { certification: result1, certification2: result2, certification3: result3 }
        } catch (error) {
            throw error
        }
    }
    public async removeIdentifyByUdid(req) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        const udid = db.escape(req.udid);

        try {
            //查询板子列表
            let sql = `DELETE FROM user_ban_udid_verify WHERE udid=${udid}`;
            const result = await db.query(sql);
            return result;
        } catch (error) {
            throw error;
        }
    }
    // public async getUdidQueryList(request) {
    //     const { app } = this;
    //     const playerInfo = await this.udidQueryList(request);
    //     //不存在该玩家
    //     if (!playerInfo) return null as any;
    //     // const playerState = await this.playerState(request.playerId);
    //     return {
    //         playerInfo,
    //         // ...playerState
    //     };
    // }
}
