import BaseMegaService from './BaseMegaService'
import { IplayerBeAnchor } from '../../model/wfPlayerInfo'
import * as moment from 'moment'

/*
 * @Description: 无
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-05-19 17:52:27
 * @LastEditors: jiawen.wang
 * @LastEditTime: 2022-03-10 13:47:09
 */
export default class PlayerInfoService extends BaseMegaService {
    public async toAnchor(req: IplayerBeAnchor) {
        const { ctx, logger } = this
        try {
            let sqlStr = `SELECT scorce FROM tuser WHERE no = ?;`
            const userInfo = await this.selectOne(sqlStr, [req.playerId])
            if (userInfo && userInfo.scorce >= 3500) {
                sqlStr = `UPDATE tuser SET wins= 252,loss=252,win=252,lose = 252 WHERE no = ?;`
            } else {
                sqlStr = `UPDATE tuser SET scorce = 4500,wins=251,loss=251,win=251,lose = 251 WHERE no = ?;`
            }
            await this.execSql(sqlStr, [req.playerId])
        } catch (err) {
            logger.error(err)
            throw err
        }
    }
    public async beAnchor(req: IplayerBeAnchor) {
        const { app, logger } = this
        try {
            const db = app.mysql.get('werewolf')
            const user_id = db.escape(req.playerId)
            let sqlStr = `INSERT INTO 
                            user_tag (id, user_id, tag_id, num, sort, is_show, user_coe, createtime, delsign)
                        VALUES 
                            (NULL, ${user_id}, '1', '1', '0', '0', '0', ${moment().format()}, '0')`
            await db.query(sqlStr)
        } catch (err) {
            logger.error(err)
            throw err
        }
    }
    public async removeBan(req) {
        const { app, logger } = this
        try {
            const db = app.mysql.get('werewolf')
            const udid = db.escape(req.udid)
            let sqlStr = `DELETE FROM user_ban_udid WHERE udid=${udid}`
            await db.query(sqlStr)
        } catch (err) {
            logger.error(err)
            throw err
        }
    }
    public async clickRemoveResign(req) {
        const { app, logger } = this
        try {
            const werewolf = app.mysql.get('werewolf');

            //验证码发送次数
            const sqlReg = 'SELECT countrycode,username FROM tuser WHERE `no` = ?;';
            const resultReg = await werewolf.query(sqlReg,[req.playerId]);

            let phoneNum = '';
            
            if(resultReg[0].countrycode  != 86){
            phoneNum = resultReg[0].countrycode + resultReg[0].username;
            }else{
            phoneNum = resultReg[0].username;
            }
            phoneNum = phoneNum + '_t';

            await this.app.redis.get("registRedis").set(phoneNum,0);

        } catch (err) {
            logger.error(err)
            throw err
        }
    }
    public async addOverseaItem(req) {
        const { app, logger } = this
        try {
            const db = app.mysql.get('werewolf')
            const playId = db.escape(req.playerId)
            let sqlStr = `INSERT INTO user_header_white (user_id) VALUES (${playId})`
            await db.query(sqlStr)
        } catch (err) {
            logger.error(err)
            throw err
        }
    }
    public async clearSex(req: any) {
        const { app, ctx, logger } = this
        const db = app.mysql.get('werewolf')
        const manager = app.mysql.get('manager')

        //开启事物
        const conn = await db.beginTransaction()
        const managerConn = await manager.beginTransaction()

        try {
            let sqlDeleteUserClothing = 'DELETE FROM wedding_user_clothing WHERE user_id = ? AND clothing_id IN (1,2,3,4,5,6)'
            await conn.query(sqlDeleteUserClothing, [req.playerId])

            let sqlUpdateUserInfoSex = 'UPDATE tuser_info SET sex = 0 WHERE userno = ? '
            await conn.query(sqlUpdateUserInfoSex, [req.playerId])

            let sqlUpdateUserSex = 'DELETE FROM wedding_user WHERE user_id = ? '
            await conn.query(sqlUpdateUserSex, [req.playerId])

            let sqlOperate = `
                INSERT INTO wf_clear_sex_record (clear_sex_user_id, operate_user_id) VALUES 
                (?, ?)
            `
            await managerConn.query(sqlOperate, [req.playerId, req.uid])

            await conn.commit() // 提交事务
            await managerConn.commit()
        } catch (error) {
            await conn.rollback() // 一定记得捕获异常后回滚事务！
            await managerConn.rollback()

            logger.error(error)
            throw error
        }
    }
    public async clearTime(req: any) {
        const { app, ctx, logger } = this
        const db = app.mysql.get('werewolf')
        const manager = app.mysql.get('manager')

        //开启事物
        const conn = await db.beginTransaction()
        const managerConn = await manager.beginTransaction()

        try {
            //redis删除key
            await app.redis.get("liveDanmaku").hdel("wf_user_ban",req.playerId)
		

        } catch (error) {

            logger.error(error)
            throw error
        }
    }
    public async clearBoard(req: any) {
        const { app, ctx, logger } = this

        try {
		
            let res = await ctx.curl(app.config.WerewolfJPFunRoom + 'board/message/clear', {
                method: 'POST',
                headers: {
                    'content-type': 'application/json',
                },
                data: {userId:req.playerId},
                timeout: 3000, // 3 秒超时
            })

        } catch (error) {

            logger.error(error)
            throw error
        }
    }
    public async getNodleStatus(req: IplayerBeAnchor) {
        const { app, logger } = this
        try {
            const db = app.mysql.get('werewolf')
            // const user_id = db.escape(req.playerId);
            let sqlStr = `SELECT * FROM user_noble WHERE user_no = ${req.playerId}`
            let result = await db.query(sqlStr)
            // let result = await this.selectOne(`SELECT * FROM user_noble WHERE user_no = ?`,[req.playerId]);
            return result
        } catch (err) {
            logger.error(err)
            throw err
        }
    }
    public async addUserCoe(req) {
        const { app, logger } = this
        const db = app.mysql.get('werewolf')
        try {
            const user_id = db.escape(req.playerId)
            const user_coe = db.escape(req.user_coe)
            // let sqlStr = `UPDATE user_tag SET user_coe = ${user_coe} WHERE user_id = ${user_id} AND tag_id = 0`
            let sqlStr = `INSERT INTO 
                            user_tag (user_id,tag_id,num,sort,is_show,user_coe)
                        VALUES
                            ('${user_id}','0','1','0','1','${user_coe}') 
                        ON DUPLICATE KEY UPDATE user_coe = ${user_coe}`
            await db.query(sqlStr)
        } catch (err) {
            throw err
        }
    }
    public async getGroupWhiteStatus(req) {
        const { app, logger } = this
        const db = app.mysql.get('werewolf')
        try {
            const sqlStr = `SELECT * FROM user_cheat_group_white WHERE user_no = ?`
            const result = await this.selectList(sqlStr, [req.playerId])
            if (result && result.length > 0) {
                return true
            } else {
                return false
            }
        } catch (error) {
            logger.error(error)
            throw error
        }
    }
}
