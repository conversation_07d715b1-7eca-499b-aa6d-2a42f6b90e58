/*
 * @Description: 首页弹窗-控制类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-03-24 10:28:10
 * @LastEditors: hammercui
 * @LastEditTime: 2020-03-24 14:00:12
 */
import { Controller } from 'egg';
import BaseMegaController from './BaseMegaController';
import { IhomeDialogConf, IhomeDialogEidtReq, IhomeDialogDeleteReq, IhomeDialogType } from '../../model/wfHomeDIalog';

export default class HomeDialogController extends BaseMegaController {

    /**
     * @name: create one home dialog
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async create() {
        const { ctx, logger } = this;
        // 校验规则
        const rule = {
            dialog_type: { type: 'number' },
            show_time: { type: 'string' },
            img_url: { type: 'string' },
        };
        try {
            ctx.validate(rule);
            const request: IhomeDialogConf = ctx.request.body;
            await ctx.service.werewolf.homeDialog.create(request);
            this.respSucc()
        } catch (err) { this.respFail(err) }
    }

    public async list() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.homeDialog.list();
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async typeList() {
        const { ctx, logger } = this;
        try {
            const list: IhomeDialogType[] = await ctx.service.werewolf.homeDialog.typeList();
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async edit() {
        const { ctx, logger } = this;
        // 校验规则
        const rule = {
            id: { type: 'number' },
            dialog_state: { type: 'number' },
        };
        try {
            const request: IhomeDialogEidtReq = ctx.request.body;
            await ctx.service.werewolf.homeDialog.edit(request);
            this.respSucc()
        } catch (err) { this.respFail(err) }
    }

    public async delete() {
        const { ctx, logger } = this;
        // 校验规则
        const rule = {
            id: { type: 'number' },
        };
        try {
            const request: IhomeDialogDeleteReq = ctx.request.body;
            await ctx.service.werewolf.homeDialog.delete(request);
            this.respSucc()
        } catch (err) { this.respFail(err) }
    }
}
