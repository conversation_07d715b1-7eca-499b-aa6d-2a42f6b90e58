/*
 * @Author: your name
 * @Date: 2021-07-29 16:47:07
 * @LastEditTime: 2021-07-29 17:04:32
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /MGKFHTServer/app/routerArenaActivity.ts
 */
import { Router, Application } from "egg";
import { AccessRouteId } from './model/accessRouteCof';
import { once } from 'process';
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

    const { controller, router } = app;
    //竞技场查询  accCtr(AccessRouteId.wolf_redbag),
    router.post(`${API_VERSION}/werewolf/activity/getArenaActivityList`, controller.werewolf.arenaActivity.getArenaActivityList);
}

export default load