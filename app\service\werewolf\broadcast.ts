/*
 * @Description: 世界频道聊天-服务器
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammer<PERSON><PERSON>
 * @Date: 2019-08-06 14:14:17
 * @LastEditors: jiawen.wang
 * @LastEditTime: 2021-06-07 15:04:57
 */

import { Service } from 'egg';
import { IbroadcastListReq, IbroadcastBanReq, IbroadcastListResp, ItnoticeReq, IupdateIdCardInfoReq } from '../../model/werewolf';
import * as moment from "moment";

export default class AvatarFrameService extends Service {

    /**
     * @name: 查询聊天记录列表
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async recordList(request: IbroadcastListReq): Promise<IbroadcastListResp> {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        const playerId = db.escape(request.playerId);
        let results;
        //不存在playerId
        if (request.playerId < 1) {
            results = await db.query(
                `SELECT
                ubrt.id,
                ubrt.group_id,
                ubrt.bc_id,
                ubrt.bc_type,
                ubrt.comment,
                ubrt.user_id,
                ubrt.createtime,
								tuser.nickname
            FROM
                user_boardcast_record_temp as ubrt,tuser
            WHERE
                ubrt.delsign = 0
						AND tuser.no = ubrt.user_id
            ORDER BY
                ubrt.id DESC LIMIT 120`
            );
        } else {
            results = await db.query(
                `SELECT
                ubrt.id,
                ubrt.group_id,
                ubrt.bc_id,
                ubrt.bc_type,
                ubrt.comment,
                ubrt.user_id,
                ubrt.createtime,
								tuser.nickname
            FROM
                user_boardcast_record_temp as ubrt,tuser
            WHERE
                ubrt.delsign = 0
                AND ubrt.user_id = ${playerId}
				AND tuser.no = ubrt.user_id
            ORDER BY
                ubrt.id DESC `
            );
        }

        if (!!results && results.length > 0) {
            return { dataArray: results };
        } else {
            return { dataArray: [] };
        }
    }

    /**
     * @name: 屏蔽聊天记录
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async ban(request: IbroadcastBanReq) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        const playerId = db.escape(request.playerId);
        const broadcastId = db.escape(request.broadcastId);
        const werewolfConn = await db.beginTransaction();
        try {
            const wereRow = {
                delsign: 1
            };
            const wereOptions = {
                where: {
                    user_id: playerId,
                    id: broadcastId,
                }
            };
            await werewolfConn.update("user_boardcast_record_temp", wereRow, wereOptions);
            await werewolfConn.commit(); // 提交事务
        } catch (error) {
            await werewolfConn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }
    public async getTnotice(req: ItnoticeReq) {
        const { app, logger } = this;
        const db = app.mysql.get('werewolf');
        try {
            const sqlStr = `SELECT title,content,n_time FROM tnotcie WHERE userNo = ? AND n_time > DATE_SUB(CURDATE(), INTERVAL 3 MONTH)`
            const result = await db.query(sqlStr, [req.playerId])
            return result
        } catch (error) {
            logger.error(error);
            throw error;
        }
    }
    public async idCardInfoList(req: ItnoticeReq) {
        const { app, logger } = this;
        const db = app.mysql.get('werewolf');
        try {
            const sqlStr = `SELECT idcard,create_time,request_id FROM describe_verify WHERE user_id = ? AND verify_status = 1 ;`
            const result = await db.query(sqlStr, [req.playerId])
            return result
        } catch (error) {
            logger.error(error);
            throw error;
        }
    }
    public async updateIdCardInfo(req: IupdateIdCardInfoReq) {
        const { app, logger } = this;
        const db = app.mysql.get('werewolf');
        const playerId = db.escape(req.playerId);
        const idCard = db.escape(req.idCard);
        const reason = db.escape(req.reason);
        const werewolfConn = await db.beginTransaction();
        try {
            const sqlStr = `UPDATE describe_verify SET idcard= ${idCard + reason} WHERE user_id = ${playerId}`
            await werewolfConn.query(sqlStr)
            await werewolfConn.commit(); // 提交事务
        } catch (error) {
            await werewolfConn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }
}
