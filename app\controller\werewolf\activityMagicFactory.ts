import BaseMegaController from './BaseMegaController'
import { HttpErr, IerrorMsg } from '../../model/common'

export default class ActivityMagicFactoryController extends BaseMegaController {

    public async findFrameItemDicList() {
        const { ctx, logger } = this
        const rules = {
        }
        try {
            ctx.validate(rules)
            const requestBody = ctx.request.body
            const body = await ctx.service.werewolf.activityMagicFactory.findFrameItemDicList(requestBody)
            ctx.body = body
            ctx.status = HttpErr.Success
        } catch (e) {
            logger.error(e)
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' }
            ctx.body = err
            ctx.status = HttpErr.BadRequest //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async findItems() {
        const { ctx, logger } = this
        const rules = {
        }
        try {
            ctx.validate(rules)
            const requestBody = ctx.request.body
            const body = await ctx.service.werewolf.activityMagicFactory.findItems(requestBody)
            ctx.body = body
            ctx.status = HttpErr.Success
        } catch (e) {
            logger.error(e)
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' }
            ctx.body = err
            ctx.status = HttpErr.BadRequest //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateItem() {
        const { ctx, logger } = this
        const rules = {
            id: { type: 'number' },
            item_dic_id: { type: 'number' },
            remark: { type: 'string',required:false },
        }
        try {
            ctx.validate(rules)
            const requestBody = ctx.request.body
            const body = await ctx.service.werewolf.activityMagicFactory.updateItem(requestBody)
            ctx.body = body
            ctx.status = HttpErr.Success
        } catch (e) {
            logger.error(e)
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' }
            ctx.body = err
            ctx.status = HttpErr.BadRequest //请求参数错误 401Unauthorized未鉴权
        }
    }



}
