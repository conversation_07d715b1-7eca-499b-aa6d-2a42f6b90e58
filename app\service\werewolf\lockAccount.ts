import BaseMegaService from './BaseMegaService';

export default class LockAccountService extends BaseMegaService {

    public async getUserListByUserId(req) {
        const {app, ctx, logger} = this;
        try {
            let sql = `SELECT
            t.\`no\`,
            t.nickname,
            t.\`password\`,
            t.udid,
            t.createtime,
            t.logintime,
            apr.umid,
            IFNULL(utt.t_price, 0) AS t_price,
            dr.ip,
            t.delsign
        FROM
            tuser t
        LEFT JOIN account_abuse_pro_record apr ON apr.account_id = t.\`no\`
        LEFT JOIN user_transation_total utt ON utt.user_id = t.\`no\`
        LEFT JOIN device_regist_record dr ON dr.phone = t.\`no\`
        WHERE
            t.\`no\` = ?`;
            return await this.selectList(sql, [req.userNo]);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }
    public async getUserListByAliId(req) {
        const {app, ctx, logger} = this;
        try {
            let sql = `SELECT
            t.\`no\`,
            t.nickname,
            t.\`password\`,
            t.udid,
            t.createtime,
            t.logintime,
            apr.umid,
            IFNULL(utt.t_price, 0) AS t_price,
            dr.ip,
            t.delsign
        FROM
            account_abuse_pro_record apr
        LEFT JOIN tuser t ON  t.\`no\` = apr.account_id 
        LEFT JOIN user_transation_total utt ON utt.user_id = t.\`no\`
        LEFT JOIN device_regist_record dr ON dr.phone = t.\`no\`
        WHERE
            apr.umid = '?'`;
            return await this.selectList(sql, [req.aliId]);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }
    public async getUserListByUdid(req) {
        const {app, ctx, logger} = this;
        try {
            let sql = `SELECT
            t.\`no\`,
            t.nickname,
            t.\`password\`,
            t.udid,
            t.createtime,
            t.logintime,
            apr.umid,
            IFNULL(utt.t_price, 0) AS t_price,
            dr.ip,
            t.delsign
        FROM
            tuser t
        LEFT JOIN account_abuse_pro_record apr ON apr.account_id = t.\`no\`
        LEFT JOIN user_transation_total utt ON utt.user_id = t.\`no\`
        LEFT JOIN device_regist_record dr ON dr.phone = t.\`no\`
        WHERE
            t.udid = '?'`;
            return await this.selectList(sql, [req.udid]);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }
    public async getUserListByIDNumber(req) {
        const {app, ctx, logger} = this;
        try {
            let sql = `SELECT user_id,idcard FROM describe_verify WHERE idcard = ? GROUP BY phone user_id`;
            return await this.selectList(sql, [req.idNum]);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }
    public async getUserListByRegisterIP(req) {
        const {app, ctx, logger} = this;
        try {
            let sql = `SELECT phone,ip FROM device_regist_record WHERE ip = '${req.ip}' GROUP BY phone`;
            return await this.selectList(sql, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }
    public async getIdCardByUserIds(userIds) {
        const {app, ctx, logger} = this;
        try {
            let sql = `SELECT a.user_id,b.idcard FROM(
                SELECT user_id,MAX(create_time) as create_time FROM  describe_verify WHERE user_id IN (?)
                AND idcard is NOT NULL
                 GROUP BY create_time
                )as a,
                describe_verify as b
                WHERE a.user_id = b.user_id
                AND a.create_time = b.create_time
                AND b.idcard IS NOT NULL;`;
            return await this.selectList(sql, [userIds]);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }
    public async getUserInfolistUserIds(userIds) {
        const {app, ctx, logger} = this;
        try {
            let sql = ``;
            return await this.selectList(sql, [userIds]);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getUserBaseInfoListByUserIds(userIds) {
        const {app, ctx, logger} = this;
        try {
            let sql = `SELECT
            t.\`no\`,
            t.nickname,
            t.\`password\`,
            t.udid,
            t.createtime,
            t.logintime,
            IFNULL(utt.t_price, 0) AS t_price,
            t.delsign
        FROM
            tuser t
        LEFT JOIN user_transation_total utt ON utt.user_id = t.\`no\`
        WHERE
            t.\`no\` IN (${userIds});`;
            return await this.selectList(sql, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }
    public async getUserIpInfolistUserIds(userIds) {
        const {app, ctx, logger} = this;
        try {
            let sql = ``;
            return await this.selectList(sql, [userIds]);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }
    public async getUserIdCardInfoListByUserIds(userIds) {
        const {app, ctx, logger} = this;
        try {
            let sql = `SELECT user_id AS userId,idcard AS idCard FROM describe_verify WHERE user_id IN (${userIds}) AND verify_status = 1`;
            return await this.selectList(sql, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }
    public async getUserIdCardListByIdCard(req) {
        const {app, ctx, logger} = this;
        try {
            let sql = `SELECT user_id AS userId,idcard AS idCard FROM describe_verify WHERE idcard = ? AND verify_status = 1`;
            return await this.selectList(sql, [req.idCard]);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }
    public async getUserAliIdInfoListByUserIds(userIds) {
        const {app, ctx, logger} = this;
        try {
            let sql = `SELECT account_id AS userId,umid AS aliId  FROM account_abuse_pro_record WHERE account_id IN (${userIds}) `;
            return await this.selectList(sql, [userIds]);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }
    public async getUserIdsByAliId(req) {
        const {app, ctx, logger} = this;
        try {
            let sql = `SELECT account_id AS userId,umid AS aliId  FROM account_abuse_pro_record WHERE umid = ?`;
            return await this.selectList(sql, [req.aliId]);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }
    public async getUserBaseInfoListByUdid(req) {
        const {app, ctx, logger} = this;
        try {
            let sql = `SELECT
            t.\`no\`,
            t.nickname,
            t.\`password\`,
            t.udid,
            t.createtime,
            t.logintime,
            IFNULL(utt.t_price, 0) AS t_price,
            t.delsign
        FROM
            tuser t
        LEFT JOIN user_transation_total utt ON utt.user_id = t.\`no\`
        WHERE
            t.udid = ? ;`;
            return await this.selectList(sql, [req.udid]);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }
    
}
