import BaseMegaService from "./BaseMegaService";
import {IaddTeamWeekGiftBagParams, IupdateTeamWeekGiftBagParams} from "../../model/teamWeekGiftBagDto";

export default class TeamWeekGiftBag extends BaseMegaService {

    public async getItems(params: undefined) {

        const sql = `SELECT a.id,
                            a.gift_id,
                            a.item_dic_id,
                            c.\`name\` AS                                  item_dic_name,
                            c.item_cate_id,
                            a.num_limit,
                            a.num,
                            a.weight,
                            a.sort,
                            a.delsign,
                            a.\`name\`,
                            a.percent,
                            DATE_FORMAT(a.start_time, '%Y-%m-%d %H:%i:%s') start_time,
                            DATE_FORMAT(a.end_time, '%Y-%m-%d %H:%i:%s')   end_time
                     FROM team_week_gift_bag a
                              LEFT JOIN item_dic c ON a.item_dic_id = c.id
                     ORDER BY id DESC`

        try {
            const res = await this.selectList(sql)
            return res ?? []
        } catch (e) {
            this.logger.error(e)
            return false
        }
    }

    public async addItem(params: IaddTeamWeekGiftBagParams) {

        // 2010 2020
        const sql = `INSERT INTO team_week_gift_bag (gift_id, item_dic_id, item_cate_id,num_limit, num, weight, sort, delsign,
                                                     \`name\`, percent, start_time, end_time)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?)`

        try {
            const res = await this.execSql(sql, [params.gift_id, params.item_dic_id, params.item_cate_id, params.num_limit, params.num, params.weight, params.sort, params.delsign, params.name, params.percent, params.start_time, params.end_time])
            return res
        } catch (e) {
            this.logger.error(e)
            return false
        }
    }

    public async updateItem(params: IupdateTeamWeekGiftBagParams) {

        const sql = `UPDATE team_week_gift_bag
                     SET gift_id = ?,
                         item_dic_id = ?,
                         item_cate_id = ?,
                         num_limit = ?,
                         num = ?,
                         weight = ?,
                         sort = ?,
                         delsign = ?,
                         \`name\` = ?,
                         percent = ?,
                         start_time = ?,
                         end_time = ?
                     WHERE id = ?`

        try {
            const res = await this.execSql(sql, [params.gift_id, params.item_dic_id, params.item_cate_id, params.num_limit, params.num, params.weight, params.sort, params.delsign, params.name, params.percent, params.start_time, params.end_time, params.id])
            return res
        } catch (e) {
            this.logger.error(e)
            return false
        }
    }

}