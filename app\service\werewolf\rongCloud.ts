import { Service } from 'egg';
import { HttpErr, IerrorMsg } from '../../model/common';
import * as moment from "moment";
import { IrongCloudRequest, IrongCloudResponse, IrongCloudItem } from '../../model/werewolf';

export default class RongCloudService extends Service {

    public async info(request: IrongCloudRequest): Promise<IrongCloudResponse> {
        const { app } = this;
        let count = 0;
        let args = {};
        let res: IrongCloudItem[] = new Array();
        const endTime = moment(new Date()).subtract(request.time, 'month').format("YYYY-MM-DD HH:mm:ss")

        if (request.userNo == "" && request.groupNo != "") {
            const countArgs = { query: { groupid: request.groupNo, datetime: { $gt: endTime } } };
            count = await app['mongo'].count('rongCloud-history', countArgs);
            args = { query: { groupid: request.groupNo, datetime: { $gt: endTime } }, skip: request.start, limit: request.offset, sort: [['datetime', -1]] };
            res = await app['mongo'].find("rongCloud-history", args);
        } else if (request.userNo != "" && request.groupNo == "") {
            const countArgs = { query: { fromuserid: request.userNo, datetime: { $gt: endTime } } };
            count = await app['mongo'].count('rongCloud-history', countArgs);
            args = { query: { fromuserid: request.userNo, datetime: { $gt: endTime } }, skip: request.start, limit: request.offset, sort: [['datetime', -1]] };
            res = await app['mongo'].find("rongCloud-history", args);
        } else if (request.userNo != "" && request.groupNo != "") {
            const countArgs = { query: { fromuserid: request.userNo, groupid: request.groupNo, datetime: { $gt: endTime } } };
            count = await app['mongo'].count('rongCloud-history', countArgs);
            args = { query: { fromuserid: request.userNo, groupid: request.groupNo, datetime: { $gt: endTime } }, skip: request.start, limit: request.offset, sort: [['datetime', -1]] };
            res = await app['mongo'].find("rongCloud-history", args);
        }

        return {
            ...request,
            count: count,
            dataArray: res
        };
    }
}
