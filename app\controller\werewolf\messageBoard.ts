import BaseMegaController from "./BaseMegaController";
import {HttpErr, IerrorMsg} from "../../model/common";
import {
    IMessageBoardItem,
    // IMessageBoardRepliesItem
} from "../../model/messageBoardDao";

export default class MessageBoardController extends BaseMegaController{


    public async getMessageBoardList(){
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: IMessageBoardItem = ctx.request.body;
            const responseBody = await ctx.service.werewolf.messageBoard.getMessageBoardList(requestBody);
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async delMessageOrReply(){
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: any = ctx.request.body;
            const responseBody = await ctx.service.werewolf.messageBoard.delMessageOrReply(requestBody);
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
}