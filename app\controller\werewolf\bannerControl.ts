/*
 * @Description: banner管理
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: 张宇
 * @Date: 2019-08-22 15:40:22
 * @LastEditors: jiawen.wang
 * @LastEditTime: 2021-02-27 11:08:26
 */
import { Controller } from 'egg';
import { IbannerListRequest, IbannerListResponse, IuploadBannerRequest, BannerIsShowRequest, DelBannerInfoRequest } from '../../model/werewolf';
import { HttpErr, IerrorMsg } from '../../model/common';
import { IbannerBaseInfo, IbannerImageInfo, IUPbannerBaseInfo } from '../../model/wfNewBanner'
import BaseMegaController from './BaseMegaController';
export default class BannerControl extends BaseMegaController {

	public async bannerList() {
		const { ctx, logger, app } = this;
		// 校验规则
		const rule = {
			start: { type: 'number' },
			offset: { type: 'number' }
		};
		try {
			//1 校验
			ctx.validate(rule);
			const request: IbannerListRequest = ctx.request.body;
			const response: IbannerListResponse = await ctx.service.werewolf.bannerControl.bannerList(request);
			ctx.body = response;
			ctx.status = HttpErr.Success;
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}

	//上传banner信息
	public async uploadBannerInfo() {
		const { ctx, logger, app } = this;
		// 校验规则
		const rule = {
			banner_name: { type: 'string' },
			banner_img: { type: 'string' },
			url: { type: 'string' },
			type: { type: 'number' },
			page: { type: 'number' },
			start_time: { type: 'string' },
			end_time: { type: 'string' },
		};
		try {
			//1 校验
			ctx.validate(rule);
			const request: IuploadBannerRequest = ctx.request.body;
			await ctx.service.werewolf.bannerControl.uploadBannerInfo(request);
			ctx.body = {
				err_code: HttpErr.Success,
				err_msg: '上传成功'
			};
			ctx.status = HttpErr.Success;
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}

	//控制banner显示隐藏
	public async bannerIsShow() {
		const { ctx, logger, app } = this;
		// 校验规则
		const rule = {
			id: { type: 'number' },
			is_show: { type: 'number' }
		};
		try {
			//1 校验
			ctx.validate(rule);
			const request: BannerIsShowRequest = ctx.request.body;
			await ctx.service.werewolf.bannerControl.bannerIsShow(request);
			ctx.body = {
				err_code: HttpErr.Success,
				err_msg: '修改状态成功！'
			};
			ctx.status = HttpErr.Success;
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}

	//删除banner
	public async delBannerInfo() {
		const { ctx, logger, app } = this;
		// 校验规则
		const rule = {
			id: { type: 'number' },
		};
		try {
			//1 校验
			ctx.validate(rule);
			const request: DelBannerInfoRequest = ctx.request.body;
			await ctx.service.werewolf.bannerControl.delBannerInfo(request);
			ctx.body = {
				err_code: HttpErr.Success,
				err_msg: '删除成功！'
			};
			ctx.status = HttpErr.Success;
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}
	//上传banner基本信息
	public async uploadNewBannerBaseInfo() {
		const { ctx, logger, app } = this;
		// 校验规则
		const rule = {
			sort_id: { type: 'number', min: 0 },
			banner_name: { type: 'string' },
			url: { type: 'string', required: false, default: '' },
			show_type: { type: 'number' },
			type: { type: 'number', required: false, default: 0 },
			page: { type: 'number', required: false, default: 0 },
			start_time: { type: 'string' },
			end_time: { type: 'string' },
			tend_type: { type: "number" },
			activity_id: { type: "number", required: false, default: 0 },
			tab1_id: { type: "number", required: false, default: null },
			tab2_id: { type: "number", required: false, default: null }
		};
		try {
			//1 校验
			ctx.validate(rule);
			const request: IbannerBaseInfo = ctx.request.body;
			const response = await ctx.service.werewolf.bannerControl.uploadNewBannerBaseInfo(request);
			this.respSuccData(response)
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}
	//上传banner图片信息
	public async uploadNewBannerImageInfo() {
		const { ctx, logger, app } = this;
		// 校验规则
		const rule = {
			banner_img: { type: 'string' },
			id: { type: 'number' },
		};
		try {
			//1 校验
			ctx.validate(rule);
			const request: IbannerImageInfo = ctx.request.body;
			const response = await ctx.service.werewolf.bannerControl.uploadNewBannerImageInfo(request);
			this.respSuccData(response)
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}
	public async uploadNewHotImageInfo() {
		const { ctx, logger, app } = this;
		// 校验规则
		const rule = {
			banner_img: { type: 'string' },
			id: { type: 'number' },
		};
		try {
			//1 校验
			ctx.validate(rule);
			const request: IbannerImageInfo = ctx.request.body;
			await ctx.service.werewolf.bannerControl.uploadNewHotImageInfo(request);
			this.respSucc()
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}
	//更新banner基本信息
	public async updateNewBannerBaseInfo() {
		const { ctx, logger, app } = this;
		// 校验规则
		const rule = {
			banner_name: { type: 'string' },
			url: { type: 'string', required: false, default: '' },
			show_type: { type: 'number' },
			type: { type: 'number', required: false, default: 0 },
			page: { type: 'number', required: false, default: 0 },
			start_time: { type: 'string' },
			end_time: { type: 'string' },
			id: { type: 'number' },
			sort_id: { type: 'number' },
			is_show: { type: 'number' },
			is_prod: { type: 'number' },
			tend_type: { type: "number" },
			tab1_id: { type: "number", required: false, default: null },
			tab2_id: { type: "number", required: false, default: null }
		};
		try {
			//1 校验
			ctx.validate(rule);
			const request: IUPbannerBaseInfo = ctx.request.body;
			await ctx.service.werewolf.bannerControl.updateNewBannerBaseInfo(request);
			this.respSucc()
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}


	public async getBetaBannerList() {
		const { ctx, logger, app } = this;
		// 校验规则
		const rule = {
		};
		try {
			//1 校验
			// ctx.validate(rule);
			const request = ctx.request.body;
			const response = await ctx.service.werewolf.bannerControl.getBetaBannerList(request);
			ctx.body = response;
			ctx.status = HttpErr.Success;
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}


	public async addBetaBanner() {
		const { ctx, logger, app } = this;
		// 校验规则
		// const rule = {
		// 	activity_id: { type: "number", required: false, default: 0 }
		// };
		try {
			//1 校验
			// ctx.validate(rule);
			const request = ctx.request.body;
			const response = await ctx.service.werewolf.bannerControl.addBetaBanner(request);
			this.respSuccData(response)
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}

	public async updateBetaBannerInfo() {
		const { ctx, logger, app } = this;
		// 校验规则
		// const rule = {
		// 	id: { type: 'number' },
		// 	activity_id: { type: 'number' },
		// 	desc: { type: 'string' },
		// 	url: { type: 'string' }
		// };
		try {
			//1 校验
			// ctx.validate(rule);
			const request = ctx.request.body;
			const response = await ctx.service.werewolf.bannerControl.updateBetaBannerInfo(request);
			this.respSuccData(response)
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}

}
