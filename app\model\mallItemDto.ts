export interface IaddRealityLotteryBoxParams {
    uid: number,
    name: string,
    level: number,
    show_level: number,
    img: string,
    num: number,
    num_surplus: number,
    remark: string,
    delsign: number,
    sort: number,
    activity_start_time: string,
    activity_end_time: string,

    price: number,

    mall_tag_id: number,
    mall_tag_start_time: string,
    mall_tag_end_time: string,
}

export interface IupdateRealityLotteryBoxParams extends IaddRealityLotteryBoxParams{
    id: number,
}

export interface IdeleteRealityLotteryBoxParams{
    id: number,
}