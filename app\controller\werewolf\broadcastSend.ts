/*
 * @Description: 商城道具管理-控制类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: z<PERSON><PERSON>
 * @Date: 2020-04-24 13:00:00
 * @LastEditors: zhang<PERSON>
 * @LastEditTime: 2021-06-18 10:17:46
 */
import { Controller } from 'egg';
import BaseMegaController from './BaseMegaController';
import { IerrorMsg, HttpErr } from '../../model/common';

export default class BroadcastSendController extends BaseMegaController {

    public async getBroadcastList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.broadcastSend.getBroadcastList();
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async updateBroadcastType() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.broadcastSend.updateBroadcastType(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async updateBroadcast() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.broadcastSend.updateBroadcast(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }

    public async insertBroadcast() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.broadcastSend.insertBroadcast(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (err) { this.respFail(err); }
    }
}
