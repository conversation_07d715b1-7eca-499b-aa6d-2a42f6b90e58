/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-05 09:42:11
 * @LastEditTime: 2021-06-05 13:45:04
 * @LastEditors: jiawen.wang
 */

import { Controller } from 'egg';
import { IbanDanMuListRes } from "../../model/banDanMu"
import { IerrorMsg, HttpErr } from '../../model/common';

export default class BanDanMuContorller extends Controller {
    public async getBanDanMuList() {
        const { ctx, logger } = this;
        try {
            const selectType = ctx.request.body.selectType
            const resp: IbanDanMuListRes = await ctx.service.werewolf.banDanMu.getBanDanMuList(selectType);
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
}
