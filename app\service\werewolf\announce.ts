/*
 * @Description: 系统公告
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-04-15 15:31:05
 * @LastEditors: hammercui
 * @LastEditTime: 2020-05-06 09:37:22
 */

import BaseMegaService from './BaseMegaService';
import { IannounceItem, IupAnnounceSortReq, IdownAnnounceSortReq, IupdateShowStateReq, IdelAnnounceReq, IcreateAnnounceReq, IeditAnnounceReq } from '../../model/wfAnnounce';

export default class AnnounceService extends BaseMegaService {

    /**
     * @name: 获得系统公告列表
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async announceList(): Promise<IannounceItem[]> {
        const { logger } = this;
        try {
            const sqlStr = `SELECT id,sort_id,title,content,create_time,show_state 
            FROM tsys_announce WHERE  del_sign = 0
            ORDER BY 
            sort_id DESC,
            create_time DESC;`;
            const list: IannounceItem[] = await this.selectList(sqlStr);
            return list;
        } catch (err) {
            logger.error(err);
            throw err;
        }
    }

    /**
     * @name: 删除公告
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async delAnnounce(req: IdelAnnounceReq) {
        const { logger } = this;
        try {
            const sqlStr = `UPDATE tsys_announce SET del_sign = 1 WHERE id = ? ;`;
            await this.execSql(sqlStr, [req.id]);
        } catch (err) {
            logger.error(err);
            throw err;
        }
    }

    /**
     * @name: 提升排序
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async upAnnounce(req: IupAnnounceSortReq) {
        const { logger } = this;
        try {
            //查询自己
            let sqlStr = `SELECT * FROM tsys_announce WHERE id = ${req.id};`;
            const selfInfo: IannounceItem = await this.selectOne(sqlStr);
            let selfSortId = selfInfo.sort_id;
            //查询上一级
            sqlStr = `SELECT * FROM tsys_announce WHERE sort_id > ${selfInfo.sort_id} LIMIT 1 ;`;
            const upInfo: IannounceItem = await this.selectOne(sqlStr);

            if (upInfo) {
                //本换成上一级sort_id
                await this.execSql(`UPDATE tsys_announce SET sort_id = ?  WHERE id = ?`, [upInfo.sort_id, selfInfo.id]);
                //上级换成本sort_id
                await this.execSql(`UPDATE tsys_announce SET sort_id = ?  WHERE id = ?`, [selfInfo.sort_id, upInfo.id]);
            }

        } catch (err) {
            logger.error(err);
            throw err;
        }
    }

    /**
     * @name: 降低排序
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async downAnnounce(req: IdownAnnounceSortReq) {
        const { logger } = this;
        try {
            //查询自己
            let sqlStr = `SELECT * FROM tsys_announce WHERE id = ${req.id};`;
            const selfInfo: IannounceItem = await this.selectOne(sqlStr);
            let selfSortId = selfInfo.sort_id;
            //查询下一级
            sqlStr = `SELECT * FROM tsys_announce WHERE sort_id < ${selfInfo.sort_id} ORDER BY sort_id DESC  LIMIT 1 ;`;
            const downInfo: IannounceItem = await this.selectOne(sqlStr);

            if (downInfo) {
                //本替换下一级sort_id
                await this.execSql(`UPDATE tsys_announce SET sort_id = ? WHERE id = ?`, [downInfo.sort_id, selfInfo.id]);
                //下级替换为本sort_id
                await this.execSql(`UPDATE tsys_announce SET sort_id = ?  WHERE id = ?`, [selfInfo.sort_id, downInfo.id]);
            }

        } catch (err) {
            logger.error(err);
            throw err;
        }
    }

    /**
     * @name: 更新公告状态
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async updateShow(req: IupdateShowStateReq) {
        const { logger } = this;
        try {
            await this.execSql(`UPDATE tsys_announce SET show_state = ?  WHERE id = ?`, [req.show_state, req.id]);
        } catch (err) {
            logger.error(err);
            throw err;
        }
    }

    /**
     * @name: 新建公告
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async create(req: IcreateAnnounceReq) {
        const { logger } = this;
        try {
            //插入
            let sqlStr = `INSERT INTO tsys_announce (title,content,create_time)
            VALUES (?,?,?)`;
            const result = await this.execSql(sqlStr, [req.title, req.content, req.create_time]);
            let lastId = result.insertId
            logger.info("插入announce", result.insertId);

            //更新sort_id
            sqlStr = `UPDATE tsys_announce SET sort_id = ?  WHERE id = ?`;
            await this.execSql(sqlStr, [lastId, lastId]);
        } catch (err) {
            logger.error(err);
            throw err;
        }
    }

    /**
     * @name: 编辑
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async edit(req: IeditAnnounceReq) {
        const { logger } = this;
        try {
            //更新sort_id
            const sqlStr = `UPDATE tsys_announce SET title = ? ,content=?,create_time=?  WHERE id = ?`;
            await this.execSql(sqlStr, [req.title, req.content, req.create_time, req.id]);
        } catch (err) {
            logger.error(err);
            throw err;
        }
    }

}
