import BaseMegaController from "./BaseMegaController";
import {
    IentertainmentSearchRoomRequest,
    IhallPopupsAddRequest,
    IhallPopupsDeleteRequest,
    IhallPopupsUpdateRequest
} from "../../model/werewolf";
import {HttpErr, IerrorMsg} from "../../model/common";

export default class HallPopupsController extends BaseMegaController {

    public async loadRecord() {
        const {ctx, logger} = this;

        try {
            //service
            const respBody = await ctx.service.werewolf.hallPopups.loadRecord();
            ctx.body = respBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = {
                err_code: HttpErr.BadRequest,
                err_msg: "参数有误"
            };

            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }

    }

    public async addRecord() {
        const {ctx, logger} = this;
        const rule = {
            uid: {type: "number"},
            name: {type: 'string'},
            url: {type: 'string'},
            starttime: {type: 'string'},
            endtime: {type: 'string'},
            delsgin: {type: 'number'},
        };
        try {
            // 校验
            ctx.validate(rule);
            //service
            const requestBody: IhallPopupsAddRequest = ctx.request.body;
            const respBody = await ctx.service.werewolf.hallPopups.addRecord(requestBody);
            ctx.body = respBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = {
                err_code: HttpErr.BadRequest,
                err_msg: "参数有误"
            };

            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }

    }

    public async updateRecord() {
        const {ctx, logger} = this;
        const rule = {
            uid: {type: "number"},
            id: {type: 'number'},
            name: {type: 'string'},
            url: {type: 'string'},
            starttime: {type: 'string'},
            endtime: {type: 'string'},
            delsgin: {type: 'number'},
        };
        try {
            // 校验
            ctx.validate(rule);
            //service
            const requestBody: IhallPopupsUpdateRequest = ctx.request.body;
            const respBody = await ctx.service.werewolf.hallPopups.updateRecord(requestBody);
            ctx.body = respBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = {
                err_code: HttpErr.BadRequest,
                err_msg: "参数有误"
            };

            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }

    }

    public async deleteRecord() {
        const {ctx, logger} = this;
        const rule = {
            uid: {type: "number"},
            id: {type: 'number'},
        };
        try {
            // 校验
            ctx.validate(rule);
            //service
            const requestBody: IhallPopupsDeleteRequest = ctx.request.body;
            const respBody = await ctx.service.werewolf.hallPopups.deleteRecord(requestBody);
            ctx.body = respBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = {
                err_code: HttpErr.BadRequest,
                err_msg: "参数有误"
            };

            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }

    }

}