/*
 * @Description: 游戏中工具类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2019-07-10 14:48:20
 * @LastEditors: leeou
 * @LastEditTime: 2019-07-19 14:52:50
 */

/**
 * @name: 根据camp_no获得阵营名称
 * @msg: 
 * @param {type} 
 * @return: 
 */
export function GetCampName(camp_no: number): string {
  let camp = "默认阵营";
  if (camp_no < 10) {
    if (camp_no == 1 || camp_no == 2) {
      camp = "好人阵营";
    } else if (camp_no == 3) {
      camp = "狼人阵营";
    } else if (camp_no == 4) {
      camp = "三方阵营";
    }
  } else {
    if (camp_no % 10 == 1 || camp_no % 10 == 2) {
      camp = "好人阵营(情侣)";
    } else if (camp_no % 10 == 3) {
      camp = "狼人阵营(情侣)";
    } else if (camp_no % 10 == 4) {
      camp = "三方阵营(丘比特)";
    }
  }
  return camp;
}

/**
 * @name: 获得游戏结束状态描述
 * @msg: 
 * @param {type} 
 * @return: 
 */
export function GetGameLifeDesc(lifeNo: number): string {
  let life = lifeNo + "";
  switch (lifeNo) {
    case 1:
      life = "存活";
      break;
    case 2:
      life = "白天死亡";
      break;
    case 3:
      life = "夜晚被毒";
      break;
    case 4:
      life = "白痴翻牌";
      break;
    case 5:
      life = "自爆";
      break;
    case 6:
      life = "夜晚被刀";
      break;
  }
  return life;
}

/**
 * 
 * @param win 获得游戏结束结果描述
 */
export function GetGameWinDesc(win: number): string {
  let winDesc = win + "";
  if (win == 0) {
    winDesc = "平局";
  } else if (win == 1) {
    winDesc = "胜利";
  } else if (win == 2) {
    winDesc = "失败";
  }
  return winDesc;
}
