// tslint:disable-next-line:no-var-requires
/**
 * @name: manager数据库建模
 * @msg:
 * @param {type}
 * @return:
 */
export interface Igift {
	no: number;
	name: string;
	price: number;
	reprice: number;
	timeprice: number;
	charm: number;
	prive_remark: string;
	props_explain: string;
	MD5: string;
	datatime: string;
	img_name: string;
	gif_name: string;
	gift_type: number;
	power_value: number;
	gift_level_one_num: number;
	gift_level_two_num: number;
	gift_super: number;
	gift_check_num: number;
	nobleLevel: number;
	show: number;
	hot: number;
	priority: number;
	delsign: number;
	cateId: number;
	gift_source: number;
	box_level: number;
	min_charm: number;
	max_charm: number;
	add_in: number;
	g_give: number;
	team_exp: number;
	user_exp: number;
  }
  
  export interface IitemCate {
	id: number;
	item_catalog_id: number;
	cls: number;
	type: string;
	name: string;
	remark: string;
	once: number;
	time_limit: number;
	consume: number;
	item_table: string;
	item_user_table: string;
  }

  export interface IitemDic {
	id: number;
	item_cate_id: number;
	item_id: number;
	name: string;
	pic: string;
	icon: string;
	remark: string;
	version: number;
	delsign: number;
  }

  export interface Ianimation {
	id: number;
	name: string;
	role: number;
	type: number;
	isLight: number;
	bg: number;
	timer: number;
	price: number;
	show: number;
	buy: number;
	sort: number;
	createtime: string;
	datePath: string;
	serialNumber: string;
	alert: string;
	hot: number;
	remark: string;
	channel: number;
	delsign: number;
  }

  export interface Imaskshow {
	no: number;
	name: string;
	prive_remark: string;
	props_explain: string;
    datatime: string;
	priority: number;
	buy: number;
	delsign: number;
	show: number;
	hot: number;
	md5: string;
  }

  export interface Igroupbadge {
	id: number;
	name: string;
  }

  export interface InormalItem {
	no: number;
	name: string;
	role: number;
	unit: number;
	unit_num: number;
	price: number;
	datatime: string;
	img_name: number;
	nobleLevel: number;
	show: number;
	props_explain: string;
	priority: number;
	delsign: number;
  }