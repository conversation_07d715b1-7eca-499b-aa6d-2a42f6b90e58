/*
 * @Description: 无
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-06-02 16:03:58
 * @LastEditors: hammercui
 * @LastEditTime: 2020-08-28 13:53:25
 */
import { Subscription, Context } from "egg";
import moment = require('moment');
import { IoldPopularItem, InewPopularItem, IwfKingBaseItem } from '../model/wfKing';
// import { Logger } from 'egg-logger';
// const phantom = require('phantom');
// const cheerio = require('cheerio')

// const sleep = (ms) => {
//     return new Promise((resolve) => setTimeout(resolve, ms));
// }

// /**
//  * @name: 
//  * @msg: 
//  * @param {type} 
//  * @return: 
//  */
// const getNewPopularList = async (ctx: Context, logger: Logger, oldList: IoldPopularItem[]): Promise<InewPopularItem[]> => {
//     let newPopularList: InewPopularItem[] = new Array();
//     //遍历获得
//     for (const item of oldList) {
//         let newPopular = await getNewPopularValue(ctx, item.popular, item.room_url)
//         logger.info(`主播${item.anchor_id}的new popular`, newPopular);
//         newPopularList.push({ anchor_id: item.anchor_id, popular: newPopular, rank: 1 })
//         await sleep(500 + Math.random() * 100)
//     }
//     newPopularList = sortNewPopularList(newPopularList)
//     //排序
//     return newPopularList;
// };

// const sortNewPopularList = (befar: InewPopularItem[]): InewPopularItem[] => {
//     //降序排列
//     let after = befar.sort((a, b) =>
//         a.popular - b.popular
//     )
//     for (let i = 0, len = after.length; i < len; i++) {
//         after[i].rank = len - i;
//     }

//     return after;
// }

module.exports = (app) => {

    return {
        schedule: {
            interval: app.config.loopKingInterval, // 10s
            type: 'worker', // 指定所有的 worker 都需要执行
        },

        async task(ctx: Context) {
            // app.logger.info("1分钟检测是否抓取主播人气值");
            // let baseItem: IwfKingBaseItem = await ctx.service.werewolf.wolfKing.getAtyingBaseItem();
            // if (!baseItem || !baseItem.anchor_day) {
            //     return;
            // }
            // if (baseItem.crawler_state == 0) {
            //     app.logger.info("自动抓取已关闭");
            //     return;
            // }

            // let curTime = moment();
            // let curDay = curTime.get("day");
            // let curHour = curTime.get("hours");
            // let curMinutes = curTime.get("minutes");
            // let targetDay = moment(baseItem.anchor_day).get("day")
            // //比赛日，并且晚上6点之后,每整点抓取一次
            // if (curDay == targetDay && curHour >= 18 && curMinutes == 0) {
            //     app.logger.info("整点,自动抓取一次");
            //     await ctx.service.werewolf.wolfKing.crawlerPopular(baseItem.quarter_id,baseItem.channel_id);
            // }
        }
    }
};
