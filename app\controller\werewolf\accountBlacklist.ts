/*
 * @Description: 小号黑名单查询
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: 赵宝强
 * @Date: 2020-11-09 17:37
 * @LastEditors: 赵宝强
 * @LastEditTime:
 */

import BaseMegaController from './BaseMegaController';
import {DelLittleRequest, IaccountBlacklistRequest} from "../../model/werewolf2";


export default class AccountBlacklistController extends BaseMegaController {

    //1 查指定用户小号黑名单
    public async getLittleBlacklist() {

        const { ctx, logger } = this;
        const requestBody: IaccountBlacklistRequest = ctx.request.body;
        logger.info(requestBody);

        try {
            const list =  await ctx.service.werewolf.accountBlacklist.getLittleBlacklist(requestBody);
            this.respSuccData(list)
        } catch (err) {
            this.respFail(err)
        }
    }

    //删除小号
    public async delLittleBlacklist() {
        const { ctx, logger } = this;
        const requestBody: DelLittleRequest = ctx.request.body;
        logger.info(requestBody);
        try {
            const flag =  await ctx.service.werewolf.accountBlacklist.delLittleBlacklist(requestBody);
            this.respSuccData(flag)
        } catch (err) {
            this.respFail(err)
        }
    }

}
