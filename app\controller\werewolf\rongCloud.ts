import { Controller } from "egg";
import { HttpErr, IerrorMsg } from "../../model/common";
import { IrongCloudRequest, IrongCloudResponse } from '../../model/werewolf';

export default class RongCloudController extends Controller {
    public async info() {
        const { ctx, logger } = this;
        // 校验规则
        const rule = {
            time: { type: "number" },
            start: { type: "number" },
            offset: { type: "number" },
        };
        try {
            // 校验
            ctx.validate(rule);
            //service
            const requestBody: IrongCloudRequest = ctx.request.body;
            const responseBody: IrongCloudResponse = await ctx.service.werewolf.rongCloud.info(
                requestBody
            );
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = {
                err_code: HttpErr.BadRequest,
                err_msg: "参数有误"
            };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
}
