/*
 * @Description: 公告-控制类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-04-15 16:16:02
 * @LastEditors: hammercui
 * @LastEditTime: 2020-04-15 16:46:47
 */

import BaseMegaController from './BaseMegaController';
import { IupAnnounceSortReq, IdownAnnounceSortReq, IdelAnnounceReq, IupdateShowStateReq, IannounceItem, IcreateAnnounceReq, IeditAnnounceReq } from '../../model/wfAnnounce';

export default class AnnounceController extends BaseMegaController {

    public async upSort() {
        const { ctx, logger } = this;
        const rule = {
            id: { type: 'number' }
        }
        try {
            ctx.validate(rule);
            const request: IupAnnounceSortReq = ctx.request.body;
            await ctx.service.werewolf.announce.upAnnounce(request);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }

    public async downSort() {
        const { ctx, logger } = this;
        const rule = {
            id: { type: 'number' }
        }
        try {
            ctx.validate(rule);
            const request: IdownAnnounceSortReq = ctx.request.body;
            await ctx.service.werewolf.announce.downAnnounce(request);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }

    public async del() {
        const { ctx, logger } = this;
        const rule = {
            id: { type: 'number' }
        }
        try {
            ctx.validate(rule);
            const request: IdelAnnounceReq = ctx.request.body;
            await ctx.service.werewolf.announce.delAnnounce(request);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }

    public async updateState() {
        const { ctx, logger } = this;
        const rule = {
            id: { type: 'number' },
            show_state: { type: 'number' }
        }
        try {
            ctx.validate(rule);
            const request: IupdateShowStateReq = ctx.request.body;
            await ctx.service.werewolf.announce.updateShow(request);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }

    public async list() {
        const { ctx, logger } = this;
        try {
            const list: IannounceItem[] = await ctx.service.werewolf.announce.announceList();
            this.respSuccData(list)
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }

    /**
     * @name: 新建公告
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async create() {
        const { ctx, logger } = this;
        const rule = {
            title: { type: 'string' },
            content: { type: 'string' },
            create_time: { type: 'string' },
        }
        try {
            ctx.validate(rule);
            const request: IcreateAnnounceReq = ctx.request.body;
            await ctx.service.werewolf.announce.create(request);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }

    public async edit(){
        const { ctx, logger } = this;
        const rule = {
            id: { type: 'number' },
            title: { type: 'string' },
            content: { type: 'string' },
            create_time: { type: 'string' },
        }
        try {
            ctx.validate(rule);
            const request: IeditAnnounceReq = ctx.request.body;
            await ctx.service.werewolf.announce.edit(request);
            this.respSucc()
        } catch (error) {
            logger.error(error);
            this.respFail(error.message)
        }
    }
}
