/*
 * @Description: 路由权限中间件
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2018-11-26 15:23:59
 * @LastEditors: hammercui
 * @LastEditTime: 2019-02-01 16:02:21
 */
import { Application, Context, EggAppConfig } from 'egg';
import { Ipayload, HttpErr, IerrorMsg } from '../model/common';
import { IsAccessEnable } from '../util/utils';
const forbidden: IerrorMsg = { err_code: HttpErr.Forbidden, err_msg: '已鉴权，但访问禁止' };

export default function accessRouteMiddleWare({ routeId }): any {
	return async (ctx: Context, next: () => Promise<any>) => {
        //路由不需要检验
		if (!routeId) {
			await next();
			return;
        }
        
		//todo 判断用户的路由进入权限
		const payload: Ipayload = ctx.helper.exPayload;
		// console.info('需要路由权限', routeId);
		// console.info('用户拥有路由权限', payload.accessRoutes);
		if (!!payload && IsAccessEnable(routeId, payload.accessRoutes)) {
			await next();
		} else {
			ctx.body = forbidden;
			ctx.status = HttpErr.Forbidden;
			return;
		}
	};
}
