/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-01-15 18:03:05
 * @LastEditTime: 2021-03-05 11:34:29
 * @LastEditors: jiawen.wang
 */

import { Controller } from 'egg';
import { IavFrameUserListRequest, IbroadcastListReq, IbroadcastBanReq, IbroadcastListResp, IclearEscapeReq, IescapeListReq } from '../../model/werewolf';
import { IerrorMsg, HttpErr } from '../../model/common';

export default class ClearEscapeController extends Controller {

    public async getUdidQueryList() {
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
            udid: { type: 'string' },
        };
        try {
            ctx.validate(rule);
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.udidQuery.getUdidQueryList(requestBody);
            ctx.body = list;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    public async userVerifyStatus() {
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
            userId: { type: 'number' },
        };
        try {
            ctx.validate(rule);
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.udidQuery.userVerifyStatus(requestBody);
            ctx.body = list;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    public async removeIdentifyByUdid() {
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
            udid: { type: 'string' }
        }
        try {
            ctx.validate(rule);
            const requestBody = ctx.request.body;
            const res = await ctx.service.werewolf.udidQuery.removeIdentifyByUdid(requestBody);
            ctx.body = res;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
}