/*
 * @Description: 玩家状态查询列表service
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2019-06-11 11:09:25
 * @LastEditors: hammercui
 * @LastEditTime: 2019-07-01 16:31:38
 */

import { Service } from 'egg';
import { IboardcastImpListRequest, IboardcastImpListResponse, BanUserAbilityRequest, RemoveForeverBlockRequest } from '../../model/werewolf';
import { HttpErr, IerrorMsg } from '../../model/common';

export default class PlayerStatusListService extends Service {

  public async boardcastImpList(request: IboardcastImpListRequest): Promise<IboardcastImpListResponse> {
    const { app, logger } = this;
    const werewolf = app.mysql.get("werewolf");
    const playerId = werewolf.escape(request.playerId);
    const sql = "SELECT" +
      " ubrd.bc_id As bcId," +
      " ubrd.bc_type As bcType," +
      " ubrd.`comment`," +
      " ubrd.createtime AS speakTime," +
      " tbird.type AS contentType," +
      " tbird.time AS impTime" +
      " FROM" +
      " tuser_boardcast_imprison_record AS tbird" +
      " LEFT JOIN  user_boardcast_record AS ubrd ON tbird.user_boardcast_record_id = ubrd.id" +
      " WHERE" +
      " ubrd.user_id = " + playerId +
      " AND ubrd.delsign = 0" +
      " ORDER BY speakTime" +
      " LIMIT 10";
    const result = await werewolf.query(sql);
    logger.debug("result", result);
    let dataArray = [];
    if (!!result && result.length > 0) {
      dataArray = result;
    }
    return { ...request, dataArray }
  }

  /**
   * 用户使用中背景图animationId
   * @param playerId 
   */
  public async selectCurrBgAnimaId(playerId: number) {
    const { app, logger } = this;
    const werewolf = app.mysql.get("werewolf");
    let result = await werewolf.query(`
    SELECT
	animation_id
FROM
	user_animation_state
WHERE
	user_id = ${playerId}
AND role_id = 2000
AND delsign = 0
    `)
    if (result && result.length > 0) {
      return result[0].animation_id;
    } else {
      return null;
    }
  }

  /**
     * 禁用背景图功能时，重置背景
     * @param dbTransaction 数据库事物 
     * @param playerId 
     */
  public async resetBgByBan(dbTransaction, currBgAnimaId, playerId: number) {
    const { logger } = this;
    logger.debug("测试重置背景板playerId:", playerId);
    //1.封禁使用中背景
    if (currBgAnimaId && currBgAnimaId > 0) {
      await dbTransaction.update("user_animation", { delsign: 1 }, {
        where: {
          user_id: playerId,
          animation_id: currBgAnimaId
        }
      });
    }
    //2.重置使用中为0
    await dbTransaction.update("user_animation_state", { animation_id: 0 }, {
      where: {
        user_id: playerId,
        role_id: 2000
      }
    });
  }

  /**
   * 禁用上传头像时，重置头像框
  * @param dbTransaction 数据库事物 
   * @param playerId 
   */
  public async resetAvatarByBan(dbTransaction, playerId: number) {
    const { logger } = this;
    logger.debug("测试重置头像框playerId:", playerId);
    await dbTransaction.update("tuser", { headicon: 6 }, {
      where: {
        no: playerId,
        delsign: 0
      }
    });
  }

  /**
   * @name: 封禁用户功能
   * @param request 
   */
  public async banUserAbility(request: BanUserAbilityRequest) {
    const { app, logger } = this;
    const werewolf = app.mysql.get("werewolf");
    const manager = app.mysql.get("manager");

    const uid = werewolf.escape(request.uid);
    const playerId = werewolf.escape(request.playerId);
    const abilityId = werewolf.escape(request.abilityId);
    const banTime = werewolf.escape(request.banTime);

    let currBgAnimaId = await this.selectCurrBgAnimaId(request.playerId)

    //开启事物
    const werewolfConn = await werewolf.beginTransaction();
    const managerConn = await manager.beginTransaction();

    try {
      if (banTime < 1 || (banTime > 15 && banTime != 999)) {
        const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
        throw err;
      } else {
        let sql = "";
        let imgSql = "";
        let wf_user_ban_type = 2;//功能区
        if (banTime > 15) {
          //封禁功能永久
          wf_user_ban_type = 3;//永封功能区
          if (request.abilityId == 1) {
            await this.resetBgByBan(werewolfConn, currBgAnimaId, request.playerId)
          } else if (request.abilityId == 2) {
            await this.resetAvatarByBan(werewolfConn, request.playerId)
            let imgurl = werewolf.escape(request.imgurl);
            imgSql = "INSERT INTO `user_oss_detect_record` ( `user_no`, `img`, `img_type`, `reject`, `suggestion`, `content` )" +
              "VALUES ( " + playerId + ", " + imgurl + ", '0', 'ad', 'block', " + imgurl + ")"
          } else if(request.abilityId == 6) {
            // 心情
            let sqlStr = `
                UPDATE user_mood_state
                SET mood = ?
                WHERE \`user_id\` = ?
            `;
            await werewolfConn.query(sqlStr,['',playerId])
          }
          sql = `INSERT INTO tuser_ban_deal ( user_no, type, create_time )VALUES( ${playerId} , ${abilityId} , NOW()) ON DUPLICATE KEY UPDATE is_delsign = 0, create_time =  NOW()`;
        } else {
          //封禁功能时限
          if (request.abilityId == 15) {
            await this.resetAvatarByBan(werewolfConn, request.playerId)
            let imgurl = werewolf.escape(request.imgurl);
            imgSql = "INSERT INTO `user_oss_detect_record` ( `user_no`, `img`, `img_type`, `reject`, `suggestion`, `content` )" +
              "VALUES ( " + playerId + ", " + imgurl + ", '0', 'ad', 'block', " + imgurl + ")"
          } else if (request.abilityId == 16) {
            await this.resetBgByBan(werewolfConn, currBgAnimaId, request.playerId)
          } else if(request.abilityId == 26) {
            let sqlStr = `
                UPDATE user_mood_state
                SET mood = ?
                WHERE \`user_id\` = ?
            `;
            await werewolfConn.query(sqlStr,['',playerId])
          }
          sql = `INSERT INTO tuser_imprison_deal ( user_no , type , release_time ) VALUES (${playerId}, ${abilityId} , 
          DATE_ADD(NOW(),INTERVAL ${banTime} DAY)) 
          ON DUPLICATE KEY UPDATE release_time = DATE_ADD(NOW(),INTERVAL ${banTime} DAY)`;
        }

        if (imgSql != "") {
          await werewolfConn.query(imgSql);
        }

        await werewolfConn.query(sql);
        await managerConn.insert("wf_user_ban", {
          admin_id: uid,
          user_id: playerId,
          type: wf_user_ban_type,
          category: request.abilityId,
          operation: 1,
          desc: request.reason
        });

        await werewolfConn.commit();
        await managerConn.commit();
      }
    } catch (error) {
      await werewolfConn.rollback();
      await managerConn.rollback();
      throw error;
    }
  }
  /**
   * 解封永久功能封禁
   */
  public async removeForeverBlock(request: RemoveForeverBlockRequest) {
    const { app, logger } = this;
    const werewolf = app.mysql.get("werewolf");
    const manager = app.mysql.get("manager");

    const uid = werewolf.escape(request.uid);
    const playerId = werewolf.escape(request.playerId);
    const abilityId = werewolf.escape(request.abilityId);

    const werewolfConn = await werewolf.beginTransaction();
    const managerConn = await manager.beginTransaction();

    try {
      let sql = "";
      if (abilityId == 100) {
        sql = `UPDATE danmaku_ban SET  forever='0' , endtime = CURRENT_TIME() WHERE user_id = ${playerId};`;
      } else {
        sql = `UPDATE tuser_ban_deal SET is_delsign = 1 WHERE user_no = ${playerId} AND type = ${abilityId};`;
      }
      await werewolfConn.query(sql);
      await managerConn.insert("wf_user_ban", {
        admin_id: uid,
        user_id: playerId,
        type: 3,
        category: request.abilityId,
        operation: 0,
        desc: '解除永久功能封禁'
      });
      await werewolfConn.commit();
      await managerConn.commit();
    } catch (error) {
      await werewolfConn.rollback();
      await managerConn.rollback();
      throw error;
    }
  }
}
