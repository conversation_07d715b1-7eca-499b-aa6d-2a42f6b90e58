/*
 * @Author: your name
 * @Date: 2021-01-25 13:21:01
 * @LastEditTime: 2021-02-07 17:19:41
 * @LastEditors: jiawen.wang
 * @Description: In User Settings Edit
 * @FilePath: \ConsoleSystemServer\app\controller\werewolf\identificaition.ts
 */
import { Controller } from 'egg';
import { IerrorMsg, HttpErr } from '../../model/common';

export default class Identification extends Controller {
    /**
     * @name: 用户认证
     * @msg:
     * @param {type}
     * @return:
    */
    public async identify() {
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
            userno: { type: 'string' }
        }
        try {
            ctx.validate(rule);
            const requestBody = ctx.request.body;
            const res = await ctx.service.werewolf.identification.identify(requestBody);
            ctx.body = res;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    public async cancelIdentify() {
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
            userno: { type: 'string' }
        }
        try {
            ctx.validate(rule);
            const requestBody = ctx.request.body;
            const res = await ctx.service.werewolf.identification.cancelIdentify(requestBody);
            ctx.body = res;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    /**
     * @name: 用户认证udid
     * @msg:
     * @param {type}
     * @return
    */
    public async identifyByUdid() {
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
            udid: { type: 'string' }
        }
        try {
            ctx.validate(rule);
            const requestBody = ctx.request.body;
            const res = await ctx.service.werewolf.identification.identifyByUdid(requestBody);
            ctx.body = res;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
}