/*
 * @Description: 玩家钻石
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2018-11-26 11:17:12
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2020-06-19 15:01:09
 */

import { Controller } from 'egg';
import { HttpErr, IerrorMsg } from '../../model/common';
import {
	IpropsRequest,
	IpropsResponse,
	IdiamodIncRequest,
	IdiamodDecRequest,
	IdiamodDecResponse,
	IdiamodIncResponse
} from '../../model/werewolf';
import BaseMegaController from './BaseMegaController';
import { I2WTransacRecord } from '../../model/wfDaimond';

export default class DiamonsController extends BaseMegaController {
	public async info() {
		const { ctx, logger, app } = this;
	}
	public async decrease() {
		const { ctx, logger } = this;
		// 校验规则
		const rule = {
			playerId: { type: 'number' },
			uid: { type: 'number' },
			decrease: { type: 'int', min: 1 },
			comment: { type: 'string' }
		};
		try {
			// 校验
			ctx.validate(rule);
			const requestBody: IdiamodDecRequest = ctx.request.body;
			const responseBody: IdiamodDecResponse = await ctx.service.werewolf.diamond.decrease(requestBody);
			ctx.body = responseBody;
			ctx.status = HttpErr.Success;
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}
	public async increase() {
		const { ctx, logger } = this;
		// 校验规则
		const rule = {
			playerId: { type: 'number' },
			uid: { type: 'number' },
			increase: { type: 'int', min: 1 },
			comment: { type: 'string' }
		};
		try {
			// 校验
			ctx.validate(rule);
			const requestBody: IdiamodIncRequest = ctx.request.body;
			const responseBody: IdiamodIncResponse = await ctx.service.werewolf.diamond.increase(requestBody);
			ctx.body = responseBody;
			ctx.status = HttpErr.Success;
		} catch (e) {
			logger.error(e);
			const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
			ctx.body = err;
			ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
		}
	}

	/**
  * @description: 充值2w
  * @param {type} 
  * @return: 
  */
	public async twoWTransace() {
		const { ctx, logger, app } = this;
		// 校验规则
		const rule = {
			playerId: { type: 'number' },
			uid: { type: 'number' },
		};
		try {
			let body = ctx.request.body;
			// 校验
			ctx.validate(rule);
			//todo 请求url
			const curlResp = await ctx.curl(app.config.php2wTransacUrl, {
				method: 'POST',
				contentType: 'json',
				data: { userNo: body.playerId },
				dataType: 'json',
				timeout: 30000, // 30 秒超时
			});
			logger.info("请求php",curlResp);
			if (curlResp.status == 200 && curlResp.data.code == 1) {
				let accIp = ctx.request.ip;
				let req: I2WTransacRecord = { admin_id: body.uid, user_id: body.playerId, comment: "请求ip" + accIp, type: 2, num: 0 }
				await ctx.service.werewolf.diamond.insertTwoWTransRecord(req)
				this.respSucc();
			} else {
				this.respFail(curlResp.data);
			}
		} catch (e) {
			logger.error(e);
			this.respFail(e);
		}
	}

}
