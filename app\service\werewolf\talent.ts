/*
 * @Description: 
 * @Company: sdbean
 * @Author: zhang<PERSON>
 * @Date: 2021-03-30 17:23:36
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-09-07 15:20:35
 */

import BaseMegaService from './BaseMegaService';
import { MallItemBaseType, MallItemBaseInfo, CoinOperation, UploadTagInfoReq } from './../../model/mallItemManager';
export default class TalentService extends BaseMegaService {

    public async getVideoList(req: any) {
        const { app, ctx, logger } = this;
        try {

            let sql = ` 
            SELECT p.*,u.nickname,u.headicon,u.frame
            FROM video_play_back p
            LEFT JOIN tuser u ON u.\`no\` = p.user_id
            WHERE p.season_id = ?
            ORDER BY p.id ASC
            Limit ?, ?
            `;
            let list = await this.selectList(sql, [req.seasonId, (req.currentPage - 1) * req.pageCount, req.pageCount]);

            let totalCountSql = `
            SELECT
            COUNT(*) AS num
            FROM
            video_play_back
            WHERE season_id = ?
            `
            let count = await this.selectOne(totalCountSql, [req.seasonId]);
            return { list, totalCount: count.num };
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async insertVideo(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get('werewolf');
        // const manager = app.mysql.get("manager");

        //开启事物
        const conn = await db.beginTransaction();
        // const managerConn = await manager.beginTransaction();
        let rate = 0;
        let praise_real_num = req.praise_num > 10 ? req.praise_num / 10 : 1;
        let play_real_num = req.play_num > 10 ? req.play_num / 10 : 1;
        if (req.play_num == 0 || req.praise_num == 0) {
            rate = 0;
        } else {
            rate = req.praise_num > req.play_num ? 100 : req.praise_num / req.play_num * 100;
        }
        try {
            let sqlStr = `
            INSERT INTO video_play_back (
                praise_num,
                praise_real_num,
                play_num,
                play_real_num,
                title,
                rate,
                user_id,
                season_id,
                create_time,
                delsign 
            )
            VALUES
                (
                  ?,?,?,?,?,?,?,?,NOW(),1
                );
            `;
            await conn.query(sqlStr, [
                req.praise_num,
                praise_real_num,
                req.play_num,
                play_real_num,
                req.title,
                rate,
                req.user_id,
                req.seasonId]);

            await conn.commit(); // 提交事务
            // await managerConn.commit();
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            // await managerConn.rollback();

            logger.error(error);
            throw error
        }
    }

    public async updateVideo(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get('werewolf');
        // const manager = app.mysql.get("manager");

        //开启事物
        const conn = await db.beginTransaction();
        // const managerConn = await manager.beginTransaction();
        let rate = 0;
        if (req.play_num == 0 || req.praise_num == 0) {
            rate = 0;
        } else {
            rate = req.praise_num > req.play_num ? 100 : req.praise_num / req.play_num * 100;
        }
        let praise_real_num = req.praise_num > 10 ? req.praise_num / 10 : 1;
        let play_real_num = req.play_num > 10 ? req.play_num / 10 : 1;

        try {
            let sqlStr = `
            UPDATE video_play_back 
            SET praise_num = ?,
            praise_real_num = ?,
            play_num = ?,
            play_real_num = ?,
            title = ?,
            rate = ?,
            user_id = ?
            WHERE
                id = ?
            `;
            await conn.query(sqlStr, [
                req.praise_num,
                praise_real_num,
                req.play_num,
                play_real_num,
                req.title,
                rate,
                req.user_id,
                req.id]);
            await conn.commit(); // 提交事务
            // await managerConn.commit();

        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            // await managerConn.rollback();

            logger.error(error);
            throw error
        }
    }

    public async updateVideoDelsign(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get('werewolf');

        //开启事物
        const conn = await db.beginTransaction();

        try {
            let sqlStr = `
            UPDATE 
                video_play_back 
            SET 
                delsign = ?
            WHERE
                id = ?
            `;
            await conn.query(sqlStr, [req.delsign, req.id]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            logger.error(error);
            throw error
        }
    }

    public async updateVideoUrl(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get('werewolf');

        //开启事物
        const conn = await db.beginTransaction();

        try {
            let sqlStr = `
            UPDATE 
                video_play_back 
            SET 
                url = ?,
                pic_url  = ?
            WHERE
                id = ?
            `;
            await conn.query(sqlStr, [req.url, req.pic_url, req.id]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            logger.error(error);
            throw error
        }
    }

    public async getSeasonList(req: any) {
        const { app, ctx, logger } = this;
        try {
            let sql = ` 
            SELECT * FROM video_play_season 
            `;
            return await this.selectList(sql, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async insertVideoSeason(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sqlStr = `
            INSERT INTO video_play_season(\`name\`, delsign) VALUES (?, 1)
            `;
            await conn.query(sqlStr, [
                req.name]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            logger.error(error);
            throw error
        }
    }

    public async updateVideoSeason(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sqlStr = `
            UPDATE video_play_season SET \`name\` = ? WHERE \`id\` = ?;
            `;
            await conn.query(sqlStr, [
                req.name,
                req.id]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            logger.error(error);
            throw error
        }
    }

    public async updateVideoSeasonDelsign(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sqlStr = `
            UPDATE video_play_season SET delsign = ? WHERE \`id\` = ?;
            `;
            await conn.query(sqlStr, [
                req.delsign,
                req.id]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            logger.error(error);
            throw error
        }
    }

    public async updateVideoSeasonCurrent(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sqlStr = `
            UPDATE video_play_season SET current = 0;
            `;
            await conn.query(sqlStr, []);
            let sqlCurrentStr = `
            UPDATE video_play_season SET current = 1 WHERE \`id\` = ?;
            `;
            await conn.query(sqlCurrentStr, [
                req.id]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            logger.error(error);
            throw error
        }
    }

}
