/*
 * @Description: 调度器工具类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhang<PERSON>
 * @Date: 2024-01-01 00:00:00
 */
import { Context } from "egg";

/**
 * 调度器环境检测和执行工具类
 */
export class ScheduleHelper {
    
    /**
     * 检查是否为开发环境
     * @param app Egg应用实例
     * @returns 是否为开发环境
     */
    static isDevelopmentMode(app: any): boolean {
        return app.config.serverEnv === 'local';
    }

    /**
     * 安全执行调度器任务
     * @param ctx 上下文
     * @param app 应用实例
     * @param taskName 任务名称
     * @param taskFunction 任务执行函数
     * @param skipInDev 是否在开发环境中跳过（默认为true）
     */
    static async safeExecuteTask(
        ctx: Context,
        app: any,
        taskName: string,
        taskFunction: () => Promise<void>,
        skipInDev: boolean = true
    ): Promise<void> {
        // 检查是否为开发环境且需要跳过
        if (skipInDev && this.isDevelopmentMode(app)) {
            ctx.logger.info(`[${taskName}] 调度器在开发模式下被禁用，跳过执行`);
            return;
        }

        ctx.logger.info(`[${taskName}] 开始执行任务`);
        try {
            await taskFunction();
            ctx.logger.info(`[${taskName}] 任务执行完成`);
        } catch (error) {
            ctx.logger.error(`[${taskName}] 任务执行失败:`, error);
            throw error; // 重新抛出错误，让调度器框架处理
        }
    }

    /**
     * 创建标准的调度器配置
     * @param app 应用实例
     * @param interval 执行间隔
     * @param type 调度器类型（默认为'worker'）
     * @returns 调度器配置对象
     */
    static createScheduleConfig(app: any, interval: string, type: string = 'worker') {
        return {
            interval,
            type,
        };
    }

    /**
     * 获取环境信息字符串
     * @param app 应用实例
     * @returns 环境信息
     */
    static getEnvironmentInfo(app: any): string {
        const env = app.config.serverEnv || 'unknown';
        const isDev = this.isDevelopmentMode(app);
        return `环境: ${env} (${isDev ? '开发模式' : '生产模式'})`;
    }
}

/**
 * 调度器装饰器 - 用于简化调度器的创建
 * @param taskName 任务名称
 * @param interval 执行间隔
 * @param skipInDev 是否在开发环境中跳过
 */
export function createScheduler(
    taskName: string,
    interval: string,
    skipInDev: boolean = true
) {
    return function(app: any) {
        return {
            schedule: ScheduleHelper.createScheduleConfig(app, interval),
            async task(ctx: Context) {
                await ScheduleHelper.safeExecuteTask(
                    ctx,
                    app,
                    taskName,
                    async () => {
                        // 这里需要在具体的调度器中实现具体的业务逻辑
                        throw new Error('请在具体的调度器中实现业务逻辑');
                    },
                    skipInDev
                );
            }
        };
    };
}
