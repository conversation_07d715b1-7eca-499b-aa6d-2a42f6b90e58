
/*
 * @Description: web运营活动配置中心-服务类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-03-30 11:49:30
 * @LastEditors: hammercui
 * @LastEditTime: 2020-11-26 16:58:09
 */

import BaseMegaService from './BaseMegaService';
import { IatyInfo, IautoConfItem, IawardConfItem, IsetAutoConfReq } from '../../model/wfActivityConf';
import moment = require('moment');

export default class WebAtyConfService extends BaseMegaService {

    /**
   * @name: 创新新的活动
   * @msg: 
   * @param {type} 
   * @return: 
   */
    public async createAty(req: IatyInfo) {
        const { logger } = this;
        try {
            // 插入mysql
            const sqlStr = `INSERT INTO activity_index (id,name,start_time,end_time,end_data_time,sql_admin)
            VALUES(?,?,?,?,?,?);`;
            const result = await this.execSql(sqlStr, [req.id, req.name, req.start_time, req.end_time, req.end_data_time, req.sql_admin]);
            let lastId = req.id;
            // let lastId = result.insertId;
            // 新增mysql user
            if (req.sql_admin && req.sql_admin.length > 4) {
                await this.createMySqlUser(req.sql_admin);
            }
        } catch (err) {
            logger.error(err);
            throw err;
        }
    }

    /**
     * @name: 创建数据库新用户
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async createMySqlUser(userName: string) {
        const { logger } = this;
        try {
            let sqlStr = `CREATE USER '${userName}'@'' IDENTIFIED BY 'Mangosteen0!';;`;
            await this.execSql(sqlStr, []);
            sqlStr = `GRANT SELECT, INSERT, UPDATE, DELETE, EXECUTE ON *.* TO '${userName}'@'';`;
            await this.execSql(sqlStr, []);
        } catch (err) {
            logger.error(err);
        }
    }

    /**
     * 获得db8 redis名
     * @param env 
     */
    public getConfRedisName(env: string){
        let redisName = "confRedis"
        if(env == "beta" || env == "test"){
            redisName = "confBetaRedis"
        }
        return redisName
    }

    /**
     * @name: 更新自由配置
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async setAutoConf(req: IsetAutoConfReq) {
        let varStr = JSON.stringify(req.value);
        try {
            let redis = this.app.redis.get(this.getConfRedisName(req.env));
            let hashKey = `web_aty_conf_auto_${req.activityId}_${req.env}`
            const result = await redis.hset(hashKey, req.key, varStr);
            this.logger.debug("setAutoConf result ", result);
        } catch (err) {
            throw err;
        }
    }

    /**
     * @name: 获得自由配置
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async getAutoConf(activityId, env: string, key: string) {
        try {
            let redis = this.app.redis.get(this.getConfRedisName(env));
            let hashKey = `web_aty_conf_auto_${activityId}_${env}`
            const value = await redis.hget(hashKey, key);
            return JSON.parse(value)
        } catch (err) {
            throw err;
        }
    }

    public async getAllAutoConf(activityId, env: string): Promise<IautoConfItem[]> {
        const { logger } = this;
        try {
            let hashKey = `web_aty_conf_auto_${activityId}_${env}`
            const value = await this.app.redis.get(this.getConfRedisName(env)).hgetall(hashKey);
            console.info("全部自由配置value", value);
            const valueList: IautoConfItem[] = new Array();

            //写入startDataTime endDataTime endTIme
            let keyArray = ['startDataTime', 'endDataTime', 'endTime']
            // tslint:disable-next-line:prefer-for-of
            for (let i = 0; i < keyArray.length;i++){
                let tkey = keyArray[i]
                if(value[tkey]){
                    logger.debug("配置存在key",tkey);
                    logger.debug("配置value",value[tkey]);
                    let obj: IautoConfItem = JSON.parse(value[tkey]);
                    obj.key = tkey;
                    valueList.push(obj);
                    //从value删除tkey
                    delete value[tkey];
                }
            }
    
            // tslint:disable-next-line:forin
            for (const key in value) {
                if(!key){
                    continue;
                }
                let obj: IautoConfItem = JSON.parse(value[key]);
                obj.key = key;
                valueList.push(obj);
            }
            // let len = valueList.length;

            // for (let i = 0; i < len; i++) {
            //     for (let j = 0, len2 = len - i - 1; j < len2; j++) {
            //         let aTimestamp = moment(valueList[j].val).valueOf()
            //         let bTimestamp = moment(valueList[j + 1].val).valueOf()
            //         if (!aTimestamp || !bTimestamp) {
            //             continue;
            //         }
            //         if (aTimestamp > bTimestamp) {
            //             let temp = valueList[j + 1]
            //             valueList[j + 1] = valueList[j]
            //             valueList[j] = temp
            //         }
            //     }
            // }

            // console.info("全部自由配置valueList", valueList);
            return valueList;
        } catch (err) {
            throw err;
        }
    }

    /**
     * 获得db3 redis name
     * @param env 
     */
    public getTokenRedisName(env: string){
        let redisName = "tokenRedis"
        if(env == "beta" || env == "test"){
            redisName = "tokenBetaRedis"
        }
        return redisName
    }

    /**
     * 获得活动任务配置
     * @param env 
     * @param key 
     */
    public async getAtyTaskConf(env: string): Promise<IautoConfItem[]> {
        try {
            let redis = this.app.redis.get(this.getTokenRedisName(env));
            let hashKey = `aty_task_comm_conf_${env}`
            let retArray: IautoConfItem[] = []

            const startVal = await redis.hget(hashKey, "startTime");
            const endVal = await redis.hget(hashKey, "endTime");
            retArray[0] = { key: "startTime", desc: "任务开始结算时间", val: startVal }
            retArray[1] = { key: "endTime", desc: "任务结束结算时间", val: endVal }
            
            return retArray
        } catch (err) {
            throw err;
        }
    }

    /**
     * 获得任务刷新配置
     * @param env 
     */
    public async getAtyTaskRefreshConf(env: string): Promise<IautoConfItem[]> {
        try {
            let redis = this.app.redis.get(this.getTokenRedisName(env));
            let hashKey = `wolf_task_refresh_plugs_${env}`
            let retArray: IautoConfItem[] = []
            let items  = await redis.hgetall(hashKey)
            // tslint:disable-next-line:forin
            for (let key in items) {
                let val = items[key]
                retArray.push({ key:key, desc: "php状态", val: val })
            }

            return retArray
        } catch (err) {
            throw err;
        }
    }

    /**
     * 设置活动任务配置
     * @param env 
     * @param key 
     * @param val 
     */
    public async setAtyTaskConf(env: string, key: string, val: string) {
        try {
            let redis = this.app.redis.get(this.getTokenRedisName(env));
            let hashKey = `aty_task_comm_conf_${env}`
            const value = await redis.hset(hashKey, key,Number(val));
            return
        } catch (err) {
            throw err;
        }
    }

    public async setAtyTaskRefreshConf(env: string, key: string, val: string) {
        const {logger} = this;
        try {
            let redis = this.app.redis.get(this.getTokenRedisName(env));
            let hashKey = `wolf_task_refresh_plugs_${env}`
            logger.debug("刷新hashname",hashKey);
            logger.debug("刷新key ",key);
            logger.debug("刷新val ",val);
            const value = await redis.hset(hashKey, key,val);
            return
        } catch (err) {
            throw err;
        }
    }

    /**
     * 获得活动任务列表
     * @param activityId 
     */
    public async getTaskList(activityId) {
        const { logger } = this;
        try {
            let sqlStr = `SELECT a.id as mission_id,
            a.ui_sort,
            a.content,
            b.id as task_id,
            b.cate,
            b.type as task_type,
            c.content as task_name,
            b.num as target_num,
            a.is_delsign,
            a.activity_id
            ,a.mission_type
            ,a.mission_sub_type
            ,a.limit_num
            FROM task_config_mission_activity as a
            LEFT JOIN task_detail as b on a.task_id = b.id
            LEFT JOIN task_type   as c on c.id = b.type
            WHERE a.activity_id = ?
            ;`;
            let list = await this.selectList(sqlStr, [activityId])
            return list;
        } catch (err) {
            logger.error(err);
        }
    }
}
