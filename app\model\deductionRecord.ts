/*
 * @Description: 用户违规
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: 赵宝强
 * @Date: 2020-17-42 17:42
 * @LastEditors: 赵宝强
 * @LastEditTime: 2020-09-23 09:34:42
 */

//用户违规列表
export interface IuserList {
    user_no: number;          // 用户id
    game_list: string;    // 被扣分局列表
    game_num: number;     //   被扣局数
    cut_score: number;   //   被扣分分数
    user_little: string;  // null or cheat_group_id
    createtime: string;   //创建时间
    id_delsign: number; //0未处理 ， 1已返还  2违规不返还
}


export interface IgameMatch {

    no: number;   //匹配局ID
    game_type: number;
    round: number;
    starttime: string;
    server_type: number;
    desc: string;
    user_no: number;
    game_no: number;
    valid: number;
    name: string;
    seat_index: number; //座位号
    camp_no: number;//阵营
    role_no: number;//角色号码
    life: number;//生命状态
    win: string;//游戏结果
    escape: number//是否逃跑
    role: string;// 角色名字

}

export interface LittleOpenBlack{
    user_no: number;
    gameNum: number;
    remark: string;
    createTime: string;
    is_delsign: number;
}


