/*
 * @Author: chen peng 
 * @Date: 2021-08-18 13:28:00
 * @LastEditTime: 2021-08-18 14:49:26
 * @LastEditors: Please set LastEditors
 * @Description: 集市模型
 * @FilePath: /MGKFHTServer/app/model/wfMarket.ts
 */



// 集市 - 数据记录 - 每日大盘模型
export interface ImarketResp {
    tranList: ImarketDetailsResp[]; //交易笔数list
    diamondList: ImarketDetailsResp[]; //钻石数list
}
// 集市 - 数据记录 - 每日大盘详细模型
export interface ImarketDetailsResp {
    date: string; //时间
    scales: number; //值
}

export interface ImarketCirculateReq {
    name: string;
}
// 集市 - 数据记录 - 数据流通模型
export interface ImarketCirculateResp {
    deal_num: number; //交易成功笔数
    item_id: string;// id
    remark: string;//描述
    total_num: number; //全服总数量
    selling_num: number; // 在架数量
    max: number; //成交最高价
    min: number;//成交最低价
    avg: number;//成交均价
    url: string;//图片地址
}

// 集市列表显示隐藏
export interface ImarketStateReq {
    id: number; //
    delsign: number;//
}

export interface IgetAvatarMapContentList {
    avatarframe_map_id: number;
}

export interface IupdateAvatarMapReq {
    id: number;
    name: string;
    sort: number;
    delsign: number;
    type: number; //`type` tinyint(4) DEFAULT NULL COMMENT '1正常2光辉3CP',
}

export interface IinsertAvatarMapReq {
    name: string;
    sort: number;
    delsign: number;
    type: number; //`type` tinyint(4) DEFAULT NULL COMMENT '1正常2光辉3CP',
}

export interface IdeleteAvatarMapReq {
    id: number;//`type` tinyint(4) DEFAULT NULL COMMENT '1正常2光辉3CP',
}

export interface IinsertAvatarMapContentReq {
    avatarframe_id: number;
    avatarframe_map_id: number;
    sort: number;
    source: string;
}

export interface IdeleteAvatarMapContentReq {
    id: number;
}

export interface IupdateAvatarMapContentReq {
    id: number;
    avatarframe_id: number;
    sort: number;
    source: string;
}

export interface IgetGemRecordsReq {
    userId: number;
}



