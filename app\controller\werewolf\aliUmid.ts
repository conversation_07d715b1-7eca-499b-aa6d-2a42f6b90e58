/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-06 10:09:59
 * @LastEditTime: 2021-07-08 17:08:11
 * @LastEditors: jiawen.wang
 */
import { Controller } from 'egg';
import { IerrorMsg, HttpErr } from '../../model/common';

export default class AliUmid extends Controller {

    public async getAliUmidList() {
        const { ctx, logger } = this;
        const rule = {
            umid: { type: "string" },
        };
        try {
            // 校验
            ctx.validate(rule);
            const req = ctx.request.body
            const res = await ctx.service.werewolf.aliUmid.getAliUmidList(req);
            ctx.body = res;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    public async banUserAliUmid() {
        const { ctx, logger } = this;
        const rule = {
            umid: { type: "string" },
            reason: { type: "string" }
        };
        try {
            // 校验
            ctx.validate(rule);
            const req = ctx.request.body
            const res = await ctx.service.werewolf.aliUmid.banUserAliUmid(req);
            ctx.body = res
            ctx.status = HttpErr.Success;

        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
}

