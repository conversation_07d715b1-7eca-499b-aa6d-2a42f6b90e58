import { IerrorMsg, HttpErr } from '../../model/common';
import { ImarketCirculateReq, ImarketCirculateResp, ImarketResp, ImarketDetailsResp ,ImarketStateReq} from "../../model/wfMarket";
import BaseMegaController from './BaseMegaController';

export default class HiddenFundOutputContorller extends BaseMegaController {

    public async getHiddenFundOutputList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const resp = await ctx.service.werewolf.hiddenFundOutput.getHiddenFundOutputList(requestBody);
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
}