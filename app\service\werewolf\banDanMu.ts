/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-05 09:44:39
 * @LastEditTime: 2021-06-07 13:22:38
 * @LastEditors: jiawen.wang
 */

import { Service } from 'egg';
import { IrecordList } from '../../model/randomRecordCof';
import * as moment from 'moment';

export default class BanDanMuService extends Service {
    public async getBanDanMuList(selectType) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        try {
            //查询板子列表
            let sqlStart = `SELECT 
                            a.user_id,b.nickname,a.content,a.createtime 
                       FROM 
                            user_danmaku_log a 
                        LEFT JOIN 
                            tuser b 
                        ON 
                            a.user_id = b.no 
                        WHERE 
                            a.createtime > DATE_SUB(NOW(),INTERVAL 2 HOUR)`;
            let matchSql = " AND server_type > 20000 ";
            let sqlEnd = " ORDER BY  a.id DESC ;";
            let sql = sqlStart + `${selectType == 1 ? matchSql : ""}` + sqlEnd;
            const banDanMuList = await db.query(sql);
            return banDanMuList;
        } catch (error) {
            throw error;
        }
    }
}