# 使用多阶段构建
#
# ---- 1 Base Node ----
# FROM node:carbon AS base
# # 创建 app 目录
# WORKDIR /app

# ---- 2 Dependencies ----
# FROM node:carbon AS dependencies  
# WORKDIR /nodeapp/
# # 使用通配符复制 package.json 
# COPY package.json  ./
# # 安装在‘devDependencies’中包含的依赖
# RUN npm install --registry=https://registry.npm.taobao.org

# # ---- 3 Copy文件进行编译 ----
# FROM dependencies AS build  
# WORKDIR /nodeapp/
# COPY . ./
# # 如需对 react/vue/angular 打包，生成静态文件，使用：
# RUN npm run clean
# RUN npm run  ci


# --- 4 Release with Alpine ----
# 4.1 第一层 基础景象
FROM node:lts-alpine AS release  
ENV HOST 0.0.0.0
ENV NODE_ENV=production
# 4.2 第二层 设置时区
RUN sed -i "s/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g" /etc/apk/repositories
RUN apk --update add tzdata \
    && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone \
    && apk del tzdata
RUN echo "Asia/Shanghai" > /etc/timezone


# 4.3 第二层 拷贝package 
WORKDIR /nodeapp
COPY package.json /nodeapp/
RUN npm install --only=production --registry=https://registry.npmmirror.com
RUN pwd
RUN ls -all

# 拷贝已编译的文件
COPY  . /nodeapp/
RUN pwd
RUN ls -all
#RUN npm install --production --registry=https://registry.npm.taobao.org
# 5 执行默认
CMD ["npm","run","start"]