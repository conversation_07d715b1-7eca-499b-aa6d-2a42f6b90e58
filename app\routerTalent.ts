/*
 * @Description: 
 * @Company: sdbean
 * @Author: z<PERSON><PERSON>
 * @Date: 2021-03-30 17:23:36
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-09-07 14:31:52
 */
import { Router, Application } from "egg";
import { AccessRouteId, AccessRouteName } from "./model/accessRouteCof";
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

    const { controller, router } = app;

    router.post(`${API_VERSION}/werewolf/talent/getVideoList`, accCtr(AccessRouteId.wolf_aty_talent), controller.werewolf.talent.getVideoList)
    router.post(`${API_VERSION}/werewolf/talent/insertVideo`, accCtr(AccessRouteId.wolf_aty_talent), controller.werewolf.talent.insertVideo)
    router.post(`${API_VERSION}/werewolf/talent/updateVideo`, accCtr(AccessRouteId.wolf_aty_talent), controller.werewolf.talent.updateVideo)
    router.post(`${API_VERSION}/werewolf/talent/updateVideoDelsign`, accCtr(AccessRouteId.wolf_aty_talent), controller.werewolf.talent.updateVideoDelsign)
    router.post(`${API_VERSION}/werewolf/talent/updateVideoUrl`, accCtr(AccessRouteId.wolf_aty_talent), controller.werewolf.talent.updateVideoUrl)
    router.post(`${API_VERSION}/werewolf/talent/getSeasonList`, accCtr(AccessRouteId.wolf_aty_talent), controller.werewolf.talent.getSeasonList)
    router.post(`${API_VERSION}/werewolf/talent/updateVideoSeason`, accCtr(AccessRouteId.wolf_aty_talent), controller.werewolf.talent.updateVideoSeason)
    router.post(`${API_VERSION}/werewolf/talent/updateVideoSeasonDelsign`, accCtr(AccessRouteId.wolf_aty_talent), controller.werewolf.talent.updateVideoSeasonDelsign)
    router.post(`${API_VERSION}/werewolf/talent/updateVideoSeasonCurrent`, accCtr(AccessRouteId.wolf_aty_talent), controller.werewolf.talent.updateVideoSeasonCurrent)
    router.post(`${API_VERSION}/werewolf/talent/insertVideoSeason`, accCtr(AccessRouteId.wolf_aty_talent), controller.werewolf.talent.insertVideoSeason)
}

export default load
