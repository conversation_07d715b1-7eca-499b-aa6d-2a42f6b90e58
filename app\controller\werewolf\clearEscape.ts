/*
 * @Description: 清逃跑controller
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2019-10-29 10:12:06
 * @LastEditors: hammercui
 * @LastEditTime: 2020-03-07 21:35:34
 */

import { Controller } from 'egg';
import { IavFrameUserListRequest, IbroadcastListReq, IbroadcastBanReq, IbroadcastListResp, IclearEscapeReq, IescapeListReq } from '../../model/werewolf';
import { IerrorMsg, HttpErr } from '../../model/common';

export default class ClearEscapeController extends Controller {

    /**
     * @name: 查询逃跑列表
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async getEscapeList(){
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
            startTime: { type: 'string' },
            endTime: { type: 'string' },
        };
        try {
            ctx.validate(rule);
            const requestBody: IescapeListReq = ctx.request.body;
            const list =  await ctx.service.werewolf.clearEscape.getEscapeList(requestBody);
			ctx.body = list;
			ctx.status = HttpErr.Success;
        }catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async do(){
        const { ctx, logger, app } = this;
         // 校验规则
         const rule = {
            uid: { type: 'number', },
            game_no: { type: 'number' },
            user_no: { type: 'number' },
            index: { type: 'number' },
        };
        try {
            ctx.validate(rule);
            const requestBody: IclearEscapeReq = ctx.request.body;
            await ctx.service.werewolf.clearEscape.clearEscape(requestBody);
			ctx.body = requestBody;
			ctx.status = HttpErr.Success;
        }catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: e.message};
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
}
