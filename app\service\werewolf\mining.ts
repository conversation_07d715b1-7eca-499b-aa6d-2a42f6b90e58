/* eslint-disable @typescript-eslint/ban-ts-comment */
import BaseMegaService from './BaseMegaService'
import {
    IaddWeekAwardParams,
    IinsertFrameCrystalParams,
    IinsertRoleSparParams, IinsertRoleSparParamsConfig,
    IinsertSeasonParams,
    IupdateFrameCrystalParams,
    IupdateIllustrationParams,
    IupdateRoleSparParams,
    IupdateSeasonParams,
    IupdateWeekAwardParams
} from "../../model/mining";
import { isAvatarFrameCateId } from "../../util/utils";
import { OkPacket } from "../../../typings";

export default class MiningService extends BaseMegaService {
    /*查询框石列表*/
    public async getMiningList() {
        const { app, logger } = this

        try {
            const sqlStr = `SELECT \`id\`, \`name\`, \`pic\`, \`delsign\` FROM mining_frame ORDER BY id DESC;`
            return await this.selectList(sqlStr, [])
        } catch (error) {
            logger.error(error)
            throw error
        }
    }

    /*添加框石*/
    public async addMiningFrame(req: any) {
        const { app, logger } = this
        const db = app.mysql.get('werewolf')
        const conn = await db.beginTransaction()
        try {
            let sql = `INSERT INTO \`mining_frame\` (\`name\`,\`delsign\`) VALUES (?,?);`
            const result = await conn.query(sql, [req.name, req.delsign])
            await conn.commit() //提交事务
        } catch (error) {
            await conn.rollback()
            throw error
        }
    }

    /*编辑框石*/
    public async editMiningFrame(req: any) {
        const { app } = this
        const db = app.mysql.get('werewolf')
        const conn = await db.beginTransaction()
        try {
            let sql = 'UPDATE `mining_frame` SET `name` = ?, `pic` = ?, `delsign` = ? WHERE `id` = ?;'
            await conn.query(sql, [req.name, req.pic, req.delsign, req.id])

            let updateDropSql = 'UPDATE `mining_drop_v3` SET `delsign` = ? WHERE `type` = 6 AND `mining_frame_id` = ?;';
            await conn.query(updateDropSql, [req.delsign, req.id]);

            await conn.commit()

        } catch (error) {
            await conn.rollback()
            throw error
        }
    }

    /*获取奖池列表*/
    public async getAwardPoolList() {
        const { app, logger } = this
        try {
            const sqlStr = `SELECT \`id\`, \`name\`, \`remark\` FROM mining_frame_award_pool ORDER BY id DESC;`
            return await this.selectList(sqlStr, [])
        } catch (error) {
            logger.error(error)
            throw error
        }
    }

    /*添加奖池*/
    public async addAwardPool(req: any) {
        const { app, logger } = this
        const db = app.mysql.get('werewolf')
        const conn = await db.beginTransaction()
        try {
            let sql = `INSERT INTO \`mining_frame_award_pool\` (\`name\`, \`remark\`) VALUES (?, ?);`
            const result = await conn.query(sql, [req.name, req.remark])
            await conn.commit()
        } catch (error) {
            await conn.rollback()
            throw error
        }
    }

    /*编辑奖池*/
    public async editAwardPool(req: any) {
        const { app, logger } = this
        const db = app.mysql.get('werewolf')
        const conn = await db.beginTransaction()
        try {
            let sql = `UPDATE mining_frame_award_pool SET \`name\` = ?, \`remark\` = ? WHERE \`id\` = ?;`
            const result = await conn.query(sql, [req.name, req.remark, req.id])
            await conn.commit()
        } catch (error) {
            await conn.rollback()
            throw error
        }
    }

    /*获取奖品列表*/
    public async getAwardList() {
        const { app, logger } = this
        try {
            let sql = `SELECT
                idc.id item_dic_id,
                ic.\`name\` item_cate_name,
                ic.id item_cate_id,
                idc.pic item_dic_pic,
                idc.name name,
                idc.item_id item_id,
                idc.item_cate_id cate_id,
                ic.item_table item_table, 
                idc.\`level\`,
                mfav.id id,
                mfav.remark remark
                FROM
                mining_frame_award_v2 mfav
                LEFT JOIN (
                SELECT
                item_dic.*,
                af.\`level\` level 
                FROM
                item_dic
                LEFT JOIN avatarframe af ON item_dic.item_id = af.id 
                AND item_dic.item_cate_id IN ( 2010, 2020 ) 
                ) idc ON mfav.item_dic_id = idc.id
                LEFT JOIN item_cate ic ON idc.item_cate_id = ic.id
                WHERE
                mfav.delsign = 0
                ORDER BY mfav.id DESC
                `
            return await this.selectList(sql, [])
        } catch (error) {
            logger.error(error)
            throw error
        }
    }

    /*添加奖品*/
    public async addAward(req: any) {
        const { app, logger } = this
        const db = app.mysql.get('werewolf')
        const conn = await db.beginTransaction()
        try {
            let sql = `INSERT INTO \`mining_frame_award_v2\` (\`item_dic_id\`, \`type\`, \`max\`, \`remark\`) VALUES (?, ?, ?, ?);`
            const result = await conn.query(sql, [req.item_dic_id, req.type, req.max, req.remark])
            await conn.commit()
        } catch (error) {
            await conn.rollback()
            throw error
        }
    }
    public async editAward(req: any) {
        const { app, logger } = this
        const db = app.mysql.get('werewolf')
        const conn = await db.beginTransaction()
        try {
            let sql = `UPDATE 
                mining_frame_award_v2 
            SET 
                item_dic_id = ?,
                type = ?,
                max = ?,
                remark=?
            WHERE 
                id = ?;`
            // let sql = `INSERT INTO \`mining_frame_award_v2\` (\`item_dic_id\`, \`type\`, \`max\`, \`remark\`) VALUES (?, ?, ?, ?);`
            const result = await conn.query(sql, [req.item_dic_id, req.type, req.max, req.remark, req.id])
            await conn.commit()
        } catch (error) {
            await conn.rollback()
            throw error
        }
    }
    /*下架奖品列表奖品*/
    public async updateAward(req: any) {
        const { app, logger } = this
        const db = app.mysql.get('werewolf')
        const conn = await db.beginTransaction()
        try {
            let sql = `UPDATE \`mining_frame_award_v2\` SET \`delsign\` = 1 WHERE \`item_dic_id\` = ?;`
            const result = await conn.query(sql, [req.item_dic_id])
            await conn.commit()
        } catch (error) {
            await conn.rollback()
            throw error
        }
    }
    /*上下架奖品*/
    public async updateAwardDelsign(req: any) {
        const { app, logger } = this
        const db = app.mysql.get('werewolf')
        const conn = await db.beginTransaction()
        try {
            let sql = `UPDATE \`mining_frame_pool_award_relation\` SET \`delsign\` = ? WHERE \`pool_id\` = ? AND \`id\` = ?;`
            await conn.query(sql, [req.delsign, req.pool_id, req.award_id]) //注：此处传入award_id，判断用id，因为客户端获取的时候返回的award_id为id
            await conn.commit()
        } catch (error) {
            await conn.rollback()
            throw error
        }
    }
    /*为奖池添加奖品*/
    public async addAwardInPool(req: any) {
        const { app, logger } = this
        const db = app.mysql.get('werewolf')
        const conn = await db.beginTransaction()
        try {
            // let sql = `INSERT INTO \`mining_frame_pool_award_relation\` (\`pool_id\`, \`award_id\`, \`weight\`, \`stock_num\`, \`percent\`, \`num\`, \`sort\`, \`delsign\`, \`remark\`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?);`
            let delsign = req.delsign == null ? 0 : req.delsign
            let sql = `INSERT INTO \`mining_frame_pool_award_relation\` (
                    \`pool_id\`,
                    \`award_id\`,
                    \`weight\`,
                    \`stock_num\`,
                    \`percent\`,
                    \`num\`,
                    \`sort\`,
                    \`delsign\`,
                    \`remark\` 
                    )
                    VALUES
                    (
                    ?,
                    ?,
                    ?,
                    (
                    SELECT
                    mfav2.max 
                    FROM
                    mining_frame_award_v2 mfav2 
                    WHERE
                    mfav2.id = ? 
                    ),
                    ?,
                    ?,
                    ?,
                    ?,
                    ? 
                    );`
            await conn.query(sql, [req.pool_id, req.award_id, req.weight, req.award_id, req.percent, req.num, req.sort, delsign, req.remark])
            await conn.commit()
        } catch (error) {
            await conn.rollback()
            throw error
        }
    }
    /*获取奖池内奖品*/
    public async getAwardInPool(req: any) {
        const { app, logger } = this
        try {
            const sqlStr = `SELECT
                mfpar.id id,
                idc.\`name\` NAME,
                mfpar.sort sort,
                ic.NAME type_name,
                idc.pic pic,
                idc.\`level\` LEVEL,
                mfpar.num num,
                mfpar.weight weight,
                mfpar.delsign delsign,
                idc.item_id,
                idc.item_cate_id cate_id,
                ic.item_table item_table
                FROM
                mining_frame_pool_award_relation mfpar
                LEFT JOIN mining_frame_award_v2 mfav2 ON mfpar.award_id = mfav2.id
                LEFT JOIN (
                SELECT
                item_dic.*,
                af.\`level\` LEVEL 
                FROM
                item_dic
                LEFT JOIN avatarframe af ON item_dic.item_id = af.id 
                AND item_dic.item_cate_id IN ( 2010, 2020 ) 
                ) idc ON mfav2.item_dic_id = idc.id
                LEFT JOIN item_cate ic ON idc.item_cate_id = ic.id 
                WHERE
                mfpar.pool_id = ? 
                ORDER BY
                mfpar.id ASC`
            return await this.selectList(sqlStr, [req.pool_id])
        } catch (error) {
            logger.error(error)
            throw error
        }
    }
    /*获取奖池列表*/
    public async getAwardPool(req: any) {
        const { app, logger } = this
        try {
            const sqlStr = `SELECT
                mfpr.mining_frame_id,
                mfpr.LEVEL mining_frame_level,
                mfpr.probability mining_frame_probability,
                mfpr.sort pool_sort,
                mfap.id pool_id,
                mfap.NAME pool_name,
                mfpar.weight award_weight,
                mfpar.sort award_sort,
                mfpar.delsign delsitn 
                FROM
                mining_frame_pool_award_relation mfpar
                LEFT JOIN mining_frame_pool_relation mfpr ON mfpr.mining_frame_id = ?
                AND mfpr.delsign = 0
                LEFT JOIN mining_frame_award_pool mfap ON mfpar.pool_id = mfap.id 
                WHERE
                mfpar.pool_id = mfpr.pool_id;`

            return await this.selectList(sqlStr, [req.mining_frame_id])
        } catch (error) {
            logger.error(error)
            throw error
        }
    }

    /*编辑奖池中的奖品*/
    public async editAwardInPool(req: any) {
        const { app, logger } = this
        const db = app.mysql.get('werewolf')
        const conn = await db.beginTransaction()
        try {
            let sql = `UPDATE mining_frame_pool_award_relation 
                SET weight = ?,
                num = ?,
                sort = ?,
                delsign = ?,
                remark = ? 
                WHERE
                pool_id = ? 
                AND id = ?;`
            await conn.query(sql, [req.weight, req.num, req.sort, req.delsign, req.remark, req.pool_id, req.award_id]) //注：此处传入award_id，判断用id，因为客户端获取的时候返回的award_id为id
            await conn.commit()
        } catch (error) {
            await conn.rollback()
            throw error
        }
    }
    // /*编辑框石*/
    // public async editMiningAwardPool() {
    //
    // }

    /*获取框石的奖池列表*/
    public async getMiningAwardPool(req: any) {
        const { app, logger } = this
        try {
            let sql = `SELECT
                mfpr.id relation_id,
                mfpr.mining_frame_id,
                mfpr.level level,
                mfap.id pool_id,
                mfap.name name,
                mfpr.sort sort,
                mfpr.probability probability,
                mfpr.delsign,
                mfpr.remark remark 
                FROM
                mining_frame_pool_relation mfpr
                LEFT JOIN mining_frame_award_pool mfap ON mfpr.pool_id = mfap.id 
                WHERE
                mfpr.mining_frame_id = ?;`
            return await this.selectList(sql, [req.mining_frame_id])
        } catch (error) {
            logger.error(error)
            throw error
        }
    }

    /*为框石添加奖池*/
    public async addAwardPoolToMining(req: any) {
        const { app, logger } = this
        const db = app.mysql.get('werewolf')
        const conn = await db.beginTransaction()
        try {
            let sql = `INSERT INTO mining_frame_pool_relation (
                \`mining_frame_id\`,
                \`pool_id\`,
                \`level\`,
                \`probability\`,
                \`sort\`,
                \`delsign\`,
                \`remark\` 
                )
                VALUES
                (
                ?,
                ?,
                ?,
                ?,
                ?,
                ?,
                ? 
                );`
            const result = await conn.query(sql, [req.mining_frame_id, req.pool_id, req.level, req.probability, req.sort, req.delsign, req.remark])
            await conn.commit()
        } catch (error) {
            await conn.rollback()
            throw error
        }
    }

    /*编辑框石的奖池*/
    public async editAwardPoolToMining(req: any) {
        const { app, logger } = this
        const db = app.mysql.get('werewolf')
        const conn = await db.beginTransaction()
        try {
            let sql = `UPDATE mining_frame_pool_relation 
                SET \`probability\` = ?,
                \`level\` = ?,
                \`sort\` = ?,
                \`delsign\` = ?,
                \`pool_id\` = ?,
                \`remark\` = ?
                WHERE
                \`mining_frame_id\` = ? 
                AND \`id\` = ?;`
            const result = await conn.query(sql, [
                req.probability,
                req.level,
                req.sort,
                req.delsign,
                req.pool_id,
                req.remark,
                req.mining_frame_id,
                req.relation_id,
            ])
            await conn.commit()
        } catch (error) {
            await conn.rollback()
            throw error
        }
    }

    /*获取框石掉率列表*/
    public async selectMiningDropList() {
        const { app, logger } = this;
        try {
            let sql = `SELECT
                mdv2.*,
                mf.\`name\` 
                FROM
                mining_drop_v3 mdv2
                LEFT JOIN mining_frame mf ON mdv2.mining_frame_id = mf.id 
                WHERE
                mdv2.type = 6 
                AND mdv2.delsign = 0 
                ORDER BY
                mdv2.ca_start,
                mdv2.ca_start DESC`;
            return await this.selectList(sql, []);
        } catch (error) {
            logger.error(error)
            throw error
        }
    }



    /*添加颜色区间框石掉率*/
    public async addMiningColorTotalSection(req: any) {
        const { app, logger } = this;

        if (req.weight <= 0) {
            return
        }
        const db = app.mysql.get('werewolf');
        const conn = await db.beginTransaction()
        try {
            let sql = `INSERT INTO mining_drop_v3 (
                \`type\`,
                \`weight\`,
                \`mining_frame_id\`,
                \`ca_start\`,
                \`ca_end\`,
                \`delsign\` 
                )
                VALUES
                (
                6,
                ?,
                ?,
                ?,
                ?,
                0 
                );`;
            const result = await conn.query(sql, [req.weight, req.mining_frame_id, req.ca_start, req.ca_end])
            await conn.commit()
        } catch (error) {
            await conn.rollback()
            throw error
        }
    }

    /*删除颜色区间块*/
    public async deleteMiningColorSection(req: any) {
        const { app, logger } = this;
        const db = app.mysql.get('werewolf');
        const conn = await db.beginTransaction();
        try {
            let sql = `DELETE FROM \`mining_drop_v3\` WHERE \`ca_start\` = ? AND \`ca_end\` = ? AND \`type\` = 6;`;
            const result = await conn.query(sql, [req.ca_start, req.ca_end]);
            await conn.commit();
        } catch (error) {
            await conn.rollback();
            throw error;
        }

    }

    /*编辑颜色区间内容*/
    public async editMiningColorSection(req: any) {
        const { app, logger } = this;
        const db = app.mysql.get('werewolf');
        const conn = await db.beginTransaction();
        try {
            let mining_frame_dic: any = [];
            let sql = `UPDATE mining_drop_v3 SET ca_start = ?, ca_end = ?, weight = CASE mining_frame_id `;
            const param = [req.ca_start_modif, req.ca_end_modif];
            for (const miningItem of req.dropList) {

                if (miningItem.weight > 0) {
                    let appendSql = `WHEN ? THEN ?`;
                    sql = `${sql} ${appendSql}`;

                    param.push(miningItem.mining_frame_id);
                    param.push(miningItem.weight);
                }

            }

            let trailSql = ` END WHERE ca_start = ? AND ca_end = ? AND type = 6;`
            sql = `${sql} ${trailSql}`;
            param.push(req.ca_start);
            param.push(req.ca_end);

            const result = await conn.query(sql, param);

            await conn.commit();
        } catch (error) {
            await conn.rollback();
            throw error;
        }
    }


    /*编辑框石权重掉率*/
    public async editMiningDropItem(req: any) {
        const { app, logger } = this;
        const db = app.mysql.get('werewolf');
        const conn = await db.beginTransaction();
        try {
            let sql = `UPDATE mining_drop_v3 SET weight = ?, ca_start = ?, ca_end = ? WHERE type = 6 AND mining_frame_id = ? AND ca_start = ? AND ca_end = ?`;
            const result = await conn.query(sql, [req.weight, req.ca_start_modif, req.ca_end_modif, req.mining_frame_id, req.ca_start, req.ca_end]);
            await conn.commit();
        } catch (error) {
            await conn.rollback();
            throw error;
        }
    }


    /*删除框石权重掉率*/
    public async deleteMiningDropItem(req: any) {
        const { app, logger } = this;
        const db = app.mysql.get('werewolf');
        const conn = await db.beginTransaction();
        try {
            let sql = `DELETE FROM mining_drop_v3 WHERE type = 6 AND mining_frame_id = ? AND ca_start = ? AND ca_end = ?`;
            const result = await conn.query(sql, [req.mining_frame_id, req.ca_start, req.ca_end]);
            await conn.commit();
        } catch (error) {
            await conn.rollback();
            throw error;
        }
    }


    public async weekAwardList(params: null) {
        const { app, logger } = this

        try {

            const sqlStr = `SELECT a.id,
                                   a.item_dic_id,
                                   a.num,
                                   a.max,
                                   a.stock_num,
                                   a.rank,
                                   a.mining_frame_level,
                                   a.isFrame,
                                   a.sort,
                                   a.delsign,
                                   a.remark,
                                   b.\`name\` AS item_dic_name,
                                   b.item_cate_id
                            FROM mining_week_award_view a
                                     LEFT JOIN item_dic b ON a.item_dic_id = b.id
                            ORDER BY a.sort ASC`;
            return await this.selectList(sqlStr, [])
        } catch (error) {
            logger.error(error)
            throw error
        }
    }

    public async addWeekAward(params: IaddWeekAwardParams) {
        const { app, logger } = this

        try {

            const isFrame = isAvatarFrameCateId(params.item_cate_id)

            const sqlStr = `INSERT INTO mining_week_award_view (item_dic_id, num, max, stock_num, rank,
                                                                mining_frame_level, isFrame, sort, delsign, remark)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;
            return await this.execSql(sqlStr, [params.item_dic_id, params.num, params.max, params.max, params.rank, params.mining_frame_level, isFrame, params.sort, params.delsign, params.remark])
        } catch (error) {
            logger.error(error)
            throw error
        }
    }

    public async updateWeekAward(params: IupdateWeekAwardParams) {
        const { app, logger } = this

        const isFrame = isAvatarFrameCateId(params.item_cate_id)

        try {

            const sqlStr = `UPDATE mining_week_award_view
                            SET item_dic_id=?,
                                num=?,
                                max=?,
                                stock_num=?,
                                rank=?,
                                mining_frame_level=?,
                                isFrame=?,
                                sort=?,
                                delsign=?,
                                remark=?
                            WHERE id = ?`;
            return await this.execSql(sqlStr, [params.item_dic_id, params.num, params.max, params.max, params.rank, params.mining_frame_level, isFrame, params.sort, params.delsign, params.remark, params.id])
        } catch (error) {
            logger.error(error)
            throw error
        }
    }

    // 框石结晶

    public async frameCrystalList() {
        const { app, logger } = this

        try {

            const sqlStr = `SELECT a.*,
                                   b.name   AS season_name,
                                   c.remark AS item_dic_remark,
                                   c.id     AS item_dic_id,
                                   d.id     AS illustrated_guide_id,
                                   d.level  AS illustrated_level,
                                   d.s_no   AS illustrated_s_no,
                                   d.name   AS illustrated_name,
                                   d.desc   AS illustrated_desc,
                                   d.source AS illustrated_source,
                                   d.img    AS illustrated_img
                            FROM mining_frame_crystal a
                                     LEFT JOIN mining_season b ON a.season_id = b.id
                                     LEFT JOIN item_dic c ON a.id = c.item_id AND c.item_cate_id = 19000
                                     LEFT JOIN mining_illustrated_guide d ON c.illustrated_guide_id = d.id
                            ORDER BY a.id DESC
            `;
            return await this.execSql(sqlStr, [])
        } catch (error) {
            logger.error(error)
            throw error
        }
    }

    public async addFrameCrystal(params: IinsertFrameCrystalParams) {
        const { app, logger } = this

        const client = app.mysql.get('werewolf')
        const ts = await client.beginTransaction()

        try {

            let sqlStr = `INSERT INTO mining_frame_crystal (\`name\`, pic, season_id, hole_exp, delsign)
                          VALUES (?, ?, ?, ?, ?)`;
            let res: OkPacket = await ts.query(sqlStr, [params.name, params.pic, params.season_id, params.hole_exp, params.delsign]);

            sqlStr = `INSERT INTO item_dic (item_cate_id, item_id, \`name\`, remark, illustrated_guide_id)
                      VALUES (?, ?, ?, ?, ?)`;
            res = await ts.query(sqlStr, [19000, res.insertId, params.name, params.item_dic_remark, params.illustrated_guide_id]);

            await ts.commit()
            return res
        } catch (error) {
            await ts.rollback()
            logger.error(error)
            throw error
        }
    }

    public async updateFrameCrystal(params: IupdateFrameCrystalParams) {
        const { app, logger } = this

        const client = app.mysql.get('werewolf')
        const ts = await client.beginTransaction()

        try {
            let sqlStr = `UPDATE mining_frame_crystal
                          SET \`name\`=?,
                              pic=?,
                              season_id=?,
                              hole_exp=?,
                              delsign=?
                          WHERE id = ?`;

            let res = await ts.query(sqlStr, [params.name, params.pic, params.season_id, params.hole_exp, params.delsign, params.id])

            sqlStr = `UPDATE item_dic
                      SET remark=?,
                          illustrated_guide_id=?
                      WHERE id = ?`;

            res = await ts.query(sqlStr, [params.item_dic_remark, params.illustrated_guide_id, params.item_dic_id])

            await ts.commit()

            return res

        } catch (error) {
            await ts.rollback
            logger.error(error)
            throw error
        }
    }

    // 角色结晶

    public async roleSparList() {
        const { app, logger } = this

        try {

            const sqlStr = `SELECT a.*,
                                   b.name   AS season_name,
                                   c.remark AS item_dic_remark,
                                   c.id     AS item_dic_id,
                                   d.id     AS illustrated_guide_id,
                                   d.level  AS illustrated_level,
                                   d.s_no   AS illustrated_s_no,
                                   d.name   AS illustrated_name,
                                   d.desc   AS illustrated_desc,
                                   d.source AS illustrated_source,
                                   d.img    AS illustrated_img
                            FROM mining_role_spar a
                                     LEFT JOIN mining_season b ON a.season_id = b.id
                                     LEFT JOIN item_dic c ON a.id = c.item_id AND c.item_cate_id = 20000
                                     LEFT JOIN mining_illustrated_guide d ON c.illustrated_guide_id = d.id
                            ORDER BY a.id DESC

            `;
            let res = await this.execSql(sqlStr, [])

            if (res && res.length > 0) {
                const sql = `SELECT * FROM mining_role_spar_config WHERE delsign != 1 OR delsign is NULL`;
                const resultArr: IinsertRoleSparParamsConfig[] = await this.execSql(sql, [])
                const map = {}
                resultArr.map((item) => {
                    let arr = map[item.role_spar_id] ?? []
                    arr.push(item)
                    map[item.role_spar_id] = arr
                })

                logger.info('res:', map)
                res = res.map((item) => ({ ...item, configs: map[String(item.id)] }))
            }

            return res
        } catch (error) {

            logger.error(error)
            throw error
        }
    }

    public async addRoleSpar(params: IinsertRoleSparParams) {
        const { app, logger } = this

        const client = app.mysql.get('werewolf')
        const ts = await client.beginTransaction()

        try {

            let sqlStr = `INSERT INTO mining_role_spar (\`name\`, pic, season_id,
                                                          hole_exp, \`level\`, delsign)
                            VALUES (?, ?, ?, ?, ?, ?)`;
            let insertRes: OkPacket = await ts.query(sqlStr, [
                params.name,
                params.pic,
                params.season_id,
                params.hole_exp,
                params.level,
                params.delsign
            ])


            for (const config of params.configs) {
                sqlStr = `INSERT INTO mining_role_spar_config (role_spar_id, min_hole_level, max_hole_level, weight)
                          VALUES (?, ?, ?, ?)`
                const configRes = await ts.query(sqlStr, [insertRes.insertId, config.min_hole_level, config.max_hole_level, config.weight]);
            }


            sqlStr = `INSERT INTO item_dic (item_cate_id, item_id, name, remark, illustrated_guide_id)
                      VALUES (?, ?, ?, ?, ?)`;
            const res = await ts.query(sqlStr, [20000, insertRes.insertId, params.name, params.item_dic_remark, params.illustrated_guide_id]);
            await ts.commit()

            return res

        } catch (error) {
            await ts.rollback()
            logger.error(error)
            throw error
        }
    }

    public async updateRoleSpar(params: IupdateRoleSparParams) {
        const { app, logger } = this

        const client = app.mysql.get('werewolf')
        const ts = await client.beginTransaction()

        try {

            let sqlStr = `UPDATE mining_role_spar
                            SET \`name\`=?,
                                pic=?,
                                season_id=?,
                                hole_exp=?,
                                \`level\`=?,
                                delsign=?
                          WHERE id = ?
            `;

            let res: OkPacket = await ts.query(sqlStr, [
                params.name,
                params.pic,
                params.season_id,
                params.hole_exp,
                params.level,
                params.delsign,
                params.id
            ]);

            if (params.configs && params.configs.length > 0) {

                const sql = `SELECT * FROM mining_role_spar_config WHERE role_spar_id = ?`;
                const exitsArr: IinsertRoleSparParamsConfig[] = await ts.query(sql, [params.id])
                const idMap: { [key: string]: IinsertRoleSparParamsConfig } = {}
                exitsArr.map((item) => {
                    idMap[item.id] = item
                })

                // 更新或新增
                for (const config of params.configs) {
                    if (config.id) {

                        if (idMap[config.id]) {
                            // 筛选出原来存在但是，编辑后被移出的
                            delete idMap[config.id]
                        }


                        sqlStr = `UPDATE mining_role_spar_config
                                  SET min_hole_level=?,
                                      max_hole_level=?,
                                      weight=?,
                                      delsign=?
                                  WHERE id = ?`
                        const updateRes = await ts.query(sqlStr, [config.min_hole_level, config.max_hole_level, config.weight, config.delsign, config.id]);
                    } else {
                        sqlStr = `INSERT INTO mining_role_spar_config (role_spar_id, min_hole_level, max_hole_level, weight)
                                  VALUES (?, ?, ?, ?)`
                        const insertRes = await ts.query(sqlStr, [params.id, config.min_hole_level, config.max_hole_level, config.weight]);
                    }

                }

                // 把被删除的delsign设置1
                // @ts-ignore
                const toDeleteIds = Object.values(idMap).map((item) => item.id)
                if (toDeleteIds.length > 0) {

                    sqlStr = `UPDATE mining_role_spar_config
                              SET delsign=1
                              WHERE id in (?)`
                    const deleteRes = await ts.query(sqlStr, [toDeleteIds]);
                }


            }





            sqlStr = `UPDATE item_dic
                      SET remark=?,
                          illustrated_guide_id=?
                      WHERE id = ?`;

            res = await ts.query(sqlStr, [params.item_dic_remark, params.illustrated_guide_id, params.item_dic_id])

            await ts.commit()

            return res

        } catch (error) {
            await ts.rollback()
            logger.error(error)
            throw error
        }
    }

    public async seasonList(params) {
        const { app, logger } = this

        try {
            const sql = `SELECT id,
                                \`name\`,
                                delsign,
                                DATE_FORMAT(start_time, '%Y-%m-%d %H:%i:%s') start_time,
                                DATE_FORMAT(end_time, '%Y-%m-%d %H:%i:%s')   end_time
                         FROM mining_season
                         WHERE delsign = 0`;
            const res = await this.execSql(sql, [])
            return res
        } catch (e) {
            logger.error(e)
            throw e
        }
    }

    public async insertSeason(params: IinsertSeasonParams) {
        const { app, logger } = this

        try {
            const sql = `INSERT INTO mining_season (\`name\`, delsign, start_time, end_time)
                         VALUES (?, ?, ?, ?)`;
            const res = await this.execSql(sql, [params.name, params.delsign, params.start_time, params.end_time])
            return res
        } catch (e) {
            logger.error(e)
            throw e
        }
    }


    public async updateSeason(params: IupdateSeasonParams) {
        const { app, logger } = this

        try {
            const sql = `UPDATE mining_season
                         SET \`name\`=?,
                             delsign=?,
                             start_time=?,
                             end_time=?
                         WHERE id = ?`;
            const res = await this.execSql(sql, [params.name, params.delsign, params.start_time, params.end_time, params.id])
            return res
        } catch (e) {
            logger.error(e)
            throw e
        }
    }

    public async illustratedList(params) {
        const { app, logger } = this

        try {
            const sql = `SELECT * FROM mining_illustrated_guide WHERE delsign = 0`;
            const res = await this.execSql(sql, [])
            return res
        } catch (e) {
            logger.error(e)
            throw e
        }
    }

    public async updateIllustration(params: IupdateIllustrationParams) {
        const { app, logger } = this
        try {
            const sql = `UPDATE mining_illustrated_guide
                         SET \`level\`=?,
                             s_no=?,
                             \`name\`=?,
                             \`desc\`=?,
                             \`source\`=?,
                             img=?
                         WHERE id = ?`;
            const res = await this.execSql(sql, [params.level, params.s_no, params.name, params.desc, params.source, params.img, params.id])
            return res
        } catch (e) {
            logger.error(e)
            throw e
        }
    }

}
