/*
 * @Description: 新手辅助
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: wb
 * @Date: 2020-04-24 11:35:39
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-07-21 10:54:23
 */
import BaseMegaService from './BaseMegaService';
import {
    MstAddImageSearchParams,
    MstAddImageSearchLikeNameParams,
    MstAddImageInsertInfoParams,
    // MstAddImageUpdateInfoParams,
} from "../../model/mstAddImageDto";

const chatgptDB = "chatgpt";
const stable_diffusion_model_dic = "stable_diffusion_model_dic";
const stable_diffusion_model_dic_samples = "stable_diffusion_model_dic_samples";
const stable_diffusion_draw_task = "stable_diffusion_draw_task";
const stable_diffusion_system_dic = "stable_diffusion_system_dic";
const table_diffusion_draw_task_lora = "table_diffusion_draw_task_lora";
const stable_diffusion_draw_task_image = "stable_diffusion_draw_task_image";


export default class IMstAddImagetService extends BaseMegaService {

    public async searchFromStableList(req: MstAddImageSearchParams) {
        const { app, ctx, logger } = this;
        try {

            let sql = `
            SELECT md.model_id,md.model_type_id,md.id,md.model_name,ds.image_url,ds.task_id
            FROM ${stable_diffusion_model_dic} md, ${stable_diffusion_model_dic_samples} ds
            WHERE md.model_id = "${req.model_id.replace(/^\s+|\s+$/g,"")}" AND ds.model_id = md.model_id; 
            `;
            // SELECT * FROM stable_diffusion_system_dic WHERE type = 4

            const chatgpt = app.mysql.get(chatgptDB);
            const chatgptConn = await chatgpt.beginTransaction();
            let list = await chatgptConn.query(sql, []);

            for (const item of list) {

                if(item.task_id != null && item.task_id != ""){
                    let taskSql = `
                    SELECT dt.*
                    FROM ${stable_diffusion_draw_task} dt
                    WHERE dt.task_id = "${item.task_id}"; 
                    `;
                    
                    
                    let taskList = await chatgptConn.query(taskSql, []);
                    if(taskList.length > 0){


                        let sampling_name = '';
                        if(taskList[0].sampling_id != null && taskList[0].sampling_id != ""){
                            let sam_sql = `
                            SELECT * FROM ${stable_diffusion_system_dic} sd WHERE sd.type = 4 AND sd.system_dic_id = "${taskList[0].sampling_id}";
                            `
                            let samList = await chatgptConn.query(sam_sql, []);

                            if(samList != null && samList.length > 0){
                                sampling_name = samList[0].dic_name;
                            }
                            
                        }

                        let vae_model_name = '';
                        if(taskList[0].vae_model_id != null && taskList[0].vae_model_id != ""){
                            let vae_sql = `
                            SELECT * FROM ${stable_diffusion_model_dic} md WHERE md.model_type_id = 4 AND md.model_id = "${taskList[0].vae_model_id}";
                            `
                            let vaeList = await chatgptConn.query(vae_sql, []);

                            if(vaeList != null && vaeList.length > 0){
                                vae_model_name = vaeList[0].model_name;
                            }
                            
                        }

                        if(item.task_id != null){


                            

                            //查询大模型ID、名称
                            let stable_diffusion_draw_task_big_sql = `
                            SELECT * FROM stable_diffusion_draw_task tl WHERE tl.task_id = "${item.task_id}";
                            `;
                        
                            let stable_diffusion_draw_task_big_list = await chatgptConn.query(stable_diffusion_draw_task_big_sql, []);
                            if(stable_diffusion_draw_task_big_list != null && stable_diffusion_draw_task_big_list.length > 0 && stable_diffusion_draw_task_big_list[0].task_model_id != null && stable_diffusion_draw_task_big_list[0].task_model_id != ""){
                                item.task_model_id = stable_diffusion_draw_task_big_list[0].task_model_id;

                                let stable_diffusion_model_dic_sql = `
                                SELECT * FROM stable_diffusion_model_dic WHERE model_id = '${stable_diffusion_draw_task_big_list[0].task_model_id}' AND (model_type_id = 1 || model_type_id = 2);
                                `;
                                let stable_diffusion_model_dic_List= await chatgptConn.query(stable_diffusion_model_dic_sql, []);

                                logger.error(JSON.stringify(stable_diffusion_model_dic_List));

                                if(stable_diffusion_model_dic_List != null && stable_diffusion_model_dic_List.length > 0 && stable_diffusion_model_dic_List[0].model_name != null && stable_diffusion_model_dic_List[0].model_name != ""){
                                    item.task_model_name = stable_diffusion_model_dic_List[0].model_name;
                                }

                            }


                            //查询小模型ID、名称
                            let task_lora_sql = `
                                SELECT lora_model_id FROM stable_diffusion_draw_task_lora tl WHERE tl.task_id = "${item.task_id}";
                            `;

                            let taskLoraList = await chatgptConn.query(task_lora_sql, []);


                            if(taskLoraList != null && taskLoraList.length > 0 && taskLoraList[0].lora_model_id != null && taskLoraList[0].lora_model_id != ""){
                                // item.lora_model_id = taskLoraList[0].lora_model_id;
                                item.lora_model_id = JSON.stringify(taskLoraList);


                                // let lora_model_name = "";
                                const lora_model_names = [""];

                                for (const taskLoraItem of taskLoraList) {
                                    let stable_diffusion_model_dic_lora_sql = `
                                    SELECT model_name FROM stable_diffusion_model_dic WHERE model_id = '${taskLoraItem.lora_model_id}' AND model_type_id = 3;
                                    `;
                                    let stable_diffusion_model_dic_lora_List= await chatgptConn.query(stable_diffusion_model_dic_lora_sql, []);
    
                                    if(stable_diffusion_model_dic_lora_List != null && stable_diffusion_model_dic_lora_List.length > 0 && stable_diffusion_model_dic_lora_List[0].model_name != null && stable_diffusion_model_dic_lora_List[0].model_name != ""){
                                        // item.lora_model_name = stable_diffusion_model_dic_lora_List[0].model_name;

                                        // let commaString = "";
                                        // if(lora_model_name != ""){
                                        //     commaString = ",";
                                        // }

                                        // lora_model_name = lora_model_name + commaString +stable_diffusion_model_dic_lora_List[0].model_name;
    
                                        lora_model_names.push(stable_diffusion_model_dic_lora_List[0].model_name);
                                    }
                                }

                                if(lora_model_names != null && lora_model_names.length > 1)
                                {
                                    lora_model_names.shift();
                                    item.lora_model_name = JSON.stringify(lora_model_names);
                                }

                            }
                        }


                        item.vae_model_id = taskList[0].vae_model_id;
                        item.vae_model_name = vae_model_name;
                        item.sampling_step = taskList[0].sampling_step;
                        item.sampling_name = sampling_name;
                        item.sampling_id = taskList[0].sampling_id;
                        item.clip_skip = taskList[0].clip_skip;
                        item.cfg_scale = taskList[0].cfg_scale;
                        item.seed = taskList[0].seed;
                        item.ensd = taskList[0].ensd;
                        item.width = taskList[0].width;
                        item.height = taskList[0].height;
                        item.origin_prompt = taskList[0].origin_prompt;
                        item.negative_prompt = taskList[0].negative_prompt;
                        item.create_time = taskList[0].create_time;

                    }
                }
            }

            await chatgptConn.commit();

             return list;

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getLikeModelName(req: MstAddImageSearchLikeNameParams) {
        const { app, ctx, logger } = this;
        try {
 
            let sql = `
            SELECT * FROM ${stable_diffusion_model_dic} WHERE model_name LIKE '%${req.model_name.replace(/^\s+|\s+$/g,"")}%' AND model_type_id = 3;
            `;
            if(req.is_big_name){
                sql = `
            SELECT * FROM ${stable_diffusion_model_dic} WHERE model_name LIKE '%${req.model_name.replace(/^\s+|\s+$/g,"")}%' AND (model_type_id = 1 || model_type_id = 2);
            `;
            }

             return this.selectList(sql,[],chatgptDB);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getVaeList() {
        const { app, ctx, logger } = this;
        try {
 
            let sql = `
            SELECT * FROM ${stable_diffusion_model_dic} WHERE model_type_id = 4;
            `;

             return this.selectList(sql,[],chatgptDB);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getSamplerList() {
        const { app, ctx, logger } = this;
        try {
 
            let sql = `
            SELECT * FROM ${stable_diffusion_system_dic} WHERE type = 4;
            `;

             return this.selectList(sql,[],chatgptDB);

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async insertMstImageInfo(req: MstAddImageInsertInfoParams) {
        const { app, ctx, logger } = this;
        try {
 

            const chatgpt = app.mysql.get(chatgptDB);
            const chatgptConn = await chatgpt.beginTransaction();

                // 1画图任务表  
                // stable_diffusion_draw_task
                // task_id  随机生成   
                // account_id  固定 46    
                // task_status  任务状态 2   
                // origin_prompt  描述
                // negative_prompt 排除描述   
                // sampling_id  采样器id
                // sampling_step  采样步数 
                // seed， clip_skip ， ensd ，cfg_scale
                // denoising_strength
                // width  ,  height
                // vae_model_id   vae模型Id
                // sd_use_lora  是不是 lora 画图任务——是否选小模型
                // task_model_id 大模型ID

                    let insert_draw_task_sql = `
                    INSERT INTO stable_diffusion_draw_task
                                (\`task_id\`,
                                \`origin_prompt\`,
                                \`translate_prompt\`,
                                \`task_model_id\`,
                                \`negative_prompt\`,
                                \`translate_negative_prompt\`,
                                \`vae_model_id\`,
                                \`sampling_id\`,
                                \`sampling_step\`,
                                \`seed\`,
                                \`clip_skip\`,
                                \`ensd\`,
                                \`cfg_scale\`,
                                \`width\`,
                                \`height\`,
                                \`sd_use_lora\`,
                                
                                \`denoising_strength\`,
                                \`user_channel\`,
                                \`task_type\`,
                                \`image_num\`,
                                \`draw_method\`,
                                \`model_is_public\`,
                                \`create_time\`,
                                \`complete_time\`,
                                \`queue_time\`,
                                \`sd_use_hires\`,
                                \`account_region\`,
                                \`delsign\`,
                                \`account_id\`,
                                \`task_status\`,
                                \`id\`,
                                \`wx_open_id\`,
                                \`message_id\`,
                                \`task_error_message\`,
                                \`pic_url\`,
                                \`media_id\`,
                                \`mark_read\`,
                                \`mj_draw_message_id\`,
                                \`mj_draw_message_hash\`,
                                \`mj_draw_channel_no\`,
                                \`mj_draw_task_mode\`,
                                \`mj_draw_select_index\`,
                                \`mj_extra_param\`,
                                \`sd_use_controlnet\`,
                                \`mask_url\`,
                                \`mask_invert\`,
                                \`mj_draw_seed_image_url\`,
                                \`mj_draw_seed_id\`,
                                \`target_width\`,
                                \`target_height\`,
                                \`variation_seed\`,
                                \`variation_denoising_strength\`,
                                \`hires_upscale\`,
                                \`hires_upscale_by\`,
                                \`hires_upscale_steps\`,
                                \`hires_upscale_ds\`
                                )
                    VALUES (REPLACE(UUID(), '-', ''),
                    '${req.origin_prompt.replace('\'', ' ')}',
                    '${req.origin_prompt.replace('\'', ' ')}',
                    '${req.bigModelId}',
                    '${req.negative_prompt.replace('\'', ' ')}',
                    '${req.negative_prompt.replace('\'', ' ')}',
                    '${req.vae_model_id}',
                    ${req.sampling_id},
                    ${req.sampling_step},
                    ${req.seed},
                    ${req.clip_skip},
                    ${req.ensd},
                    ${req.cfg_scale},
                    ${req.width},
                    ${req.height},
                    ${req.smallModelId!=''?'1':'0'},
                    
                    '0.750000',
                    '2',
                    '1',
                    '1',
                    '1',
                    '1',
                    '2023-06-19 13:48:39',
                    '2023-06-19 13:48:39',
                    '2023-06-19 13:48:39',
                    '0',
                    '0',
                    '0',
                    '943676',
                    '2',
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL
                    
                    );
                `;


                if(req.vae_model_id=='undefined' || req.vae_model_id=='' || req.vae_model_id==null){
                    insert_draw_task_sql = `
                    INSERT INTO stable_diffusion_draw_task
                                (\`task_id\`,
                                \`origin_prompt\`,
                                \`translate_prompt\`,
                                \`task_model_id\`,
                                \`negative_prompt\`,
                                \`translate_negative_prompt\`,
                                \`vae_model_id\`,
                                \`sampling_id\`,
                                \`sampling_step\`,
                                \`seed\`,
                                \`clip_skip\`,
                                \`ensd\`,
                                \`cfg_scale\`,
                                \`width\`,
                                \`height\`,
                                \`sd_use_lora\`,
                                
                                \`denoising_strength\`,
                                \`user_channel\`,
                                \`task_type\`,
                                \`image_num\`,
                                \`draw_method\`,
                                \`model_is_public\`,
                                \`create_time\`,
                                \`complete_time\`,
                                \`queue_time\`,
                                \`sd_use_hires\`,
                                \`account_region\`,
                                \`delsign\`,
                                \`account_id\`,
                                \`task_status\`,
                                \`id\`,
                                \`wx_open_id\`,
                                \`message_id\`,
                                \`task_error_message\`,
                                \`pic_url\`,
                                \`media_id\`,
                                \`mark_read\`,
                                \`mj_draw_message_id\`,
                                \`mj_draw_message_hash\`,
                                \`mj_draw_channel_no\`,
                                \`mj_draw_task_mode\`,
                                \`mj_draw_select_index\`,
                                \`mj_extra_param\`,
                                \`sd_use_controlnet\`,
                                \`mask_url\`,
                                \`mask_invert\`,
                                \`mj_draw_seed_image_url\`,
                                \`mj_draw_seed_id\`,
                                \`target_width\`,
                                \`target_height\`,
                                \`variation_seed\`,
                                \`variation_denoising_strength\`,
                                \`hires_upscale\`,
                                \`hires_upscale_by\`,
                                \`hires_upscale_steps\`,
                                \`hires_upscale_ds\`
                                )
                    VALUES (REPLACE(UUID(), '-', ''),
                    '${req.origin_prompt.replace('\'', ' ')}}',
                    '${req.origin_prompt.replace('\'', ' ')}}',
                    '${req.bigModelId}',
                    '${req.negative_prompt.replace('\'', ' ')}}',
                    '${req.negative_prompt.replace('\'', ' ')}}',
                     NULL,
                    ${req.sampling_id},
                    ${req.sampling_step},
                    ${req.seed},
                    ${req.clip_skip},
                    ${req.ensd},
                    ${req.cfg_scale},
                    ${req.width},
                    ${req.height},
                    ${req.smallModelId!=''?'1':'0'},
                     
                    '0.750000',
                    '2',
                    '1',
                    '1',
                    '1',
                    '1',
                    '2023-06-19 13:48:39',
                    '2023-06-19 13:48:39',
                    '2023-06-19 13:48:39',
                    '0',
                    '0',
                    '0',
                    '943676',
                    '2',
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL,
                    NULL
                    
                    );
                `;
                }

                logger.error("insert_draw_task_sql:\n"+insert_draw_task_sql);

                const draw_task_result = await chatgptConn.query(insert_draw_task_sql, []);
                let lastId = draw_task_result.insertId;

                let task_id_sql = `
                SELECT task_id FROM stable_diffusion_draw_task WHERE id = ${lastId};
                `;
                logger.error("task_id_sql:\n"+task_id_sql);

                let task_id_list = await chatgptConn.query(task_id_sql, []);
                let task_id = "";

                // 1.1是小模型插入这个表
                // 使用lora画图任务记录
                // table_diffusion_draw_task_lora
                // task_id task_id

                if(task_id_list != null && task_id_list.length > 0){
                    task_id = task_id_list[0].task_id;
                }

                if(req.smallModelId != ""){
                    let insert_draw_task_lora_sql = `
                    INSERT INTO stable_diffusion_draw_task_lora
                            (\`id\`,\`task_id\`,\`lora_model_id\`,\`lora_is_public\`,\`lora_weight\`)
                    VALUES (NULL,?,?,1,0.7);
                `;

                await chatgptConn.query(insert_draw_task_lora_sql, [task_id,req.smallModelId]);

                if(req.smallModelId2 != ""){
                    let insert_draw_task_lora_sql2 = `
                    INSERT INTO stable_diffusion_draw_task_lora
                            (\`id\`,\`task_id\`,\`lora_model_id\`,\`lora_is_public\`,\`lora_weight\`)
                    VALUES (NULL,?,?,1,0.7);
                    `;

                await chatgptConn.query(insert_draw_task_lora_sql2, [task_id,req.smallModelId2]);
                }
              }

                // 2画图任务结果信息记录表  （始终需要插）
                // stable_diffusion_draw_task_image  
                // id  画图id   （主键自动生成） 
                // task_id
                // oss_path_url  图片地址

                let insert_draw_task_Image_sql = `
                INSERT INTO stable_diffusion_draw_task_image 
                        (task_id,oss_path_url,result_code)
                VALUES (?,?,0);
                `;
                const task_image_result = await chatgptConn.query(insert_draw_task_Image_sql, [task_id,req.image_url]);
                let task_image_id = task_image_result.insertId;

                // 3
                // stable_diffusion_model_dic_samples
                // task_id ：stable_diffusion_draw_task的task_id
                // task_image_id: stable_diffusion_draw_task_image 的id

                let insert_model_dic_samples_sql = `
                UPDATE stable_diffusion_model_dic_samples 
                SET 
                task_id = '${task_id}',task_img_id = '${task_image_id}'
                WHERE model_id = "${req.inputModelId}" AND image_url = "${req.image_url}";
                `;

                logger.error("insert_model_dic_samples_sql:"+insert_model_dic_samples_sql);

                await chatgptConn.query(insert_model_dic_samples_sql, []);

                await chatgptConn.commit();

        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    // public async updateMstImageInfo(req: MstAddImageUpdateInfoParams) {
    //     const { app, ctx, logger } = this;
    //     try {
 
    //         const chatgpt = app.mysql.get(chatgptDB);
    //         const chatgptConn = await chatgpt.beginTransaction();

    //             // 1画图任务表  
    //             // stable_diffusion_draw_task
    //             // task_id  随机生成   
    //             // account_id  固定 0    
    //             // task_status  任务状态 2   
    //             // origin_prompt  描述
    //             // negative_prompt 排除描述   
    //             // sampling_id  采样器id
    //             // sampling_step  采样步数 
    //             // seed， clip_skip ， ensd ，cfg_scale
    //             // denoising_strength
    //             // width  ,  height
    //             // vae_model_id   vae模型Id
    //             // sd_use_lora  是不是 lora 画图任务——是否选小模型
    //             // task_model_id 大模型ID

    //                 let insert_draw_task_sql = `
    //                 INSERT INTO stable_diffusion_draw_task 
    //                             (account_id,
    //                             task_status,
    //                             origin_prompt,
    //                             negative_prompt,
    //                             sampling_id,
    //                             sampling_step,
    //                             seed,
    //                             clip_skip,
    //                             ensd,
    //                             cfg_scale,
    //                             denoising_strength,
    //                             width,
    //                             height,
    //                             vae_model_id,
    //                             sd_use_lora,
    //                             task_model_id)
    //                 VALUES (0, 2, ?, ?, ?, ?, ?, ?,?,?,?,?,?,?,?,?);
    //             `;
    //             const draw_task_result = await chatgptConn.query(insert_draw_task_sql, [req.origin_prompt,req.negative_prompt,
    //                 req.sampling_id,req.sampling_step,req.seed,req.clip_skip,req.ensd,req.cfg_scale,
    //                 req.denoising_strength,req.width,req.height,req.vae_model_id,req.sd_use_lora,req.task_model_id]);
    //             let lastId = draw_task_result.insertId;

    //             let task_id_sql = `
    //             SELECT task_id,model_type_id FROM stable_diffusion_system_dic WHERE id = ${lastId}
    //             `;
    //             let task_id_list = await chatgptConn.query(task_id_sql, []);
    //             let task_id = "";

    //             // 1.1是小模型插入这个表
    //             // 使用lora画图任务记录
    //             // table_diffusion_draw_task_lora
    //             // id task_id

    //             if(task_id_list != null && task_id_list.length > 0){

    //                 task_id = task_id_list[0].task_id;

    //                 if(task_id_list[0].model_type_id == 3){
    //                     let insert_draw_task_lora_sql = `
    //                     INSERT INTO table_diffusion_draw_task_lora 
    //                             (\`id\`)
    //                     VALUES (?);
    //                 `;
    
    //                 await chatgptConn.query(insert_draw_task_lora_sql, [task_id]);

    //               }
                
    //             }

    //             // 2画图任务结果信息记录表  （始终需要插）
    //             // stable_diffusion_draw_task_image  
    //             // id  画图id   （主键自动生成） 
    //             // task_id
    //             // oss_path_url  图片地址

    //             let insert_draw_task_Image_sql = `
    //             INSERT INTO stable_diffusion_draw_task_image 
    //                     (task_id,oss_path_url)
    //             VALUES (?,?);
    //             `;
    //             const task_image_result = await chatgptConn.query(insert_draw_task_Image_sql, [task_id,req.image_url]);
    //             let task_image_id = task_image_result.insertId;

    //             // 3
    //             // stable_diffusion_model_dic_samples
    //             // task_id ：stable_diffusion_draw_task的task_id
    //             // task_image_id: stable_diffusion_draw_task_image 的id

    //             let insert_model_dic_samples_sql = `
    //             INSERT INTO stable_diffusion_model_dic_samples 
    //                     (task_id,task_image_id)
    //             VALUES (?,?);
    //             `;
    //             await chatgptConn.query(insert_model_dic_samples_sql, [task_id,task_image_id]);

    //             await chatgptConn.commit();

    //     } catch (error) {
    //         logger.error(error);
    //         throw error
    //     }
    // }

}
