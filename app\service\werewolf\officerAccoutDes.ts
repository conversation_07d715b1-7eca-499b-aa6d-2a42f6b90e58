/*
 * @Author: your name
 * @Date: 2021-07-29 13:54:11
 * @LastEditTime: 2021-08-02 13:27:24
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /MGKFHTServer/app/service/werewolf/arenaActivity.ts
 */
import { Service } from 'egg';

 export default class OfficerAccoutDesService extends Service {

    public async getOfficerAccoutDes(req: any) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        const user_no = db.escape(req.user_id);
        try {


            let sql = `SELECT (SELECT IFNULL(SUM(coin_num),0) 
                           FROM item_produce_market_history WHERE from_user_id = 9761957 ) AS earnTotalNum,
                           (SELECT IFNULL(SUM(coin_num),0) FROM item_produce_market_history WHERE to_user_id = 9761957 ) 
                           AS costTotalNum,(SELECT accountNum FROM tuser_props WHERE userno = 9761957 ) 
                           AS accountnum`;
            let result = await db.query(sql);
            return result;

        } catch (error) {
            throw error;
        }
    }
 }
