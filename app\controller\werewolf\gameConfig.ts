/*
 * @Description: 游戏板子模块
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2019-10-10 17:42:19
 * @LastEditors: hammercui
 * @LastEditTime: 2020-09-02 11:25:42
 */
import { Controller } from 'egg';
import { <PERSON>errorMsg, HttpErr } from '../../model/common';
import { SendEmailsToUsersRequest, SearchNewUserListResponse, PushMessageResquest } from '../../model/werewolf';
import {
    IupdateGameConfOpenReq, IcreateGameConfOpenReq, IupdateGameConfOpenSortReq,
    ItboxSeasonOpenReq, ItboxUpdateEndTimeReq, IpropsConfigItem, IuserScoreReq, IavatarFramePeriod, IavatarFramePeriodv2, FrameRequestPeriod, FrameRequestPeriodSort
} from '../../model/werewolf2';
import BaseMegaController from './BaseMegaController';

export default class GameConfigController extends BaseMegaController {

    //获得open表板子列表
    public async getOpenList() {
        const { ctx, logger } = this;
        // 校验规则
        // const rule = {
        //     userList: { type: 'array' },
        //     title: { type: 'string' },
        //     content: { type: 'string' }
        // };
        try {
            // 校验
            // ctx.validate(rule);
            const requestBody: ItboxSeasonOpenReq = ctx.request.body;
            const resp = await ctx.service.werewolf.gameConfig.getOpenList(requestBody);
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    //获得open表板子列表
    public async getBoxList() {
        const { ctx, logger } = this;
        // 校验规则
        // const rule = {
        //     userList: { type: 'array' },
        //     title: { type: 'string' },
        //     content: { type: 'string' }
        // };
        try {
            // 校验
            // ctx.validate(rule);
            // const requestBody: ItboxSeasonOpenReq = ctx.request.body;
            const resp = await ctx.service.werewolf.gameConfig.getBoxList();
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    //获得open表板子列表
    public async getAllBoxList() {
        const { ctx, logger } = this;
        // 校验规则
        // const rule = {
        //     userList: { type: 'array' },
        //     title: { type: 'string' },
        //     content: { type: 'string' }
        // };
        try {
            // 校验
            // ctx.validate(rule);
            // const requestBody: ItboxSeasonOpenReq = ctx.request.body;
            const resp = await ctx.service.werewolf.gameConfig.getAllBoxList();
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    //获得open表板子列表
    public async getTboxSeasonList() {
        const { ctx, logger } = this;
        // 校验规则
        // const rule = {
        //     userList: { type: 'array' },
        //     title: { type: 'string' },
        //     content: { type: 'string' }
        // };
        try {
            // 校验
            // ctx.validate(rule);
            // const requestBody: SendEmailsToUsersRequest = ctx.request.body;
            const resp = await ctx.service.werewolf.gameConfig.getTboxSeasonList();
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    // 更新宝箱
    public async updateOpen() {
        const { ctx, logger } = this;
        // 校验规则
        const rule = {
            no: { type: 'number' },
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: IupdateGameConfOpenReq = ctx.request.body;
            // console.info("requestBody",requestBody);
            await ctx.service.werewolf.gameConfig.updateBox(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    // 更新宝箱
    public async updateEndTime() {
        const { ctx, logger } = this;
        // 校验规则
        // const rule = {
        //     no: { type: 'number' },
        // };
        try {
            // 校验
            // ctx.validate(rule);
            const requestBody: ItboxUpdateEndTimeReq = ctx.request.body;
            // console.info("requestBody",requestBody);
            await ctx.service.werewolf.gameConfig.updateEndTime(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    //新建宝箱
    public async createOpen() {
        const { ctx, logger } = this;
        // 校验规则
        // const rule = {
        //     no: { type: 'number' },
        // };
        try {
            // 校验
            // ctx.validate(rule);
            const requestBody: IcreateGameConfOpenReq = ctx.request.body;
            await ctx.service.werewolf.gameConfig.createOpen(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: error };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    //新建宝箱
    public async createOpenList() {
        const { ctx, logger } = this;
        // 校验规则
        // const rule = {
        //     no: { type: 'number' },
        // };
        try {
            // 校验
            // ctx.validate(rule);
            const requestBody: IcreateGameConfOpenReq[] = ctx.request.body;
            await ctx.service.werewolf.gameConfig.createOpenList(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: error };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    //新建宝箱
    public async createNewSeason() {
        const { ctx, logger } = this;
        // 校验规则
        // const rule = {
        //     no: { type: 'number' },
        // };
        try {
            // 校验
            // ctx.validate(rule);
            await ctx.service.werewolf.gameConfig.createNewSeason();
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: error };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    //获得open表板子列表
    public async getFrame() {
        const { ctx, logger } = this;
        try {
            // 校验
            // ctx.validate(rule);
            const resp = await ctx.service.werewolf.gameConfig.getFrame();
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async getFrameWithItemDic() {
        const { ctx, logger } = this;
        try {
            // 校验
            // ctx.validate(rule);
            const resp = await ctx.service.werewolf.gameConfig.getFrameWithItemDic();
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    //获得open表板子列表
    public async getProps() {
        const { ctx, logger } = this;
        try {
            // 校验
            // ctx.validate(rule);
            const resp = await ctx.service.werewolf.gameConfig.getProps();
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    //获得open表板子列表
    public async getAllProps() {
        const { ctx, logger } = this;
        try {
            // 校验
            // ctx.validate(rule);
            const resp = await ctx.service.werewolf.gameConfig.getAllProps();
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    // 更新宝箱
    public async updateProps() {
        const { ctx, logger } = this;
        // 校验规则
        // const rule = {
        //     no: { type: 'number' },
        // };
        try {
            // 校验
            // ctx.validate(rule);
            const requestBody: IpropsConfigItem = ctx.request.body;
            await ctx.service.werewolf.gameConfig.updateProps(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    // 更新宝箱
    public async insertProps() {
        const { ctx, logger } = this;
        // 校验规则
        // const rule = {
        //     no: { type: 'number' },
        // };
        try {
            // 校验
            // ctx.validate(rule);
            const requestBody: IpropsConfigItem = ctx.request.body;
            await ctx.service.werewolf.gameConfig.insertProps(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    // 更新宝箱
    public async getAnimation() {
        const { ctx, logger } = this;
        // 校验规则
        // const rule = {
        //     no: { type: 'number' },
        // };
        try {
            // 校验
            // ctx.validate(rule);
            // const requestBody: IpropsConfigItem = ctx.request.body;
            // console.info("requestBody",requestBody);

            const resp = await ctx.service.werewolf.gameConfig.getAnimation();
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    // 更新宝箱
    public async getAnimationRole() {
        const { ctx, logger } = this;
        // 校验规则
        // const rule = {
        //     no: { type: 'number' },
        // };
        try {
            // 校验
            // ctx.validate(rule);
            // const requestBody: IpropsConfigItem = ctx.request.body;
            // console.info("requestBody",requestBody);

            const resp = await ctx.service.werewolf.gameConfig.getAnimationRole();
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    // 更新宝箱
    public async updateAni() {
        const { ctx, logger } = this;
        // 校验规则
        // const rule = {
        //     no: { type: 'number' },
        // };
        try {
            // 校验
            // ctx.validate(rule);
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.gameConfig.updateAni(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    // 更新宝箱
    public async insertAni() {
        const { ctx, logger } = this;
        // 校验规则
        // const rule = {
        //     no: { type: 'number' },
        // };
        try {
            // 校验
            // ctx.validate(rule);
            const requestBody = ctx.request.body;
            console.info("requestBody", requestBody);
            await ctx.service.werewolf.gameConfig.insertAni(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    // 更新宝箱
    public async updateFrame() {
        const { ctx, logger } = this;
        // 校验规则
        // const rule = {
        //     no: { type: 'number' },
        // };
        try {
            // 校验
            // ctx.validate(rule);
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.gameConfig.updateFrame(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    // 更新宝箱
    public async getGiftBagInfo() {
        const { ctx, logger } = this;
        // 校验规则
        // const rule = {
        //     no: { type: 'number' },
        // };
        try {
            // 校验
            // ctx.validate(rule);
            // const requestBody: IpropsConfigItem = ctx.request.body;
            // console.info("requestBody",requestBody);

            const resp = await ctx.service.werewolf.gameConfig.getGiftBagInfo();
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateBagInfo() {
        const { ctx, logger } = this;
        // 校验规则
        // const rule = {
        //     no: { type: 'number' },
        // };
        try {
            // 校验
            // ctx.validate(rule);
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.gameConfig.updateGiftBag(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async createBagInfo() {
        const { ctx, logger } = this;
        // 校验规则
        // const rule = {
        //     no: { type: 'number' },
        // };
        try {
            // 校验
            // ctx.validate(rule);
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.gameConfig.createGiftBag(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateBagContentInfo() {
        const { ctx, logger } = this;
        // 校验规则
        // const rule = {
        //     no: { type: 'number' },
        // };
        try {
            // 校验
            // ctx.validate(rule);
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.gameConfig.updateGiftBagContent(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async createBagContentInfo() {
        const { ctx, logger } = this;
        // 校验规则
        // const rule = {
        //     no: { type: 'number' },
        // };
        try {
            // 校验
            // ctx.validate(rule);
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.gameConfig.createGiftBagContent(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async insertDocumentResource() {
        const { ctx, logger } = this;
        // 校验规则
        try {
            // 校验
            // ctx.validate(rule);
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.gameConfig.insertDocumentResource(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async getUserScoreList() {
        const { ctx, logger } = this;
        // 校验规则
        // const rule = {
        //     no: { type: 'number' },
        // };
        try {
            // 校验
            // ctx.validate(rule);
            // const requestBody: IpropsConfigItem = ctx.request.body;
            // console.info("requestBody",requestBody);
            const requestBody: IuserScoreReq = ctx.request.body;
            const resp = await ctx.service.werewolf.gameConfig.getUserScoreList(requestBody.userList);
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async getAvatarFramePeriodList() {
        const { ctx, logger } = this;
        try {
            const resp = await ctx.service.werewolf.gameConfig.getAvatarFramePeriodList();
            ctx.body = resp;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updatePeriod() {
        const { ctx, logger } = this;
        // 校验规则
        // const rule = {
        //     no: { type: 'number' },
        // };
        try {
            // 校验
            // ctx.validate(rule);
            // const requestBody: IpropsConfigItem = ctx.request.body;
            // console.info("requestBody",requestBody);
            const requestBody: IavatarFramePeriod = ctx.request.body;

            const resp = await ctx.service.werewolf.gameConfig.updatePeriod(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
            // console.log('resp', resp);
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数`有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    
    public async createPeriod() {
        const { ctx, logger } = this;
        // 校验规则
        // const rule = {
        //     no: { type: 'number' },
        // };
        try {
            // 校验
            // ctx.validate(rule);
            // const requestBody: IpropsConfigItem = ctx.request.body;
            // console.info("requestBody",requestBody);
            const requestBody: IavatarFramePeriod = ctx.request.body;

            const resp = await ctx.service.werewolf.gameConfig.createPeriod(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
            // console.log('resp', resp);
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数`有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updatePeriodImg() {
        const { ctx, logger } = this;
        // 校验规则
        // const rule = {
        //     no: { type: 'number' },
        // };
        try {
            // 校验
            // ctx.validate(rule);
            // const requestBody: IpropsConfigItem = ctx.request.body;
            // console.info("requestBody",requestBody);
            const requestBody: IavatarFramePeriod = ctx.request.body;

            const resp = await ctx.service.werewolf.gameConfig.updatePeriodImg(requestBody);
            ctx.body = { err_code: HttpErr.Success, err_msg: 'ok' };
            ctx.status = HttpErr.Success;
            // console.log('resp', resp);
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数`有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    
}
