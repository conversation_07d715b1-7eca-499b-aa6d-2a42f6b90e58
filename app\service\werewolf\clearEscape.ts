/*
 * @Description: 请逃跑service
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2019-10-29 10:15:33
 * @LastEditors: zhanglu
 * @LastEditTime: 2020-12-25 11:29:49
 */

import { Service } from 'egg';
import { IclearEscapeReq, IescapeListReq } from '../../model/werewolf';

export default class ClearEscapeService extends Service {
    /**
     * @name:查询逃跑列表 
     * @msg: 
     * @param {type} 
     * @return: 
     */
    public async getEscapeList(req: IescapeListReq) {
        const { app } = this;
        const db = app.mysql.get('werewolf');
        const uid = db.escape(req.uid);
        const startTime = db.escape(req.startTime);
        const endTime = db.escape(req.endTime);
        try {
            // 查询逃跑记录
            const sql = `SELECT tg.\`no\`as game_no,tug.user_no FROM tgamerecord tg
            LEFT JOIN tusergamerecord tug ON tug.game_no = tg.\`no\`
            WHERE tg.endtime >= ${startTime}
            AND tg.endtime <= ${endTime}
            AND tug.\`escape\` = 1;
            `;

            const result = await db.query(sql);
            return result
        } catch (error) {
            throw error;
        }
    }

    public async clearEscape(req: IclearEscapeReq) {
        const { app, logger } = this;
        const db = app.mysql.get('werewolf');
        const playerId = db.escape(req.user_no);
        const gameNo = db.escape(req.game_no);
        const uid = db.escape(req.uid);
        const werewolfConn = await db.beginTransaction();
        try {
            //1  修改escape为0
            let sql = `UPDATE tusergamerecord SET \`escape\` = 0 WHERE game_no = ${gameNo} AND user_no = ${playerId};`;
            await werewolfConn.query(sql);
            //2 tuser表scorce+20
            sql = `UPDATE tuser SET scorce = scorce+20  WHERE \`no\` = ${playerId};`;
            await werewolfConn.query(sql);
            //3 修改release_time为CUR_Data
            sql = ` UPDATE tuser_imprison_deal SET release_time = CURRENT_DATE() WHERE user_no = ${playerId} AND type= 7 AND release_time >NOW();`;
            await werewolfConn.query(sql);

            sql = `
                UPDATE tusergamerecordscorce 
                SET 
                scorce = scorce + 20
                WHERE
                    user_no = ${playerId}
                AND game_no = ${gameNo} 
            `;
            await werewolfConn.query(sql);

            await werewolfConn.commit(); // 提交事务

            //4 写入mongo文档
            const mongo = app['mongo'];
            let doc = { adminId: uid, playerId, gameNo, createTime: new Date(), releaseTime: new Date().toLocaleString };
            logger.info("清逃跑记录：", doc);
            const mongoArgs = { doc };
            await mongo.insertOne("clearMultiEscape", mongoArgs);

        } catch (error) {
            logger.error("清理逃跑失败", error);
            await werewolfConn.rollback(); // 一定记得捕获异常后回滚事务！
            throw error;
        }
    }
}
