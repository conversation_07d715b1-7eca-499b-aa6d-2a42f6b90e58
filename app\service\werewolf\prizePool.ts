/*
 * @Description: 2w
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhangyu
 * @Date: 2020-04-24 11:35:39
 * @LastEditors: zhang<PERSON>
 * @LastEditTime: 2022-09-13 14:25:36
 */
import BaseMegaService from './BaseMegaService';
export default class LotteryBoxService extends BaseMegaService {

    //奖池列表
    public async getActivityList() {
        const { app, ctx, logger } = this;
        try {
            let sql = ` SELECT season.* FROM coin_2w_season_info season
            ORDER BY end_time DESC , id DESC`;
            return await this.selectList(sql, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }


    
    public async insertActivity(req: any) {
        const { app, logger } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
            INSERT INTO werewolf.coin_2w_season_info (
                start_time,
                end_time,
                name,
                delsign 
            )
            VALUES
                (?,?,?,'0')
                `;
             await conn.query(sql, [
                req.start_time,
                req.end_time,
                req.name,
                req.delsign]);

            await conn.commit(); // 提交事务

        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！

            logger.error(error);

            throw error;
        }
    }

    public async updateActivity(req: any) {
        const { app, logger } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {

            let sql_select = ` SELECT season.* FROM coin_2w_season_info season WHERE id = ?`;
            let list =  await this.selectList(sql_select, [req.id]);


            let isChangeH5_1 = req.official_h5_1_url != undefined;
            let isChangeH5_2 = req.official_h5_2_url != undefined;
            let isChangeH5_3 = req.official_h5_3_url != undefined;
            let isChangePC = req.official_pc_url != undefined;
            let isChangeH5 = req.activity_h5_url != undefined;


            let sql = `
            UPDATE coin_2w_season_info  SET name = '${req.name}',  start_time = '${req.start_time}', end_time = '${req.end_time}',
            delsign = '0' ,
            official_h5_1_url = '${isChangeH5_1?req.official_h5_1_url:(list[0].official_h5_1_url?list[0].official_h5_1_url:'')}',
            official_h5_2_url = '${isChangeH5_2?req.official_h5_2_url:(list[0].official_h5_2_url?list[0].official_h5_2_url:'')}',
            official_h5_3_url = '${isChangeH5_3?req.official_h5_3_url:(list[0].official_h5_3_url?list[0].official_h5_3_url:'')}',
            official_pc_url = '${isChangePC?req.official_pc_url:(list[0].official_pc_url?list[0].official_pc_url:'')}',
            activity_h5_url = '${isChangeH5?req.activity_h5_url:(list[0].activity_h5_url?list[0].activity_h5_url:'')}'
            WHERE id = '${req.id}'`;

            logger.error('\n\n\n'+sql+'\n\n\n'+list+'\n\n\n\n');

            await conn.query(sql, []);

            await conn.commit(); // 提交事务

        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！

            logger.error(error);

            throw error;
        }
    }

    public async getAwardList() {
        const { app, ctx, logger } = this;
        try {
            let sql = ` SELECT * FROM coin_2w_season_info WHERE id > 0 ORDER BY id desc`;
            return await this.selectList(sql, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

   

    //奖品列表
    public async getBoxList(req) {
        const { app, ctx, logger } = this;
        try {
            let sql = ` SELECT c.*,s.name AS season_name FROM coin_2w_season_award_info c,coin_2w_season_info s
            WHERE c.season_id = s.id
            ORDER BY id ASC
            `;
            
            if(req.season_id != undefined) {
                sql = ` SELECT c.*,s.name AS season_name FROM coin_2w_season_award_info c,coin_2w_season_info s
                WHERE season_id = ? AND c.season_id = s.id
                ORDER BY id ASC
                `; 
                return await this.selectList(sql, [req.season_id]);

            }

            return await this.selectList(sql, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }



    public async updateLotteryBoxDelsign(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = ` 
                UPDATE coin_2w_season_award_info 
                SET
                delsign = ?
                WHERE
                    id = ?
            `;
            let data = await this.execSql(sqlStr, [
                req.delsign,
                req.id
            ])

            return data;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async insertLotteryBox(req) {
        const { app, logger } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {


            let sql0 = ` SELECT * FROM coin_2w_season_award_info WHERE item_dic_id = ? AND season_id = ?`;
            let result0 = await conn.query(sql0, [req.item_dic_id,req.season_id]);


            if(result0.length > 0){
                await conn.commit(); // 提交事务
                return '该奖池已有该奖品';
            }

            let icon = 'http://img.53site.com/Werewolf/Frame/';

            let sql1 = ` SELECT * FROM item_dic WHERE id = ?`;
            let result1 = await conn.query(sql1, [req.item_dic_id]);
        
            let sql2 = ` SELECT * FROM avatarframe WHERE pid in ( SELECT item_id FROM item_dic WHERE id = '${req.item_dic_id}');`;
            let result2 = await conn.query(sql2, []);



            if(result2.length == 0){
                icon = icon+result1[0].item_id+'_player.png';
            }else{
                icon = icon+result2[0].id+'_player.webp';
            }
                  
            let sqlStr = ` INSERT INTO coin_2w_season_award_info ( season_id, name, sort, item_dic_id, \`desc\`, delsign,icon ) VALUES (?,?,?,?,?,'0',?)
                        `;
            await this.execSql(sqlStr, [
                req.season_id,
                req.name,
                0,
                req.item_dic_id,
                req.desc,
                icon
            ])

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            logger.error(error);
            throw error
        }
    }

    public async updateLotteryBox(req) {
        const { app, logger } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {

            let sql0 = ` SELECT * FROM coin_2w_season_award_info WHERE item_dic_id = ? AND season_id = ?`;
            let result0 = await conn.query(sql0, [req.item_dic_id,req.season_id]);


            if(result0.length > 0 && result0[0].id != req.id){
                await conn.commit(); // 提交事务
                return '该奖池已有该奖品';
            }

            let icon = 'http://img.53site.com/Werewolf/Frame/';

            let sql1 = ` SELECT * FROM item_dic WHERE id = ?`;
            let result1 = await conn.query(sql1, [req.item_dic_id]);
        
            let sql2 = ` SELECT * FROM avatarframe WHERE pid in ( SELECT item_id FROM item_dic WHERE id = '${req.item_dic_id}');`;
            let result2 = await conn.query(sql2, []);

            if(result2.length == 0){
                icon = icon+result1[0].item_id+'_player.png';
            }else{
                icon = icon+result2[0].id+'_player.webp';
            }

            
            let sqlStr = ` 
                UPDATE coin_2w_season_award_info SET season_id = ?, name = ?, item_dic_id = ?, \`desc\` = ?, icon = ? WHERE id = ?
            `;
            await conn.query(sqlStr, [
                req.season_id,
                req.name,
                req.item_dic_id,
                req.desc,
                icon,
                req.id
            ])

            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            logger.error(error);
            throw error
        }
    }
}
