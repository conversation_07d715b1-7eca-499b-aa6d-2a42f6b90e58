/*
 * @Description: 邮件系统
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: 张宇
 * @Date: 2019-06-21 15:02:09
 * @LastEditors: hammercui
 * @LastEditTime: 2020-03-16 14:32:11
 */
import { Controller } from 'egg';
import { IerrorMsg, HttpErr } from '../../model/common';
import { SendEmailsToUsersRequest, SearchNewUserListResponse, PushMessageResquest } from '../../model/werewolf';

export default class SendEmailController extends Controller {

    //给用户发送邮件
    public async sendEmailsToUsers() {
        const { ctx, logger } = this;
        // 校验规则
        const rule = {
            userList: { type: 'array' },
            title: { type: 'string' },
            content: { type: 'string' }
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: SendEmailsToUsersRequest = ctx.request.body;
            await ctx.service.werewolf.sendEmail.sendEmailsToUsers(requestBody);
            const body: IerrorMsg = {
                err_code: HttpErr.Success,
                err_msg: "操作成功"
            };
            ctx.body = body;
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
}
