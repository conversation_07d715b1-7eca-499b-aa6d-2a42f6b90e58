/*
 * @Description: 无
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercui
 * @Date: 2020-04-30 10:25:52
 * @LastEditors: hammercui
 * @LastEditTime: 2020-06-08 21:13:53
 */
import { Controller } from 'egg';
let fs = require('fs');
export default class UserController extends Controller {

    async index(){
        const {ctx} = this;
        await ctx.redirect("/index.html");
    }

    async downloadPng(){
        const { ctx,app,logger } = this;
        try {
            const path = 'headicon/20190618/u_7237442_e0d7bd0e99a4cd43ca672edb9ae77263.png';
            let result = await ctx.service.download.ossPng(path);
            ctx.body = { code: 200,msg:result };
        } catch(er) {
            ctx.body = { code: 500,msg:er };
        }
    }
}
