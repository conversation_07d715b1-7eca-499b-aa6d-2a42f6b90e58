/*
 * @Description: 2w框刻字服务类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: hammercu<PERSON>
 * @Date: 2020-08-31 13:17:33
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2021-10-11 19:21:54
 */

import BaseMegaService from './BaseMegaService';
import {
    Icoin2wListReq,
    Icoin2wItem,
    Icoin2wUpdateInfoReq,
    Coin2wStatus,
    Icoin2wUploadNoteSuccReq,
    IsendDingReq,
    NoteSearchRes
} from '../../model/wf2wCoin';
import moment = require('moment');
import { IavatarFramePeriodv2 } from '../../model/werewolf2';
import { IobtianItemReq } from '../../model/wfExchange';
import {AreneAreaData} from "../../model/ArenaArea";
import * as Console from "console";
export default class Coin2WNoteOrderService extends BaseMegaService {

    /**
     * 获得2w框刻字订单列表
     * @param req
     */
    public async getNoteOrderList(req: NoteSearchRes): Promise<Icoin2wItem[]> {
        const { app, ctx, logger } = this;
        try {
            const db = app.mysql.get('werewolf');
            let sql='';
            //Console.log("req status="+req.status);
            //Console.log("req user_id="+req.user_id);


            if(req.status==-1){
                if(req.user_id==null||req.user_id==""){
                   // Console.log("req user_id=-1-1-1-1-1-1-1-1-1-1");
                    sql =`SELECT
                a.*,
                b.nickname
                FROM
                coin_2w_note a
                LEFT JOIN tuser  AS b ON b.no = a.user_id
                WHERE  b.delsign = 0 ORDER BY a.create_time DESC `;
                }else{
                    sql =`SELECT
                a.*,
                b.nickname
                FROM
                coin_2w_note a 
                LEFT JOIN tuser  AS b ON b.no = a.user_id
                where a.user_id = ${req.user_id} AND b.delsign = 0 ORDER BY a.create_time DESC`;
                }

            }else{
                if(req.status!=-1&&req.user_id!=null&&req.user_id!=""){
                    sql =`SELECT
                a.*,
                b.nickname
                FROM
                coin_2w_note a
                LEFT JOIN tuser  AS b ON b.no = a.user_id
                where a.status = ${req.status} AND a.user_id=${req.user_id} AND b.delsign = 0 ORDER BY a.create_time DESC`;
                } else if(req.status!=-1){
                    sql =`SELECT
                    a.*,
                    b.nickname
                    FROM
                    coin_2w_note a 
                    LEFT JOIN tuser  AS b ON b.no = a.user_id
                    where a.status = ${req.status} AND b.delsign = 0 ORDER BY a.create_time DESC`;
                }else{
                    sql =`SELECT
                a.*,
                b.nickname
                FROM
                coin_2w_note a 
                LEFT JOIN tuser  AS b ON b.no = a.user_id
                where a.user_id = ${req.user_id} AND b.delsign = 0 ORDER BY a.create_time DESC`;
                }

            }

            const data: Icoin2wItem[] =  await db.query(sql);
            return data
        }
        catch (error) {
            logger.error(error)
            throw error;
        }
    }



    /**
     * 更新2w框刻字订单信息
     * @param req
     */
         public async updateCoin2wNoteInfo(req: Icoin2wUpdateInfoReq) {
            const { app, ctx, logger } = this;
            try {
                //无资源
                if (req.status == Coin2wStatus.COIN_2W_NULL) {
                    Console.log("更新++++++++pgg1");

                    await this.execSql(`UPDATE coin_2w_note SET status = ? WHERE id = ?  `, [req.status, req.id]);
                    return;
                }
                //旧订单信息
                const oldTradeInfo = await this.selectOne(`SELECT * FROM coin_2w_note WHERE id = ?`, [req.id])
                if (!oldTradeInfo) {
                    Console.log("更新++++++++pgg2"+oldTradeInfo);

                    throw new Error("数据错误")
                }
                await this.updateCoin2winfoImp(req)

            } catch (error) {
                Console.log("更新失败++++++++pgg");

                logger.error(error)
                throw error;
            }
        }

        public async updateCoin2winfoImp(req: Icoin2wUpdateInfoReq) {
            //更新订单信息
            await this.execSql(`
    UPDATE coin_2w_note SET status = ?,to_user_id = ?,avatar_frame_period_id = ? ,select_avatar_frame_id = ?,note_command= ? ,period_name= ?, uid= ? WHERE id =  ?;
    `, [req.status,
            req.to_user_id,
            req.avatar_frame_period_id,
            req.avatar_frame_period_id,
            req.note_command,
            req.period_name,
            req.uid,
            req.id
            ])
        }

        //增加售卖数量
        public async increaseAvatarPeriodSellNum(periodId: number) {
            await this.execSql(`UPDATE base20000_avatar_frame_period_v2  SET selled_num  = selled_num + 1 ,selled_show_num  = selled_show_num+1 WHERE  id  = ? `, [periodId])
        }

        public async decreaseAvatarPeriodSellNum(periodId: number) {
            await this.execSql(`UPDATE base20000_avatar_frame_period_v2  SET selled_num  = selled_num - 1  WHERE  id  = ? `, [periodId])
        }

        /**
         * 更新订单刻字信息
         * @param req
         */
        public async uploadCoin2wNoteSuccess(req: Icoin2wUploadNoteSuccReq) {
            const { app, ctx, logger } = this;
            try {
                await this.execSql(`UPDATE coin_2w_note SET note_id = ? WHERE id = ?;`, [req.note_id, req.id]);
            } catch (error) {
                logger.error(error)
                throw error;
            }
        }

        /**
         * 发送订单全部信息
         * @param req
         */
        public async sendAllRes(req: Icoin2wItem) {
            const { app, ctx, logger } = this;
            try {
                //1 查询头像框item_id
                const avatarFrameResult = await this.selectOne(`SELECT id FROM item_dic WHERE item_id = ? AND item_cate_id in (2010,2020);`, [req.select_avatar_frame_id]);
                if (!avatarFrameResult || !avatarFrameResult.id) {
                    throw new Error("头像框不存在")
                }
                //2 查询成就item_id
                await this.execSql(`INSERT IGNORE INTO user_achievement (user_id,achievement_id,complete_num,level,delsign)
                VALUES(?,?,1,0,0);`, [req.to_user_id, req.achievement_id])

                const achieveResult = await this.selectOne(`SELECT id FROM item_dic WHERE item_cate_id in (9010);`, [req.achievement_id]);
                if (!achieveResult || !achieveResult.id) {
                    throw new Error("成就不存在")
                }
                //3 刻字
                // await this.execSql(`INSERT IGNORE INTO user_avatarframe (user_id,avatarframe_id,note_id,delsign)
            //   VALUES(?,?,?,0);`, [req.to_user_id, req.select_avatar_frame_id, req.note_id])
                await this.execSql(`UPDATE user_avatarframe SET note_id= ? WHERE user_id= ? AND avatarframe_id= ? AND uid= ?;`, [req.note_id,req.to_user_id,req.select_avatar_frame_id,req.uid]);

                Console.log("pgg发送++++++++pgg",req);


                // 4 状态
                await this.execSql(`UPDATE coin_2w_note SET status=2,send_time=NOW()  WHERE id = ?;`, [req.id]);

                let avatarFrameUrl = this.getAvatarFrameImg(req.select_avatar_frame_id)
                let noteUrl = this.getNoteImg(req.note_id)
                let achievementUrl = this.getAchievementImg(req.achievement_id)
                const msg = `* UserId:${req.to_user_id}刻字服务订单资源全部发放成功！\n`
                    + ` * 2W头像框【${req.period_name}】\n`
                    + `  ![头像框](${avatarFrameUrl}) \n`
                    + ` * 刻字【${req.note_command}】\n`
                    + `  ![刻字](${noteUrl}) \n`
                    + ` * 成就`
                    + `  ![成就](${achievementUrl}) \n`
                    ;
                //4 钉钉
                const dingReq: IsendDingReq = {
                    msgtype: 'markdown',
                    markdown: {
                        title: "发放2W头像框刻字服务订单全部资源通知",
                        text: msg,
                    }
                }
                logger.info("待发送钉钉信息", dingReq);
                await this.sendDingtalk(dingReq);
             
             //5 飞书
             let aperiodName = req.period_name;
             let noteCommand = req.note_command;

             let feiShuMsg = `* UserId:${req.to_user_id}资源全部发放成功！\n`

             const feiReq = {
                 text:feiShuMsg,

                 period_name: aperiodName,
                 avatarFrame: avatarFrameUrl,

                 note_command: noteCommand,
                 note: noteUrl,

                 achievement: achievementUrl

             }

                await this.sendFeishuTalk(feiReq);


            } catch (error) {
                logger.error(error)
                throw error;
            }
        }

        public getAvatarFrameImg(avatarFrameId) {
            if (this.globalEnv() == 'prod') {
                return `http://img.53site.com/Werewolf/Frame/${avatarFrameId}_player.png`;
            }
            return `http://coder.53site.com/Werewolf/Frame/${avatarFrameId}_player.png`;
        }

        /**
         * 获得刻字图片地址
         * @param {*} noteId
         */
        public getNoteImg(noteId) {
            if (this.globalEnv() == 'prod') {
                return `http://img.53site.com/Werewolf/frame_note/${noteId}.png`;
            }
            return `http://coder.53site.com/Werewolf/frame_note/${noteId}.png`;
        }

        /**
         * 获得成就图片地址
         * @param {*} ahievementId
         */
        public getAchievementImg(ahievementId) {
            if (this.globalEnv() == 'prod') {
                return `http://img.53site.com/Werewolf/achieveNew/achieve_${ahievementId}.png`;
            }
            return `http://coder.53site.com/Werewolf/achieveNew/achieve_${ahievementId}.png`;
        }

        public globalEnv() {
            const { app, ctx, logger } = this;
            this.logger.info("当前运行环境", app.config.serverEnv);
            if (app.config.serverEnv == 'prod') {
                return 'prod';
            } else {
                return 'development';
            }
        }
        public async sendDingtalk(dingReq: IsendDingReq) {
            const { app, ctx, logger } = this;
            let host = app.config.phpAtyBaseUrl;
            //正式上线时屏蔽掉
            if (app.config.serverEnv == 'local' || app.config.serverEnv == 'coder') {
                host = "http://coder.53site.com/Werewolf/DailyMission/PHPCode/api";
            } else {
                host = "http://werewolf.53site.com/Werewolf/DailyMission/PHPCodeProd/api";
            }
            const curlResp = await ctx.curl(host + '/Dingtalk/send.php', {
                method: 'POST',
                headers: {
                    "content-type": "application/json",
                },
                data: dingReq,
                dataType: 'json',
                timeout: 30000, // 30 秒超时
            });

            const { status, headers, data } = curlResp
            if (status == 200) {
                this.logger.debug("发送php成功", data);
            } else {
                this.logger.error("发送php失败", status, data);
            }
        }


        public async sendFeishuTalk(feiReq: any) {
            const { app, ctx, logger } = this;
            let host = app.config.phpAtyBaseUrl;

            let feishuReq = {
                msg_type: 'post',
                content: {
                    post: {
                        zh_cn: {
                            title: feiReq.text,
                            content: [
                                [
                                    {
                                        tag: "a",
                                        text: "2w头像框："+feiReq.period_name+"\n",
                                        href: feiReq.avatarFrame
                                    },
                                    {
                                        tag: "a",
                                        text: "刻字："+feiReq.note_command+"\n",
                                        href: feiReq.note
                                    },
                                    {
                                        tag: "a",
                                        text: "成就\n",
                                        href: feiReq.achievement
                                    }
                                ]
                            ]
                        }
                    }
                }
            };

            //正式上线时屏蔽掉
            if (app.config.serverEnv == 'local' || app.config.serverEnv == 'coder') {
                host = "http://coder.53site.com/Werewolf/DailyMission/PHPCode/api";
            } else {
                host = "http://werewolf.53site.com/Werewolf/DailyMission/PHPCodeProd/api";
            }
            const curlResp = await ctx.curl(host + '/Dingtalk/sendFeishu.php', {
                method: 'POST',
                headers: {
                    "content-type": "application/json",
                },
                data: feishuReq,
                dataType: 'json',
                timeout: 30000, // 30 秒超时
            });

            const { status, headers, data } = curlResp
            if (status == 200) {
                this.logger.debug("发送php成功", data);
            } else {
                this.logger.error("发送php失败", status, data);
            }
        }
}
