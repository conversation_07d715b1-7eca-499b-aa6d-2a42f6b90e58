/*
 * @Description: 商城道具路由
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhangyu
 * @Date: 2020-04-24 11:50:11
 * @LastEditors: zhang<PERSON>
 * @LastEditTime: 2021-04-19 17:22:28
 */
import { Router, Application } from "egg";
import { AccessRouteId, AccessRouteName } from "./model/accessRouteCof";
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {

    const { controller, router } = app;

    router.post(`${API_VERSION}/werewolf/prizePool/getActivityList`, controller.werewolf.prizePool.getActivityList)
    router.post(`${API_VERSION}/werewolf/prizePool/insertActivity`, controller.werewolf.prizePool.insertActivity)
    router.post(`${API_VERSION}/werewolf/prizePool/updateActivity`, controller.werewolf.prizePool.updateActivity)
    router.post(`${API_VERSION}/werewolf/prizePool/getAwardList`, controller.werewolf.prizePool.getAwardList)

    router.post(`${API_VERSION}/werewolf/prizePool/getBoxList`, controller.werewolf.prizePool.getBoxList)
    router.post(`${API_VERSION}/werewolf/prizePool/updateLotteryBoxDelsign`, controller.werewolf.prizePool.updateLotteryBoxDelsign)
    router.post(`${API_VERSION}/werewolf/prizePool/insertLotteryBox`, controller.werewolf.prizePool.insertLotteryBox)
    router.post(`${API_VERSION}/werewolf/prizePool/updateLotteryBox`, controller.werewolf.prizePool.updateLotteryBox)
    
}

export default load
