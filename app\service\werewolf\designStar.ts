import { IdesignStarItem, IdesignStarUpdateNum, IdesignStarUpdateStatus, IfliteListReq } from "../../model/wfDesignStar";
import BaseMegaService from "./BaseMegaService";

/**
 * 设计之星服务类
 *
 *
 */
export default class DesignStarService extends BaseMegaService {
    /**
     * 设计列表
     * @returns
     */
    public async list(): Promise<IdesignStarItem[]> {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `SELECT a.id,a.user_id,b.nickname,a.\`name\` as design_name
            ,a.\`desc\` as design_desc
            ,a.img
            ,a.design_type
            ,a.vote_status
            ,a.vote_num
            ,a.create_time
            FROM activity2021_design_star_item as a
            LEFT JOIN tuser as b on a.user_id = b.\`no\`
            ORDER BY a.id ;`;
            let rowList: IdesignStarItem[] = await this.selectList(sqlStr);
            for (let i = 0; i < rowList.length; i++) {
                rowList[i].index = i;
            }
            return rowList;
        } catch (e) {
            logger.error(e);
            throw e;
        }
    }

    public async fliteList(req: IfliteListReq): Promise<IdesignStarItem[]> {
        const { app, ctx, logger } = this;
        try {

            let voteStatus = "1";
            if (req.voteStatus) {
                voteStatus = req.voteStatus + ""
            }
            //是否展示不可投票状态
            if (req.voteStatus == 1 && req.isShowUnVote && req.isShowUnVote == 1) {
                voteStatus = voteStatus + ",0"
            }


            let sqlStr = `SELECT a.id,a.user_id,b.nickname,a.\`name\` as design_name
            ,a.\`desc\` as design_desc
            ,a.img
            ,a.design_type
            ,a.vote_status
            ,a.vote_num
            ,a.truing_img
            ,a.final_img
            ,a.create_time
            FROM activity2021_design_star_item as a
            LEFT JOIN tuser as b on a.user_id = b.\`no\`
            Where a.design_type = ?
            AND a.vote_status in (${voteStatus})
            ORDER BY a.id;`;
            logger.debug(sqlStr)
            let rowList: IdesignStarItem[] = await this.selectList(sqlStr, [req.designType]);
            let index = 0
            if (!rowList) {
                return [];
            }
            // tslint:disable-next-line:prefer-for-of
            for (let i = 0; i < rowList.length; i++) {
                //只有显示中稿件，拥有前端index
                if (rowList[i].vote_status == 1) {
                    rowList[i].index = index;
                    index++
                } else {
                    rowList[i].index = -1;
                }
            }
            return rowList;
        } catch (e) {
            logger.error(e);
            throw e;
        }
    }

    public async updateStatus(req: IdesignStarUpdateStatus) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `UPDATE activity2021_design_star_item SET vote_status = ? WHERE id = ?;`;
            await this.execSql(sqlStr, [req.newStatus, req.id])
        } catch (e) {
            logger.error(e);
            throw e;
        }
    }

    public async updateNum(req: IdesignStarUpdateNum) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `UPDATE activity2021_design_star_item SET vote_num = ? WHERE id = ?;`;
            await this.execSql(sqlStr, [req.newNum, req.id])
        } catch (e) {
            logger.error(e);
            throw e;
        }
    }

    public async updateImg(req) {
        const { app, ctx, logger } = this;
        try {
            let sqlStr = `UPDATE activity2021_design_star_item SET truing_img = ? WHERE id = ?;`
            if (req.upModalState == 2) {
                sqlStr = `UPDATE activity2021_design_star_item SET final_img = ? WHERE id = ?;`
            }
            await this.execSql(sqlStr, [req.newImgUrl, req.designId])
        } catch (e) {
            logger.error(e);
            throw e;
        }
    }
}
