import { ActivityChangeNameRequest } from './../../model/werewolf';
/*
 * @Description: 商城管理-控制类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: zhanglu
 * @Date: 2020-04-22 16:00:00
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-08-19 15:27:37
 */
import { Controller } from 'egg';
import BaseMegaController from './BaseMegaController';
import { IerrorMsg, HttpErr } from '../../model/common';

export default class MallController extends BaseMegaController {

    public async getGiftList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.mall.getGiftList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async updateGift() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.updateGift(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async createGift() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.createGift(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async getClothingCateList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.mall.getClothingCateList();
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getClothingList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.mall.getClothingList();
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async createClothing() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.createClothing(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateClothing() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.updateClothing(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateClothingIcon() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.updateClothingIcon(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async getTitleList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.mall.getTitleList();
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async createTitle() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.createTitle(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateTitle() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.updateTitle(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async refreshTitle() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.refreshTitle(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateDelsignTitle() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.updateDelsignTitle(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    //自定义称号-背景
    public async getCustomTagBgList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.mall.getCustomTagBgList();
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async createCustomTagBg () {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.createCustomTagBg(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateCustomTagBg() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.updateCustomTagBg(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async refreshCustomTagBg() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.refreshCustomTagBg(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateDelsignCustomTagBg() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.updateDelsignCustomTagBg(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    //自定义称号-装饰1
    public async getCustomTagDecorationList1() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.mall.getCustomTagDecorationList1();
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async createCustomTagDecoration1() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.createCustomTagDecoration1(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateCustomTagDecoration1() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.updateCustomTagDecoration1(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async refreshCustomTagDecoration1() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.refreshCustomTagDecoration1(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateDelsignCustomTagDecoration1() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.updateDelsignCustomTagDecoration1(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    //自定义称号-装饰2
    public async getCustomTagDecorationList2() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.mall.getCustomTagDecorationList2();
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async createCustomTagDecoration2() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.createCustomTagDecoration2(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateCustomTagDecoration2() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.updateCustomTagDecoration2(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async refreshCustomTagDecoration2() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.refreshCustomTagDecoration2(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateDelsignCustomTagDecoration2() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.updateDelsignCustomTagDecoration2(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    //自定义称号-装饰3
    public async getCustomTagDecorationList3() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.mall.getCustomTagDecorationList3();
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async createCustomTagDecoration3() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.createCustomTagDecoration3(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateCustomTagDecoration3() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.updateCustomTagDecoration3(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async refreshCustomTagDecoration3() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.refreshCustomTagDecoration3(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateDelsignCustomTagDecoration3() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.updateDelsignCustomTagDecoration3(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    //自定义称号-装饰文本
    public async getCustomTagTextList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.mall.getCustomTagTextList();
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async createCustomTagText() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.createCustomTagText(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateCustomTagText() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.updateCustomTagText(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async refreshCustomTagText() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.refreshCustomTagText(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateDelsignCustomTagText() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.updateDelsignCustomTagText(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    //自定义称号-装饰字体
    public async getCustomTagFontList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.mall.getCustomTagFontList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async getCustomTagHaveFontList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.mall.getCustomTagHaveFontList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async createCustomTagFont() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.createCustomTagFont(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateCustomTagFont() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.updateCustomTagFont(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async refreshCustomTagFont() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.refreshCustomTagFont(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    //信物
    public async getKeepSakeList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.mall.getKeepSakeList(requestBody);
            this.respSuccData(list)
        } catch (err) { this.respFail(err) }
    }

    public async createKeepSake() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.createKeepSake(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateKeepSake() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.updateKeepSake(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateShowInfoKeepSake() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.updateShowInfoKeepSake(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    
    public async updateDelsignKeepSake() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.updateDelsignKeepSake(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async getKeepSakeInfoList(){
        const { ctx, logger, app } = this;
        // 校验规则
        const rule = {
        };
        try {
            // 校验
            ctx.validate(rule);
            const requestBody: any = ctx.request.body;
            const responseBody = await ctx.service.werewolf.mall.getKeepSakeInfoList(requestBody);
            ctx.body = responseBody;
            ctx.status = HttpErr.Success;
        } catch (e) {
            logger.error(e);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    
    public async refreshKeepSake() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.refreshKeepSake(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    
    //
    public async getAchievementList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            let list = await ctx.service.werewolf.mall.getAchievementList(requestBody);
            this.respSuccData(list)
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async createAchievement() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.createAchievement(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateAchievement() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.updateAchievement(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateAchievementDelsign() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.updateAchievementDelsign(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateAchievementComplete() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.updateAchievementComplete(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async getAchievementListCount() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            let res = await ctx.service.werewolf.mall.getAchievementListCount(requestBody);
            this.respSuccData(res)
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    
    public async getItemDicList() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            const list = await ctx.service.werewolf.mall.getItemDicList(requestBody);
            this.respSuccData(list)
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async getItemCateList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.mall.getItemCateList();
            this.respSuccData(list)
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateItemDic() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.updateItemDic(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async createItemDic() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.createItemDic(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async getAnimationList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.mall.getAnimationList();
            this.respSuccData(list)
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async getMaskshowList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.mall.getMaskshowList();
            this.respSuccData(list)
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    
    public async getGroupBadgeList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.mall.getGroupBadgeList();
            this.respSuccData(list)
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async getGroupFrameList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.mall.getGroupFrameList();
            this.respSuccData(list)
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async getGroupBannerList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.mall.getGroupBannerList();
            this.respSuccData(list)
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async getAvatarFrameList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.mall.getAvatarFrameList();
            this.respSuccData(list)
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async getNormalItemList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.mall.getNormalItemList();
            this.respSuccData(list)
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    
    public async getGroupPropsList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.mall.getGroupPropsList();
            this.respSuccData(list)
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    
    public async getGiftBagList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.mall.getGiftBagList();
            this.respSuccData(list)
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async getAllMaskshowList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.mall.getAllMaskshowList();
            this.respSuccData(list)
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async insertMaskshow() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.insertMaskshow(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    
    public async updateMaskshow() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.updateMaskshow(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async getRoleList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.mall.getRoleList();
            this.respSuccData(list)
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    
    public async insertNormalItem() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.insertNormalItem(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    
    public async updateNormalItem() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.updateNormalItem(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async getCouponList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.mall.getCouponList();
            this.respSuccData(list)
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
    
    public async createCoupon() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.createCoupon(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateCoupon() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.updateCoupon(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateCouponImage() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.updateCouponImage(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateCouponDelsign() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.updateCouponDelsign(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async getFrameConditionList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.mall.getFrameConditionList();
            this.respSuccData(list)
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async getAllFrameList() {
        const { ctx, logger } = this;
        try {
            const list = await ctx.service.werewolf.mall.getAllFrameList();
            this.respSuccData(list)
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateFrameCondition() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.updateFrameCondition(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async insertFrameCondition() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.insertFrameCondition(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }

    public async updateFrameConditionDelsign() {
        const { ctx, logger } = this;
        try {
            const requestBody = ctx.request.body;
            await ctx.service.werewolf.mall.updateFrameConditionDelsign(requestBody);
            ctx.body = {err_code: HttpErr.Success, err_msg: 'ok'};
            ctx.status = HttpErr.Success;
        } catch (error) {
            logger.error(error);
            const err: IerrorMsg = { err_code: HttpErr.BadRequest, err_msg: '参数有误' };
            ctx.body = err;
            ctx.status = HttpErr.BadRequest; //请求参数错误 401Unauthorized未鉴权
        }
    }
}
