/*
 * @Description: 商城道具服务类
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: z<PERSON><PERSON>
 * @Date: 2020-04-24 11:35:39
 * @LastEditors: zhanglu
 * @LastEditTime: 2021-12-16 14:28:50
 */
import BaseMegaService from './BaseMegaService';
import { MallItemBaseType, MallItemBaseInfo, CoinOperation, UploadTagInfoReq } from './../../model/mallItemManager';
export default class GameAreaService extends BaseMegaService {

    public getConfRedisName(env: string) {
        let redisName = "gameAreaRedis"
        if (env == "beta") {
            redisName = "confBetaRedis"
        }
        return redisName
    }

    public async getGameList(req: any) {
        const { app, ctx, logger } = this;
        try {
            let minVersion = "test";
            if (req.env == "prod") {
                minVersion = "prod";
            }

            let sql = ` SELECT * FROM tserver_config 
                WHERE minVersion = '${minVersion}'
                AND type > 9000 AND type < 10000
            `;

            let gameList = await this.selectList(sql, []);
            let resList: any = [];
            for (let game of gameList) {
                const area = await app.redis.get(this.getConfRedisName(req.env)).hget("wf_tournament_area", game.type);
                if (area && area.length > 0) {
                    game.area = JSON.parse(area);
                    resList.push({ ...game, ...game.area })
                } else {
                    let area = {
                        gciList: [-1],
                        time: 180,
                        timeAddFlag: false,
                        boomFlag: true,
                        buyRoleFlag: false,
                        giftFlag: false,
                        showFlag: false,
                        audiencnFlag: true,
                        pId: 0,
                        vId: 0,
                        bId: 0,
                        bg: 0,
                        anDp: "0",
                        playAvatar: "9",
                        frame: "0",
                        i2DU: "0",
                        audienceAvatar: "6",
                    }
                    resList.push({ ...game, ...area })
                }
            }

            return resList;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getGameShowList(req: any) {
        const { app, ctx, logger } = this;
        try {
            let minVersion = "test";
            if (req.env == "prod") {
                minVersion = "prod";
            }

            let sql = ` SELECT * FROM tserver_config 
                WHERE minVersion = '${minVersion}'
                AND type >= 10000 AND type < 20000
            `;

            let gameList = await this.selectList(sql, []);
            let resList: any = [];
            for (let game of gameList) {
                const area = await app.redis.get(this.getConfRedisName(req.env)).hget("wf_performance_area", game.type);
                if (area && area.length > 0) {
                    game.area = JSON.parse(area);
                    resList.push({ ...game, ...game.area })
                } else {
                    let area = {
                        round: 3,
                        champion: 5,
                        runnerUp: 3,
                        third: 1,
                        gciList: [43],
                        time: 180,
                        timeAddFlag: false,
                        boomFlag: false,
                        buyRoleFlag: false,
                        giftFlag: true,
                        showFlag: true,
                        audiencnFlag: true,
                        pId: 0,
                        vId: 0,
                        bId: 0,
                        bg: 0,
                        anDp: "0",
                        playAvatar: "9",
                        frame: "0",
                        i2DU: "0",
                        audienceAvatar: "6",
                    }
                    resList.push({ ...game, ...area })
                }
            }

            return resList;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async getGameConfigList() {
        const { app, ctx, logger } = this;
        try {
            let sql = ` SELECT *, \`desc\` AS name FROM tgameconfig`;
            return await this.selectList(sql, []);
        } catch (error) {
            logger.error(error);
            throw error
        }
    }


    public async updateGameConfig(req: any) {
        const { app, logger } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
            UPDATE tserver_config 
            SET 
            name = ?,
            flag = ?,
            gameConfigID = ?,
            teamGameConfigID = ?
            WHERE
                id = ?
                        `;
            await conn.query(sql, [
                req.name,
                req.flag,
                req.teamGameConfigID,
                req.teamGameConfigID,
                req.id]);

            await this.app.redis.get(this.getConfRedisName(req.env)).hset("wf_tournament_area", req.type, JSON.stringify(req.redis));
            await conn.commit(); // 提交事务

        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！

            logger.error(error);

            throw error;
        }
    }

    public async updateGameConfigFlag(req: any) {
        const { app, logger } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
            UPDATE tserver_config 
            SET 
            flag = ?
            WHERE
                id = ?
                        `;
            await conn.query(sql, [
                req.flag,
                req.id]);
            await conn.commit(); // 提交事务
        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            logger.error(error);
            throw error;
        }
    }

    public async updateGameShow(req: any) {
        const { app, logger } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
            UPDATE tserver_config 
            SET 
            name = ?,
            flag = ?,
            gameConfigID = ?,
            teamGameConfigID = ?
            WHERE
                id = ?
                        `;
            await conn.query(sql, [
                req.name,
                req.flag,
                req.teamGameConfigID,
                req.teamGameConfigID,
                req.id]);

            await this.app.redis.get(this.getConfRedisName(req.env)).hset("wf_performance_area", req.type, JSON.stringify(req.redis));
            await conn.commit(); // 提交事务

        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！

            logger.error(error);

            throw error;
        }
    }

    public async getAnimationList(req: any) {
        const { app, logger } = this;
        try {
            let content = ``;
            if (req.role == 999) {
                content = ` a.id, a.id AS item_id, a.name, 1020 AS cate_id `;
            } else if (req.role == 1000) {
                content = ` a.id, a.id AS item_id, a.name, 1010 AS cate_id `;
            } else {
                content = ` a.id, a.id AS item_id, a.name, 1031 AS cate_id `;
            }
            let sql = ` SELECT ${content} FROM animation a `;
            if (req.role == 2000) {
                sql += ` INNER JOIN animation_role r ON a.role = r.role AND a.role = ${req.role} AND a.bg != 2 `;
            } else {
                sql += ` INNER JOIN animation_role r ON a.role = r.role AND a.role = ${req.role} `;
            }
            sql += ` WHERE a.delsign = 0  ORDER BY a.id `;
            return await this.selectList(sql, []);
        } catch (error) {
            logger.error(error);
            throw error;
        }
    }

    public async getAvatarFrameList(req: any) {
        const { app, logger } = this;
        try {
            let sql = ` SELECT a.id, a.id AS item_id, a.name, 2010 AS cate_id FROM avatarframe a
                    WHERE delsign = 0
                    ORDER BY a.id `;
            return await this.selectList(sql, []);
        } catch (error) {
            logger.error(error);
            throw error;
        }
    }

    public async getUserList(req: any) {
        const { app, logger } = this;
        try {
            let sql = ` SELECT  u.\`no\`,u.nickname, 1 AS hasAdd 
                FROM tuser_match m 
                INNER JOIN tuser u ON u.no = m.user_no
                WHERE m.type = ?
                ORDER BY m.create_time DESC `;
            return await this.selectList(sql, [req.type]);
        } catch (error) {
            logger.error(error);
            throw error;
        }
    }

    public async getJudgeUserList(req: any) {
        const { app, logger } = this;
        try {
            let sql = ` SELECT  u.\`no\`,u.nickname, 1 AS hasAdd 
            FROM tuser_performance_judge m 
            INNER JOIN tuser u ON u.\`no\` = m.user_id
            WHERE m.type = ?
            ORDER BY m.create_time DESC `;
            return await this.selectList(sql, [req.type]);
        } catch (error) {
            logger.error(error);
            throw error;
        }
    }

    public async getSearchUserList(req: any) {
        const { app, logger } = this;
        let ids: any = req.userIds.split(",");
        try {
            let sql = ` SELECT tuser.\`no\`,tuser.nickname, IF(IFNULL(m.\`no\`, 0) > 0, 1, 0) AS hasAdd
            FROM tuser 
            LEFT JOIN tuser_match m ON m.user_no = tuser.\`no\` AND type = ?
            WHERE tuser.\`no\` IN (?) `;
            return await this.selectList(sql, [req.type, ids]);
        } catch (error) {
            logger.error(error);
            throw error;
        }
    }

    public async getSearchUserJudgeList(req: any) {
        const { app, logger } = this;
        let ids: any = req.userIds.split(",");
        try {
            let sql = `SELECT tuser.\`no\`,tuser.nickname, IF(IFNULL(m.\`id\`, 0) > 0, 1, 0) AS hasAdd
            FROM tuser 
            LEFT JOIN tuser_performance_judge m ON m.user_id = tuser.\`no\` AND type = ?
            WHERE tuser.\`no\` IN (?)  `;
            return await this.selectList(sql, [req.type, ids]);
        } catch (error) {
            logger.error(error);
            throw error;
        }
    }

    public async insertUserMatch(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sqlStr = `
            INSERT INTO 
            tuser_match
                (user_no, type, create_time) 
            VALUES 
                (?, ?, NOW())
            `;
            await conn.query(sqlStr, [req.user_id, req.type]);
            await conn.commit(); // 提交事务_

        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            logger.error(error);
            throw error
        }
    }

    public async deleteUserMatch(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sqlStr = `
            DELETE from 
            tuser_match
            WHERE user_no = ? AND type = ?
            `;
            await conn.query(sqlStr, [req.user_id, req.type]);
            await conn.commit(); // 提交事务_

        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            logger.error(error);
            throw error
        }
    }

    public async deleteAllUserMatch(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sqlStr = `
            DELETE from 
            tuser_match
            WHERE type = ?
            `;
            await conn.query(sqlStr, [req.type]);
            await conn.commit(); // 提交事务_

        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            logger.error(error);
            throw error
        }
    }

    public async insertUserJudgeMatch(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sqlStr = `
            INSERT INTO 
            tuser_performance_judge
                (user_id, type, create_time) 
            VALUES 
                (?, ?, NOW())
            `;
            await this.app.redis.get(this.getConfRedisName(req.env)).sadd("wf_performance_judges:" + req.type, req.user_id);

            await conn.query(sqlStr, [req.user_id, req.type]);
            await conn.commit(); // 提交事务_

        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            logger.error(error);
            throw error
        }
    }

    public async deleteUserJudgeMatch(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sqlStr = `
            DELETE from 
            tuser_performance_judge
            WHERE user_id = ? AND type = ?
            `;
            await this.app.redis.get(this.getConfRedisName(req.env)).srem("wf_performance_judges:" + req.type, req.user_id);
            await conn.query(sqlStr, [req.user_id, req.type]);
            await conn.commit(); // 提交事务_

        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            logger.error(error);
            throw error
        }
    }

    public async deleteAllUserJudgeMatch(req) {
        const { app, ctx, logger } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sqlStr = `
            DELETE from 
            tuser_performance_judge
            WHERE type = ?
            `;
            await this.app.redis.get(this.getConfRedisName(req.env)).del("wf_performance_judges:" + req.type);
            await conn.query(sqlStr, [req.type]);
            await conn.commit(); // 提交事务_

        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！
            logger.error(error);
            throw error
        }
    }


    public async getGameArenaList(req: any) {
        const { app, ctx, logger } = this;
        try {
            let minVersion = "test";
            if (req.env == "prod") {
                minVersion = "prod";
            }

            let sql = ` SELECT * FROM tserver_config 
                WHERE minVersion = '${minVersion}'
                AND type >= 20000 AND type < 30000
            `;

            let gameList = await this.selectList(sql, []);
            let resList: any = [];
            for (let game of gameList) {
                const area = await app.redis.get(this.getConfRedisName(req.env)).hget("wf_arena_area", game.type);
                if (area && area.length > 0) {
                    game.area = JSON.parse(area);
                    resList.push({ ...game, ...game.area })
                } else {
                    let area = {
                        season: 2,
                        time: 180,
                        gciList: [2],
                        timeAddFlag: false,
                        boomFlag: false,
                        buyRoleFlag: false,
                        giftFlag: true,
                        showFlag: true,
                        audiencnFlag: true,
                        pId: 0,
                        vId: 0,
                        bId: 0,
                        bg: 0,
                        anDp: "0",
                        frame: "0",
                        i2DU: "0",
                        audienceAvatar: "6",
                        playAvatarList: [
                            "1",
                            "2",
                            "3",
                            "4",
                            "5",
                            "6",
                            "7",
                            "8",
                            "9",
                            "10",
                            "11",
                            "12"
                        ],
                        openTime: [
                            {
                              startTime: "2021-01-01 00:00:00",
                              endTime: "2021-01-01 00:00:00"
                            }
                          ]                       
                    }
                    resList.push({ ...game, ...area })
                }
            }
            return resList;
        } catch (error) {
            logger.error(error);
            throw error
        }
    }

    public async updateGameArena(req: any) {
        const { app, logger } = this;
        const db = app.mysql.get('werewolf');
        //开启事物
        const conn = await db.beginTransaction();
        try {
            let sql = ` 
            UPDATE tserver_config 
            SET 
            name = ?,
            flag = ?,
            gameConfigID = ?,
            teamGameConfigID = ?
            WHERE
                id = ?
                        `;
            await conn.query(sql, [
                req.name,
                req.flag,
                req.teamGameConfigID,
                req.teamGameConfigID,
                req.id]);

            await this.app.redis.get(this.getConfRedisName(req.env)).hset("wf_arena_area", req.type, JSON.stringify(req.redis));
            await conn.commit(); // 提交事务

        } catch (error) {
            await conn.rollback(); // 一定记得捕获异常后回滚事务！

            logger.error(error);

            throw error;
        }
    }
}
