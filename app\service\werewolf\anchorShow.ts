import { IanchorShowDayReq, IanchorShowDayResp, IanchorShowScore, IchangeKingReq, IchangeReadyReq, IchangeScoreReq, IchangeVotableReq, IchangeWinCampReq } from "../../model/wfAnchorShow";
import BaseMegaService from "./BaseMegaService";


export default class AnchorShownService extends BaseMegaService {

    //当前第几季

    private season = 2

    /**
     * 第几季
     */
    public async getInfo(): Promise<number> {
        return this.season
    }

    /**
     * 获得某天信息
     * @param season 
     * @param season_day 
     */
    public async getInfoByDay(req: IanchorShowDayReq): Promise<IanchorShowDayResp> {
        const { app, logger } = this;
        try {

            //查询本期指定天状态
            let status = await this.selectOne(`SELECT * FROM activity2021_show_status 
            WHERE season = ? AND season_day = ? ;`,
                [req.season, req.season_day])

            //查询指定天对局结果
            let roundList = await this.selectList(`SELECT win_camp FROM activity2021_show_round_record WHERE season = ? AND season_day = ?;`,
                [req.season, req.season_day])
            let roundWindList = [roundList[0].win_camp, roundList[1].win_camp]
            //查询主播列表
            let anchorList: IanchorShowScore[] = await this.selectList(`
            SELECT * FROM activity2021_show_anchor_score WHERE season = ? AND season_day = ?
            ORDER BY id ASC
            `, [req.season, req.season_day])

            let resp: IanchorShowDayResp = {
                dayStatus: {
                    season_day: req.season_day,
                    is_votable: status.is_votable,
                    anchor_ready: status.anchor_ready,
                    roundWinList: roundWindList
                },
                scoreList: anchorList,
            }

            return resp;
        } catch (error) {
            logger.error(error);
            throw error;
        }
    }

    /**
     * 变更主播准备状态
     * @param req 
     */
    public async changeAnchorReady(req: IchangeReadyReq) {
        this.logger.debug(`UPDATE activity2021_show_status SET anchor_ready =? WHERE season = ? AND season_day = ?;`,
            [req.anchor_ready, req.season, req.season_day]);
        await this.execSql(`UPDATE activity2021_show_status SET anchor_ready =? WHERE season = ? AND season_day = ?;`,
            [req.anchor_ready, req.season, req.season_day]
        )
    }

    /**
     * 变更可竞猜状态
     * @param req 
     */
    public async changeVotable(req: IchangeVotableReq) {
        await this.execSql(`UPDATE activity2021_show_status SET is_votable =? WHERE season = ? AND season_day = ?;`,
            [req.is_votable, req.season, req.season_day]
        )
    }

    /**
     * 变更狼王
     * @param req 
     */
    public async changeKing(req: IchangeKingReq) {
        //设置狼王
        await this.execSql(`UPDATE activity2021_show_anchor_score SET is_king = 1 WHERE id = ? AND season  = ? AND season_day = ?;`,
            [req.king_anchor_id, req.season, req.season_day]
        )
        //设置其他为非狼王
        await this.execSql(`UPDATE activity2021_show_anchor_score SET is_king = 0 WHERE id != ? AND season  = ? AND season_day = ?;`,
            [req.king_anchor_id, req.season, req.season_day]
        )
        //设置活动状态狼王
        await this.execSql(`UPDATE activity2021_show_status SET wolf_king_id = ? WHERE  season  = ? AND season_day = ?;`,
            [req.king_anchor_id, req.season, req.season_day]
        )
    }

    /**
     * 增加积分
     * @param req 
     */
    public async incScore(req: IchangeScoreReq) {
        //增加积分
        await this.execSql(`UPDATE activity2021_show_anchor_score SET score = score+1 WHERE id = ? AND season  = ? AND season_day = ?;`,
            [req.anchor_id, req.season, req.season_day]
        )
    }

    /**
     * 减少积分
     * @param req 
     */
    public async decScore(req: IchangeScoreReq) {
        //增加积分
        await this.execSql(`UPDATE activity2021_show_anchor_score SET score = score-1 WHERE id = ? AND season  = ? AND season_day = ?;`,
            [req.anchor_id, req.season, req.season_day]
        )
    }

    /**
     * 更改获胜阵营
     * @param req 
     */
    public async changeWinCamp(req: IchangeWinCampReq) {
        //增加积分
        await this.execSql(`INSERT INTO activity2021_show_round_record (season, season_day, round, win_camp) VALUES (?, ?, ?, ?) ON DUPLICATE KEY UPDATE win_camp = ?;`,
            [req.season, req.season_day, req.round, req.win_camp, req.win_camp]
        )
    }

}