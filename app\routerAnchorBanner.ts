/*
 * @Description: 主播板块路由
 * @version: 1.0.0
 * @Company: sdbean
 * @Author: jiawen.wang
 * @Date: 2020-09-29 15:23:11
 */

import { Application } from 'egg';
import { AccessRouteId } from './model/accessRouteCof';
// accCtr(AccessRouteId.wolf_anchor_banner),
const load = (API_VERSION: string, app: Application, accCtr: (routeId: number) => any) => {
    const { router, controller } = app
    router.post(`${API_VERSION}/werewolf/anchorBanner/getAnchorList`, accCtr(AccessRouteId.wolf_anchor_banner_control), controller.werewolf.anchorBanner.getAnchorList);
    router.post(`${API_VERSION}/werewolf/anchorBanner/createAnchorBanner`, accCtr(AccessRouteId.wolf_anchor_banner_control), controller.werewolf.anchorBanner.createAnchorBanner);
    router.post(`${API_VERSION}/werewolf/anchorBanner/uploadBannerImg`, accCtr(AccessRouteId.wolf_anchor_banner_control), controller.werewolf.anchorBanner.uploadBannerImg);
    router.post(`${API_VERSION}/werewolf/anchorBanner/delAnchorBanner`, accCtr(AccessRouteId.wolf_anchor_banner_control), controller.werewolf.anchorBanner.delAnchorBanner);
    router.post(`${API_VERSION}/werewolf/anchorBanner/updateAnchorBanner`, accCtr(AccessRouteId.wolf_anchor_banner_control), controller.werewolf.anchorBanner.updateAnchorBanner);
}
export default load